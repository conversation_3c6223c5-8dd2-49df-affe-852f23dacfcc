package com.bizark.op.web.controller.api.v2.account;

import cn.hutool.core.collection.CollectionUtil;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.enm.promotions.AmzPromotionsChannelEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.account.AccountCommonWarehouse;
import com.bizark.op.api.entity.op.account.AccountDTO;
import com.bizark.op.api.request.AmzPromotionsShopQueryRequest;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.sale.TemuCategoryService;
import com.bizark.op.api.vo.sale.SelectorTreeNode;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.web.controller.api.AbstractApiController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/v2/accounts")
public class AccountController extends AbstractApiController {


    @Autowired
    private AccountService accountService;

    @Autowired
    private TemuCategoryService temuCategoryService;

    @GetMapping("/list")
    public TableDataInfo list(Account account, Integer contextId) {
        startPage();
        account.setOrgId(contextId);
        account.querySetting();
        List<Account> accounts = accountService.selectAccountList(account);
        return getDataTable(accounts);
    }

    @GetMapping("/listAll")
    public ApiResponseResult listAll(Account account, Integer contextId) {
        account.setOrgId(contextId);
        account.querySetting();
        List<Account> accounts = accountService.selectAccountList(account);
        return ApiResponseResult.buildSuccessResult(accounts);
    }


    @PostMapping("/saveOrUpdate")
    public ApiResponseResult saveOrUpdate(@RequestBody AccountDTO account, @RequestParam Integer contextId) {
        accountService.saveOrUpdate(account);
        return ApiResponseResult.buildSuccessResult();
    }




    @GetMapping("/investmentSelector")
    public ApiResponseResult investmentSelector(@RequestParam("contextId") Integer contextId) {
        List<String> investments = temuCategoryService.distinctInvestment();
        return ApiResponseResult.buildSuccessResult(investments);

//        return ApiResponseResult.buildSuccessResult(accountService.investmentSelector(contextId));
    }


    @GetMapping("/temuAccessInfo")
    public ApiResponseResult temuAccessInfo(Integer orgId,Integer id){
        accountService.syncTemuAccessInfo(orgId,id);
        return ApiResponseResult.buildSuccessResult();
    }






    @GetMapping("/getWarehouseList")
    public ApiResponseResult getWarehouseList(
            @RequestParam("contextId") Integer contextId,
            @RequestParam("accountFlag") String accountFlag) {
        List<AccountCommonWarehouse> storeWarehouse = accountService.getStoreWarehouse(contextId, accountFlag);
        return ApiResponseResult.buildSuccessResult(storeWarehouse);
    }


    @GetMapping("/companySelector")
    public ApiResponseResult companySelector(@RequestParam("contextId") Integer contextId) {
        List<SelectorTreeNode<String>> nodes = accountService.companySelector(contextId);
        SelectorTreeNode<String> node = new SelectorTreeNode<>();
        node.setName("无");
        node.setValue("无");
        nodes.add(node);
        return ApiResponseResult.buildSuccessResult(nodes);
    }

}
