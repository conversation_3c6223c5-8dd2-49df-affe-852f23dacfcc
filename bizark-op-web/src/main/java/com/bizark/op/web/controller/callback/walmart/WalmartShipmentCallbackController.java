package com.bizark.op.web.controller.callback.walmart;

import com.alibaba.fastjson.JSONObject;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.entity.op.walmart.WalmartShipmentGtin;
import com.bizark.op.api.entity.op.walmart.WalmartShipmentGtinWithShipmentId;
import com.bizark.op.api.service.mar.WalmartInboundTagService;
import com.bizark.op.api.service.mar.WalmartShipmentAttachService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: Ailill
 * @Date: 2024/12/23 10:39
 */
@RestController
@RequestMapping(value = "/callback/walmart/shipment")
public class WalmartShipmentCallbackController {

    @Autowired
    private WalmartShipmentAttachService walmartShipmentAttachService;

    @Autowired
    private WalmartInboundTagService walmartInboundTagService;

    @GetMapping(value = "/getGtin")
    public List<WalmartShipmentGtin> getNoGtinShipment() {

        return walmartShipmentAttachService.getNoGtinShipment();
    }

    @PostMapping(value = "/getGtinByShipmentIdList")
    public List<WalmartShipmentGtinWithShipmentId> getGtinByShipmentIdList(@RequestBody List<String> shipmentIdList) {

        return walmartInboundTagService.getGtinByShipmentIdList(shipmentIdList);
    }

}
