package com.bizark.op.web.controller.api.v2.cuscenter;

import com.alibaba.fastjson.JSON;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.form.customer.CusCenterOrderReissueQuery;
import com.bizark.op.api.service.customer.ICusCenterOrderReissueService;
import com.bizark.op.api.vo.customer.CusCenterOrderReissueDTO;
import com.bizark.op.api.vo.customer.CusCenterOrderReissueVO;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.function.page.PageHelperTemplate;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * VIEWController
 *
 * <AUTHOR>
 * @date 2022-05-17
 */
@RestController("CusCenterOrderReissueController")
@SecurityRequirement(name = "Authorization")
@RequestMapping(value = "/api/v2/cusCenter/reissue")
@Tag(name = "cusCenterOrderReissue", description = "客服中心补发信息")
public class CusCenterOrderReissueController extends AbstractApiController {
    @Autowired
    private ICusCenterOrderReissueService cusCenterOrderReissueService;

    @Autowired
    private PageHelperTemplate<CusCenterOrderReissueDTO, CusCenterOrderReissueVO> pageHelperTemplate;


    /**
     * 查询补发信息列表
     *
     * @param query
     * @return
     */
    @GetMapping("/list/reissue")
    @RequiresPermissions(PermDefine.MAR_REISSUE_INFO)
    public TableDataInfo<CusCenterOrderReissueVO> list(CusCenterOrderReissueQuery query) {
        if (query.getContextId() == null || query.getContextId() < 1) {
            query.setContextId(getAuthUserEntity().getOrgId());
        }
        return pageHelperTemplate.execute(page -> cusCenterOrderReissueService.selectCusCenterReissueList(query, page));
    }

    /**
     * 导出补发信息列表
     *
     * @param query
     * @param response
     */
    @GetMapping("/original/export")
    public ApiResponseResult export(CusCenterOrderReissueQuery query, HttpServletResponse response) {
        if (query.getContextId() == null || query.getContextId() < 1) {
            query.setContextId(getAuthUserEntity().getOrgId());
        }
        cusCenterOrderReissueService.originalExportReissue(query, response, getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult("导出成功");

    }

    /**
     * 获取补发信息详细
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/detail/{id}")
    public ApiResponseResult<CusCenterOrderReissueVO> getInfo(@PathVariable("id") Long id) {
        return ApiResponseResult.buildSuccessResult(cusCenterOrderReissueService.detail(id));
    }


    @GetMapping("/async/export")
    public ApiResponseResult asyncExport(CusCenterOrderReissueQuery query) {
        if (query.getContextId() == null || query.getContextId() < 1) {
            query.setContextId(getAuthUserEntity().getOrgId());
        }
        String result = cusCenterOrderReissueService.asyncExportReissueList(JSON.toJSONString(query), query.getContextId());
        return ApiResponseResult.buildSuccessResult(result);

    }
}
