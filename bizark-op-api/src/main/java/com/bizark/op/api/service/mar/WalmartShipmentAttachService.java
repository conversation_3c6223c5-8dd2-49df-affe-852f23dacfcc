package com.bizark.op.api.service.mar;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.mar.WalmartShipmentAttach;
import com.bizark.op.api.entity.op.walmart.WalmartShipmentGtin;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * walmart 货件附件服务
 * @Author: Ailill
 * @Date: 2024/12/13 18:08
 */
public interface WalmartShipmentAttachService extends IService<WalmartShipmentAttach> {

    /**
     * @Description:
     * 定时拉取托盘标和箱标
     * @Author: wly
     * @Date: 2024/12/13 18:16
     * @Params: * @param null:
     * @Return: * @return: null
     **/

    void updateSyncAttachmentByPalletLabelAndCartonLabel();
    void updateSyncAttachmentByPalletLabelAndCartonLabel(Long id);

    /**
     * @Description:定时拉取BOL标(弃用)
     * @Author: wly
     * @Date: 2024/12/13 18:17
     * @Params: []
     * @Return: void
     **/
    void updateSyncAttachmentByCarrierLabel();


    /**
     * @Description: 定时更新同步附件是否已完整
     */
    void updateSyncAttachComplete();
    void testUpdateSyncAttachComplete();

    /**
     * 定时更新BOL号 弃用
     */
    void updateBolNumber();

    void pullBolAndUpdateBolNumber();
    void pullBolAndUpdateBolNumberById(Long id);

    void processBolAndBolNumber(String message);

    List<WalmartShipmentGtin> getNoGtinShipment();

    /**
     * 下载附件文件
     * @param query
     */
    String downLoadAttachFileAsync(String query);

    /**
     * 下载附件文件
     * @param attach
     * @param userEntity
     */
    void downLoadAttachFile(WalmartShipmentAttach attach, UserEntity userEntity);

    /**
     * 定时拉取发票信息
     */
    void pullNvoFreightInvoice();


    /**
     * 接收wfs货件发票信息
     * @param messageJson
     */
    void receiveNvoFreightInvoiceQueue(String messageJson);
}
