package com.bizark.op.api.service.amazon.fba;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.amazon.vendor.fba.model.*;
import com.bizark.op.api.entity.op.amazon.fba.DTO.*;
import com.bizark.op.api.entity.op.amazon.fba.MarFbaCartonSpec;
import com.bizark.op.api.entity.op.amazon.fba.MarFbaShipmentInfo;
import com.bizark.op.api.entity.op.amazon.fba.MarStaTaskInfo;
import com.bizark.op.api.entity.op.amazon.fba.VO.*;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import java.util.List;

/**
 *
 */
public interface MarStaTaskInfoService extends IService<MarStaTaskInfo> {


    /** 获取操作状态
     *
     * @param operationId
     * @param shopId
     * @return
     */
    InboundOperationStatusResponse getInboundOperationStatus(String operationId, Long shopId);


    /**
     * @param
     * @param query
     * @param contextId
     * @param shopId
     * @description: 查询商品列表
     * @author: Moore
     * @date: 2025/7/28 11:32
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarStaProductSkuVO>
     **/
    List<MarStaProductSkuVO> selectProductList(MarStaProductQueryDTO query, Integer contextId);


    /**
     * @param
     * @param contextId
     * @description: 查询箱规信息列表
     * @author: Moore
     * @date: 2025/7/28 11:32
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarStaProductSkuVO>
     **/
    List<MarStaProductSkuVO> selectCartonSpecList(Integer contextId, String erpSKu);


    /**
     * @param
     * @param query
     * @description:查询STA列表
     * @author: Moore
     * @date: 2025/7/23 17:29
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.MarStaTaskInfo>
     **/
    List<MarStaTaskInfoVO> selectStaTaskList(MarStaTaskListQueryDTO query);


    /**
     * Sta发货商品节点
     *
     * @param taskId
     * @return
     */
    MarStaTaskShipmentInfoDTO selectStaTaskListByShipmentSku(Integer taskId);


    /**
     * @param
     * @param query
     * @description: 暂存STA信息
     * @author: Moore
     * @date: 2025/7/24 11:38
     * @return: void
     **/
    MarStaTaskInfo saveStaTaskList(MarStaTaskShipmentInfoDTO query);

    /**
     * @param
     * @param query
     * @description: 提交STA任务
     * @author: Moore
     * @date: 2025/7/28 15:55
     * @return: String 货件任务ID
     **/
    InboundOperationStatusResponse subStaTask(MarStaTaskShipmentInfoDTO query);


    /**
     * 预处理方式校验
     *
     * @param query
     * @return
     */
    List<MarFbaVerifyPrepDetailsVO> pretreatmentMethodVerify(MarStaTaskShipmentInfoDTO query);


    /**
     * 确认包装组信息
     *
     * @param contextId
     * @param taskId
     * @return
     */
    InboundOperationStatusResponse confirmPackingOption(Integer contextId, Long taskId);


    /**
     * @param
     * @param contextId
     * @param taskId 任务主键
     * @description: 获取节点2装箱明细信息
     * @author: Moore
     * @date: 2025/8/6 17:36
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarStaPackGroupVO>
     **/
    List<MarStaPackGroupVO> staPickingGroupNodeSkus(Integer contextId, Long taskId);


    /**
     * @param
     * @param contextId
     * @param marStaPackingSkuVO
     * @param inboundPlanId
     * @param packingGroupId
     * @description:保存商品装箱信息
     * @author: Moore
     * @date: 2025/8/11 12:37
     * @return: void
     **/
    void staPickingNodeSkusSave(Integer contextId, MarStaPackGroupVO marStaPackingSkuVO, String inboundPlanId, String packingGroupId, String packMethod);


    /**
     * @param
     * @param contextId
     * @param inboundPlanId
     * @description:调用接口更新包装组信息
     * @author: Moore
     * @date: 2025/8/11 16:46
     * @return: void
     **/
    InboundOperationStatusResponse updateAmzPackInfo(Integer contextId, String inboundPlanId);


    /**
     * @param
     * @param contextId
     * @param taskId
     * @description: 生成预览信息
     * @author: Moore
     * @date: 2025/8/18 9:36
     * @return: void
     **/
    InboundOperationStatusResponse generatePlacementOptions(Integer contextId, Long taskId);


    /**
     * 货件预览
     *
     * @param contextId
     * @param taskId
     * @return
     */
    List<MarStaPlacementOptionsVO> listplacementoptions(Integer contextId, Long taskId);

    /**
     * @param
     * @param fbaTransportationOptionsDTOList
     * @description: 更新FBA信息
     * @author: Moore
     * @date: 2025/8/27 10:08
     * @return: void
     **/
    public void updateFabShipmentInfo(List<FbaConfirmCarrierDTO> fbaTransportationOptionsDTOList,Integer orgId);

    /**
     * 确认申报
     *
     * @param contextId
     * @param taskId
     * @param placementOptionId 操作ID
     */
    InboundOperationStatusResponse confirmPlacementOption(Integer contextId, Long taskId, String placementOptionId);


    /**
     * 保存FBA信息
     *
     * @param contextId
     * @param taskId
     */
    void saveFbaInfo(Integer contextId, Long taskId, MarFbaSaveDTO marFbaSaveDTO);


    String asyncExportPackingList(String jsonString);


    /**
     * @param
     * @param inboundPlanId
     * @param shipmentId
     * @param shopId
     * @description: 获取shipment信息，地址信息等
     * @author: Moore
     * @date: 2025/8/24 21:13
     * @return: com.bizark.op.api.amazon.vendor.fba.model.ShipmentInfoResponse
     **/
    ShipmentInfoResponse getShipment(String inboundPlanId, String shipmentId, Long shopId);

    /**
     * @param
     * @param contextId
     * @param taskId
     * @description:
     * @author: Moore
     * @date: 2025/8/24 21:17
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarFbaShipmentNodeVO>
     **/
    List<MarFbaShipmentNodeVO> getFbaNodeThree(Integer contextId, Long taskId);


    /**
     * @param
     * @param contextId
     * @param name
     * @description:
     * @author: Moore
     * @date: 2025/8/25 11:49
     * @return: void
     **/
    void updateFbaName(Integer contextId, Long fbaId, String name);


    ListShipmentBoxes getListShipmentBoxes(String inboundPlanId, String shipmentId, Long shopId);

    /**
     * @param
     * @param contextId
     * @description: 获取送达时间区间信息
     * @author: Moore
     * @date: 2025/8/25 16:45
     * @return: void
     **/
    List<ListDeliveryWindowOptions.DeliveryWindowOptionsBean> deliveryTimeSelect(Integer contextId, Long taskId, String shipmentId);


    /**
     * 生成承运商
     *
     * @param contextId
     * @param taskId
     * @param shipmentId
     */
    void generateTransports(Integer contextId, List<FbaTransportationOptionsDTO> fbaTransportationOptionsDTOList);


    ListTransportationOptionsVO listTransportationOptions(Integer contextId, FbaTransportationOptionsDTO fbaTransportationOptionsDTOList);


    /**
     * 确认送达时间窗口
     *
     * @param contextId
     * @param inboundPlanId          任务ID
     * @param shipmentId             FBAID
     * @param deliveryWindowOptionId 送达时间窗口ID
     */
    InboundOperationStatusResponse confirmDeliveryWindowOptions(Integer contextId, String inboundPlanId, String shipmentId, String deliveryWindowOptionId);

    /**
     * 确认FBA承运商
     *
     * @param contextId
     * @param
     * @param
     * @param
     */
    InboundOperationStatusResponse confirmTransportationOptions(Integer contextId,  List<FbaConfirmCarrierDTO> fbaConfirmCarrierDTOS);

    /**
     * 查询打印fnsku信息
     *
     * @param contextId
     * @param fbaFnskuPrintDTO
     * @return
     */
    List<MarFnSkuPrintVO> printFnskuSelect(Integer contextId, FbaFnskuPrintDTO fbaFnskuPrintDTO);



    /**
     * 更新Box中trakcing信息
     *
     * @param marFbaCartonSpecs
     */
    CreateInboundPlanResponse updateBoxTraking(String shipmentId,List<MarFbaCartonSpec> marFbaCartonSpecs);


    /**
     * 更新FBA中BOL和跟踪编号PRO
     *
     * @param marFbaShipmentInfo
     */
    CreateInboundPlanResponse setFbaPalletBolAndPro(Integer contextId, MarFbaShipmentInfo marFbaShipmentInfo);



    /**删除STA信息
     *
     * @param staId
     */
    void deleteFbaInfo(Long staId);

    /** 取消STA信息
     *
     * @param staId
     */
    InboundOperationStatusResponse cancelSta(Long staId);


    /** 更新节点信息（第四步下一步）
     *
     * @param contextId
     * @param taskId
     */
    void udpateNodeNextstep(Integer contextId, Long taskId);

    /** 导出STA列表信息
     *
     * @param query
     * @return
     */
    String marStaTaskInfoExport(MarStaTaskListQueryDTO query);


    /**  导出FBA调用
     *
     * @param query
     * @param authUserEntity
     */
    void orgExportStaList(MarStaTaskListQueryDTO query, UserEntity authUserEntity);


    /**
     * 刷新FBA状态
     *
     * @param contextId
     */
    void refreshFbaStatusJob(Integer contextId);

}
