package com.bizark.op.api.entity.op.amazon.fba.VO;

import com.bizark.op.api.entity.op.amazon.fba.MarFbaPalletDetail;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName MarFbaPalletInfoVO
 * @Description FBA对应托拍信息VO
 * <AUTHOR>
 * @Date 2025/9/17 11:08
 */
@Data
public class MarFbaPalletInfoVO {

    /**
     * 货运等级
     */
    private String freightLevel;

    /**
     * 申报价值
     */
    private BigDecimal declareValue;

    /**
     * 申报价值币种
     */
    private String declareCurrency;


    /**
     * 托拍明细信息
     */
    private List<MarFbaPalletDetail> marFbaPalletDetails;


}
