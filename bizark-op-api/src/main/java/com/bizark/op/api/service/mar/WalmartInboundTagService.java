package com.bizark.op.api.service.mar;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.mar.WalmartInboundTag;
import com.bizark.op.api.entity.op.walmart.WalmartShipmentGtinWithShipmentId;

import java.util.List;

/**
 * 下载沃尔玛的GTIN标签服务接口
 *
 * @Author: Ailill
 * @Date: 2024/12/11 18:05
 */
public interface WalmartInboundTagService extends IService<WalmartInboundTag> {
    void processWalmartInboundTag(String messageJson);
    void skuMappingTitleChange(String messageJson);

    List<WalmartShipmentGtinWithShipmentId> getGtinByShipmentIdList(List<String> shipmentIdList);
}
