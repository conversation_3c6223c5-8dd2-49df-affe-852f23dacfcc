package com.bizark.op.api.entity.op.ticket;

import com.bizark.op.common.annotation.Excel;
import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 工单优先级信息对象 sc_ticket_priority
 *
 * <AUTHOR>
 * @date 2023-04-20
 */

@Data
public class ScTicketPriority extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 组织id
     */
    private Long organizationId;


    /**
     * 工单类型主键
     */
    @Excel(name = "工单类型主键")
    private Long dictId;

    /**
     * 工单类型
     */
    @Excel(name = "工单类型")
    private String dictType;

    /**
     * 字典值
     */
    private String dictValue;

    /**
     * 字典名称（工单名称）
     */
    private String dictName;


    /**
     * 细分类型（1.customer-客服 2.operation-运营 3.refund-退款 4.shipping-发货）
     */
    private String segmentType;


    /**
     * 优先级(Low,Medium,High)
     */
    @Excel(name = "优先级(Low,Medium,High)")
    private String priority;


    /**
     * 开启状态（Y:开启 N:关闭）
     */
    private String openStatus;

}
