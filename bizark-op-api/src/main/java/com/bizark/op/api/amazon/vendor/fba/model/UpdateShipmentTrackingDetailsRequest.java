package com.bizark.op.api.amazon.vendor.fba.model;

import java.util.List;

/**
 * <AUTHOR> @ClassName UpdateShipmentTrackingDetailsRequest
 * @description: 更新Box请求参数
 * @date 2025年09月09日
 */
public class UpdateShipmentTrackingDetailsRequest {


    /**
     * trackingDetails : {"spdTrackingDetail":{"spdTrackingItems":[{"boxId":"FBA10ABC0YY100001","trackingId":"FBA10002000"}]}}
     */

    private TrackingDetailsBean trackingDetails;


    public TrackingDetailsBean getTrackingDetails() {
        return trackingDetails;
    }

    public void setTrackingDetails(TrackingDetailsBean trackingDetails) {
        this.trackingDetails = trackingDetails;
    }

    public static class TrackingDetailsBean {
        /**
         * spdTrackingDetail : {"spdTrackingItems":[{"boxId":"FBA10ABC0YY100001","trackingId":"FBA10002000"}]}
         */

        private SpdTrackingDetailBean spdTrackingDetail;

        private LtlTrackingDetail ltlTrackingDetail;

        // LTL  设置提货单号 及跟踪编号   Less-Than-Truckload (LTL)
        public static class LtlTrackingDetail {
            private String billOfLadingNumber;

            private List<String> freightBillNumber;

            public String getBillOfLadingNumber() {
                return billOfLadingNumber;
            }

            public void setBillOfLadingNumber(String billOfLadingNumber) {
                this.billOfLadingNumber = billOfLadingNumber;
            }

            public List<String> getFreightBillNumber() {
                return freightBillNumber;
            }

            public void setFreightBillNumber(List<String> freightBillNumber) {
                this.freightBillNumber = freightBillNumber;
            }
        }


        //小包裹 tracking               Parcel Delivery (SPD)
        public static class SpdTrackingDetailBean {
            private List<SpdTrackingItemsBean> spdTrackingItems;

            public List<SpdTrackingItemsBean> getSpdTrackingItems() {
                return spdTrackingItems;
            }

            public void setSpdTrackingItems(List<SpdTrackingItemsBean> spdTrackingItems) {
                this.spdTrackingItems = spdTrackingItems;
            }

            public static class SpdTrackingItemsBean {
                /**
                 * boxId : FBA10ABC0YY100001
                 * trackingId : FBA10002000
                 */

                private String boxId;
                private String trackingId;

                public String getBoxId() {
                    return boxId;
                }

                public void setBoxId(String boxId) {
                    this.boxId = boxId;
                }

                public String getTrackingId() {
                    return trackingId;
                }

                public void setTrackingId(String trackingId) {
                    this.trackingId = trackingId;
                }
            }
        }

        public LtlTrackingDetail getLtlTrackingDetail() {
            return ltlTrackingDetail;
        }

        public void setLtlTrackingDetail(LtlTrackingDetail ltlTrackingDetail) {
            this.ltlTrackingDetail = ltlTrackingDetail;
        }

        public SpdTrackingDetailBean getSpdTrackingDetail() {
            return spdTrackingDetail;
        }

        public void setSpdTrackingDetail(SpdTrackingDetailBean spdTrackingDetail) {
            this.spdTrackingDetail = spdTrackingDetail;
        }



    }
}
