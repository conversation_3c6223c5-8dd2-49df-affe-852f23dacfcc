package com.bizark.op.api.entity.op.amazon.fba;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * FBA货件计划表
 * @TableName mar_fba_shipment_info
 */
@TableName(value ="mar_fba_shipment_info")
@Data
public class MarFbaShipmentInfo extends BaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 组织ID
     */
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * 店铺ID
     */
    @TableField(value = "shop_id")
    private Long shopId;

    /**
     * 任务ID
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 货件名称
     */
    @TableField(value = "shipment_name")
    private String shipmentName;

    /**
     * 任务编号
     */
    @TableField(value = "inbound_plan_id")
    private String inboundPlanId;

    /**
     * 选项ID
     */
    @TableField(value = "placement_option_Id")
    private String placementOptionId;

    /**
     * 货件编号
     */
    @TableField(value = "shipment_id")
    private String shipmentId;

    /**
     * 货件唯一单号
     */
    @TableField(value = "shipment_confirmation_id")
    private String shipmentConfirmationId;

    /**
     * 预计总费用
     */
    @TableField(value = "plan_fee")
    private BigDecimal planFee;

    /**
     * 币种
     */
    private String currency;

    /**
     * 入库区域
     */
    @TableField(value = "warehouse_area")
    private String warehouseArea;

    /**
     * 物流中心编码
     */
    @TableField(value = "logistics_center")
    private String logisticsCenter;

    /**
     * reference_id
     */
    @TableField(value = "referece_id")
    private String refereceId;

    /**
     * 货件状态 ABANDONED, CANCELLED, CHECKED_IN, CLOSED, DELETED, DELIVERED, IN_TRANSIT, MIXED, READY_TO_SHIP, RECEIVING, SHIPPED, UNCONFIRMED, WORKING
     */
    @TableField(value = "shipment_state")
    private String shipmentState;

    /**
     * 发货时间
     */
    @TableField(value = "shipping_time")
    private Date shippingTime;

    /**
     * 承运人:  USE_YOUR_OWN_CARRIER(其他)  AMAZON_PARTNERED_CARRIER(亚马逊合作承运人)
     */
    @TableField(value = "carrier")
    private String carrier;

    /**
     * 运输类型: GROUND_SMALL_PARCEL(小包裹快递)  FREIGHT_LTL(汽运零担)
     */
    @TableField(value = "shipping_type")
    private String shippingType;

    /**
     * 运输方式 1.空运、2.海运、3.陆运
     */
    @TableField(value = "shipping_method")
    private Long shippingMethod;

    /**
     * 承运方式
     */
    @TableField(value = "carrier_method")
    private String carrierMethod;

    /**
     * 承运商CODE:USPS DHLEX
     */
    @TableField(value = "carrier_code")
    private String carrierCode;

    /**
     * 送达时段
     */
    @TableField(value = "delivery_time")
    private Date deliveryTime;

    /**
     * 送达时段截止
     */
    @TableField(value = "delivery_time_to")
    private Date deliveryTimeTo;

    /**
     * 签收时间
     */
    @TableField(value = "sign_time")
    private Date signTime;

    /**
     * 发货产品数量
     */
    @TableField(value = "product_num")
    private Integer productNum;

    /**
     * 申报总数量
     */
    @TableField(value = "apply_num")
    private Integer applyNum;

    /**
     * 签收数量总
     */
    @TableField(value = "received_num")
    private Integer receivedNum;


    /**
     * 货物等级 -汽运零担使用
     */
    private String freightLevel;

    /**
     * 申报价值-汽运零担使用
     */
    private BigDecimal declareValue;

    /**
     * 申报价值币种-汽运零担使用
     */
    private String declareCurrency;


    /** 提货单号(BOL)-汽运零担使用
     *
     */
    private String bolNumber;


    /**
     * 跟踪编号(PRO)-汽运零担使用
     */
    private String proNumber;


    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建者id
     */
    @TableField(value = "created_by")
    private Integer createdBy;

    /**
     * 创建者名称
     */
    @TableField(value = "created_name")
    private String createdName;

    /**
     * 创建时间戳
     */
    @TableField(value = "created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新者ID
     */
    @TableField(value = "updated_by")
    private Integer updatedBy;

    /**
     * 更新者名称
     */
    @TableField(value = "updated_name")
    private String updatedName;

    /**
     * 更新时间戳
     */
    @TableField(value = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 删除者ID
     */
    @TableField(value = "disabled_by")
    private Integer disabledBy;

    /**
     * 删除者名称
     */
    @TableField(value = "disabled_name")
    private String disabledName;

    /**
     * 删除时间戳
     */
    @TableField(value = "disabled_at")
    private Date disabledAt;
}