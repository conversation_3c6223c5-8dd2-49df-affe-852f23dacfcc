package com.bizark.op.api.entity.op.amazon.fba.VO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * FBA货件列表响应
 *
 * <AUTHOR>
 */
@ApiModel("FBA货件节点响应信息")
@Data
public class MarFbaShipmentNodeVO {


    private Long id;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 任务主键
     */
    private Long taskId;

    /**
     * 任务编号
     */
    private String inboundPlanId;

    /**
     * 选择ID
     */
    private String placementOptionId;


    /**
     * 货件名称
     */
    private String shipmentName;


    /**
     * reference_id
     */
    private String refereceId;


    /**
     * 货件ID
     */
    private String shipmentId;


    /**
     * 货件单号
     */
    private String shipmentConfirmationId;


    /**
     * 物流中心编码
     */
    private String logisticsCenter;


    /**
     * 发货地址
     */
    private String shipFromAddress;

    /**
     * 发货方
     */
    private String shipFrom;



    /**
     * 配送地址
     */
    private String shipToAddress;


    /**
     * SellerSku数量
     */
    private Integer sellerSkuNumber;
    /**
     * 申报数量
     */
    private Integer applyNum;

    /**
     * 箱数
     */
    private Integer boxCount;


    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date shippingTime;


    /**
     * 承运人 USE_YOUR_OWN_CARRIER 其他承运人 AMAZON_PARTNERED_CARRIER 亚马逊合作承运人
     */
    private String carrier;


    /** 承运人 转义
     *
     */
    private String carrierStr;


    /**
     * 运输类型 GROUND_SMALL_PARCEL 小包裹快递 FREIGHT_LTL 汽运零担
     */
    private String shippingType;

    /**
     * 运输类型 转义
     */
    private String shippingTypeStr;


    /**
     * 运输方式 运输方式 1.空运、2.海运、3.陆运
     */
    private Long shippingMethod;


    /**
     * 运输方式转换 中文
     */
    private String shippingMethodStr;


    /**
     * 承运方式
     */
    private String carrierMethod;

    /**
     * 送达时段
     */
    private String deliveryTime;

    /**
     * 送达时段
     */
    private String deliveryTimeTo;


    /**
     * FBA状态
     */
    private String shipmentState;

    /**
     * 明细信息
     */
    private List<GoodsInfo> goodsInfos;


    /**
     * 汽运零担-托拍数
     */
    private Integer palletQuantity;

    /**
     * 汽运零担-总重量
     */
    private BigDecimal totalWeight;

    /**
     * 汽运零担-申报价值
     */
    private BigDecimal declareValue;

    /**
     * 汽运零担-申报价值币种
     */
    private String declareCurrency;

    /**
     * 汽运零担-总体积
     */
    private BigDecimal totalVolume;

    /**
     * 汽运零担-货物等级
     */
    private String freightLevel;


    /**
     * 提货单号(BOL)
     */
    private String bolNumber;


    /**
     * 跟踪编号(PRO)
     */
    private String proNumber;


    public static class GoodsInfo {

        private String imageUrl;

        private Integer applyNum;


        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public Integer getApplyNum() {
            return applyNum;
        }

        public void setApplyNum(Integer applyNum) {
            this.applyNum = applyNum;
        }
    }
}