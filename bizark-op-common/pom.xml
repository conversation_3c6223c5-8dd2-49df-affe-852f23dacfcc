<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bizark-op</artifactId>
        <groupId>com.bizark</groupId>
        <version>1.0.4-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bizark-op-common</artifactId>
    <packaging>jar</packaging>

    <name>bizark-op-common</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <!--Hutool工具包-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <!--<version>4.5.10</version>-->
        </dependency>
        <!-- https://mvnrepository.com/artifact/io.swagger.core.v3/swagger-models -->
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/io.swagger.core.v3/swagger-annotations -->
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.eonml</groupId>
            <artifactId>freemarker-excel</artifactId>
            <version>0.1.5</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.swagger.core.v3/swagger-integration -->
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-integration</artifactId>
        </dependency>
        <!--Hutool工具ExcelUtil依赖这个 https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml-->
        <!--<dependency>-->
            <!--<groupId>org.apache.poi</groupId>-->
            <!--<artifactId>poi-ooxml</artifactId>-->
            <!--<version>4.1.0</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <!--<version>2.6.0</version>-->
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-common</artifactId>
        </dependency>
        <!--配置中心-->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-conf-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-framework</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>base-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- pagehelper 分页插件 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>

        <!--        redis-->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>2.2.3.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.1.0</version>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>org.javassist</groupId>-->
            <!--<artifactId>javassist</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.bizark</groupId>-->
<!--            <artifactId>bizark-op-api</artifactId>-->
<!--            <version>1.0.4-SNAPSHOT</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->

        <!-- redis 缓存操作 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- dozer DozerUtils-->
        <dependency>
            <groupId>net.sf.dozer</groupId>
            <artifactId>dozer</artifactId>
            <version>${dozer.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <!--  增加easyExcel的      -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.2.1</version>
        </dependency>

        <!-- nb-bank -->
        <dependency>
            <groupId>com.nbcb</groupId>
            <artifactId>open-basic-sdk</artifactId>
            <version>${nbsdk.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-core</artifactId>-->
            <!--<exclusions>-->
                <!--&lt;!&ndash; Exclude Commons Logging in favor of SLF4j &ndash;&gt;-->
                <!--<exclusion>-->
                    <!--<groupId>commons-logging</groupId>-->
                    <!--<artifactId>commons-logging</artifactId>-->
                <!--</exclusion>-->
            <!--</exclusions>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-context</artifactId>-->
            <!--<exclusions>-->
                <!--&lt;!&ndash; Exclude Commons Logging in favor of SLF4j &ndash;&gt;-->
                <!--<exclusion>-->
                    <!--<groupId>commons-logging</groupId>-->
                    <!--<artifactId>commons-logging</artifactId>-->
                <!--</exclusion>-->
            <!--</exclusions>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-context-support</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-jdbc</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-orm</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-web</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-webmvc</artifactId>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-aop</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.freemarker</groupId>-->
            <!--<artifactId>freemarker</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-jms</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.fasterxml.jackson.dataformat</groupId>-->
            <!--<artifactId>jackson-dataformat-xml</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.thoughtworks.xstream</groupId>-->
            <!--<artifactId>xstream</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>redis.clients</groupId>-->
            <!--<artifactId>jedis</artifactId>-->
        <!--</dependency>-->
        <!--&lt;!&ndash; AspectJ &ndash;&gt;-->
        <!--<dependency>-->
            <!--<groupId>org.aspectj</groupId>-->
            <!--<artifactId>aspectjweaver</artifactId>-->
            <!--<version>${aspectj.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.aspectj</groupId>-->
            <!--<artifactId>aspectjrt</artifactId>-->
            <!--<version>${aspectj.version}</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>commons-net</groupId>-->
            <!--<artifactId>commons-net</artifactId>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>org.apache.commons</groupId>-->
            <!--<artifactId>commons-pool2</artifactId>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>org.mybatis</groupId>-->
            <!--<artifactId>mybatis</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.mybatis</groupId>-->
            <!--<artifactId>mybatis-spring</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>dom4j</groupId>-->
            <!--<artifactId>dom4j</artifactId>-->
            <!--<version>1.6</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>junit</groupId>-->
            <!--<artifactId>junit</artifactId>-->
        <!--</dependency>-->
        <!--&lt;!&ndash; Logging &ndash;&gt;-->
        <!--<dependency>-->
                <!--<groupId>org.slf4j</groupId>-->
                <!--<artifactId>slf4j-api</artifactId>-->
            <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>commons-beanutils</groupId>-->
            <!--<artifactId>commons-beanutils</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.apache.commons</groupId>-->
            <!--<artifactId>commons-lang3</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>commons-io</groupId>-->
            <!--<artifactId>commons-io</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>commons-fileupload</groupId>-->
            <!--<artifactId>commons-fileupload</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.apache.commons</groupId>-->
            <!--<artifactId>commons-collections4</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.slf4j</groupId>-->
            <!--<artifactId>jcl-over-slf4j</artifactId>-->
            <!--<scope>runtime</scope>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.apache.httpcomponents</groupId>-->
            <!--<artifactId>httpclient</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.apache.geronimo.specs</groupId>-->
            <!--<artifactId>geronimo-jms_1.1_spec</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.google.code.gson</groupId>-->
            <!--<artifactId>gson</artifactId>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>com.google.guava</groupId>-->
            <!--<artifactId>guava</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.bizark</groupId>-->
            <!--<artifactId>bizeventcollect</artifactId>-->
            <!--<version>${bizeventcollect.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>io.jsonwebtoken</groupId>-->
            <!--<artifactId>jjwt</artifactId>-->
            <!--<version>${jjwt.version}</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>com.fasterxml.jackson.datatype</groupId>-->
            <!--<artifactId>jackson-datatype-jsr310</artifactId>-->
            <!--<version>${jackson-datetype-jsr310.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.mybatis</groupId>-->
            <!--<artifactId>mybatis-typehandlers-jsr310</artifactId>-->
            <!--<version>${mybatis-typehandlers-jsr310.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>commons-collections</groupId>-->
            <!--<artifactId>commons-collections</artifactId>-->
            <!--<version>${commons-collections.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.apache.commons</groupId>-->
            <!--<artifactId>commons-math3</artifactId>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>org.apache.shiro</groupId>-->
            <!--<artifactId>shiro-spring</artifactId>-->
            <!--<version>${shiro.version}</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>org.apache.shiro</groupId>-->
            <!--<artifactId>shiro-ehcache</artifactId>-->
            <!--<version>${shiro.version}</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>org.mariuszgromada.math</groupId>-->
            <!--<artifactId>MathParser.org-mXparser</artifactId>-->
            <!--<version>${mXparser.version}</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>org.hibernate</groupId>-->
            <!--<artifactId>hibernate-core</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.hibernate</groupId>-->
            <!--<artifactId>hibernate-entitymanager</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.hibernate</groupId>-->
            <!--<artifactId>hibernate-envers</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.github.debop</groupId>-->
            <!--<artifactId>hibernate-redis</artifactId>-->
            <!--<version>${hibernate-redis.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework.data</groupId>-->
            <!--<artifactId>spring-data-jpa</artifactId>-->
            <!--<version>${spring-data-jpa.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework.data</groupId>-->
            <!--<artifactId>spring-data-commons</artifactId>-->
            <!--<version>${spring-data-common.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.fasterxml.jackson.core</groupId>-->
            <!--<artifactId>jackson-core</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.fasterxml.jackson.core</groupId>-->
            <!--<artifactId>jackson-databind</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.fasterxml.jackson.core</groupId>-->
            <!--<artifactId>jackson-annotations</artifactId>-->
        <!--</dependency>-->

        <!--&lt;!&ndash; JSR303 Bean Validator &ndash;&gt;-->
        <!--<dependency>-->
            <!--<groupId>org.hibernate.validator</groupId>-->
            <!--<artifactId>hibernate-validator</artifactId>-->
            <!--<version>${hibernate-validator.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>javax.validation</groupId>-->
            <!--<artifactId>validation-api</artifactId>-->
            <!--<version>${validation-api.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.projectlombok</groupId>-->
            <!--<artifactId>lombok</artifactId>-->
            <!--<scope>provided</scope>-->
            <!--<version>${lombok.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework</groupId>-->
            <!--<artifactId>spring-test</artifactId>-->
            <!--<version>${spring-test.version}</version>-->
            <!--<scope>test</scope>-->
        <!--</dependency>-->
        <!--&lt;!&ndash;钉钉&ndash;&gt;-->
        <!--<dependency>-->
            <!--<groupId>com.alibaba.dingtalk</groupId>-->
            <!--<artifactId>chatbot</artifactId>-->
            <!--<version>${dingtalk.version}</version>-->
        <!--</dependency>-->
        <!--&lt;!&ndash;阿里大鱼短信通道&ndash;&gt;-->
        <!--<dependency>-->
            <!--<groupId>com.aliyun</groupId>-->
            <!--<artifactId>aliyun-java-sdk-core</artifactId>-->
            <!--<version>${aliyun-core.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.aliyun</groupId>-->
            <!--<artifactId>aliyun-java-sdk-dysmsapi</artifactId>-->
            <!--<version>${aliyun-dysms.version}</version>-->
        <!--</dependency>-->
        <!--&lt;!&ndash;邮件推送通道&ndash;&gt;-->
        <!--<dependency>-->
            <!--<groupId>javax.mail</groupId>-->
            <!--<artifactId>javax.mail-api</artifactId>-->
            <!--<version>${java-mail.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.sun.mail</groupId>-->
            <!--<artifactId>javax.mail</artifactId>-->
            <!--<version>${javax-mail.version}</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- PDFBox -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>

    </dependencies>
</project>
