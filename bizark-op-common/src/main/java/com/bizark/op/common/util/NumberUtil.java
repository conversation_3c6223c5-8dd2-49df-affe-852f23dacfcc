package com.bizark.op.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class NumberUtil {

    /**
     * 数字转为 K 或 M 的字符串形式
     * @param number
     * @return
     */
    public static String numberToStr(Integer number) {
        return numberToStr(BigDecimal.valueOf(number));
    }
    /**
     * 数字转为 K 或 M 的字符串形式
     * @param number
     * @return
     */
    public static String numberToStr(Long number) {
        return numberToStr(BigDecimal.valueOf(number));
    }
    /**
     * 数字转为 K 或 M 的字符串形式
     * @param number
     * @return
     */
    public static String numberToStr(BigDecimal number) {
        if (number.compareTo(BigDecimal.valueOf(100)) < 0) {
            return number.toString();
        }
        if (number.compareTo(BigDecimal.valueOf(1000000)) < 0) {
            BigDecimal divide =number.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
            return divide + "K";
        }
        BigDecimal divide =number.divide(BigDecimal.valueOf(1000000), 2, RoundingMode.HALF_UP);
        return divide + "M";
    }


    /**
     * 判断数字是否大于0
     */
    public static boolean numberGreaterThanZero(Integer number) {
        if (number == null || number <= 0) {
            return false;
        }
        return true;
    }
    /**
     * 如果数字为null或者小于等于0，则返回1，否则返回原数字
     */
    public static Integer numberToOneIfZero(Integer number) {
        if (number == null || number <= 0) {
            return 1;
        }
        return number;
    }


}
