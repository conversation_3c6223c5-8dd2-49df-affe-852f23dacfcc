package com.bizark.op.common.enm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import java.util.*;

/**
 * <AUTHOR>
 * 物流报价相关枚举
 */
public class HeadTripOrderEnum {

    public enum orgType{
        HONGKONG_HENGJIAN("香港恒健",1000049)
        ;

        /**
         * 属性名称
         */
        private final String name;
        /**
         * 属性名称
         */
        private final Integer value;

        orgType(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        public static hengJianErpPricePriceType getByValue(String value) {
            for (hengJianErpPricePriceType e : hengJianErpPricePriceType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return null;
        }
    }

    public enum orderTerms{
        /**
         * 订单条款
         * FCA，FOB，MFP，MPP，DDP
         */
        FCA("FCA",1),
        FOB("FOB",2),
        <PERSON><PERSON>("MFP",3),
        <PERSON><PERSON>("MMP",4),
        DDP("DDP",5)
        ;

        /**
         * 属性名称
         */
        private final String name;
        /**
         * 属性名称
         */
        private final Integer value;

        orderTerms(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        public static orderTerms getByValue(String value) {
            for (orderTerms e : orderTerms.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return null;
        }
    }

    public enum hengJianErpPricePriceType {
        /**
         * 拖车报关费
         */
        TRAILER_CUSTOMS_DECLARATION_FEE("拖车报关费", "CustomsTrailer"),
        /**
         * 港杂费
         */
        PORT_MISCELLANEOUS_FEES("港杂费", "PortSurcharge"),
        /**
         * 海运费
         */
        OCEAN_FREIGHT("海运费", "OceanFreight"),
        /**
         * 清关税金
         */
        CUSTOMS_CLEARANCE_TAX("清关税金及服务费", "CustomsClearTax"),

        /**
         * 卡派送仓费
         */
        CARD_DELIVERY_WAREHOUSE_FEE("卡派送仓费", "DeliveryFee"),
        /**
         * 一口价
         */
        BUY_IT_NOW("一口价", "FixedPrice"),
        ;
        /**
         * 属性名称
         */
        private final String name;
        /**
         * 属性名称
         */
        private final String value;

        hengJianErpPricePriceType(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }

        public static hengJianErpPricePriceType getByValue(String value) {
            for (hengJianErpPricePriceType e : hengJianErpPricePriceType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return null;
        }


        public static hengJianErpPricePriceType getByName(String name) {
            for (hengJianErpPricePriceType e : hengJianErpPricePriceType.values()) {
                if (e.getName().equals(name)) {
                    return e;
                }
            }
            return null;
        }
    }

    /**
     * 拖车报关费、港杂费、海运费、清关税金和服务费、卡派送仓费
     */
    public enum priceType {
        /**
         * 拖车报关费
         */
        TRAILER_CUSTOMS_DECLARATION_FEE("拖车报关费", 1),
        /**
         * 港杂费
         */
        PORT_MISCELLANEOUS_FEES("港杂费", 2),
        /**
         * 海运费
         */
        OCEAN_FREIGHT("海运费", 3),
        /**
         * 清关税金
         */
        CUSTOMS_CLEARANCE_TAX("清关税金及服务费", 4),

        /**
         * 卡派送仓费
         */
        CARD_DELIVERY_WAREHOUSE_FEE("卡派送仓费", 5),
        /**
         * 一口价
         */
        FIXED_PRICE("一口价", 6),
        /**
         * 一口价
         */
        ALL("未匹配", 7),

        ;
        /**
         * 属性名称
         */
        private final String name;

        /**
         * 属性名称
         */
        private final Integer value;

        priceType(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        public static priceType getByValue(Integer value) {
            for (priceType e : priceType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ALL;
        }

        public static Integer queryValueByName(String name) {
            for (priceType e : priceType.values()) {
                if (e.getName().equals(name)) {
                    return e.value;
                }
            }
            return -1;
        }
    }

    /**
     * 报关费、异地装柜费、预提费、落箱费、固定费用汇总、标签费、超重费
     */
    public enum priceName {
        /**
         * 报关费
         */
        DECLARATION_FEE("报关费", "1"),
        /**
         * 异地装柜费
         */
        REMOTE_CONTAINERS_FEE("异地装柜费", "2"),
        /**
         * 预提费
         */
        ADVANCE_PAYMENT_FEE("预提费", "3"),
        /**
         * 落箱费
         */
        DROP_BOX_FEE("落箱费", "4"),
        /**
         * 固定费用汇总
         */
        FIXED_COSTS_TOTAL("固定费用汇总", "5"),
        /**
         * 标签费
         */
        LABEL_FEE("标签费", "6"),
        /**
         * 超重费
         */
        OVERWEIGHT_FEE("超重费", "7");

        /**
         * 属性名称
         */
        private final String name;

        /**
         * 属性名称
         */
        private final String value;

        priceName(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据value获取名称
         *
         * @param value 值
         * @return String name
         */
        public static String getName(Integer value) {
            for (priceName thisValue : priceName.values()) {
                if (thisValue.value.equals(value)) {
                    return thisValue.name();
                }
            }
            return null;
        }
    }

    public enum calculationMethod {
        /**
         * +（按票）
         */
        BY_TICKET_ADD("+（按票）", 1),
        /**
         * ×（1+%）
         */
        PERCENTAGE_ADD("×（1+%）", 2),
        /**
         * （按柜）
         */
        BY_CONTAINER_ADD("+（按柜）", 3),
        BY_FACTORY("+（按工厂）", 4),
        BY_BOX("+（按箱）", 5);

        private final String name;
        private final Integer value;


        calculationMethod(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        public static calculationMethod getByValue(Integer value) {
            for (calculationMethod e : calculationMethod.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return null;
        }
    }

    public enum cabinetType {
        /**
         * 尺柜类型
         */
        FORTY_FOOT_CABINET("四十尺柜", 1),

        TWENTY_FOOT_CABINET("二十尺柜", 2);


        private final String name;
        private final Integer value;

        cabinetType(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        /**
         * 根据value获取名称
         *
         * @param value 值
         * @return String name
         */
        public static String getName(Integer value) {
            for (calculationMethod thisValue : calculationMethod.values()) {
                if (thisValue.value.equals(value)) {
                    return thisValue.name();
                }
            }
            return null;
        }
    }

    public enum HeadTripStatus {
        /**
         * 头程单状态
         */
        CREATED(0, "已创建(草稿)"),
        AWAITING_BOOKING(1, "待订舱"),
        CONFIRMED_BOOKING(2, "确定舱位"),
        AWAITING_LOADING(3, "待装柜"),
        IN_TRANSIT(4, "在途"),
        ARRIVED(5, "待入库"),
        COMPLETED(6, "已完成");

        private final Integer value;
        private final String name;

        HeadTripStatus(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        public Integer getValue() {
            return value;
        }

        public String getName() {
            return name;
        }
    }

    public enum HeadTripPullingProcessStatus {
        /**
         * 头程单状态
         */
        CREATED(1, "未完成"),
        COMPLETED(2, "已完成");

        private final Integer value;
        private final String name;

        HeadTripPullingProcessStatus(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        public Integer getValue() {
            return value;
        }

        public String getName() {
            return name;
        }
    }

    public enum BusinessType {
        /**
         * 头程单业务类型
         */
        CARGO(1, "海外仓备货","大货业务"),
        UNKNOWN(-1, "未知类型",""),
        DISTRIBUTION(3, "分销代采业务",""),
        VC_DI(2, "VC-DI","VC DI业务");

        private final Integer value;
        private final String description;
        private final String importDescription;

        BusinessType(Integer value, String description, String importDescription) {
            this.value = value;
            this.description = description;
            this.importDescription = importDescription;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
        public String getImportDescription() {
            return importDescription;
        }

        public static BusinessType getByValue(Integer value) {
            for (BusinessType e : BusinessType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return CARGO;
        }

        public static BusinessType getEnumByValue(Integer value) {
            for (BusinessType e : BusinessType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }

        public static BusinessType getEnumByName(String name){
            for (BusinessType e : BusinessType.values()) {
                if (e.getImportDescription().equals(name)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    public enum cabinStatus{
        /**
         *订舱状态
         */
        ALL("无状态",0),
        NOT_SUBMITTED("未提交",1),
        SUBMIT_BOOKING("提交订舱",2),
        APPLY_FOR_BOOKING("已订舱",3),
        ALLOCATE_CABIN_SPACE("分配舱位",4),
        GO_BACK("退回",5),
        ;

        private final String name;
        private final Integer value;

        cabinStatus(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        public static cabinStatus getByValue(Integer value) {
            for (cabinStatus e : cabinStatus.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ALL;
        }
    }

    public enum shippingOrderStatus{
        /**
         * 发运单状态
         * 待装柜
         * 待发运
         * 已发运
         * 作废
         */
        ALL_IN("全部", 0),
        CABINET_TO_BE_INSTALLED("待装柜",1),
        TO_BE_SHIPPED("待发运",2),
        SHIPPED("已发运",3),
        CANCEL("作废",10),;

        private final String name;
        private final Integer value;

        shippingOrderStatus(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        /**
         * 根据value获取名称
         *
         * @param value 值
         * @return String name
         */
        public static String getName(Integer value) {
            for (shippingOrderStatus thisValue : shippingOrderStatus.values()) {
                if (thisValue.value.equals(value)) {
                    return thisValue.getName();
                }
            }
            return null;
        }

        /**
         * 根据value获取名称
         *
         * @param value 值
         * @return String name
         */
        public static Integer getValue(String name) {
            for (shippingOrderStatus thisValue : shippingOrderStatus.values()) {
                if (thisValue.name.equals(name)) {
                    return thisValue.getValue();
                }
            }
            return null;
        }
    }
    public enum initialLogisticsOrderStatus{
        /**
         * 物流单状态
         * 	待订舱
         * 		待开船
         * 			待到港
         * 				派送
         * 					已妥投
         */
        ALL_IN("全部",0),
        PENDING_BOOKING("待订舱",1),
        TO_BE_SET_SAIL("待开船",2),
        WAITING_FOR_ARRIVAL_AT_THE_PORT("待到港",3),
        DISPATCH("待派送",4),
        PROPER_DELIVERY("已妥投",5),
        CANCEL("作废",10),
        ;
        private final String name;
        private final Integer value;

        initialLogisticsOrderStatus(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        public static String getName(Integer value) {
            for (initialLogisticsOrderStatus thisValue : initialLogisticsOrderStatus.values()) {
                if (thisValue.value.equals(value)) {
                    return thisValue.getName();
                }
            }
            return null;
        }

        public static Integer getValue(String name) {
            for (initialLogisticsOrderStatus thisValue : initialLogisticsOrderStatus.values()) {
                String enName = thisValue.name;
                if (enName.equals(name)) {
                    return thisValue.getValue();
                }
            }
            return null;
        }
    }

    public enum  logisticsStatus{
        /**
         * 物流状态
         */
        ALL_IN("全部",0),
        NOT_INSTALLED("未装柜",1),
        CABINET_INSTALLED("已装柜", 2),//待发运
        ALREADY_DECLARED("已报关", 3),
        THE_SHIP_HAS_DEPARTED("已开船", 4),
        ARRIVED_AT_PORT("已到港", 5),
        PROPER_DELIVERY("已妥投", 6),
        ;

        private final String name;
        private final Integer value;

        logisticsStatus(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        public static String getName(Integer value) {
            for (logisticsStatus thisValue : logisticsStatus.values()) {
                if (thisValue.value.equals(value)) {
                    return thisValue.getName();
                }
            }
            return null;
        }

        public static Integer getValue(String name) {
            for (logisticsStatus thisValue : logisticsStatus.values()) {
                if (thisValue.name.equals(name)) {
                    return thisValue.getValue();
                }
            }
            return null;
        }


    }

    public enum ShippingStatus {
        /**
         * 物流状态
         */
        DRAFT("草稿", 1),
        SUBMITTED("已提交", 2),
        BOOKING_HAS_BEEN_APPLIED_FOR("已申请订舱", 3),
        /**
         * 头程单物流状态
         */
        BOOKED("已订舱", 4),
        CLEARED_CUSTOMS("已装柜", 5),
        SHIPPED("已报关", 6),
        CLEARED("已开船", 7),
        ARRIVED("已到港", 8),
        BE_PUT_IN_STORAGE("已入仓", 9),
        BE_PUT_IN_WAREHOUSE("已入库", 10),
        RETURN_CABINET("已还柜", 11);

        private final String name;
        private final Integer value;

        ShippingStatus(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }
    }

    public enum ShippingCompany {
        /**
         * 船务公司
         */
        CMA(1, "CMA"),
        EMC(2, "EMC"),
        HPL(3, "HPL"),
        ZYC(4, "致远船务"),
        COSCO(5, "COSCO"),
        HMM(6, "HMM"),
        MAERSK(7, "MAERSK"),
        MATSON(8, "MATSON"),
        MSC(9, "MSC"),
        ONE(10, "ONE"),
        OOCL(11, "OOCL"),
        SML(12, "SML"),
        SNL(13, "SNL"),
        TSL(14, "TSL"),
        WHL(15, "WHL"),
        YML(16, "YML"),
        ZIM(17, "ZIM"),
        HBS(18, "HBS"),
        PIL(19, "PIL"),
        KMTC(20, "KMTC"),
        CUL(21, "CUL");

        private final Integer value;
        private final String name;

        ShippingCompany(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        public Integer getValue() {
            return value;
        }

        public String getName() {
            return name;
        }


        public static ShippingCompany getByName(String name) {
            for (ShippingCompany e : ShippingCompany.values()) {
                if (e.getName().contains(name)) {
                    return e;
                }
            }
            return null;
        }
    }

    public enum LogisticsOrderStatus {

        /**
         * 头程单业务类型
         */
        ALL_IN(0,"全部"),
        NOT_SUBMITTED(1, "未确认"),
        SUBMITTED(2, "已确认"),
        CLOSED(3, "已关闭");

        private final Integer value;
        private final String description;

        LogisticsOrderStatus(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static LogisticsOrderStatus getByValue(Integer value) {
            for (LogisticsOrderStatus e : LogisticsOrderStatus.values()) {
                if (Objects.equals(e.getValue(),value)) {
                    return e;
                }
            }
            return null;
        }
    }

    /**
     * 发运单状态
     * 待装柜、待发运、已发运和作废
     */
    public enum ShipmentStatus {
        /**
         * 发运单状态
         * 待装柜、待发运、已发运和作废
         */
        WAITING_FOR_LOADING(1,"待装柜"),
        WAITING_FOR_SHIPPING(2, "待发运"),
        SHIPPED(3,"已发运"),
        CANCELED(4,"作废");

        private final Integer value;
        private final String description;

        ShipmentStatus(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static String getName(Integer value) {
            for (ShipmentStatus e : ShipmentStatus.values()) {
                if (e.getValue().equals(value)) {
                    return e.getDescription();
                }
            }
            return null;
        }
    }



    /**
     * 价格生效状态
     * 待装柜、待发运、已发运和作废
     */
    public enum takeEffectStatus {
        /**
         * 价格生效状态
         */
        Y(1,"生效"),
        N(2, "不生效"),
        ;

        private final Integer value;
        private final String description;

        takeEffectStatus(Integer value) {
            this.value = value;
            this.description = name();
        }

        takeEffectStatus(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static String getName(Integer value) {
            for (ShipmentStatus e : ShipmentStatus.values()) {
                if (e.getValue().equals(value)) {
                    return e.getDescription();
                }
            }
            return null;
        }
    }


    /**
     * 发运单关联状态
     */
    public enum verifyCorrelation {
        /**
         * 关联状态
         */
        Y(1,"关联"),
        N(2, "未关联"),
        ;

        private final Integer value;
        private final String description;

        verifyCorrelation(Integer value) {
            this.value = value;
            this.description = name();
        }

        verifyCorrelation(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 发运单关联状态
     */
    public enum pushStatus {
        /**
         * 关联状态
         */
        Y(1,"推送"),
        N(2, "未推送"),
        ;

        private final Integer value;
        private final String description;

        pushStatus(Integer value) {
            this.value = value;
            this.description = name();
        }

        pushStatus(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }
    /**
     * 发运单关联状态
     */
    public enum auditStatus {
        /**
         * 草稿
         */
        ALL(0, "全部"),
        /**
         * 未知状态
         */
        UNKNOWN(-1, "未知状态"),

        /**
         * 已审核
         */
        IN_EFFECT(2, "生效中"),

        /**
         * 未审核
         */
        PENDING_REVIEW(1, "未审核"),
        /**
         * 已失效
         */
        EXPIRED  (4, "已失效"),
        /**
         * 草稿
         */
        DRAFT(3, "草稿");
//        /**
//         * 关联状态
//         */
//        ALL_IN(0,"全部"),
//        Y(2, "已审核"),
//        N(1, "待审核"),
//        T(3, "草稿"),
//        T(4, "已失效"),
        //0.全部 1.未审核 2.已审核 3.草稿

        private final Integer value;
        private final String description;

        auditStatus(Integer value) {
            this.value = value;
            this.description = name();
        }

        auditStatus(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static auditStatus getByValue(Integer value) {
            for (auditStatus e : auditStatus.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return auditStatus.UNKNOWN;
        }
    }

    public enum exceptionType{
        /**
         * 异常类型
         */
        NORMAL("0", "正常"),
        /**
         * 开船异常
         * 判断实际开船日期是否为有效日期（实际开船日期 - 预计开船日期  大于等于 -3 天， 小于等于10 天 即为有效数据
         */
        ABNORMAL_DEPARTURE("1", "开船异常"),
        /**
         * 到港异常
         * 预计到港日期、实际到港日期
         * 预计到港日期、实际到港日期早于预计开船日期、实际开船日期，列为异常；
         * 实际到港时间早于预计到港时间 5+ 天，晚于 15+天，列为异常；
         */
        ARRIVAL_ANOMALY("2", "到港异常"),
        /**
         * 收货单确认收货异常
         */
        CONFIRMING_ABNORMAL_RECEIPT("3", "确认收货异常"),

        /**
         * 开船逾期
         */
        DEPARTURE_OVERDUE("4", "开船逾期"),
        /**
         * 到港逾期
         */
        ARRIVAL_OVERDUE("5", "到港逾期"),
        /**
         * 到仓逾期
         */
        WAREHOUSE_ARRIVAL_OVERDUE("6", "到仓逾期")

        ;

        private final String value;
        private final String description;

        exceptionType(String value) {
            this.value = value;
            this.description = name();
        }

        exceptionType(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static exceptionType getByValue(String value) {
            for (exceptionType e : exceptionType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return null;
        }

        /**
         * 为异常类型字段添加异常
         * 无需担心会多添加 会对原意常去重
         * @param exceptionTypeStr 原异常字段
         * @param values 异常状态汇总
         * @return 异常汇总的字符串
         */
        public static String addExceptions(String exceptionTypeStr, String... values) {
            Set<String> typeSet = new TreeSet<>();
            // 如果异常不为空 则在原基础上加
            if (StrUtil.isNotBlank(exceptionTypeStr)) {
                // 如果有0存在 则去除
                if (Objects.equals(NORMAL.value, exceptionTypeStr)) {
                    exceptionTypeStr = null;
                }
                String[] splitList = StrUtil.split(exceptionTypeStr, ",");
                if (splitList != null && splitList.length > 0) {
                    CollUtil.addAll(typeSet, splitList);
                }
            }
            // 添加新异常
            Arrays.stream(values)
                    .filter(Objects::nonNull)
                    .forEach(typeSet::add);
            // 重新设为字符串
            return StrUtil.join(",", typeSet);
        }

        /**
         * 为异常类型字段移除异常
         * @param exceptionTypeStr 原异常字段
         * @param values 异常状态汇总
         * @return 异常汇总的字符串
         */
        public static String removeExceptions(String exceptionTypeStr, String... values) {
            Set<String> typeSet = new TreeSet<>();
            // 如果异常不为空 则在原基础上加
            if (StrUtil.isNotBlank(exceptionTypeStr)) {
                String[] splitList = StrUtil.split(exceptionTypeStr, ",");
                if (splitList != null && splitList.length > 0) {
                    CollUtil.addAll(typeSet, splitList);
                }
            }
            // 移除异常
            Arrays.asList(values).forEach(typeSet::remove);
            // 若全部移除了 则说明是正常状态 添加默认值
            if (CollUtil.isEmpty(typeSet)) {
                typeSet.add(NORMAL.value);
            }
            // 重新设为字符串
            return StrUtil.join(",", typeSet);
        }
    }


    public enum UrgentStatusEnum {
        /**
         * 加急状态
         */
        URGENT("是", 1),
        NOT_URGENT("否", 0)
        ;

        private final String name;
        private final Integer value;

        UrgentStatusEnum(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        /**
         * 根据value获取加急描述 为空返回否
         * @param value 参数
         * @return {@link String} 描述
         */
        public static String getNameByValue(Integer value) {
            for (UrgentStatusEnum e : UrgentStatusEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e.getName();
                }
            }
            return NOT_URGENT.getName();
        }
    }

    /**
     * 是否拖车装柜
     */
    public enum factoryDragCabinetEnum {
        /**
         * 是否拖车装柜
         */
        Y("是", 1),
        N("否", 0)
        ;

        private final String name;
        private final Integer value;

        factoryDragCabinetEnum(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        /**
         * 根据value获取加急描述 为空返回否
         * @param value 参数
         * @return {@link String} 描述
         */
        public static String getNameByValue(Integer value) {
            for (factoryDragCabinetEnum e : factoryDragCabinetEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e.getName();
                }
            }
            return N.getName();
        }
    }



    public enum PurchaseDeliveryNoteExceptionType{
        /**
         * 异常类型
         */
        NORMAL("0", "正常"),
        /**
         * 自动确认收货异常
         */
        ABNORMAL_DEPARTURE("3", "自动收货异常");

        private final String value;
        private final String description;

        PurchaseDeliveryNoteExceptionType(String value) {
            this.value = value;
            this.description = name();
        }

        PurchaseDeliveryNoteExceptionType(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static PurchaseDeliveryNoteExceptionType getByValue(String value) {
            for (PurchaseDeliveryNoteExceptionType e : PurchaseDeliveryNoteExceptionType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return null;
        }
    }

    public enum overdueType {
        /**
         * 加急状态
         */
        SAIL("开船逾期", 1),
        ARRIVAL("到港逾期", 2),
        TO_WAREHOUSE("到仓逾期", 3),
        ;

        private final String name;
        private final Integer value;

        overdueType(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        /**
         * 根据value获取加急描述 为空返回否
         * @param value 参数
         * @return {@link String} 描述
         */
        public static String getNameByValue(Integer value) {
            for (overdueType e : overdueType.values()) {
                if (e.getValue().equals(value)) {
                    return e.getName();
                }
            }
            return SAIL.getName();
        }
    }

    /**
     * 预警类型
     */
    public enum overdueTypeByShippingOrder {
        /**
         * 加急状态
         */
        SAIL("开船逾期", 1),
        ARRIVAL("装柜逾期", 2),
        TO_WAREHOUSE("到仓逾期", 3),
        ;

        private final String name;
        private final Integer value;

        overdueTypeByShippingOrder(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        /**
         * 根据value获取加急描述 为空返回否
         * @param value 参数
         * @return {@link String} 描述
         */
        public static String getNameByValue(Integer value) {
            for (overdueTypeByShippingOrder e : overdueTypeByShippingOrder.values()) {
                if (e.getValue().equals(value)) {
                    return e.getName();
                }
            }
            return SAIL.getName();
        }
    }
}

