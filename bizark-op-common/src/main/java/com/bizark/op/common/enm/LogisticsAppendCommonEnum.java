package com.bizark.op.common.enm;

import lombok.Getter;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * @Description 物流模块新增需求枚举
 * <AUTHOR>
 * @create: 2024-01-15 11:18
 */
public class LogisticsAppendCommonEnum {

    /**
     * 分销平台XXL配置key
     * <AUTHOR>
     * @date 2024-01-15 11:19:42
     */
    @Getter
    public enum DistributionConstantEnum {

        /**
         * 分销平台供应商
         */
        DISTRIBUTION_SUPPLIERS_ORDER("bizark-erp.head.tran.order.distribution.suppliers",  "分销平台供应商"),
        /**
         * 分销平台
         */
        DISTRIBUTION_PLATFORM_ORDER("bizark-erp.head.tran.order.distribution.platform",  "分销平台"),
        DISTRIBUTION_TRIPARTITE_RECEIPT("bizark-erp.head.tran.order.distribution.tripartite.receipt",  "分销平台 三⽅订单,批量录⼊"),
        RECEIPT_GOODS_DIFFERENCE("bizark-erp.head.receipt.goods.difference",  "收货与报关差异"),
        DEFAULT("null",  "其他(非法请求参数)")
        ;

        /**
         * 标的值
         */
        private final String key;

        /**
         * 描述
         */
        private final String describe;



        DistributionConstantEnum(String key, String describe) {
            this.key = key;
            this.describe = describe;
        }

        public static LogisticsAppendCommonEnum.DistributionConstantEnum getByValue(String key) {
            for (LogisticsAppendCommonEnum.DistributionConstantEnum e : LogisticsAppendCommonEnum.DistributionConstantEnum.values()) {
                if (e.getKey().equals(key)) {
                    return e;
                }
            }
            return null;
        }
    }

    /**
     * 分销平台供应商物流状态
     * <AUTHOR>
     * @date 2024-01-15 11:19:42
     */
   @Getter
    public enum DistributionSupplierLogisticsStatusEnum {

        /**
         * 异常
         */
        ABNORMAL("Abnormal",  "异常"),
        /**
         * 未发货
         */
        NOT_SCANNED("NotScanned",  "未发货"),
        /**
         * 标签创建
         */
        LABEL_CREATED("LabelCreated",  "标签创建"),

        /**
         * 已发货
         */
        IN_TRANSIT("InTransit", "已发货"),
        /**
         * 派送中
         */
        OUT_FOR_DELIVERY("OutForDelivery", "派送中"),
        /**
         * 延迟
         */
        DELAYED("Delayed", "延迟"),
        /**
         * 已履约
         */
        DELIVERED("Delivered", "已履约"),
        /**
         * 其他
         */
        OTHERS("Others", "其他")
        ;

        /**
         * 标的值
         */
        private final String value;

        /**
         * 描述
         */
        private final String describe;



        DistributionSupplierLogisticsStatusEnum(String value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static LogisticsAppendCommonEnum.DistributionSupplierLogisticsStatusEnum getByValue(String value) {
            for (LogisticsAppendCommonEnum.DistributionSupplierLogisticsStatusEnum e : LogisticsAppendCommonEnum.DistributionSupplierLogisticsStatusEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return OTHERS;
        }
    }


    /**
     * 分销平台供应商推送状态
     * <AUTHOR>
     * @date 2024-01-15 11:19:42
     */
    @Getter
    public enum PushDistributionStatusEnum {

        /**
         * 未推送
         */
        NOT_PUSHED(0,  "未推送"),
        /**
         * 推送成功
         */
        PUSH_SUCCESS(1,  "推送成功"),
        /**
         * 推送失败
         */
        PUSH_FAIl(-1,  "推送失败"),
        /**
         * 其他
         */
        OTHERS(-2, "其他"),
        /**
         * 状态异常
         */
        ABNORMAL_STATUS(-3, "状态异常")
        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        PushDistributionStatusEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static LogisticsAppendCommonEnum.PushDistributionStatusEnum getByValue(Integer value) {
            for (LogisticsAppendCommonEnum.PushDistributionStatusEnum e : LogisticsAppendCommonEnum.PushDistributionStatusEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return OTHERS;
        }
    }

    /**
     * 物流数量/金额累积字段枚举
     * <AUTHOR>
     * @date 2024-01-15 11:19:42
     */
    @Getter
    public enum LogisticsrSumDataEnum {

        /**
         * 总体积
         */
        TOTAL_VOLUME("totalVolume",  "总体积",BigDecimal.ZERO),
        /**
         * 总毛重
         */
        TOTAL_GROSS_WEIGHT("totalGrossWeight",  "总毛重",BigDecimal.ZERO),
        /**
         * 总净重
         */
        TOTAL_NET_WEIGHT("totalNetWeight",  "总净重",BigDecimal.ZERO),
        /**
         * 暂估金额
         */
        ESTIMATED_AMOUNT("estimatedAmount",  "暂估金额",BigDecimal.ZERO),
        /**
         * 其他
         */
        OTHERS("Others", "其他",-1)
        ;

        /**
         * 标的值
         */
        private final String value;

        /**
         * 描述
         */
        private final String describe;

        private final Object defaultSum;



        LogisticsrSumDataEnum(String value, String describe,Object defaultSum) {
            this.value = value;
            this.describe = describe;
            this.defaultSum = defaultSum;
        }

        public static LogisticsAppendCommonEnum.LogisticsrSumDataEnum getByValue(String value) {
            for (LogisticsAppendCommonEnum.LogisticsrSumDataEnum e : LogisticsAppendCommonEnum.LogisticsrSumDataEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return OTHERS;
        }
    }

    /**
     * 头程单合单校验
     *
     * <AUTHOR>
     * @date 2024-01-30 19:36:14
     */
    @Getter
    public enum MergeHeadOrderCheckEnum {

        /**
         * 通过
         */
        ADOPT(0,  "通过"),
        /**
         * 物流商不一致
         */
        LOGISTICS_COMPANY(1,  "物流商不一致,不允许合并"),
        /**
         * 船公司不一致
         */
        SHIPPING_COMPANY(2,  "船公司不一致,不允许合并"),
        /**
         * 目的口岸
         */
        DESTINATION_PORT(3, "目的口岸不一致,不允许合并"),
        /**
         * 出运口岸不一致
         */
        DEPARTURE_PORT(4, "出运口岸不一致,不允许合并"),
        VESSEL_NAME_VOYAGE(5, "船名/航次不一致,不允许合并")
        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        MergeHeadOrderCheckEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static LogisticsAppendCommonEnum.MergeHeadOrderCheckEnum getByValue(Integer value) {
            for (LogisticsAppendCommonEnum.MergeHeadOrderCheckEnum e : LogisticsAppendCommonEnum.MergeHeadOrderCheckEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ADOPT;
        }
    }

    /**
     * 物流时效设置 节点枚举
     *
     * <AUTHOR>
     * @date 2024-02-05 10:33:30
     */
    @Getter
    public enum LogisticsTimelinessSettingEnum {

        /**
         * 完工后装柜
         */
        CONTAINER_LOADING_AFTER_COMPLETION(0,  Collections.singletonList(HeadTripOrderEnum.shippingOrderStatus.CABINET_TO_BE_INSTALLED.getValue()), "完工后装柜"),
        /**
         * 装柜后开船
         */
        SETTING_SAIL_AFTER_CONTAINER_LOADING(1, Collections.singletonList(HeadTripOrderEnum.initialLogisticsOrderStatus.TO_BE_SET_SAIL.getValue()), "装柜后开船"),
        /**
         * 海运到港
         */
        SEA_FREIGHT_TO_PORT(2,  Collections.singletonList(HeadTripOrderEnum.initialLogisticsOrderStatus.WAITING_FOR_ARRIVAL_AT_THE_PORT.getValue()), "海运到港"),
        /**
         * 卡派到仓
         */
        CARD_DISPATCH_TO_WAREHOUSE(3, Collections.singletonList(HeadTripOrderEnum.initialLogisticsOrderStatus.DISPATCH.getValue()), "卡派到仓"),
        /**
         * 入库
         */
        WAREHOUSING(4, Collections.singletonList(4), "入库"),

        /**
         * 未知节点
         */
        UNKNOWN_NODE(-1, Collections.singletonList(-1), "未知节点"),
        ;

        /**
         * 节点数据库类型
         */
        private final Integer nodeType;

        /**
         * 对应的头程单状态
         */
        private final List<Integer> headType;

        /**
         * 描述
         */
        private final String describe;

        LogisticsTimelinessSettingEnum(Integer nodeType, List<Integer> headType, String describe) {
            this.nodeType = nodeType;
            this.headType = headType;
            this.describe = describe;
        }

        public static LogisticsAppendCommonEnum.LogisticsTimelinessSettingEnum getByType(Integer type) {
            for (LogisticsAppendCommonEnum.LogisticsTimelinessSettingEnum e : LogisticsAppendCommonEnum.LogisticsTimelinessSettingEnum.values()) {
                if (e.getNodeType().equals(type)) {
                    return e;
                }
            }
            return UNKNOWN_NODE;
        }
    }

    /**
     * 物流时效配置详情类型
     * <AUTHOR>
     * @date 2024-02-05 19:15:45
     */
    @Getter
    public enum TimelinessSettingDetailTypeEnum {

        /**
         * 货源地
         */
        SOURCE_OF_GOODS(0,  "货源地"),
        /**
         * 仓库
         */
        WAREHOUSE(1,  "仓库"),
        UNKNOWN(-1,  "  ")
        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        TimelinessSettingDetailTypeEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static LogisticsAppendCommonEnum.TimelinessSettingDetailTypeEnum getByValue(Integer value) {
            for (LogisticsAppendCommonEnum.TimelinessSettingDetailTypeEnum e : LogisticsAppendCommonEnum.TimelinessSettingDetailTypeEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 物流时效配置详情计算类型
     * <AUTHOR>
     * @date 2024-02-05 19:15:45
     */
    @Getter
    public enum TimelinessSettingDetailCalculateEnum {

        /**
         * 等于
         */
        EQUAL_TO(0,  " 等于 "),

        /**
         *
         */
        NOT_GREATER_THAN(1,  " 不等于 "),

        /**
         * 小于
         */
        LESS_THAN(2,  " 小于 "),

        /**
         * 大于
         */
        GREATER_THAN(3,  " 大于 "),
        UNKNOWN(-1,  "  ")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        TimelinessSettingDetailCalculateEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static LogisticsAppendCommonEnum.TimelinessSettingDetailCalculateEnum getByValue(Integer value) {
            for (LogisticsAppendCommonEnum.TimelinessSettingDetailCalculateEnum e : LogisticsAppendCommonEnum.TimelinessSettingDetailCalculateEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 物流时效配置是否启用
     * <AUTHOR>
     * @date 2024-02-05 19:15:45
     */
    @Getter
    public enum TimelinessSettingStatusEnum {

        /**
         * 等于
         */
        ABANDONED(0,  " 启用 "),

        /**
         *
         */
        DISABLED(1,  " 禁用 "),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        TimelinessSettingStatusEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static LogisticsAppendCommonEnum.TimelinessSettingStatusEnum getByValue(Integer value) {
            for (LogisticsAppendCommonEnum.TimelinessSettingStatusEnum e : LogisticsAppendCommonEnum.TimelinessSettingStatusEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 物流时效配置是否启用
     * <AUTHOR>
     * @date 2024-02-05 19:15:45
     */
    @Getter
    public enum NoteTimelinessSettingTypeEnum {

        /**
         * 等于
         */
        SET_NODE_TIMELINESS(0,  " 设置节点时效 "),

        /**
         *
         */
        CALCULATION_TIME_FOR_EACH_VOYAGE(1,  " 每单航程计算时效 "),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        NoteTimelinessSettingTypeEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static LogisticsAppendCommonEnum.NoteTimelinessSettingTypeEnum getByValue(Integer value) {
            for (LogisticsAppendCommonEnum.NoteTimelinessSettingTypeEnum e : LogisticsAppendCommonEnum.NoteTimelinessSettingTypeEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }
}
