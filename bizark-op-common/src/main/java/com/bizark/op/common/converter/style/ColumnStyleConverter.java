package com.bizark.op.common.converter.style;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * exay excel 样式转换器 -- 动态表头的列宽
 * 可以扩展为设置每列的样式
 */
public class ColumnStyleConverter extends AbstractColumnWidthStyleStrategy {

    /**
     * 校验表头是否设置
     */
    private final Set<String> headList;

    public ColumnStyleConverter(Set<String> headList) {
        this.headList = headList;
    }

    @Override
    public void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
        String headName = head.getHeadNameList().get(0);
        boolean add = headList.add(headName);
        if(add){
            Sheet sheet = writeSheetHolder.getSheet();
            int columnIndex = cell.getColumnIndex();
            int width = headName.length()  * 850;
            // 列宽40
            sheet.setColumnWidth(columnIndex, width);
        }
//                            else {
//                                // 第一个单元格
//                                // 只要不是头 一定会有数据 当然fill的情况 可能要context.getCellDataList() ,这个需要看模板，因为一个单元格会有多个 WriteCellData
//                                WriteCellData<?> cellData = list.get(0);
//                                // 这里需要去cellData 获取样式
//                                // 很重要的一个原因是 WriteCellStyle 和 dataFormatData绑定的 简单的说 比如你加了 DateTimeFormat
//                                // ，已经将writeCellStyle里面的dataFormatData 改了 如果你自己new了一个WriteCellStyle，可能注解的样式就失效了
//                                // 然后 getOrCreateStyle 用于返回一个样式，如果为空，则创建一个后返回
//                                WriteCellStyle orCreateStyle = cellData.getOrCreateStyle();
//                                WriteFont writeFont = new WriteFont();
//                                writeFont.setFontName("微软雅黑");
//                                orCreateStyle.setWriteFont(writeFont);
//                            }
    }
}
