package com.bizark.op.common.util;

import cn.hutool.core.collection.CollUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public abstract class ListUtils {

    /**
     *
     * @param originalList 源数组
     * @param numParts 分割为几个新的数组
     * @return
     * @param <T>
     */
    public static <T> List<List<T>> splitList(List<T> originalList, int numParts) {
        List<List<T>> resultList = new ArrayList<>();
        if (originalList.size() <= numParts){
            resultList.add(originalList);
            return resultList;
        }
        int listSize = originalList.size();
        int startIndex = 0;
        for (int i = 0; i < numParts; i++) {
            int endIndex = Math.min(startIndex + (listSize / numParts) + (i < listSize % numParts ? 1 : 0), listSize);
            List<T> sublist = originalList.subList(startIndex, endIndex);
            resultList.add(sublist);
            startIndex = endIndex;
        }
        return resultList;
    }


    /**
     * 判断 范围区间是否存在异常()
     * 例如 (0-1) , (0,2) true
     * (0-1) , (2,3) fale
     *
     * @param sourceList 数据集合
     * @return true 存在异常 false 不存在
     */
    public static boolean checkRange(List<List<BigDecimal>> sourceList) {
        if (CollUtil.isEmpty(sourceList)) {
            return false;
        }
        //先排序在过滤
        List<List<BigDecimal>> sortedList = sourceList.stream()
                .filter(CollUtil::isNotEmpty)
                .sorted(Comparator.comparing(CollUtil::getFirst, Comparator.nullsLast(BigDecimal::compareTo)))
                .collect(Collectors.toList());

        for (int i = 0; i < sortedList.size(); i++) {

            if (i == sortedList.size() - 1) {
                break;
            }

            int nextIndex = i + 1;
            List<BigDecimal> now = sortedList.get(i);
            List<BigDecimal> next = sortedList.get(nextIndex);

            BigDecimal nowMin = CollUtil.getFirst(now);
            BigDecimal nowMax = CollUtil.getLast(now);

            BigDecimal nextMin = CollUtil.getFirst(next);
            BigDecimal nextMax = CollUtil.getLast(next);

            // 先判断 最大值和最小值是否有值
            if (BigDecimalUtils.compareTo(nowMin, nowMax) >= 0 || BigDecimalUtils.compareTo(nextMin, nextMax) >= 0) {
                return true;
            }

            // 再判断 当前最大、最小值和 下个最大、最小值 是否存在异常
            if (BigDecimalUtils.compareTo(nowMin, nextMin) >= 0
                    || BigDecimalUtils.compareTo(nowMax, nextMax) >= 0
                    || BigDecimalUtils.compareTo(nowMax, nextMin) >= 0) {
                return true;
            }

        }
        return false;
    }
}
