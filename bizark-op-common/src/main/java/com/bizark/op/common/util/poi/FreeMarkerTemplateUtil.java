//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//
package com.bizark.op.common.util.poi;


import com.bizark.op.common.exception.CustomException;
import com.yongjiu.commons.utils.XmlReader;
import com.yongjiu.dto.freemarker.input.ExcelImageInput;
import com.yongjiu.dto.freemarker.input.FreemarkerInput;
import com.yongjiu.entity.excel.Cell;
import com.yongjiu.entity.excel.Row;
import com.yongjiu.entity.excel.*;
import com.yongjiu.entity.excel.Table;
import com.yongjiu.entity.excel.Style.Border;
import com.yongjiu.util.ColorUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateExceptionHandler;
import org.apache.commons.io.FileUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.ClientAnchor.AnchorType;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.*;
import org.dom4j.Document;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;

public class FreeMarkerTemplateUtil {
    private static final Logger log = LoggerFactory.getLogger(FreeMarkerTemplateUtil.class);

    public FreeMarkerTemplateUtil() {
    }

    public static void exportToFile(Map dataMap, String templateName, String templateFilePath, String fileFullPath) {
        try {
            File file = new File(fileFullPath);
            FileUtils.forceMkdirParent(file);
            FileOutputStream outputStream = new FileOutputStream(file);
            exportToStream(dataMap, templateName, templateFilePath, outputStream);
        } catch (Exception var6) {
            var6.printStackTrace();
        }

    }

    public static void exportToStream(Map dataMap, String templateName, String templateFilePath, FileOutputStream outputStream) {
        try {
            Template template = getTemplate(templateName, templateFilePath);
            OutputStreamWriter outputWriter = new OutputStreamWriter(outputStream, "UTF-8");
            Writer writer = new BufferedWriter(outputWriter);
            template.process(dataMap, writer);
            writer.flush();
            writer.close();
            outputStream.close();
        } catch (Exception var7) {
            var7.printStackTrace();
        }

    }

    public static void exportImageExcel(String excelFilePath, FreemarkerInput freemarkerInput) {
        try {
            File file = new File(excelFilePath);
            FileUtils.forceMkdirParent(file);
            FileOutputStream outputStream = new FileOutputStream(file);
            createImageExcleToStream(freemarkerInput, outputStream);
            FileUtils.forceDelete(new File(freemarkerInput.getXmlTempFile() + freemarkerInput.getFileName() + ".xml"));
            log.info("导出成功,导出到目录：" + file.getCanonicalPath());
        } catch (Exception var4) {
            var4.printStackTrace();
        }

    }

    public static void exportImageExcelNew(File file, FreemarkerInput freemarkerInput,Template template) {
        try {
            FileOutputStream outputStream = new FileOutputStream(file);
            createExcelToStream(freemarkerInput,template ,outputStream);
        } catch (Exception var4) {
            var4.printStackTrace();
        }

    }

    public static void exportImageExcelNew(HttpServletResponse response, FreemarkerInput freemarkerInput,Template template) {
        try {
            OutputStream outputStream = response.getOutputStream();
            response.reset();
            response.setContentType("application/msexcel;charset=UTF-8");
            response.setHeader("Response-Type", "Download");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("装柜清单", "UTF-8").replace("\\+", "%20");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为模板 然后文件流会自动关闭

            createExcelToStream(freemarkerInput,template, outputStream);
        } catch (Exception var3) {
            throw new CustomException(var3.getMessage());
        }

    }

    public static void exportImageExcel(HttpServletResponse response, FreemarkerInput freemarkerInput) {
        try {
            OutputStream outputStream = response.getOutputStream();
            response.reset();
            response.setContentType("application/msexcel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + new String((freemarkerInput.getFileName() + ".xls").getBytes("GBK"), "ISO8859-1") + "\"");
            response.setHeader("Response-Type", "Download");
            createImageExcleToStream(freemarkerInput, outputStream);
            FileUtils.forceDelete(new File(freemarkerInput.getXmlTempFile() + freemarkerInput.getFileName() + ".xml"));
        } catch (Exception var3) {
            var3.printStackTrace();
        }

    }

    private static Template getTemplate(String templateName, String filePath) throws IOException {
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_28);
        configuration.setDefaultEncoding("UTF-8");
        configuration.setTemplateUpdateDelayMilliseconds(0L);
        configuration.setEncoding(Locale.CHINA, "UTF-8");
        configuration.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        configuration.setClassForTemplateLoading(FreeMarkerTemplateUtil.class, filePath);
        configuration.setOutputEncoding("UTF-8");
        return configuration.getTemplate(templateName, "UTF-8");
    }

    private static void createImageExcleToStream(FreemarkerInput freemarkerInput, OutputStream outputStream) {
        BufferedWriter out = null;

        try {
            Template template = getTemplate(freemarkerInput.getTemplateName(), freemarkerInput.getTemplateFilePath());
            File tempXMLFile = new File(freemarkerInput.getXmlTempFile() + freemarkerInput.getFileName() + ".xml");
            FileUtils.forceMkdirParent(tempXMLFile);
            out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(tempXMLFile), "UTF-8"));
            template.process(freemarkerInput.getDataMap(), out);
            if (log.isDebugEnabled()) {
                log.debug("1.完成将文本数据导入到XML文件中");
            }

            SAXReader reader = new SAXReader();
            Document document = reader.read(tempXMLFile);
            Map<String, Style> styleMap = readXmlStyle(document);
            log.debug("2.完成解析XML中样式信息");
            List<Worksheet> worksheets = readXmlWorksheet(document);
            if (log.isDebugEnabled()) {
                log.debug("3.开始将XML信息写入Excel，数据为：" + worksheets.toString());
            }

            HSSFWorkbook wb = new HSSFWorkbook();
            Iterator var10 = worksheets.iterator();

            while(var10.hasNext()) {
                Worksheet worksheet = (Worksheet)var10.next();
                HSSFSheet sheet = wb.createSheet(worksheet.getName());
                Table table = worksheet.getTable();
                List<Row> rows = table.getRows();
                List<Column> columns = table.getColumns();
                int createRowIndex;
                if (columns != null && columns.size() > 0) {
                    createRowIndex = 0;

                    for(int i = 0; i < columns.size(); ++i) {
                        Column column = (Column)columns.get(i);
                        createRowIndex = getCellWidthIndex(createRowIndex, i, column.getIndex());
                        sheet.setColumnWidth(createRowIndex, (int)column.getWidth() * 50);
                    }
                }

                createRowIndex = 0;
                List<CellRangeAddressEntity> cellRangeAddresses = new ArrayList();

                for(int rowIndex = 0; rowIndex < rows.size(); ++rowIndex) {
                    Row rowInfo = (Row)rows.get(rowIndex);
                    if (rowInfo != null) {
                        createRowIndex = getIndex(createRowIndex, rowIndex, rowInfo.getIndex());
                        HSSFRow row = sheet.createRow(createRowIndex);
                        if (rowInfo.getHeight() != null) {
                            Integer height = rowInfo.getHeight() * 20;
                            row.setHeight(height.shortValue());
                        }

                        List<Cell> cells = rowInfo.getCells();
                        if (!CollectionUtils.isEmpty(cells)) {
                            int startIndex = 0;

                            for(int cellIndex = 0; cellIndex < cells.size(); ++cellIndex) {
                                Cell cellInfo = (Cell)cells.get(cellIndex);
                                if (cellInfo != null) {
                                    startIndex = getIndex(startIndex, cellIndex, cellInfo.getIndex());
                                    HSSFCell cell = row.createCell(startIndex);
                                    String styleID = cellInfo.getStyleID();
                                    Style style = (Style)styleMap.get(styleID);
                                    CellStyle dataStyle = wb.createCellStyle();
                                    setBorder(style, dataStyle);
                                    setAlignment(style, dataStyle);
                                    setValue((HSSFWorkbook)wb, cellInfo, (HSSFCell)cell, style, dataStyle);
                                    setCellColor(style, dataStyle);
                                    cell.setCellStyle(dataStyle);
                                    if (cellInfo.getComment() != null) {
                                        Data data = cellInfo.getComment().getData();
                                        Comment comment = sheet.createDrawingPatriarch().createCellComment(new HSSFClientAnchor(0, 0, 0, 0, (short)3, 3, (short)5, 6));
                                        comment.setString(new HSSFRichTextString(data.getText()));
                                        cell.setCellComment(comment);
                                    }

                                    startIndex = getCellRanges(createRowIndex, cellRangeAddresses, startIndex, cellInfo, style);
                                }
                            }
                        }
                    }
                }

                addCellRange((HSSFSheet)sheet, cellRangeAddresses);
            }

            log.debug("4.开始写入图片：" + freemarkerInput.getExcelImageInputs());
            if (!CollectionUtils.isEmpty(freemarkerInput.getExcelImageInputs())) {
                writeImageToExcel(freemarkerInput.getExcelImageInputs(), wb);
            }

            log.debug("5.完成写入图片：" + freemarkerInput.getExcelImageInputs());
            wb.write(outputStream);
            outputStream.close();
        } catch (Exception var39) {
            var39.printStackTrace();
            log.error("导出excel异常：" + var39.getMessage());
        } finally {
            try {
                out.close();
            } catch (Exception var38) {
            }

        }

    }

    private static void createExcelToStream(FreemarkerInput freemarkerInput,Template template, OutputStream outputStream) {
        BufferedWriter out = null;
        File tempXMLFile = null;
        try {
            tempXMLFile = File.createTempFile(freemarkerInput.getFileName() , ".xml");
            out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(tempXMLFile), "UTF-8"));
            template.process(freemarkerInput.getDataMap(), out);
            if (log.isDebugEnabled()) {
                log.debug("1.完成将文本数据导入到XML文件中");
            }

            SAXReader reader = new SAXReader();
            Document document = reader.read(tempXMLFile);
            Map<String, Style> styleMap = readXmlStyle(document);
            log.debug("2.完成解析XML中样式信息");
            List<Worksheet> worksheets = readXmlWorksheet(document);
            if (log.isDebugEnabled()) {
                log.debug("3.开始将XML信息写入Excel，数据为：" + worksheets.toString());
            }

            XSSFWorkbook wb = new XSSFWorkbook();
            Iterator var10 = worksheets.iterator();

            while(var10.hasNext()) {
                Worksheet worksheet = (Worksheet)var10.next();
                XSSFSheet sheet = wb.createSheet(worksheet.getName());
                Table table = worksheet.getTable();
                List<Row> rows = table.getRows();
                List<Column> columns = table.getColumns();
                int createRowIndex;
                if (columns != null && !columns.isEmpty()) {
                    createRowIndex = 0;

                    for(int i = 0; i < columns.size(); ++i) {
                        Column column = columns.get(i);
                        createRowIndex = getCellWidthIndex(createRowIndex, i, column.getIndex());
                        sheet.setColumnWidth(createRowIndex, (int)column.getWidth() * 40);
                    }
                }
//                for(int i = 0 ; i<1000;i++){
//                    sheet.setColumnWidth(i,3000);
//                }
                createRowIndex = 0;
                List<CellRangeAddressEntity> cellRangeAddresses = new ArrayList<>();

                for(int rowIndex = 0; rowIndex < rows.size(); ++rowIndex) {
                    Row rowInfo = rows.get(rowIndex);
                    if (rowInfo != null) {
                        createRowIndex = getIndex(createRowIndex, rowIndex, rowInfo.getIndex());
                        XSSFRow row = sheet.createRow(createRowIndex);
                        if (rowInfo.getHeight() != null) {
                            int height = rowInfo.getHeight() * 40;
                            row.setHeight((short) height);
                        }

                        List<Cell> cells = rowInfo.getCells();
                        if (!CollectionUtils.isEmpty(cells)) {
                            int startIndex = 0;

                            for(int cellIndex = 0; cellIndex < cells.size(); ++cellIndex) {
                                Cell cellInfo = cells.get(cellIndex);
                                if (cellInfo != null) {
                                    startIndex = getIndex(startIndex, cellIndex, cellInfo.getIndex());
                                    XSSFCell cell = row.createCell(startIndex);
                                    String styleID = cellInfo.getStyleID();
                                    Style style = styleMap.get(styleID);
                                    CellStyle dataStyle = wb.createCellStyle();
                                    setBorder(style, dataStyle);
                                    setAlignment(style, dataStyle);
                                    setValue(wb, cellInfo, cell, style, dataStyle);
                                    setCellColor(style, dataStyle);
                                    cell.setCellStyle(dataStyle);
                                    if (cellInfo.getComment() != null) {
                                        Data data = cellInfo.getComment().getData();
                                        Comment comment = sheet.createDrawingPatriarch().createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 3, 5, 6));
                                        comment.setString(new XSSFRichTextString(data.getText()));
                                        cell.setCellComment(comment);
                                    }

                                    startIndex = getCellRanges(createRowIndex, cellRangeAddresses, startIndex, cellInfo, style);
                                }
                            }
                        }
                    }
                }

                addCellRange(sheet, cellRangeAddresses);
            }

            log.debug("4.开始写入图片：" + freemarkerInput.getExcelImageInputs());
            if (!CollectionUtils.isEmpty(freemarkerInput.getExcelImageInputs())) {
                writeImageToExcel(freemarkerInput.getExcelImageInputs(), wb);
            }

            log.debug("5.完成写入图片：" + freemarkerInput.getExcelImageInputs());
            wb.write(outputStream);
            outputStream.close();
        } catch (Exception var39) {
            throw new CustomException(var39.getMessage());
        } finally {
            try {
                tempXMLFile.delete();
                out.close();
            } catch (Exception var38) {
                log.error("关闭流失败!");
            }

        }

    }

    public static Map<String, Style> readXmlStyle(Document document) {
        return XmlReader.getStyle(document);
    }

    public static List<Worksheet> readXmlWorksheet(Document document) {
        return XmlReader.getWorksheet(document);
    }

    private static int getIndex(int columnIndex, int i, Integer index) {
        if (index != null) {
            columnIndex = index - 1;
        }

        if (index == null && columnIndex != 0) {
            ++columnIndex;
        }

        if (index == null && columnIndex == 0) {
            columnIndex = i;
        }

        return columnIndex;
    }

    private static int getCellWidthIndex(int columnIndex, int i, Integer index) {
        if (index != null) {
            columnIndex = index;
        }

        if (index == null && columnIndex != 0) {
            ++columnIndex;
        }

        if (index == null && columnIndex == 0) {
            columnIndex = i;
        }

        return columnIndex;
    }

    private static void setBorder(Style style, CellStyle dataStyle) {
        if (style != null && style.getBorders() != null) {
            for(int k = 0; k < style.getBorders().size(); ++k) {
                Border border = style.getBorders().get(k);
                if (border != null) {
                    if ("Bottom".equals(border.getPosition())) {
                        dataStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                        dataStyle.setBorderBottom(BorderStyle.THIN);
                    }

                    if ("Left".equals(border.getPosition())) {
                        dataStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
                        dataStyle.setBorderLeft(BorderStyle.THIN);
                    }

                    if ("Right".equals(border.getPosition())) {
                        dataStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
                        dataStyle.setBorderRight(BorderStyle.THIN);
                    }

                    if ("Top".equals(border.getPosition())) {
                        dataStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
                        dataStyle.setBorderTop(BorderStyle.THIN);
                    }
                }
            }
        }

    }

    private static void writeImageToExcel(List<ExcelImageInput> excelImageInputs, HSSFWorkbook wb) throws IOException {
        BufferedImage bufferImg = null;
        if (!CollectionUtils.isEmpty(excelImageInputs)) {
            Iterator var3 = excelImageInputs.iterator();

            while(var3.hasNext()) {
                ExcelImageInput excelImageInput = (ExcelImageInput)var3.next();
                Sheet sheet = wb.getSheetAt(excelImageInput.getSheetIndex());
                if (sheet != null) {
                    Drawing patriarch = sheet.createDrawingPatriarch();
                    HSSFClientAnchor anchor = excelImageInput.getAnchorXls();
                    anchor.setAnchorType(AnchorType.DONT_MOVE_AND_RESIZE);
                    String imagePath = excelImageInput.getImgPath();
                    ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
                    bufferImg = ImageIO.read(new File(imagePath));
                    String imageType = imagePath.substring(imagePath.lastIndexOf(".") + 1, imagePath.length());
                    ImageIO.write(bufferImg, imageType, byteArrayOut);
                    patriarch.createPicture(anchor, wb.addPicture(byteArrayOut.toByteArray(), 5));
                }
            }
        }

    }

    private static void writeImageToExcel(List<ExcelImageInput> excelImageInputs, XSSFWorkbook wb) throws IOException {
        BufferedImage bufferImg = null;
        if (!CollectionUtils.isEmpty(excelImageInputs)) {
            Iterator var3 = excelImageInputs.iterator();

            while(var3.hasNext()) {
                ExcelImageInput excelImageInput = (ExcelImageInput)var3.next();
                Sheet sheet = wb.getSheetAt(excelImageInput.getSheetIndex());
                if (sheet != null) {
                    Drawing patriarch = sheet.createDrawingPatriarch();
                    XSSFClientAnchor anchor = excelImageInput.getAnchorXlsx();
                    anchor.setAnchorType(AnchorType.DONT_MOVE_AND_RESIZE);
                    String imagePath = excelImageInput.getImgPath();
                    ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
                    bufferImg = ImageIO.read(new File(imagePath));
                    String imageType = imagePath.substring(imagePath.lastIndexOf(".") + 1, imagePath.length());
                    ImageIO.write(bufferImg, imageType, byteArrayOut);
                    patriarch.createPicture(anchor, wb.addPicture(byteArrayOut.toByteArray(), 5));
                }
            }
        }

    }

    private static void addCellRange(HSSFSheet sheet, List<CellRangeAddressEntity> cellRangeAddresses) {
        if (!CollectionUtils.isEmpty(cellRangeAddresses)) {
            Iterator var2 = cellRangeAddresses.iterator();

            while(true) {
                CellRangeAddressEntity cellRangeAddressEntity;
                CellRangeAddress cellRangeAddress;
                do {
                    if (!var2.hasNext()) {
                        return;
                    }

                    cellRangeAddressEntity = (CellRangeAddressEntity)var2.next();
                    cellRangeAddress = cellRangeAddressEntity.getCellRangeAddress();
                    sheet.addMergedRegion(cellRangeAddress);
                } while(CollectionUtils.isEmpty(cellRangeAddressEntity.getBorders()));

                for(int k = 0; k < cellRangeAddressEntity.getBorders().size(); ++k) {
                    Border border = (Border)cellRangeAddressEntity.getBorders().get(k);
                    if (border != null) {
                        if ("Bottom".equals(border.getPosition())) {
                            RegionUtil.setBorderBottom(BorderStyle.THIN, cellRangeAddress, sheet);
                        }

                        if ("Left".equals(border.getPosition())) {
                            RegionUtil.setBorderLeft(BorderStyle.THIN, cellRangeAddress, sheet);
                        }

                        if ("Right".equals(border.getPosition())) {
                            RegionUtil.setBorderRight(BorderStyle.THIN, cellRangeAddress, sheet);
                        }

                        if ("Top".equals(border.getPosition())) {
                            RegionUtil.setBorderTop(BorderStyle.THIN, cellRangeAddress, sheet);
                        }
                    }
                }
            }
        }
    }

    private static void addCellRange(XSSFSheet sheet, List<CellRangeAddressEntity> cellRangeAddresses) {
        if (!CollectionUtils.isEmpty(cellRangeAddresses)) {
            Iterator var2 = cellRangeAddresses.iterator();

            while(true) {
                CellRangeAddressEntity cellRangeAddressEntity;
                CellRangeAddress cellRangeAddress;
                do {
                    if (!var2.hasNext()) {
                        return;
                    }

                    cellRangeAddressEntity = (CellRangeAddressEntity)var2.next();
                    cellRangeAddress = cellRangeAddressEntity.getCellRangeAddress();
                    sheet.addMergedRegion(cellRangeAddress);
                } while(CollectionUtils.isEmpty(cellRangeAddressEntity.getBorders()));

                for(int k = 0; k < cellRangeAddressEntity.getBorders().size(); ++k) {
                    Border border = (Border)cellRangeAddressEntity.getBorders().get(k);
                    if (border != null) {
                        if ("Bottom".equals(border.getPosition())) {
                            RegionUtil.setBorderBottom(BorderStyle.THIN, cellRangeAddress, sheet);
                        }

                        if ("Left".equals(border.getPosition())) {
                            RegionUtil.setBorderLeft(BorderStyle.THIN, cellRangeAddress, sheet);
                        }

                        if ("Right".equals(border.getPosition())) {
                            RegionUtil.setBorderRight(BorderStyle.THIN, cellRangeAddress, sheet);
                        }

                        if ("Top".equals(border.getPosition())) {
                            RegionUtil.setBorderTop(BorderStyle.THIN, cellRangeAddress, sheet);
                        }
                    }
                }
            }
        }
    }

    private static void setAlignment(Style style, CellStyle dataStyle) {
        if (style != null && style.getAlignment() != null) {
            String horizontal = style.getAlignment().getHorizontal();
            if (!ObjectUtils.isEmpty(horizontal)) {
                if ("Left".equals(horizontal)) {
                    dataStyle.setAlignment(HorizontalAlignment.LEFT);
                } else if ("Center".equals(horizontal)) {
                    dataStyle.setAlignment(HorizontalAlignment.CENTER);
                } else {
                    dataStyle.setAlignment(HorizontalAlignment.RIGHT);
                }
            }

            String vertical = style.getAlignment().getVertical();
            if (!ObjectUtils.isEmpty(vertical)) {
                if ("Top".equals(vertical)) {
                    dataStyle.setVerticalAlignment(VerticalAlignment.TOP);
                } else if ("Center".equals(vertical)) {
                    dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                } else if ("Bottom".equals(vertical)) {
                    dataStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
                } else if ("JUSTIFY".equals(vertical)) {
                    dataStyle.setVerticalAlignment(VerticalAlignment.JUSTIFY);
                } else {
                    dataStyle.setVerticalAlignment(VerticalAlignment.DISTRIBUTED);
                }
            }

            String wrapText = style.getAlignment().getWrapText();
            if (!ObjectUtils.isEmpty(wrapText)) {
                dataStyle.setWrapText(true);
            }
        }

    }

    private static void setCellColor(Style style, CellStyle dataStyle) {
        if (style != null && style.getInterior() != null) {
            String color = style.getInterior().getColor();
            if (color == null) {
                color = "#FFFFFF";
            }

            Integer[] rgb = ColorUtil.hex2Rgb(color);
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
            HSSFPalette palette = hssfWorkbook.getCustomPalette();
            HSSFColor paletteColor = palette.findSimilarColor(rgb[0], rgb[1], rgb[2]);
            dataStyle.setFillForegroundColor(paletteColor.getIndex());
            dataStyle.setFillBackgroundColor(paletteColor.getIndex());
            if ("Solid".equals(style.getInterior().getPattern())) {
                dataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            }
        }

    }

    private static int getCellRanges(int createRowIndex, List<CellRangeAddressEntity> cellRangeAddresses, int startIndex, Cell cellInfo, Style style) {
        if (cellInfo.getMergeAcross() != null || cellInfo.getMergeDown() != null) {
            CellRangeAddress cellRangeAddress = null;
            int length;
            int i;
            if (cellInfo.getMergeAcross() != null && cellInfo.getMergeDown() != null) {
                length = startIndex;
                if (cellInfo.getMergeAcross() != 0) {
                    length = startIndex + cellInfo.getMergeAcross();
                }

                i = createRowIndex;
                if (cellInfo.getMergeDown() != 0) {
                    i = createRowIndex + cellInfo.getMergeDown();
                }

                cellRangeAddress = new CellRangeAddress(createRowIndex, i, (short)startIndex, (short)length);
            } else if (cellInfo.getMergeAcross() != null && cellInfo.getMergeDown() == null) {
                if (cellInfo.getMergeAcross() != 0) {
                    length = startIndex + cellInfo.getMergeAcross();
                    cellRangeAddress = new CellRangeAddress(createRowIndex, createRowIndex, (short)startIndex, (short)length);
                }
            } else if (cellInfo.getMergeDown() != null && cellInfo.getMergeAcross() == null && cellInfo.getMergeDown() != 0) {
                length = createRowIndex + cellInfo.getMergeDown();
                cellRangeAddress = new CellRangeAddress(createRowIndex, length, (short)startIndex, (short)startIndex);
            }

            if (cellInfo.getMergeAcross() != null) {
                length = cellInfo.getMergeAcross();

                for(i = 0; i < length; ++i) {
                    ++startIndex;
                }
            }

            CellRangeAddressEntity cellRangeAddressEntity = new CellRangeAddressEntity();
            cellRangeAddressEntity.setCellRangeAddress(cellRangeAddress);
            if (style != null && style.getBorders() != null) {
                cellRangeAddressEntity.setBorders(style.getBorders());
            }

            cellRangeAddresses.add(cellRangeAddressEntity);
        }

        return startIndex;
    }

    private static void setValue(XSSFWorkbook wb, Cell cellInfo, XSSFCell cell, Style style, CellStyle dataStyle) {
        if (cellInfo.getData() != null) {
            XSSFFont font = wb.createFont();
            String color;
            Integer[] rgb;
            HSSFWorkbook hssfWorkbook;
            HSSFPalette palette;
            HSSFColor paletteColor;
            if (style != null && style.getFont() != null) {
                color = style.getFont().getColor();
                if (color == null) {
                    color = "#000000";
                }

                rgb = ColorUtil.hex2Rgb(color);
                hssfWorkbook = new HSSFWorkbook();
                palette = hssfWorkbook.getCustomPalette();
                paletteColor = palette.findSimilarColor(rgb[0], rgb[1], rgb[2]);
                font.setColor(paletteColor.getIndex());
            }

            if (!ObjectUtils.isEmpty(cellInfo.getData().getType()) && "Number".equals(cellInfo.getData().getType())) {
                cell.setCellType(CellType.NUMERIC);
            }

            if (style != null && style.getFont().getBold() > 0) {
                font.setBold(true);
            }

            if (style != null && !ObjectUtils.isEmpty(style.getFont().getFontName())) {
                font.setFontName(style.getFont().getFontName());
            }

            if (style != null && style.getFont().getSize() > 0.0D) {
                font.setFontHeightInPoints((short)((int)style.getFont().getSize()));
            }

            if (cellInfo.getData().getFont() != null) {
                if (cellInfo.getData().getFont().getBold() > 0) {
                    font.setBold(true);
                }
                cell.setCellValue(cellInfo.getData().getFont().getText());
                if (!ObjectUtils.isEmpty(cellInfo.getData().getFont().getCharSet())) {
                    font.setCharSet(Integer.valueOf(cellInfo.getData().getFont().getCharSet()));
                }
            } else if ("Number".equals(cellInfo.getData().getType())) {
                if (!ObjectUtils.isEmpty(cellInfo.getData().getText())) {
                    String data = cellInfo.getData().getText().replaceAll(",", "");
                    cell.setCellValue(Double.parseDouble(data));
                }
            } else {
                cell.setCellValue(cellInfo.getData().getText());
            }

            if (style != null && style.getNumberFormat() != null) {
                color = style.getFont().getColor();
                if (color == null) {
                    color = "#000000";
                }

                rgb = ColorUtil.hex2Rgb(color);
                hssfWorkbook = new HSSFWorkbook();
                palette = hssfWorkbook.getCustomPalette();
                paletteColor = palette.findSimilarColor(rgb[0], rgb[1], rgb[2]);
                font.setColor(paletteColor.getIndex());
                if ("0%".equals(style.getNumberFormat().getFormat())) {
                    XSSFDataFormat format = wb.createDataFormat();
                    dataStyle.setDataFormat(format.getFormat(style.getNumberFormat().getFormat()));
                } else {
                    dataStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
                }
                if (style.getId() .equals("s130")){
                    XSSFDataFormat format = wb.createDataFormat();
                    dataStyle.setDataFormat(format.getFormat(style.getNumberFormat().getFormat()));
                }
            }

            dataStyle.setFont(font);
        }

    }

    private static void setValue(HSSFWorkbook wb, Cell cellInfo, HSSFCell cell, Style style, CellStyle dataStyle) {
        if (cellInfo.getData() != null) {
            HSSFFont font = wb.createFont();
            String color;
            Integer[] rgb;
            HSSFWorkbook hssfWorkbook;
            HSSFPalette palette;
            HSSFColor paletteColor;
            if (style != null && style.getFont() != null) {
                color = style.getFont().getColor();
                if (color == null) {
                    color = "#000000";
                }

                rgb = ColorUtil.hex2Rgb(color);
                hssfWorkbook = new HSSFWorkbook();
                palette = hssfWorkbook.getCustomPalette();
                paletteColor = palette.findSimilarColor(rgb[0], rgb[1], rgb[2]);
                font.setColor(paletteColor.getIndex());
            }

            if (!ObjectUtils.isEmpty(cellInfo.getData().getType()) && "Number".equals(cellInfo.getData().getType())) {
                cell.setCellType(CellType.NUMERIC);
            }

            if (style != null && style.getFont().getBold() > 0) {
                font.setBold(true);
            }

            if (style != null && !ObjectUtils.isEmpty(style.getFont().getFontName())) {
                font.setFontName(style.getFont().getFontName());
            }

            if (style != null && style.getFont().getSize() > 0.0D) {
                font.setFontHeightInPoints((short)((int)style.getFont().getSize()));
            }

            if (cellInfo.getData().getFont() != null) {
                if (cellInfo.getData().getFont().getBold() > 0) {
                    font.setBold(true);
                }

                if ("Number".equals(cellInfo.getData().getType())) {
                    cell.setCellValue((double)Float.parseFloat(cellInfo.getData().getFont().getText()));
                } else {
                    cell.setCellValue(cellInfo.getData().getFont().getText());
                }

                if (!ObjectUtils.isEmpty(cellInfo.getData().getFont().getCharSet())) {
                    font.setCharSet(Integer.valueOf(cellInfo.getData().getFont().getCharSet()));
                }
            } else if ("Number".equals(cellInfo.getData().getType())) {
                if (!ObjectUtils.isEmpty(cellInfo.getData().getText())) {
                    cell.setCellValue((double)Float.parseFloat(cellInfo.getData().getText().replaceAll(",", "")));
                }
            } else {
                cell.setCellValue(cellInfo.getData().getText());
            }

            if (style != null && style.getNumberFormat() != null) {
                color = style.getFont().getColor();
                if (color == null) {
                    color = "#000000";
                }

                rgb = ColorUtil.hex2Rgb(color);
                hssfWorkbook = new HSSFWorkbook();
                palette = hssfWorkbook.getCustomPalette();
                paletteColor = palette.findSimilarColor(rgb[0], rgb[1], rgb[2]);
                font.setColor(paletteColor.getIndex());
                if ("0%".equals(style.getNumberFormat().getFormat())) {
                    HSSFDataFormat format = wb.createDataFormat();
                    dataStyle.setDataFormat(format.getFormat(style.getNumberFormat().getFormat()));
                } else {
                    dataStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
                }
            }
            dataStyle.setFont(font);
        }
    }
}
