/*
 * Copyright 2002-2017 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bizark.op.common.util;

import cn.hutool.core.util.StrUtil;
import com.bizark.common.enm.BaseEnum;
import com.bizark.op.common.exception.CheckException;
import com.bizark.op.common.exception.CustomException;

/**
 * 断言工具,丢出 CustomException
 *
 * <AUTHOR>
 */
public abstract class AssertUtil {

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new CustomException(message);
        }
    }

    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw new CustomException(message);
        }
    }


    public static void isTrue(boolean expression, BaseEnum<?> baseEnum) {
        if (!expression) {
            throw new CustomException(baseEnum.getName());
        }
    }

    public static void isTrue(boolean expression, BaseEnum<?> baseEnum, Object... params) {
        if (!expression) {
            throw new CustomException(StrUtil.format(baseEnum.getName(), params));
        }
    }

    public static void isFalse(boolean expression, BaseEnum<?> baseEnum, Object... params) {
        if (expression) {
            throw new CustomException(StrUtil.format(baseEnum.getName(), params));
        }
    }

    public static void isFalse(boolean expression, BaseEnum<?> baseEnum) {
        if (expression) {
            throw new CustomException(baseEnum.getName());
        }
    }

    public static void isFail(BaseEnum<?> baseEnum) {
        throw new CustomException(baseEnum.getName());
    }

    public static void isFail(BaseEnum<?> baseEnum, Exception e) {
        throw new CustomException(StrUtil.format(baseEnum.getName(), e.getMessage()), e);
    }

    public static void isFail(Exception e) {
        throw new CustomException(e.getMessage(), e);
    }

    public static void isFailCheck(boolean expression, String message) {
        if (expression){
            throw new CheckException(message);
        }
    }

}
