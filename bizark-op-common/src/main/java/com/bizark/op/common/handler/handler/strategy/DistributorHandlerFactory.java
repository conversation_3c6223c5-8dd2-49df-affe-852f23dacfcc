//package com.bizark.op.common.handler.handler.strategy;
//
//import com.bizark.op.common.handler.handler.platform.AbstractDistributorBusinessHandler;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.Map;
//
///**
// * lty notes saas平台 用户工厂
// *
// * <AUTHOR> Theo
// * @create 2023/10/12 14:25
// */
//@Component
//public class DistributorHandlerFactory {
//    @Resource
//    private Map<String, AbstractDistributorBusinessHandler> strategyMap;
//
//    /**
//     * 获取调用策略
//     *
//     * @param strategyName 策略名称
//     * @return {@link AbstractDistributorBusinessHandler}
//     */
//    public AbstractDistributorBusinessHandler getInvokeStrategy(String strategyName){
//        return strategyMap.get(strategyName);
//    }
//}
