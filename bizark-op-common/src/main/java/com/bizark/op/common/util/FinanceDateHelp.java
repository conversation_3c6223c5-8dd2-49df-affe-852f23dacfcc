package com.bizark.op.common.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

public class FinanceDateHelp {

    private LocalDateTime date;

    public FinanceDateHelp(LocalDateTime date){
        this.date = date;
    }

    public String getDateAndTime(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return this.date.format(settleFormat);
    }

    public String getYearMonthDay(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return this.date.format(settleFormat);
    }

    public String getYearMonth(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyy-MM");
        return this.date.format(settleFormat);
    }

    public String getYearMonthNoSeparator(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyyMM");
        return this.date.format(settleFormat);
    }

    public String getShortYearMonthNoSeparator(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyyMMdd");
        return this.date.format(settleFormat);
    }

    public String getMonthLastDayTime(){
        DateTimeFormatter dateTpl = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return this.date.with(TemporalAdjusters.lastDayOfMonth()).format(dateTpl)+" 23:59:59";
    }

    public String getCurrentDateTime(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime time = LocalDateTime.now(ZoneOffset.of("+8"));
        return time.format(settleFormat);
    }

    public String getCurrentYestoday(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime time = LocalDateTime.now(ZoneOffset.of("+8")).minusDays(1);
        return time.format(settleFormat);
    }

    public String getNextMonthDateAndTime(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return this.date.plusMonths(1).format(settleFormat);
    }

    public String getYear(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyy");
        return this.date.format(settleFormat);
    }

    public String getMonth(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("MM");
        return this.date.format(settleFormat);
    }

    public String getNextMonth(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("MM");
        return this.date.plusMonths(1).format(settleFormat);
    }

    public String getNextMonthYear(){
        DateTimeFormatter settleFormat = DateTimeFormatter.ofPattern("yyyy");
        return this.date.plusMonths(1).format(settleFormat);
    }
}
