package com.bizark.op.common.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.bizark.common.exception.CommonException;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.*;

public class ValidateUtil {

    private static Validator validator = Validation.buildDefaultValidatorFactory()
            .getValidator();

    public static <T> void beanValidate(T obj) {
        List<String> validatedMsg = new ArrayList<>();
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(obj);
        for (ConstraintViolation<Object> c : constraintViolations) {
            validatedMsg.add(c.getPropertyPath().toString() + ":" + c.getMessage());
        }
        if (CollectionUtil.isNotEmpty(constraintViolations)) {
            throw new CommonException(validatedMsg.toString());
        }

    }
    public static <T> void beanValidate(List<T> objs) {
        for (T t : objs) {
            beanValidate(t);
        }
    }
}
