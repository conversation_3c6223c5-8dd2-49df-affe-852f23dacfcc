package com.bizark.op.common.util;

import cn.hutool.core.collection.CollectionUtil;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.net.URL;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * zip文件工具类
 */
public class ZipFileUtil {

    /**
     * 指定不同url到不同文件夹下
     *
     * @param folderName 最外层文件夹名称
     * @param dirMapUrls 不同url放到不同文件夹下，合成一个大文件夹时，url和文件夹对应关系 Map<文件夹名称，url列表>
     * @return
     */
    public static File generateFolder(String folderName, Map<String, List<String>> dirMapUrls) {
        try {
            Path folderPath = Paths.get(folderName);
            if (Files.exists(folderPath)) {
                deleteDirectory(folderPath);
            }
            Files.createDirectories(folderPath);
            if (CollectionUtil.isEmpty(dirMapUrls)) {
                return folderPath.toFile();
            }
            for (Map.Entry<String, List<String>> entry : dirMapUrls.entrySet()) {
                Path subFolderPath = folderPath.resolve(entry.getKey());
                Files.createDirectories(subFolderPath);

                for (String url : entry.getValue()) {
                    downloadFile(url, subFolderPath.toString());
                }
            }
            return folderPath.toFile();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void deleteDirectory(Path path) throws IOException {
        if (Files.exists(path)) {
            Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    Files.delete(file);
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                    Files.delete(dir);
                    return FileVisitResult.CONTINUE;
                }
            });
        }
    }


    /**
     * 压缩文件
     *
     * @param folderName
     * @param sourceFolder
     * @return
     */
    public static File zipFile(String folderName, File sourceFolder) throws IOException {
        if (sourceFolder == null || !sourceFolder.exists()) {
            throw new IllegalArgumentException("源文件夹不存在");
        }
        if (!sourceFolder.isDirectory()) {
            throw new IllegalArgumentException("提供的路径不是文件夹");
        }

        File zipFile = new File(sourceFolder.getParent(), folderName + ".zip");
        try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(zipFile.toPath()))) {
            Path sourcePath = sourceFolder.toPath();
            Files.walk(sourcePath)
                    .filter(path -> !path.equals(sourcePath))
                    .forEach(path -> {
                        try {
                            String entryName = folderName + "/" + sourcePath.relativize(path).toString();
                            if (Files.isDirectory(path)) {
                                entryName += "/";
                                zos.putNextEntry(new ZipEntry(entryName));
                                zos.closeEntry();
                            } else {
                                zos.putNextEntry(new ZipEntry(entryName));
                                Files.copy(path, zos);
                                zos.closeEntry();
                            }
                        } catch (IOException e) {
                            throw new UncheckedIOException(e);
                        }
                    });
        }

        return zipFile;
    }

    public static void downloadFile(String fileUrl, String targetFolder) throws IOException {
        downloadFromHttp(fileUrl, targetFolder);
    }


    public static void downloadFromHttp(String fileUrl, String targetFolder) throws IOException {
        URL url = new URL(fileUrl);
        String fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
        File targetFile = new File(targetFolder, fileName);

        try (InputStream in = url.openStream();
             FileOutputStream out = new FileOutputStream(targetFile)) {
            IOUtils.copy(in, out);
        }
    }


}

