package com.bizark.op.common.core.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * 用于逻辑删除
 */
public class BaseExpandEntity extends BaseEntity{

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createdBy;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updatedBy;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedAt;

    /**
     * 更新者
     */
    private Integer disabledBy;

    /**
     * 更新者
     */
    private String disabledName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableLogic(value = "0",delval = "1")
    private Date disabledAt;

    /**
     * 备注
     */
    private String remark;

    @Override
    public Integer getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String getCreatedName() {
        return createdName;
    }

    @Override
    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    @Override
    public Date getCreatedAt() {
        return createdAt;
    }

    @Override
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    @Override
    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public String getUpdatedName() {
        return updatedName;
    }

    @Override
    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Override
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public Integer getDisabledBy() {
        return disabledBy;
    }

    @Override
    public void setDisabledBy(Integer disabledBy) {
        this.disabledBy = disabledBy;
    }

    @Override
    public String getDisabledName() {
        return disabledName;
    }

    @Override
    public void setDisabledName(String disabledName) {
        this.disabledName = disabledName;
    }

    @Override
    public Date getDisabledAt() {
        return disabledAt;
    }

    @Override
    public void setDisabledAt(Date disabledAt) {
        this.disabledAt = disabledAt;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }
}
