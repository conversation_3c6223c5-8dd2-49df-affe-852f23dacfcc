package com.bizark.op.common.annotation;

import java.lang.annotation.*;


/**
 * 自定义注解防止重复提交
 * <AUTHOR>
 * @date 2023-12-05 10:23:32
 */
@Inherited
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RepeatSubmit
{
    /**
     * 间隔时间(ms)，小于此时间视为重复提交
     */
    public int interval() default 5000;

    /**
     * 提示消息
     */
    public String message() default "提交过于频繁，请稍候再试";

    /**
     * 授权信息
     * @return
     */
    public boolean needAuthorization() default true;
}
