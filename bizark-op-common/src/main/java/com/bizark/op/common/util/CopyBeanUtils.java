package com.bizark.op.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ClassUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;


public class CopyBeanUtils extends BeanUtils {

    private final static Logger log = LoggerFactory.getLogger(CopyBeanUtils.class);

    /**
     * <AUTHOR>
     * 使用场景：Entity、Bo、Vo层数据的复制，因为BeanUtils.copyProperties只能给目标对象的属性赋值，却不能在List集合下循环赋值，因此添加该方法
     * 如：List<AdminEntity> 赋值到 List<AdminVo> ，List<AdminVo>中的 AdminVo 属性都会被赋予到值
     * S: 数据源类 ，T: 目标类::new(eg: AdminVo::new)
     */
    public static <S, T> List<T> copyPropertiesList(List<S> sources, Supplier<T> target) {
        if (CollUtil.isEmpty(sources)) {
            return CollUtil.newArrayList();
        }
        return sources.stream().map(source -> copyProperties(source, target)).collect(Collectors.toList());
    }

    public static <S, T> List<T> copyPropertiesList(List<S> sources, Class<T> clazz) {
        if (CollUtil.isEmpty(sources)) {
            return CollUtil.newArrayList();
        }
        return sources.stream().map(source -> copyProperties(source, clazz)).collect(Collectors.toList());
    }


    public static <S, T> T copyProperties(S source, Supplier<T> target) {
        T t = target.get();
        copyProperties(source, t);
        return t;
    }

    public static <S, T> T copyProperties(S source, Class<T> clazz) {
        T t = ReflectUtil.newInstanceIfPossible(clazz);
        copyProperties(source, t);
        return t;
    }


    /**
     * 拷贝属性（不会拷贝null值） 并且合并属性的值（仅合并BigDecimal类型）
     *
     * @param source  源数据
     * @param target  目标数据
     * @param isMerge 是否合并BigDecimal类型
     */
    public static void copyPropertyAndMerge(Object source, Object target, boolean isMerge) {
        if (ObjectUtil.isNull(source) || ObjectUtil.isNull(target)) {
            return;
        }
        Class<?> sourceClass = source.getClass();
        Class<?> targetClass = target.getClass();


        Field[] sourceFiles = sourceClass.getDeclaredFields();
        List<Field> sourceList = CollUtil.newArrayList(sourceFiles);

        Field[] superFields = sourceClass.getSuperclass().getDeclaredFields();
        if (ArrayUtil.isNotEmpty(superFields)) {
            sourceList.addAll(CollUtil.newArrayList(superFields));
        }
        List<String> fieldNameList = sourceList.stream().map(Field::getName).distinct().collect(Collectors.toList());

        for (String fieldName : fieldNameList) {
            Method readMethod = DozerUtils.getReadMethod(sourceClass, fieldName);

            Method writeMethod = DozerUtils.getWriteMethod(targetClass, fieldName);

            if (ObjectUtil.isNotNull(readMethod) &&
                    ClassUtils.isAssignable(writeMethod.getParameterTypes()[0], readMethod.getReturnType())) {

                try {
                    if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                        readMethod.setAccessible(true);
                    }
                    Object result = readMethod.invoke(source);

                    if (ObjectUtil.isNull(result)) {
                        continue;
                    }

                    if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                        writeMethod.setAccessible(true);
                    }

                    if (isMerge) {
                        Method targetMethod = DozerUtils.getReadMethod(targetClass, fieldName);
                        Object targetResult = DozerUtils.getInvokeResult(target, targetMethod);
                        if (ObjectUtil.isNotNull(targetResult) && ObjectUtil.isNotNull(result)) {
                            if (BigDecimal.class.equals(readMethod.getReturnType())) {
                                result = ((BigDecimal) result).add((BigDecimal) targetResult);
                            }
                        }
                    }

                    writeMethod.invoke(target, result);
                } catch (Exception e) {
                    log.error("set field  property fail", e);
                }
            }
        }
    }

    /**
     * 判断类中所有属性（不包含父类）是否为null
     */
    public static boolean allFieldsNull(Object obj) {
        if (ObjectUtil.isNull(obj)) {
            return true;
        }

        for (Field field : obj.getClass().getDeclaredFields()) {
            if (ObjectUtil.isNotNull(BeanUtil.getFieldValue(obj, field.getName()))) {
                return false;
            }
        }
        return true;
    }
}
