package com.bizark.op.common.util;

import cn.hutool.core.date.DatePattern;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.*;

public class SpiltDateUtil {

    private static SimpleDateFormat monthDateFormat = new SimpleDateFormat("MM");

    /**
     * 将时间段按小时分割
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static final List<Range> splitToHours(String startDate, String endDate) {
        Date start = DateUtil.convertStringToDate(startDate, DatePattern.NORM_DATETIME_PATTERN);
        Date end = DateUtil.convertStringToDate(endDate, DatePattern.NORM_DATETIME_PATTERN);
        Calendar c1 = Calendar.getInstance();
        c1.setTime(start);
        c1.set(Calendar.MINUTE, 0);
        c1.set(Calendar.SECOND, 0);
        c1.set(Calendar.MILLISECOND, 0);
        Date startTime = c1.getTime();
        List<Range> result = new ArrayList<>();
        result.add(Range.create(startTime));

        Calendar c2 = Calendar.getInstance();
        c2.setTime(end);
        c2.set(Calendar.MINUTE, 59);
        c2.set(Calendar.SECOND, 59);
        c2.set(Calendar.MILLISECOND, 0);
        Date endTime = c2.getTime();

        Date from = new Date(startTime.getTime() + 3600 * 1000);
        while (from.compareTo(endTime) <= 0) {
            Date dt = new Date(from.getTime());
            Calendar c = Calendar.getInstance();
            c.setTime(new Date(dt.getTime() - 3600 * 1000));
            c.set(Calendar.MINUTE, 59);
            c.set(Calendar.SECOND, 59);
            c.set(Calendar.MILLISECOND, 0);
            CollectionUtil.last(result).end(c.getTime());
            CollectionUtil.last(result).setMonth(monthDateFormat.format(new Date(dt.getTime() - 3600 * 1000)));
            result.add(Range.create(dt));
            from.setTime(from.getTime() + 3600 * 1000);
        }
        CollectionUtil.last(result).end(endTime);
        CollectionUtil.last(result).setMonth(monthDateFormat.format(end));
        return result;
    }

    /**
     * 将时间段按日分割
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static final List<Range> splitToDays(String startDate, String endDate) {
        Date start = DateUtil.convertStringToDate(startDate);
        Date end = DateUtil.convertStringToDate(endDate);
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Date from = new Date(start.getTime() + 24 * 3600 * 1000);
        while (from.compareTo(end) <= 0) {
            Date dt = new Date(from.getTime());
            CollectionUtil.last(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            CollectionUtil.last(result).setMonth(monthDateFormat.format(new Date(dt.getTime() - 24L * 3600 * 1000)));
            result.add(Range.create(dt));
            from.setTime(from.getTime() + 24 * 3600 * 1000);
        }
        CollectionUtil.last(result).end(end);
        CollectionUtil.last(result).setMonth(monthDateFormat.format(end));
        return result;
    }


    /**
     * 将指定日期时间进行分割
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static final List<Range> splitToDays(Integer dayFlag,String startDate, String endDate) {
        Date start = DateUtil.convertStringToDate(startDate);
        Date end = DateUtil.convertStringToDate(endDate);
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Date from = new Date(start.getTime() +dayFlag * 24 * 3600 * 1000);
        while (from.compareTo(end) <= 0) {
            Date dt = new Date(from.getTime());
            CollectionUtil.last(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            CollectionUtil.last(result).setMonth(monthDateFormat.format(new Date(dt.getTime() - 24L * 3600 * 1000)));
            result.add(Range.create(dt));
            from.setTime(from.getTime() + dayFlag * 24 * 3600 * 1000);
        }
        CollectionUtil.last(result).end(end);
        CollectionUtil.last(result).setMonth(monthDateFormat.format(end));
        return result;
    }

    /**
     * 将时间段按星期分割
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static final List<Range> splitToWeeks(String startDate, String endDate) {
        Date start = DateUtil.convertStringToDate(startDate);
        Date end = DateUtil.convertStringToDate(endDate);
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Date from = new Date(start.getTime() + 7L * 24 * 3600 * 1000);
        Date weekEnd = cn.hutool.core.date.DateUtil.endOfWeek(end);
        while (from.compareTo(weekEnd) <= 0) {
            Date dt = cn.hutool.core.date.DateUtil.beginOfWeek(from);
            CollectionUtil.last(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            CollectionUtil.last(result).setMonth(monthDateFormat.format(new Date(dt.getTime() - 24L * 3600 * 1000)));
            result.add(Range.create(dt));
            from.setTime(from.getTime() + 7L * 24 * 3600 * 1000);
        }
        CollectionUtil.last(result).end(end);
        CollectionUtil.last(result).setMonth(monthDateFormat.format(end));
        return result;
    }

    /**
     * 将时间段按照月分割
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static final List<Range> splitToMonths(String startDate, String endDate) {
        Date start = DateUtil.convertStringToDate(startDate);
        Date end = DateUtil.convertStringToDate(endDate);
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.MONTH, 1);
        Date monthEnd = cn.hutool.core.date.DateUtil.endOfMonth(end);
        while (cal.getTimeInMillis() <= monthEnd.getTime()) {
            Date dt = cn.hutool.core.date.DateUtil.beginOfMonth(cal.getTime());
            CollectionUtil.last(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            CollectionUtil.last(result).setMonth(monthDateFormat.format(new Date(dt.getTime() - 24L * 3600 * 1000)));
            result.add(Range.create(dt));
            cal.add(Calendar.MONTH, 1);
        }
        CollectionUtil.last(result).end(end);
        CollectionUtil.last(result).setMonth(monthDateFormat.format(end));
        return result;
    }

    /**
     * 将时间段按照季度分割
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static final List<Range> splitToQuarts(String startDate, String endDate) {
        Date start = DateUtil.convertStringToDate(startDate);
        Date end = DateUtil.convertStringToDate(endDate);
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.MONTH, 3);
        Date quartEnd = cn.hutool.core.date.DateUtil.endOfQuarter(end);
        while (cal.getTimeInMillis() <= quartEnd.getTime()) {
            Date dt = cn.hutool.core.date.DateUtil.beginOfQuarter(cal.getTime());
            CollectionUtil.last(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            if (cal.get(Calendar.MONTH) >= 1 && cal.get(Calendar.MONTH) <= 3) {
                CollectionUtil.last(result).setMonth("4");
            } else if (cal.get(Calendar.MONTH) >= 4 && cal.get(Calendar.MONTH) <= 6) {
                CollectionUtil.last(result).setMonth("1");
            } else if (cal.get(Calendar.MONTH) >= 7 && cal.get(Calendar.MONTH) <= 9) {
                CollectionUtil.last(result).setMonth("2");
            } else if (cal.get(Calendar.MONTH) >= 10 && cal.get(Calendar.MONTH) <= 12) {
                CollectionUtil.last(result).setMonth("3");
            }
            result.add(Range.create(dt));
            cal.add(Calendar.MONTH, 3);
        }
        CollectionUtil.last(result).end(end);
        if (cal.get(Calendar.MONTH) >= 1 && cal.get(Calendar.MONTH) <= 3) {
            CollectionUtil.last(result).setMonth("4");
        } else if (cal.get(Calendar.MONTH) >= 4 && cal.get(Calendar.MONTH) <= 6) {
            CollectionUtil.last(result).setMonth("1");
        } else if (cal.get(Calendar.MONTH) >= 7 && cal.get(Calendar.MONTH) <= 9) {
            CollectionUtil.last(result).setMonth("2");
        } else if (cal.get(Calendar.MONTH) >= 10 && cal.get(Calendar.MONTH) <= 12) {
            CollectionUtil.last(result).setMonth("3");
        }
        return result;
    }

    /**
     * 将时间段按照半年分割
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static final List<Range> splitToHalfOfYears(String startDate, String endDate) {
        Date start = DateUtil.convertStringToDate(startDate);
        Date end = DateUtil.convertStringToDate(endDate);
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.MONTH, 6);
        Calendar halfYearEnd = DateUtil.endOfHalfYear(end);
        while (cal.getTimeInMillis() <= halfYearEnd.getTimeInMillis()) {
            int mon = cal.get(Calendar.MONTH);
            Calendar cal1 = Calendar.getInstance();
            if (mon < 6) {
                cal1.setTimeInMillis(cal.getTimeInMillis());
                cal1.set(Calendar.MONTH, 0);
            } else {
                cal1.setTimeInMillis(cal.getTimeInMillis());
                cal1.set(Calendar.MONTH, 6);
            }
            Date dt = cn.hutool.core.date.DateUtil.beginOfMonth(cal1.getTime());
            CollectionUtil.last(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            result.add(Range.create(dt));
            cal.add(Calendar.MONTH, 6);
        }
        CollectionUtil.last(result).end(end);
        return result;
    }

    /**
     * 将时间段按照年分割
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static final List<Range> splitToYears(String startDate, String endDate) {
        Date start = DateUtil.convertStringToDate(startDate);
        Date end = DateUtil.convertStringToDate(endDate);
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.YEAR, 1);
        Date yearEnd = cn.hutool.core.date.DateUtil.endOfYear(end);
        while (cal.getTimeInMillis() <= yearEnd.getTime()) {
            Date dt = cn.hutool.core.date.DateUtil.beginOfYear(cal.getTime());
            CollectionUtil.last(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            result.add(Range.create(dt));
            cal.add(Calendar.YEAR, 1);
        }
        CollectionUtil.last(result).end(end);
        return result;
    }

    /**
     * 日期区间
     */
    public static class Range implements Serializable {
        Date start;
        Date end;
        String Month;

        public Range() {

        }

        private Range(Date start) {
            this.start = start;
        }

        public static Range create(Date start) {
            return new Range(start);
        }

        public Range end(Date end) {
            this.end = end;
            return this;
        }

        public void setStart(Date start) {
            this.start = start;
        }

        public void setEnd(Date end) {
            this.end = end;
        }

        public Date getStart() {
            return start;
        }

        public Date getEnd() {
            return end;
        }

        public String getMonth() {
            return Month;
        }

        public void setMonth(String month) {
            Month = month;
        }

        @Override
        public String toString() {
            return "[" + cn.hutool.core.date.DateUtil.format(start, "yyyy-MM-dd HH:mm:ss") + "," + cn.hutool.core.date.DateUtil.format(end, "yyyy-MM-dd HH:mm:ss")
                    + "]";
        }
    }

    public static class CollectionUtil {
        //获取最后一行
        public static final Range last(List<Range> list) {
            int size = list.size();
            if (size != 0) {
                return list.get(size - 1);
            }
            return null;
        }
    }

    //根据年，月，第几周获取时间范围
    public static Map getWeekDateLimits(String year, String month, String week) {
        Map result = new HashMap();
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c1 = Calendar.getInstance();
        c1.set(Calendar.YEAR, Integer.parseInt(year));
        c1.set(Calendar.MONTH, Integer.parseInt(month) - 1);
        c1.set(Calendar.DAY_OF_WEEK, 2);
        c1.set(Calendar.WEEK_OF_MONTH, Integer.parseInt(week));
        Date time1 = c1.getTime();
        String begin = dft.format(time1);
        c1.set(Calendar.DATE, c1.get(Calendar.DATE) + 6);
        Date time2 = c1.getTime();
        String end = dft.format(time2);
        result.put("begin", begin);
        result.put("end", end);
        return result;
    }


    public static void main(String[] args) {
        try {
            /*Calendar c = Calendar.getInstance();
            c.set(Calendar.YEAR, 2022);
            c.set(Calendar.MONTH, (3 - 1));
            c.setFirstDayOfWeek(Calendar.MONDAY);
            System.out.println(c.get(Calendar.YEAR) + "年" + (c.get(Calendar.MONTH) + 1) + "月");
            System.out.println("天数：" + c.getActualMaximum(Calendar.DAY_OF_MONTH));
            System.out.println("Actual周数：" + c.getActualMaximum(Calendar.WEEK_OF_MONTH));
            System.out.println("Max周数：" + c.getMaximum(Calendar.WEEK_OF_MONTH));
            for (int i = 0; i < 6; i++) {
                System.out.println(getWeekDateLimits(2022 + "", 3 + "", (i + 1) + ""));
            }*/
            List<Range> ranges = splitToDays("2022-04-23 23:48:29", "2022-04-30 23:48:29");
            //List<Range> ranges = splitToWeeks("2022-01-03", "2022-01-25");
            ranges.forEach(System.out::println);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
