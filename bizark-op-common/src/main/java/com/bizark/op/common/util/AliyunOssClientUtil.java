package com.bizark.op.common.util;

import cn.hutool.core.util.ObjectUtil;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.*;
import com.bizark.common.constant.PropertiesConfig;
import com.bizark.op.common.enm.ContentTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

public class AliyunOssClientUtil {
    private final static Logger logger = LoggerFactory.getLogger(AliyunOssClientUtil.class);

    private static String accessKeyId;
    private static String secretAccessKey;
    private static String bucketName;
    private static String endPoint;

    private static final int PART_SIZE = 20 * 1024 * 1024;
    private static final int MAX_RETRIES = 2;
    private static final int MAX_PARALLEL = 5;

    private static OSSClient ossClient = null;

    static {
        PropertiesConfig config = new PropertiesConfig("properties/setting");
        accessKeyId = config.getPropByKey("aliyun.accessId");
        secretAccessKey = config.getPropByKey("aliyun.accessKey");
        bucketName = config.getPropByKey("aliyun.ossBucketDefault");
        endPoint = config.getPropByKey("aliyun.ossEndpointDefault");
        initOssClient();
    }

    public AliyunOssClientUtil() {
        initOssClient();
    }

    public static void initOssClient() {
        if (ObjectUtil.isNull(ossClient)) {
            ossClient = new OSSClient(endPoint, new DefaultCredentialProvider(accessKeyId, secretAccessKey), new ClientBuilderConfiguration());
        }
    }

    /**
     * 创建模拟文件夹
     *
     * @param folder 模拟文件夹名如"qj_nanjing/"
     * @return 文件夹名
     */
    public static String createFolder(String folder) {
        //文件夹名
        final String keySuffixWithSlash = folder;
        //判断文件夹是否存在，不存在则创建
        if (!ossClient.doesObjectExist(bucketName, keySuffixWithSlash)) {
            //创建文件夹
            ossClient.putObject(bucketName, keySuffixWithSlash, new ByteArrayInputStream(new byte[0]));
            logger.info("创建文件夹成功");
            //得到文件夹名
            OSSObject object = ossClient.getObject(bucketName, keySuffixWithSlash);
            String fileDir = object.getKey();
            return fileDir;
        }
        return keySuffixWithSlash;
    }

    /**
     * 列出对应文件下的所有文件
     *
     * @param mkdirName 文件名 + /
     */
    public static void showOssFileList(String mkdirName) {
        // 构造ListObjectsRequest请求。
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);

        // 设置正斜线（/）为文件夹的分隔符。
        listObjectsRequest.setDelimiter("/");

        // 列出fun目录下的所有文件和文件夹。
        listObjectsRequest.setPrefix(mkdirName);

        ObjectListing listing = ossClient.listObjects(listObjectsRequest);

        // 遍历所有文件。
        System.out.println("Objects:");
        // objectSummaries的列表中给出的是fun目录下的文件。
        for (OSSObjectSummary objectSummary : listing.getObjectSummaries()) {
            System.out.println(objectSummary.getKey());
        }
    }

    /**
     * 列出对应文件下的所有文件夹
     *
     * @param mkdirName 文件名 + /
     * @return
     */
    public static List<String> showOssMkdirList(String mkdirName) {
        // 构造ListObjectsRequest请求。
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);

        // 设置正斜线（/）为文件夹的分隔符。
        listObjectsRequest.setDelimiter("/");

        // 列出fun目录下的所有文件和文件夹。
        listObjectsRequest.setPrefix(mkdirName);

        ObjectListing listing = ossClient.listObjects(listObjectsRequest);

        // 遍历所有文件。
        System.out.println("Objects:");
        // objectSummaries的列表中给出的是fun目录下的文件。
        for (String commonPrefix : listing.getCommonPrefixes()) {
            System.out.println(commonPrefix);
        }
        return listing.getCommonPrefixes();
    }

    /**
     * 根据key删除OSS服务器上的文件
     *
     * @param folder 模拟文件夹名 如"qj_nanjing/"
     * @param key    Bucket下的文件的路径名+文件名 如："upload/cake.jpg"
     */
    public static void deleteFile(String folder, String key) {
        ossClient.deleteObject(bucketName, folder + key);
        logger.info("删除" + bucketName + "下的文件" + folder + key + "成功");
    }

    /**
     * 上传文件至OSS
     *
     * @param fileName    文件名
     * @param inputStream 数据源
     * @param folder      文件夹
     * @return 下载路径
     */
    public static String uploadFile(String fileName, InputStream inputStream, String folder) {
        PutObjectResult result = uploadObjectOSSByStream(fileName, inputStream, folder);
        AssertUtil.isTrue(ObjectUtil.isNotNull(result), "上传阿里云OSS服务器异常");
        //https://ehenglin-public-hz.oss-cn-hangzhou.aliyuncs.com/erp/InventoryChange/库存变动记录20230925060238.xlsx
        return "https://" + bucketName + "." + endPoint + "/" + folder + fileName;
    }

    /**
     * 上传文件至OSS
     */
    public static String uploadObject2OSSByStream(String fileName, InputStream inputStream, String folder) {
        PutObjectResult result = uploadObjectOSSByStream(fileName, inputStream, folder);
        if (ObjectUtil.isNull(result)) {
            return null;
        }
        return result.getETag();
    }

    /**
     * 上传文件至OSS
     */
    public static PutObjectResult uploadObjectOSSByStream(String fileName, InputStream inputStream, String folder) {
        try {
            //创建上传Object的Metadata
            ObjectMetadata metadata = new ObjectMetadata();
            //上传的文件的长度
            metadata.setContentLength(inputStream.available());
            //指定该Object被下载时的网页的缓存行为
            metadata.setCacheControl("no-cache");
            //指定该Object下设置Header
            metadata.setHeader("Pragma", "no-cache");
            //指定该Object被下载时的内容编码格式
            metadata.setContentEncoding("utf-8");
            //文件的MIME，定义文件的类型及网页编码，决定浏览器将以什么形式、什么编码读取文件。如果用户没有指定则根据Key或文件名的扩展名生成，
            //如果没有扩展名则填默认值application/octet-stream
            metadata.setContentType(getContentType(fileName));
            //指定该Object被下载时的名称（指示MINME用户代理如何显示附加的文件，打开或下载，及文件名称）
            String dispositionFileNamePart = "filename=" + URLEncoder.encode(fileName, "UTF-8") + ";filename*=UTF-8''" + URLEncoder.encode(fileName, "UTF-8");
            metadata.setContentDisposition(dispositionFileNamePart);
            //上传文件   (上传文件流的形式)
            return ossClient.putObject(bucketName, folder + fileName, inputStream, metadata);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("上传阿里云OSS服务器异常." + e.getMessage(), e);
        }
        return null;
    }

    public static void closeClient() {
        ossClient.shutdown();
    }

    /**
     * 通过文件名判断并获取OSS服务文件上传时文件的contentType
     *
     * @param fileName 文件名
     * @return 文件的contentType
     */
    private static String getContentType(String fileName) {
        //文件的后缀名
        String fileExtension = fileName.substring(fileName.lastIndexOf("."));
        try {
            return ContentTypeEnum.nameOf(fileExtension);
        } catch (Exception e) {
            return "application/octet-stream";
        }
//        if (".bmp".equalsIgnoreCase(fileExtension)) {
//            return "image/bmp";
//        }
//        if (".gif".equalsIgnoreCase(fileExtension)) {
//            return "image/gif";
//        }
//        if (".jpeg".equalsIgnoreCase(fileExtension) || ".jpg".equalsIgnoreCase(fileExtension) || ".png".equalsIgnoreCase(fileExtension)) {
//            return "image/jpeg";
//        }
//        if (".html".equalsIgnoreCase(fileExtension)) {
//            return "text/html";
//        }
//        if (".txt".equalsIgnoreCase(fileExtension)) {
//            return "text/plain";
//        }
//        if (".vsd".equalsIgnoreCase(fileExtension)) {
//            return "application/vnd.visio";
//        }
//        if (".ppt".equalsIgnoreCase(fileExtension) || "pptx".equalsIgnoreCase(fileExtension)) {
//            return "application/vnd.ms-powerpoint";
//        }
//        if (".doc".equalsIgnoreCase(fileExtension) || "docx".equalsIgnoreCase(fileExtension)) {
//            return "application/msword";
//        }
//        if (".xml".equalsIgnoreCase(fileExtension)) {
//            return "text/xml";
//        }
//        //默认返回类型
//        return "image/jpeg";
    }

    public static String getBucketName() {
        return bucketName;
    }

    public static String getEndPoint() {
        return endPoint;
    }

    public Boolean doesObjectExist(String ObjectName) {
        return ossClient.doesObjectExist(bucketName, ObjectName);
    }

    /**
     * 获取上传文件的地址
     *
     * @param key folder+fileName
     * @return
     */
    public String getFileAddress(String key) {
        Date date = new Date(System.currentTimeMillis() + 946080000 * 1000L);
        return ossClient.generatePresignedUrl(bucketName, key, date).toString();
    }





    /**
     * @Description:分片上传文件
     * @Author: wly
     * @Date: 2025-09-17 17:44
     * @Params: [fileName, inputStream, folder, fileSize]
     * @Return: java.lang.String
     **/

    public static String resumableUpload(String fileName, InputStream inputStream,
                                         String folder, long fileSize) {
        String objectName = folder + fileName;
        String uploadId = null;
        List<PartETag> partETags = Collections.synchronizedList(new ArrayList<>());

        ExecutorService executor = Executors.newFixedThreadPool(MAX_PARALLEL);

        try {
            //初始化分片上传
            InitiateMultipartUploadRequest initRequest =
                    new InitiateMultipartUploadRequest(bucketName, objectName);
            InitiateMultipartUploadResult initResponse =
                    ossClient.initiateMultipartUpload(initRequest);
            uploadId = initResponse.getUploadId();

            final String finalUploadId = uploadId;

            //计算分片数量
            int partCount = (int) Math.ceil((double) fileSize / PART_SIZE);
            logger.info("文件大小: {} MB, 分片数: {}", fileSize / (1024 * 1024), partCount);

            //并行上传分片
            List<Future<PartETag>> futures = new ArrayList<>();
            byte[] buffer = new byte[PART_SIZE];

            for (int partNumber = 1; partNumber <= partCount; partNumber++) {
                final int currentPartNumber = partNumber;

                // 读取分片数据
                int bytesRead = readFully(inputStream, buffer);
                if (bytesRead <= 0) break;

                final byte[] partData = Arrays.copyOf(buffer, bytesRead);

                // 提交分片上传任务
                Future<PartETag> future = executor.submit(() -> {
                    try (ByteArrayInputStream partStream = new ByteArrayInputStream(partData)) {
                        UploadPartRequest uploadRequest = new UploadPartRequest();
                        uploadRequest.setBucketName(bucketName);
                        uploadRequest.setKey(objectName);
                        uploadRequest.setUploadId(finalUploadId);
                        uploadRequest.setPartNumber(currentPartNumber);
                        uploadRequest.setInputStream(partStream);
                        uploadRequest.setPartSize(partData.length);

                        UploadPartResult uploadResult = uploadPartWithRetry(uploadRequest);
                        return uploadResult.getPartETag();
                    }
                });

                futures.add(future);
            }

            for (int i = 0; i < futures.size(); i++) {
                try {
                    PartETag partETag = futures.get(i).get(8, TimeUnit.MINUTES);
                    partETags.add(partETag);
                    logger.info("分片 {}/{} 上传完成", i + 1, futures.size());
                } catch (Exception e) {
                    logger.error("分片 {} 上传失败: {}", i + 1, e.getMessage());
                    throw new RuntimeException("分片上传失败", e);
                }
            }
            //按分片号排序
            partETags.sort(Comparator.comparingInt(PartETag::getPartNumber));
            // 完成分片上传
            CompleteMultipartUploadRequest completeRequest =
                    new CompleteMultipartUploadRequest(bucketName, objectName, finalUploadId, partETags);
            CompleteMultipartUploadResult result =
                    ossClient.completeMultipartUpload(completeRequest);

            logger.info("所有分片上传完成，总大小: {} MB", fileSize / (1024 * 1024));
            String url = "https://" + bucketName + "." + endPoint + "/" + objectName;
            logger.info("文件上传地址: {}", url);
            return url;
        } catch (Exception e) {
            // 出错时中止上传
            if (uploadId != null) {
                try {
                    ossClient.abortMultipartUpload(
                            new AbortMultipartUploadRequest(bucketName, objectName, uploadId));
                } catch (Exception abortEx) {
                    logger.error("中止上传失败: {}", abortEx.getMessage());
                }
            }
            logger.error("分片上传失败: {}", e.getMessage());
            return null;
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    private static int readFully(InputStream in, byte[] buffer) throws IOException {
        int totalRead = 0;
        while (totalRead < buffer.length) {
            int bytesRead = in.read(buffer, totalRead, buffer.length - totalRead);
            if (bytesRead == -1) break;
            totalRead += bytesRead;
        }
        return totalRead;
    }


    //上传重试
    private static UploadPartResult uploadPartWithRetry(UploadPartRequest request) {
        int retry = 0;
        while (retry < MAX_RETRIES) {
            try {
                return ossClient.uploadPart(request);
            } catch (Exception e) {
                retry++;
                if (retry >= MAX_RETRIES) {
                    throw new RuntimeException("分片上传重试失败: " + e.getMessage());
                }
                try {
                    Thread.sleep(1000 * retry); // 线性退避
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("分片上传中断");
                }
            }
        }
        throw new RuntimeException("无法上传分片");
    }
}
