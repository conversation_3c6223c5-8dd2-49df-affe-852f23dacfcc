package com.bizark.op.common.util.pdf;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.awt.*;
import java.io.*;
import java.util.*;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.IntStream;

/**
 * PDF工具类
 *
 * <AUTHOR>
 * @date 2025-08-05 16:32
 **/
public class PDFUtil {

    /**
     * 合并多个PDF文件为一个PDF文件
     *
     * @param inputPaths 输入PDF文件路径列表
     * @param outputPath 输出PDF文件路径
     * @throws IOException IO异常
     */
    public static void mergePDFs(List<String> inputPaths, String outputPath) throws IOException {
        if (inputPaths == null || inputPaths.isEmpty()) {
            throw new IllegalArgumentException("输入PDF文件路径列表不能为空");
        }

        // 使用PDFBox提供的合并工具
        PDFMergerUtility merger = new PDFMergerUtility();
        merger.setDestinationFileName(outputPath);

        // 添加所有源文件
        for (String inputPath : inputPaths) {
            if (inputPath == null || inputPath.isEmpty()) {
                continue; // 跳过空路径
            }
            try {
                merger.addSource(new File(inputPath));
            } catch (Exception e) {
                System.err.println("添加文件 " + inputPath + " 到合并列表时出错: " + e.getMessage());
                throw new RuntimeException("添加PDF文件失败: " + inputPath, e);
            }
        }

        // 执行合并
        try {
            merger.mergeDocuments();
            System.out.println("成功合并 " + inputPaths.size() + " 个PDF文件到: " + outputPath);
        } catch (IOException e) {
            System.err.println("合并PDF文件时出错: " + e.getMessage());
            throw new RuntimeException("合并PDF文件失败", e);
        }
    }


    /**
     * 合并多个PDF输入流为一个PDF输出流
     *
     * @param inputStreams 输入PDF文件流列表
     * @param outputStream 输出PDF文件流
     * @throws IOException IO异常
     */
    public static void mergePDFs(List<InputStream> inputStreams, OutputStream outputStream) throws IOException {
        if (inputStreams == null || inputStreams.isEmpty()) {
            throw new IllegalArgumentException("输入PDF文件流列表不能为空");
        }

        // 使用PDFBox提供的合并工具
        PDFMergerUtility merger = new PDFMergerUtility();
        merger.setDestinationStream(outputStream);

        // 添加所有源文件
        for (InputStream inputStream : inputStreams) {
            if (inputStream == null) {
                continue; // 跳过空路径
            }
            try {
                merger.addSource(inputStream);
            } catch (Exception e) {
                throw new RuntimeException("添加PDF文件失败");
            }
        }

        // 执行合并
        try {
            merger.mergeDocuments();
        } catch (IOException e) {
            System.err.println("合并PDF文件时出错: " + e.getMessage());
            throw new RuntimeException("合并PDF文件失败", e);
        }
    }


    /**
     * 合并多个PDF文档对象为一个PDF文件
     *
     * @param documents  PDF文档对象列表
     * @param outputPath 输出PDF文件路径
     * @throws IOException IO异常
     */
    public static void mergePDFsFromDocuments(List<PDDocument> documents, String outputPath) throws IOException {
        if (documents == null || documents.isEmpty()) {
            throw new IllegalArgumentException("PDF文档对象列表不能为空");
        }

        try (PDDocument targetDoc = new PDDocument()) {
            // 遍历所有文档对象
            for (PDDocument sourceDoc : documents) {
                if (sourceDoc == null) {
                    continue; // 跳过空文档
                }

                try {
                    // 将源文档的所有页面复制到目标文档
                    for (PDPage page : sourceDoc.getPages()) {
                        // 导入页面到目标文档
                        PDPage importedPage = targetDoc.importPage(page);
                    }
                } catch (Exception e) {
                    System.err.println("处理文档对象时出错: " + e.getMessage());
                    throw new RuntimeException("合并PDF文档失败", e);
                }
            }

            // 保存合并后的文档
            targetDoc.save(outputPath);
            System.out.println("成功合并 " + documents.size() + " 个PDF文档到: " + outputPath);
        }
    }

    /**
     * 在PDF指定页面上添加居中文本
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param text       要添加的文本
     * @param fontSize   字体大小
     * @param isBold     是否加粗
     * @param color      文本颜色
     * @param pageIndex  页面索引（从0开始）
     */
    public static void addCenteredTextToPage(String inputPath, String outputPath, String text,
                                             float fontSize, boolean isBold, Color color, int pageIndex) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            addCenteredTextToPage(doc, text, fontSize, isBold, color, pageIndex);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("添加居中文本失败", e);
        }
    }

    /**
     * 在PDF指定页面上添加居中文本（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param text         要添加的文本
     * @param fontSize     字体大小
     * @param isBold       是否加粗
     * @param color        文本颜色
     * @param pageIndex    页面索引（从0开始）
     */
    public static void addCenteredTextToPage(InputStream inputStream, OutputStream outputStream, String text,
                                             float fontSize, boolean isBold, Color color, int pageIndex) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            addCenteredTextToPage(doc, text, fontSize, isBold, color, pageIndex);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("添加居中文本失败", e);
        }
    }

    /**
     * 在PDF指定页面上添加居中文本（使用PDDocument对象）
     *
     * @param doc       PDF文档对象
     * @param text      要添加的文本
     * @param fontSize  字体大小
     * @param isBold    是否加粗
     * @param color     文本颜色
     * @param pageIndex 页面索引（从0开始）
     */
    public static void addCenteredTextToPage(PDDocument doc, String text, float fontSize,
                                             boolean isBold, Color color, int pageIndex) {
        try {
            if (pageIndex < 0 || pageIndex >= doc.getNumberOfPages()) {
                throw new IllegalArgumentException("页面索引超出范围: " + pageIndex);
            }

            PDPage page = doc.getPage(pageIndex);
            PDRectangle pageSize = page.getMediaBox();

            float pageWidth = pageSize.getWidth();
            float pageHeight = pageSize.getHeight();

            // 加载字体
            PDFont font = loadChineseFont(doc, null, isBold);

            // 计算文本宽度
            float textWidth = font.getStringWidth(text) / 1000 * fontSize;

            // 计算居中位置
            float x = (pageWidth - textWidth) / 2;
            float y = pageHeight / 2; // 垂直居中，可根据需要调整

            // 创建内容流添加文本
            try (PDPageContentStream content = new PDPageContentStream(
                    doc, page,
                    PDPageContentStream.AppendMode.APPEND,
                    true)) {

                content.beginText();
                content.setFont(font, fontSize);
                if (color != null) {
                    content.setNonStrokingColor(color);
                }
                content.newLineAtOffset(x, y);
                content.showText(text);
                content.endText();
            }

            System.out.println("成功在第" + (pageIndex + 1) + "页添加居中文本");

        } catch (Exception e) {
            throw new RuntimeException("添加居中文本失败", e);
        }
    }


    /**
     * 在PDF所有页面上根据指定文本位置添加新文本
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param searchText 要查找的文本
     * @param addText    要添加的文本
     * @param xOffset    X轴偏移量
     * @param yOffset    Y轴偏移量
     * @param fontSize   字体大小
     * @param isBold     是否加粗
     * @param color      文本颜色
     */
    public static void addTextBasedOnExistingTextPositionAllPages(String inputPath, String outputPath,
                                                                  String searchText, String addText,
                                                                  float xOffset, float yOffset,
                                                                  float fontSize, boolean isBold, Color color) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            addTextBasedOnExistingTextPositionAllPages(doc, searchText, addText, xOffset, yOffset,
                    fontSize, isBold, color);
            doc.save(outputPath);
            System.out.println("成功在所有页面上添加文本基于: " + searchText);
        } catch (Exception e) {
            throw new RuntimeException("在所有页面上根据文本位置添加文本失败", e);
        }
    }

    /**
     * 在PDF所有页面上根据指定文本位置添加新文本（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param searchText   要查找的文本
     * @param addText      要添加的文本
     * @param xOffset      X轴偏移量
     * @param yOffset      Y轴偏移量
     * @param fontSize     字体大小
     * @param isBold       是否加粗
     * @param color        文本颜色
     */
    public static void addTextBasedOnExistingTextPositionAllPages(InputStream inputStream, OutputStream outputStream,
                                                                  String searchText, String addText,
                                                                  float xOffset, float yOffset,
                                                                  float fontSize, boolean isBold, Color color) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            addTextBasedOnExistingTextPositionAllPages(doc, searchText, addText, xOffset, yOffset,
                    fontSize, isBold, color);
            doc.save(outputStream);
            System.out.println("成功在所有页面上添加文本基于: " + searchText);
        } catch (Exception e) {
            throw new RuntimeException("在所有页面上根据文本位置添加文本失败", e);
        }
    }

    /**
     * 在PDF所有页面上根据指定文本位置添加新文本（使用PDDocument对象）
     *
     * @param doc        PDF文档对象
     * @param searchText 要查找的文本
     * @param addText    要添加的文本
     * @param xOffset    X轴偏移量
     * @param yOffset    Y轴偏移量
     * @param fontSize   字体大小
     * @param isBold     是否加粗
     * @param color      文本颜色
     */
    public static void addTextBasedOnExistingTextPositionAllPages(PDDocument doc, String searchText,
                                                                  String addText, float xOffset, float yOffset,
                                                                  float fontSize, boolean isBold, Color color) {
        try {
            if (doc.getNumberOfPages() == 0) {
                throw new IllegalArgumentException("文档中没有页面，无法添加文本。");
            }

            // 收集所有页面的文本位置
            List<PageTextPosition> allTextPositions = new ArrayList<>();

            // 遍历所有页面查找文本位置
            int totalPages = doc.getNumberOfPages();
            HideTextConfig hideConfig = new HideTextConfig();

            for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
                try {
                    List<TextPositionInfo> textPositions = findTextPositionsOnPage(doc, pageIndex,
                            Lists.newArrayList(searchText), hideConfig);

                    for (TextPositionInfo position : textPositions) {
                        allTextPositions.add(new PageTextPosition(pageIndex, position));
                    }
                } catch (Exception e) {
                    System.err.println("查找第 " + (pageIndex + 1) + " 页文本位置时出错: " + e.getMessage());
                }
            }

            if (allTextPositions.isEmpty()) {
                System.out.println("未找到文本: " + searchText);
                return;
            }

            // 按页面分组文本位置
            java.util.Map<Integer, List<TextPositionInfo>> positionsByPage = new java.util.HashMap<>();
            for (PageTextPosition pageTextPosition : allTextPositions) {
                positionsByPage.computeIfAbsent(pageTextPosition.getPageIndex(), k -> new ArrayList<>())
                        .add(pageTextPosition.getTextPositionInfo());
            }

            // 为每一页添加文本
            for (java.util.Map.Entry<Integer, List<TextPositionInfo>> entry : positionsByPage.entrySet()) {
                int pageIndex = entry.getKey();
                List<TextPositionInfo> positions = entry.getValue();

                // 创建文本配置列表
                List<TextConfig> textConfigs = new ArrayList<>();
                for (TextPositionInfo position : positions) {
                    TextConfig textConfig = new TextConfig()
                            .text(addText)
                            .position(position.getX() + xOffset, position.getY() + yOffset)
                            .fontSize(fontSize)
                            .isBold(isBold)
                            .color(color != null ? color : Color.BLACK);

                    textConfigs.add(textConfig);
                }

                // 获取页面
                PDPage page = doc.getPage(pageIndex);

                // 创建内容流添加文本
                try (PDPageContentStream content = new PDPageContentStream(
                        doc, page,
                        PDPageContentStream.AppendMode.APPEND,
                        true)) {

                    // 加载字体
                    PDFont font = loadChineseFont(doc, null, isBold);

                    // 为每个文本配置添加文本
                    for (TextConfig textConfig : textConfigs) {
                        // 设置字体和大小
                        float configFontSize = textConfig.getFontSize() > 0 ? textConfig.getFontSize() : 12;
                        content.setFont(font, configFontSize);

                        // 设置文本颜色
                        Color configColor = textConfig.getColor();
                        if (configColor != null) {
                            content.setNonStrokingColor(configColor);
                        } else {
                            content.setNonStrokingColor(0, 0, 0); // 默认黑色
                        }

                        // 定位文本并添加
                        content.beginText();
                        content.newLineAtOffset(textConfig.getX(), textConfig.getY());
                        content.showText(textConfig.getText());
                        content.endText();
                    }
                }
            }

            System.out.println("成功在 " + allTextPositions.size() + " 处位置添加文本");

        } catch (Exception e) {
            throw new RuntimeException("在所有页面上根据文本位置添加文本失败", e);
        }
    }

    /**
     * 在PDF所有页面上根据指定文本位置添加新文本（简化版本）
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param searchText 要查找的文本
     * @param addText    要添加的文本
     */
    public static void addTextBasedOnExistingTextPositionAllPages(String inputPath, String outputPath,
                                                                  String searchText, String addText) {
        addTextBasedOnExistingTextPositionAllPages(inputPath, outputPath, searchText, addText,
                15, -20, 16, true, Color.BLACK);
    }

    /**
     * 页面文本位置信息类
     */
    @Getter
    private static class PageTextPosition {
        private final int pageIndex;
        private final TextPositionInfo textPositionInfo;

        public PageTextPosition(int pageIndex, TextPositionInfo textPositionInfo) {
            this.pageIndex = pageIndex;
            this.textPositionInfo = textPositionInfo;
        }
    }


    /**
     * 查找PDF指定页面上指定文本的位置信息（使用文件路径）
     *
     * @param inputPath   PDF文件路径
     * @param pageIndex   页面索引（从0开始）
     * @param textsToFind 要查找的文本列表
     * @param hideConfig  隐藏配置
     * @return 文本位置信息列表
     */
    public static List<TextPositionInfo> findTextPositionsOnPage(String inputPath, int pageIndex, List<String> textsToFind, HideTextConfig hideConfig) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            return findTextPositionsOnPage(doc, pageIndex, textsToFind, hideConfig);
        } catch (Exception e) {
            throw new RuntimeException("查找文本位置信息失败", e);
        }
    }

    /**
     * 查找PDF指定页面上指定文本的位置信息（使用输入流）
     *
     * @param inputStream PDF文件输入流
     * @param pageIndex   页面索引（从0开始）
     * @param textsToFind 要查找的文本列表
     * @param hideConfig  隐藏配置
     * @return 文本位置信息列表
     */
    public static List<TextPositionInfo> findTextPositionsOnPage(InputStream inputStream, int pageIndex, List<String> textsToFind, HideTextConfig hideConfig) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            return findTextPositionsOnPage(doc, pageIndex, textsToFind, hideConfig);
        } catch (Exception e) {
            throw new RuntimeException("查找文本位置信息失败", e);
        }
    }

    /**
     * 在PDF指定位置插入空白页（使用文件路径）
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param pageIndex  插入位置（从0开始）
     */
    public static void insertBlankPage(String inputPath, String outputPath, int pageIndex) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            insertBlankPage(doc, pageIndex);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("插入空白页失败", e);
        }
    }

    /**
     * 在PDF指定位置插入空白页（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param pageIndex    插入位置（从0开始）
     */
    public static void insertBlankPage(InputStream inputStream, OutputStream outputStream, int pageIndex) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            insertBlankPage(doc, pageIndex);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("插入空白页失败", e);
        }
    }

    /**
     * 在PDF指定位置插入空白页（使用PDDocument对象）
     *
     * @param doc       PDF文档对象
     * @param pageIndex 插入位置（从0开始）
     * @throws IOException IO异常
     */
    public static void insertBlankPage(PDDocument doc, int pageIndex) throws IOException {
        int totalPages = doc.getNumberOfPages();

        if (pageIndex < 0 || pageIndex > totalPages) {
            throw new IllegalArgumentException("页面索引超出范围: " + pageIndex + " (有效范围: 0-" + totalPages + ")");
        }

        PDRectangle pageSize;
        if (totalPages > 0) {
            // 使用现有页面的尺寸
            PDPage referencePage = doc.getPage(Math.max(0, Math.min(totalPages - 1, pageIndex)));
            pageSize = referencePage.getMediaBox();
        } else {
            // 如果没有页面，使用默认A4尺寸
            pageSize = PDRectangle.A4;
        }

        // 创建新页面
        PDPage newPage = new PDPage(pageSize);

        // 添加到文档末尾
        doc.addPage(newPage);

        // 如果不是在末尾插入，需要重新排列页面
        if (pageIndex < totalPages) {
            // 获取所有页面
            List<PDPage> pages = new ArrayList<>();
            for (int i = 0; i < doc.getNumberOfPages(); i++) {
                pages.add(doc.getPage(i));
            }

            // 清空文档中的页面
            while (doc.getNumberOfPages() > 0) {
                doc.removePage(0);
            }

            // 按照新顺序重新添加页面
            for (int i = 0; i < pages.size(); i++) {
                if (i == pageIndex) {
                    // 在指定位置添加新页面
                    doc.addPage(newPage);
                    // 添加原页面
                    doc.addPage(pages.get(i));
                } else if (i < pageIndex) {
                    // 原位置之前的页面
                    doc.addPage(pages.get(i));
                } else if (i >= pageIndex) {
                    // 原位置之后的页面（除了已经在新位置的页面）
                    if (i != pageIndex) {
                        doc.addPage(pages.get(i));
                    }
                }
            }
        }
    }

    /**
     * 掩盖PDF中的文本区域
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param covers     要掩盖的区域列表
     */
    public static void coverTextInPDF(String inputPath, String outputPath, List<CoverConfig> covers) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            coverTextInPDF(doc, covers);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("掩盖PDF文本失败", e);
        }
    }

    /**
     * 掩盖PDF中的文本区域（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param covers       要掩盖的区域列表
     */
    public static void coverTextInPDF(InputStream inputStream, OutputStream outputStream, List<CoverConfig> covers) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            coverTextInPDF(doc, covers);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("掩盖PDF文本失败", e);
        }
    }

    /**
     * 掩盖PDF中的文本区域（使用PDDocument对象）
     *
     * @param doc    PDF文档对象
     * @param covers 要掩盖的区域列表
     * @throws IOException IO异常
     */
    public static void coverTextInPDF(PDDocument doc, List<CoverConfig> covers) throws IOException {
        if (doc.getNumberOfPages() == 0) {
            throw new IllegalArgumentException("文档中没有页面，无法掩盖文本。");
        }

        // 获取第一页
        PDPage page = doc.getPage(0);

        // 创建内容流 (APPEND模式在现有内容上方添加)
        try (PDPageContentStream content = new PDPageContentStream(
                doc, page,
                PDPageContentStream.AppendMode.APPEND,
                true)) {

            // 为每个掩盖配置添加掩盖区域
            for (CoverConfig coverConfig : covers) {
                coverArea(doc, content, coverConfig);
            }
        }
    }

    /**
     * 在指定区域添加掩盖
     *
     * @param doc
     * @param content     内容流
     * @param coverConfig 掩盖配置
     * @throws IOException IO异常
     */
    private static void coverArea(PDDocument doc, PDPageContentStream content, CoverConfig coverConfig) throws IOException {
        // 设置掩盖颜色
        Color coverColor = coverConfig.getCoverColor() != null ? coverConfig.getCoverColor() : Color.WHITE;
        content.setNonStrokingColor(coverColor);

        // 绘制矩形掩盖区域
        content.addRect(coverConfig.getX(), coverConfig.getY(), coverConfig.getWidth(), coverConfig.getHeight());
        content.fill();

        // 如果需要添加替代文本
        if (coverConfig.getReplacementText() != null && !coverConfig.getReplacementText().isEmpty()) {
            // 重置颜色为黑色或其他指定颜色
            Color textColor = coverConfig.getTextColor() != null ? coverConfig.getTextColor() : Color.BLACK;
            content.setNonStrokingColor(textColor);

            // 加载字体
            PDFont font = loadChineseFont(doc, null, false);

            float fontSize = coverConfig.getFontSize() > 0 ? coverConfig.getFontSize() : 12;

            content.beginText();
            content.setFont(font, fontSize);
            content.newLineAtOffset(coverConfig.getX(), coverConfig.getY() + (coverConfig.getHeight() - fontSize) / 2);
            content.showText(coverConfig.getReplacementText());
            content.endText();
        }
    }

    /**
     * 向PDF文档添加文本（使用文件路径）
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param texts      要添加的文本列表
     * @param config     文本配置信息
     */
    public static void addTextToPDF(String inputPath, String outputPath, List<TextConfig> texts, TextConfig config) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            addTextToPDF(doc, texts, config);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("向PDF添加文本失败", e);
        }
    }

    /**
     * 向PDF文档添加文本（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param texts        要添加的文本列表
     * @param config       文本配置信息
     */
    public static void addTextToPDF(InputStream inputStream, OutputStream outputStream, List<TextConfig> texts, TextConfig config) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            addTextToPDF(doc, texts, config);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("向PDF添加文本失败", e);
        }
    }

    /**
     * 向PDF文档添加文本（使用PDDocument对象）
     *
     * @param doc    PDF文档对象
     * @param texts  要添加的文本列表
     * @param config 文本配置信息
     * @throws IOException IO异常
     */
    public static void addTextToPDF(PDDocument doc, List<TextConfig> texts, TextConfig config) throws IOException {
        if (doc.getNumberOfPages() == 0) {
            throw new IllegalArgumentException("文档中没有页面，无法添加文本。");
        }

        // 获取第一页
        PDPage page = doc.getPage(0);

        // 创建内容流 (APPEND模式在现有内容上方添加)
        try (PDPageContentStream content = new PDPageContentStream(
                doc, page,
                PDPageContentStream.AppendMode.APPEND,
                true)) {

            // 加载字体
            PDFont font = loadChineseFont(doc, config.getFontPath(), config.isBold());

            // 为每个文本配置添加文本
            for (TextConfig textConfig : texts) {
                addTextToContent(content, font, textConfig);
            }
        }
    }

    /**
     * 向内容流添加单个文本
     *
     * @param content     内容流
     * @param defaultFont 默认字体
     * @param textConfig  文本配置
     * @throws IOException IO异常
     */
    private static void addTextToContent(PDPageContentStream content, PDFont defaultFont, TextConfig textConfig) throws IOException {
        // 设置字体和大小
        PDFont font = textConfig.getFont() != null ? textConfig.getFont() : defaultFont;
        float fontSize = textConfig.getFontSize() > 0 ? textConfig.getFontSize() : 12;
        content.setFont(font, fontSize);

        // 设置文本颜色
        Color color = textConfig.getColor();
        if (color != null) {
            content.setNonStrokingColor(color);
        } else {
            content.setNonStrokingColor(0, 0, 0); // 默认黑色
        }

        // 定位文本并添加
        content.beginText();
        content.newLineAtOffset(textConfig.getX(), textConfig.getY());
        content.showText(textConfig.getText());
        content.endText();
    }

    /**
     * 加载中文字体
     *
     * @param doc      PDF文档
     * @param fontPath 字体路径（可选）
     * @param isBold   是否加粗
     * @return 加载的字体
     */
    private static PDFont loadChineseFont(PDDocument doc, String fontPath, boolean isBold) {
        try {
            // 首先尝试使用指定的字体路径
            if (fontPath != null && !fontPath.isEmpty()) {
                File fontFile = new File(fontPath);
                if (fontFile.exists()) {
                    return PDType0Font.load(doc, fontFile);
                }
            }

            // 尝试从资源加载字体
            InputStream fontStream = PDFUtil.class.getResourceAsStream(isBold ? "/fonts/Alibaba-PuHuiTi-Bold.ttf" : "/fonts/SourceHanSansSC-VF.ttf");
            if (fontStream != null) {
                try {
                    return PDType0Font.load(doc, fontStream);
                } finally {
                    fontStream.close();
                }
            }

            // 尝试从系统加载字体
            File systemFontFile = findAvailableChineseFont();
            if (systemFontFile != null) {
                return PDType0Font.load(doc, systemFontFile);
            }

            // 如果都失败了，使用默认英文字体
            System.err.println("无法加载中文字体，使用默认英文字体");
            return PDType1Font.HELVETICA;
        } catch (Exception e) {
            System.err.println("加载字体失败: " + e.getMessage());
            return PDType1Font.HELVETICA;
        }
    }

    /**
     * 查找可用的中文字体
     *
     * @return 字体文件
     */
    private static File findAvailableChineseFont() {
        String os = System.getProperty("os.name").toLowerCase();
        java.util.List<String> fontPaths = new java.util.ArrayList<>();

        // 根据操作系统添加可能的字体路径
        if (os.contains("mac")) {
            fontPaths.addAll(java.util.Arrays.asList(
                    "/System/Library/Fonts/PingFang.ttc",
                    "/System/Library/Fonts/Helvetica.ttc",
                    "/System/Library/Fonts/Arial Unicode.ttf"
            ));
        } else if (os.contains("windows")) {
            fontPaths.addAll(java.util.Arrays.asList(
                    "C:/Windows/Fonts/simsun.ttc",
                    "C:/Windows/Fonts/msyh.ttc",
                    "C:/Windows/Fonts/simhei.ttf"
            ));
        } else {
            fontPaths.addAll(java.util.Arrays.asList(
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
                    "/usr/share/fonts/wenquanyi/wqy-microhei/wqy-microhei.ttc"
            ));
        }

        // 添加一些通用字体路径
        fontPaths.addAll(java.util.Arrays.asList(
                "./fonts/simsun.ttc",
                "./fonts/msyh.ttc"
        ));

        for (String path : fontPaths) {
            File fontFile = new File(path);
            if (fontFile.exists()) {
                System.out.println("使用字体文件: " + path);
                return fontFile;
            }
        }

        System.err.println("未找到任何可用的中文字体文件");
        return null;
    }


    /**
     * 从PDF文件中提取文本内容（使用文件路径）
     *
     * @param inputPath PDF文件路径
     * @return 提取的文本内容
     */
    public static String extractTextFromPDF(String inputPath) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            return extractTextFromPDF(doc);
        } catch (Exception e) {
            throw new RuntimeException("从PDF提取文本失败", e);
        }
    }

    /**
     * 从PDF文件中提取文本内容（使用输入流）
     *
     * @param inputStream PDF文件输入流
     * @return 提取的文本内容
     */
    public static String extractTextFromPDF(InputStream inputStream) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            return extractTextFromPDF(doc);
        } catch (Exception e) {
            throw new RuntimeException("从PDF提取文本失败", e);
        }
    }

    /**
     * 从PDF文档对象中提取文本内容
     *
     * @param doc PDF文档对象
     * @return 提取的文本内容
     */
    public static String extractTextFromPDF(PDDocument doc) {
        try {
            // 创建安全的文本提取器
            SafePDFTextStripper stripper = new SafePDFTextStripper();

            // 设置提取参数
            stripper.setSortByPosition(true);
            stripper.setStartPage(1);
            stripper.setEndPage(doc.getNumberOfPages());

            return stripper.getText(doc);
        } catch (Exception e) {
            System.err.println("提取PDF文本时出错: " + e.getMessage());
            // 使用备用方法
            return extractTextWithFallback(doc);
        }
    }

    /**
     * 安全的PDF文本提取器，处理字体兼容性问题
     */
    private static class SafePDFTextStripper extends PDFTextStripper {
        public SafePDFTextStripper() throws IOException {
        }

        @Override
        public void processPage(PDPage page) throws IOException {
            try {
                super.processPage(page);
            } catch (UnsupportedOperationException e) {
                // 处理TTF字体CFF表问题
                if (e.getMessage() != null && e.getMessage().contains("TTF fonts do not have a CFF table")) {
                    System.err.println("遇到TTF字体兼容性问题，跳过当前页面的字体处理");
                    // 可以在这里添加备用处理逻辑
                } else {
                    throw e; // 重新抛出其他不相关的异常
                }
            } catch (Exception e) {
                System.err.println("处理页面时出错: " + e.getMessage());
                // 继续处理其他页面
            }
        }
    }

    /**
     * 备用文本提取方法
     *
     * @param doc PDF文档对象
     * @return 提取的文本内容
     */
    private static String extractTextWithFallback(PDDocument doc) {
        try {
            // 使用最基本的文本提取方法
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            stripper.setStartPage(1);
            stripper.setEndPage(Math.min(3, doc.getNumberOfPages())); // 限制处理页数

            return stripper.getText(doc);
        } catch (Exception e) {
            System.err.println("备用文本提取方法也失败: " + e.getMessage());
            return "无法提取PDF文本内容";
        }
    }

    /**
     * 从PDF指定页面范围提取文本
     *
     * @param inputPath PDF文件路径
     * @param startPage 起始页码（从1开始）
     * @param endPage   结束页码
     * @return 提取的文本内容
     */
    public static String extractTextFromPDF(String inputPath, int startPage, int endPage) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            return extractTextFromPDF(doc, startPage, endPage);
        } catch (Exception e) {
            throw new RuntimeException("从PDF提取文本失败", e);
        }
    }

    /**
     * 从PDF指定页面范围提取文本
     *
     * @param doc       PDF文档对象
     * @param startPage 起始页码（从1开始）
     * @param endPage   结束页码
     * @return 提取的文本内容
     */
    public static String extractTextFromPDF(PDDocument doc, int startPage, int endPage) {
        try {
            SafePDFTextStripper stripper = new SafePDFTextStripper();
            stripper.setSortByPosition(true);
            stripper.setStartPage(Math.max(1, startPage));
            stripper.setEndPage(Math.min(doc.getNumberOfPages(), endPage));

            return stripper.getText(doc);
        } catch (Exception e) {
            System.err.println("提取PDF文本时出错: " + e.getMessage());
            return "无法提取PDF文本内容";
        }
    }

    /**
     * 查找PDF中包含特定文本的页面
     *
     * @param inputPath  PDF文件路径
     * @param searchText 要查找的文本
     * @return 包含该文本的页面列表
     */
    public static List<Integer> findPagesContainingText(String inputPath, String searchText) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            return findPagesContainingText(doc, searchText);
        } catch (Exception e) {
            throw new RuntimeException("查找PDF文本失败", e);
        }
    }

    /**
     * 查找PDF中包含特定文本的页面
     *
     * @param doc        PDF文档对象
     * @param searchText 要查找的文本
     * @return 包含该文本的页面列表
     */
    public static List<Integer> findPagesContainingText(PDDocument doc, String searchText) {
        try {
            SafePDFTextStripper stripper = new SafePDFTextStripper();
            stripper.setSortByPosition(true);

            TextFinder finder = new TextFinder(searchText);
            stripper.setStartPage(1);
            stripper.setEndPage(doc.getNumberOfPages());

            finder.getText(doc);
            return finder.getPagesContainingText();
        } catch (Exception e) {
            System.err.println("查找PDF文本时出错: " + e.getMessage());
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 调整PDF页面顺序（使用文件路径）
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param pageOrder  新的页面顺序数组，索引从0开始
     *                   例如: [2, 0, 1] 表示原第3页变为第1页，原第1页变为第2页，原第2页变为第3页
     */
    public static void reorderPages(String inputPath, String outputPath, int[] pageOrder) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            reorderPages(doc, pageOrder);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("调整PDF页面顺序失败", e);
        }
    }

    /**
     * 调整PDF页面顺序（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param pageOrder    新的页面顺序数组，索引从0开始
     */
    public static void reorderPages(InputStream inputStream, OutputStream outputStream, int[] pageOrder) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            reorderPages(doc, pageOrder);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("调整PDF页面顺序失败", e);
        }
    }

    /**
     * 调整PDF页面顺序（使用PDDocument对象）
     *
     * @param doc       PDF文档对象
     * @param pageOrder 新的页面顺序数组，索引从0开始
     * @throws IOException IO异常
     */
    public static void reorderPages(PDDocument doc, int[] pageOrder) throws IOException {
        if (doc.getNumberOfPages() == 0) {
            throw new IllegalArgumentException("文档中没有页面，无法调整顺序。");
        }

        if (pageOrder == null || pageOrder.length == 0) {
            throw new IllegalArgumentException("页面顺序数组不能为空。");
        }

        // 验证页面顺序数组的有效性
        validatePageOrder(pageOrder, doc.getNumberOfPages());

        // 获取所有页面
        List<PDPage> pages = new java.util.ArrayList<>();
        for (int i = 0; i < doc.getNumberOfPages(); i++) {
            pages.add(doc.getPage(i));
        }

        // 清空文档中的页面
        while (doc.getNumberOfPages() > 0) {
            doc.removePage(0);
        }

        // 按照新顺序重新添加页面
        for (int pageIndex : pageOrder) {
            doc.addPage(pages.get(pageIndex));
        }
    }

    /**
     * 验证页面顺序数组的有效性
     *
     * @param pageOrder  页面顺序数组
     * @param totalPages 总页面数
     */
    private static void validatePageOrder(int[] pageOrder, int totalPages) {
        if (pageOrder.length > totalPages) {
            throw new IllegalArgumentException("页面顺序数组长度不能超过总页面数。");
        }

        // 检查是否有重复的页面索引
        boolean[] used = new boolean[totalPages];
        for (int pageIndex : pageOrder) {
            if (pageIndex < 0 || pageIndex >= totalPages) {
                throw new IllegalArgumentException("页面索引 " + pageIndex + " 超出范围 [0, " + (totalPages - 1) + "]");
            }
            if (used[pageIndex]) {
                throw new IllegalArgumentException("页面索引 " + pageIndex + " 重复出现。");
            }
            used[pageIndex] = true;
        }
    }

    /**
     * 反转PDF页面顺序（使用文件路径）
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     */
    public static void reversePages(String inputPath, String outputPath) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            reversePages(doc);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("反转PDF页面顺序失败", e);
        }
    }

    /**
     * 反转PDF页面顺序（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     */
    public static void reversePages(InputStream inputStream, OutputStream outputStream) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            reversePages(doc);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("反转PDF页面顺序失败", e);
        }
    }

    /**
     * 反转PDF页面顺序（使用PDDocument对象）
     *
     * @param doc PDF文档对象
     * @throws IOException IO异常
     */
    public static void reversePages(PDDocument doc) throws IOException {
        if (doc.getNumberOfPages() <= 1) {
            return; // 只有0页或1页时无需反转
        }

        int totalPages = doc.getNumberOfPages();
        int[] reverseOrder = new int[totalPages];
        for (int i = 0; i < totalPages; i++) {
            reverseOrder[i] = totalPages - 1 - i;
        }
        reorderPages(doc, reverseOrder);
    }

    /**
     * 交换PDF中两个页面的位置（使用文件路径）
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param pageIndex1 第一个页面索引（从0开始）
     * @param pageIndex2 第二个页面索引（从0开始）
     */
    public static void swapPages(String inputPath, String outputPath, int pageIndex1, int pageIndex2) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            swapPages(doc, pageIndex1, pageIndex2);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("交换PDF页面失败", e);
        }
    }

    /**
     * 交换PDF中两个页面的位置（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param pageIndex1   第一个页面索引（从0开始）
     * @param pageIndex2   第二个页面索引（从0开始）
     */
    public static void swapPages(InputStream inputStream, OutputStream outputStream, int pageIndex1, int pageIndex2) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            swapPages(doc, pageIndex1, pageIndex2);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("交换PDF页面失败", e);
        }
    }

    /**
     * 交换PDF中两个页面的位置（使用PDDocument对象）
     *
     * @param doc        PDF文档对象
     * @param pageIndex1 第一个页面索引（从0开始）
     * @param pageIndex2 第二个页面索引（从0开始）
     * @throws IOException IO异常
     */
    public static void swapPages(PDDocument doc, int pageIndex1, int pageIndex2) throws IOException {
        int totalPages = doc.getNumberOfPages();

        if (totalPages <= 1) {
            return; // 没有足够页面进行交换
        }

        if (pageIndex1 < 0 || pageIndex1 >= totalPages || pageIndex2 < 0 || pageIndex2 >= totalPages) {
            throw new IllegalArgumentException("页面索引超出范围");
        }

        if (pageIndex1 == pageIndex2) {
            return; // 同一个页面无需交换
        }

        // 创建页面顺序数组
        int[] pageOrder = new int[totalPages];
        for (int i = 0; i < totalPages; i++) {
            pageOrder[i] = i;
        }

        // 交换两个页面的位置
        pageOrder[pageIndex1] = pageIndex2;
        pageOrder[pageIndex2] = pageIndex1;

        reorderPages(doc, pageOrder);
    }

    /**
     * 移动页面到指定位置（使用文件路径）
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param fromIndex  要移动的页面索引（从0开始）
     * @param toIndex    目标位置索引（从0开始）
     */
    public static void movePage(String inputPath, String outputPath, int fromIndex, int toIndex) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            movePage(doc, fromIndex, toIndex);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("移动PDF页面失败", e);
        }
    }

    /**
     * 移动页面到指定位置（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param fromIndex    要移动的页面索引（从0开始）
     * @param toIndex      目标位置索引（从0开始）
     */
    public static void movePage(InputStream inputStream, OutputStream outputStream, int fromIndex, int toIndex) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            movePage(doc, fromIndex, toIndex);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("移动PDF页面失败", e);
        }
    }

    /**
     * 移动页面到指定位置（使用PDDocument对象）
     *
     * @param doc       PDF文档对象
     * @param fromIndex 要移动的页面索引（从0开始）
     * @param toIndex   目标位置索引（从0开始）
     * @throws IOException IO异常
     */
    public static void movePage(PDDocument doc, int fromIndex, int toIndex) throws IOException {
        int totalPages = doc.getNumberOfPages();

        if (totalPages <= 1) {
            return; // 没有足够页面进行移动
        }

        if (fromIndex < 0 || fromIndex >= totalPages || toIndex < 0 || toIndex >= totalPages) {
            throw new IllegalArgumentException("页面索引超出范围");
        }

        if (fromIndex == toIndex) {
            return; // 相同位置无需移动
        }

        // 创建页面顺序数组
        int[] pageOrder = new int[totalPages];
        for (int i = 0; i < totalPages; i++) {
            pageOrder[i] = i;
        }

        // 移动页面
        if (fromIndex < toIndex) {
            // 向后移动
            int temp = pageOrder[fromIndex];
            for (int i = fromIndex; i < toIndex; i++) {
                pageOrder[i] = pageOrder[i + 1];
            }
            pageOrder[toIndex] = temp;
        } else {
            // 向前移动
            int temp = pageOrder[fromIndex];
            for (int i = fromIndex; i > toIndex; i--) {
                pageOrder[i] = pageOrder[i - 1];
            }
            pageOrder[toIndex] = temp;
        }

        reorderPages(doc, pageOrder);
    }

    /**
     * 提取PDF每一页的文本内容，生成列表（使用文件路径）
     *
     * @param inputPath PDF文件路径
     * @return 每一页文本内容的列表，索引对应页码（从0开始）
     */
    public static List<String> extractTextFromEachPage(String inputPath) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            return extractTextFromEachPage(doc);
        } catch (Exception e) {
            throw new RuntimeException("从PDF提取每页文本失败", e);
        }
    }

    /**
     * 提取PDF每一页的文本内容，生成列表（使用输入流）
     *
     * @param inputStream PDF文件输入流
     * @return 每一页文本内容的列表，索引对应页码（从0开始）
     */
    public static List<String> extractTextFromEachPage(InputStream inputStream) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            return extractTextFromEachPage(doc);
        } catch (Exception e) {
            throw new RuntimeException("从PDF提取每页文本失败", e);
        }
    }

    /**
     * 提取PDF每一页的文本内容，生成列表（使用PDDocument对象）
     *
     * @param doc PDF文档对象
     * @return 每一页文本内容的列表，索引对应页码（从0开始）
     */
    public static List<String> extractTextFromEachPage(PDDocument doc) {
        List<String> pageTexts = new java.util.ArrayList<>();

        try {
            int totalPages = doc.getNumberOfPages();

            for (int i = 0; i < totalPages; i++) {
                try {
                    // 提取单个页面的文本
                    String pageText = extractTextFromPage(doc, i + 1);
                    pageTexts.add(pageText);
                } catch (Exception e) {
                    System.err.println("提取第 " + (i + 1) + " 页文本时出错: " + e.getMessage());
                    pageTexts.add("无法提取第 " + (i + 1) + " 页内容");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("提取PDF每页文本失败", e);
        }

        return pageTexts;
    }

    /**
     * 提取PDF指定页面的文本内容
     *
     * @param doc     PDF文档对象
     * @param pageNum 页码（从1开始）
     * @return 指定页面的文本内容
     */
    public static String extractTextFromPage(PDDocument doc, int pageNum) {
        try {
            if (pageNum < 1 || pageNum > doc.getNumberOfPages()) {
                throw new IllegalArgumentException("页码超出范围: " + pageNum);
            }

            SafePDFTextStripper stripper = new SafePDFTextStripper();
            stripper.setSortByPosition(true);
            stripper.setStartPage(pageNum);
            stripper.setEndPage(pageNum);

            return stripper.getText(doc);
        } catch (Exception e) {
            System.err.println("提取第 " + pageNum + " 页文本时出错: " + e.getMessage());
            return "无法提取第 " + pageNum + " 页内容";
        }
    }

    /**
     * 提取PDF指定页面范围的文本内容，生成列表
     *
     * @param inputPath PDF文件路径
     * @param startPage 起始页码（从1开始）
     * @param endPage   结束页码（从1开始）
     * @return 指定页面范围文本内容的列表，索引对应相对页码（从0开始）
     */
    public static List<String> extractTextFromPageRange(String inputPath, int startPage, int endPage) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            return extractTextFromPageRange(doc, startPage, endPage);
        } catch (Exception e) {
            throw new RuntimeException("从PDF提取页面范围文本失败", e);
        }
    }

    /**
     * 提取PDF指定页面范围的文本内容，生成列表（使用输入流）
     *
     * @param inputStream PDF文件输入流
     * @param startPage   起始页码（从1开始）
     * @param endPage     结束页码（从1开始）
     * @return 指定页面范围文本内容的列表，索引对应相对页码（从0开始）
     */
    public static List<String> extractTextFromPageRange(InputStream inputStream, int startPage, int endPage) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            return extractTextFromPageRange(doc, startPage, endPage);
        } catch (Exception e) {
            throw new RuntimeException("从PDF提取页面范围文本失败", e);
        }
    }

    /**
     * 提取PDF指定页面范围的文本内容，生成列表（使用PDDocument对象）
     *
     * @param doc       PDF文档对象
     * @param startPage 起始页码（从1开始）
     * @param endPage   结束页码（从1开始）
     * @return 指定页面范围文本内容的列表，索引对应相对页码（从0开始）
     */
    public static List<String> extractTextFromPageRange(PDDocument doc, int startPage, int endPage) {
        List<String> pageTexts = new java.util.ArrayList<>();

        try {
            int totalPages = doc.getNumberOfPages();
            int actualStartPage = Math.max(1, startPage);
            int actualEndPage = Math.min(totalPages, endPage);

            if (actualStartPage > actualEndPage) {
                throw new IllegalArgumentException("起始页码不能大于结束页码");
            }

            for (int i = actualStartPage; i <= actualEndPage; i++) {
                try {
                    String pageText = extractTextFromPage(doc, i);
                    pageTexts.add(pageText);
                } catch (Exception e) {
                    System.err.println("提取第 " + i + " 页文本时出错: " + e.getMessage());
                    pageTexts.add("无法提取第 " + i + " 页内容");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("提取PDF页面范围文本失败", e);
        }

        return pageTexts;
    }

    /**
     * 获取PDF文档信息，包括总页数和每页文本内容
     *
     * @param inputPath PDF文件路径
     * @return PDF文档信息对象
     */
    public static PDFDocumentInfo getPDFDocumentInfo(String inputPath) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            return getPDFDocumentInfo(doc);
        } catch (Exception e) {
            throw new RuntimeException("获取PDF文档信息失败", e);
        }
    }

    /**
     * 获取PDF文档信息，包括总页数和每页文本内容（使用输入流）
     *
     * @param inputStream PDF文件输入流
     * @return PDF文档信息对象
     */
    public static PDFDocumentInfo getPDFDocumentInfo(InputStream inputStream) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            return getPDFDocumentInfo(doc);
        } catch (Exception e) {
            throw new RuntimeException("获取PDF文档信息失败", e);
        }
    }

    /**
     * 获取PDF文档信息，包括总页数和每页文本内容（使用PDDocument对象）
     *
     * @param doc PDF文档对象
     * @return PDF文档信息对象
     */
    public static PDFDocumentInfo getPDFDocumentInfo(PDDocument doc) {
        try {
            PDFDocumentInfo info = new PDFDocumentInfo();
            info.setTotalPages(doc.getNumberOfPages());
            info.setPageTexts(extractTextFromEachPage(doc));
            return info;
        } catch (Exception e) {
            throw new RuntimeException("获取PDF文档信息失败", e);
        }
    }

    /**
     * 根据页面内容中的特定行数据对页面进行重新排序（使用文件路径）
     *
     * @param inputPath   输入PDF文件路径
     * @param outputPath  输出PDF文件路径
     * @param lineMatcher 行匹配器，用于确定页面排序依据
     */
    public static void reorderPagesByContent(String inputPath, String outputPath, PageLineMatcher lineMatcher) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            reorderPagesByContent(doc, lineMatcher);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("根据内容重新排序PDF页面失败", e);
        }
    }

    /**
     * 根据页面内容中的特定行数据对页面进行重新排序（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param lineMatcher  行匹配器，用于确定页面排序依据
     */
    public static void reorderPagesByContent(InputStream inputStream, OutputStream outputStream, PageLineMatcher lineMatcher) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            reorderPagesByContent(doc, lineMatcher);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("根据内容重新排序PDF页面失败", e);
        }
    }

    /**
     * 根据页面内容中的特定行数据对页面进行重新排序（使用PDDocument对象）
     *
     * @param doc         PDF文档对象
     * @param lineMatcher 行匹配器，用于确定页面排序依据
     * @throws IOException IO异常
     */
    public static void reorderPagesByContent(PDDocument doc, PageLineMatcher lineMatcher) throws IOException {
        if (doc.getNumberOfPages() == 0) {
            throw new IllegalArgumentException("文档中没有页面，无法重新排序。");
        }

        if (lineMatcher == null) {
            throw new IllegalArgumentException("行匹配器不能为空。");
        }

        // 获取页面排序信息
        List<PageOrderInfo> pageOrderInfos = getPageOrderInfos(doc, lineMatcher);

        // 根据排序信息创建新的页面顺序
        int[] newPageOrder = createPageOrder(pageOrderInfos, lineMatcher.getSortOrder());

        // 应用新的页面顺序
        reorderPages(doc, newPageOrder);
    }

    /**
     * 获取页面排序信息
     *
     * @param doc         PDF文档对象
     * @param lineMatcher 行匹配器
     * @return 页面排序信息列表
     */
    private static List<PageOrderInfo> getPageOrderInfos(PDDocument doc, PageLineMatcher lineMatcher) {
        List<PageOrderInfo> pageOrderInfos = new ArrayList<>();

        try {
            int totalPages = doc.getNumberOfPages();
            for (int i = 0; i < totalPages; i++) {
                try {
                    String pageText = extractTextFromPage(doc, i + 1);
                    String matchLine = findMatchingLine(pageText, lineMatcher);

                    if (matchLine != null) {
                        Comparable keyValue = lineMatcher.extractKeyValue(matchLine);
                        pageOrderInfos.add(new PageOrderInfo(i, keyValue, matchLine));
                    } else {
                        // 如果没有匹配的行，使用默认值
                        pageOrderInfos.add(new PageOrderInfo(i, getDefaultKeyValue(lineMatcher), null));
                    }
                } catch (Exception e) {
                    System.err.println("处理第 " + (i + 1) + " 页时出错: " + e.getMessage());
                    pageOrderInfos.add(new PageOrderInfo(i, getDefaultKeyValue(lineMatcher), null));
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("获取页面排序信息失败", e);
        }

        return pageOrderInfos;
    }

    /**
     * 查找匹配的行
     *
     * @param pageText    页面文本
     * @param lineMatcher 行匹配器
     * @return 匹配的行文本，如果没有匹配则返回null
     */
    private static String findMatchingLine(String pageText, PageLineMatcher lineMatcher) {
        if (pageText == null || pageText.isEmpty()) {
            return null;
        }

        String[] lines = pageText.split("\\r?\\n");
        for (String line : lines) {
            if (lineMatcher.matches(line)) {
                return line;
            }
        }
        return null;
    }

    /**
     * 获取默认的排序键值
     *
     * @param lineMatcher 行匹配器
     * @return 默认键值
     */
    private static Comparable getDefaultKeyValue(PageLineMatcher lineMatcher) {
        // 根据排序类型返回默认值
        if (lineMatcher instanceof NumericPageLineMatcher) {
            return Double.MAX_VALUE; // 数字排序时放在最后
        } else if (lineMatcher instanceof DatePageLineMatcher) {
            return new Date(Long.MAX_VALUE); // 日期排序时放在最后
        } else {
            return "ZZZZZZZZZZ"; // 字符串排序时放在最后
        }
    }

    /**
     * 创建页面顺序数组
     *
     * @param pageOrderInfos 页面排序信息列表
     * @param sortOrder      排序顺序
     * @return 页面顺序数组
     */
    private static int[] createPageOrder(List<PageOrderInfo> pageOrderInfos, SortOrder sortOrder) {
        // 根据键值排序
        pageOrderInfos.sort((info1, info2) -> {
            int result = compareKeys(info1.getKeyValue(), info2.getKeyValue());
            // 如果键值相同，保持原始顺序
            return result != 0 ? result : Integer.compare(info1.getOriginalIndex(), info2.getOriginalIndex());
        });

        // 如果是降序，反转列表
        if (sortOrder == SortOrder.DESC) {
            Collections.reverse(pageOrderInfos);
        }

        // 创建页面顺序数组
        int[] pageOrder = new int[pageOrderInfos.size()];
        for (int i = 0; i < pageOrderInfos.size(); i++) {
            pageOrder[i] = pageOrderInfos.get(i).getOriginalIndex();
        }

        return pageOrder;
    }

    /**
     * 比较键值
     *
     * @param key1 键值1
     * @param key2 键值2
     * @return 比较结果
     */
    @SuppressWarnings("unchecked")
    private static int compareKeys(Comparable key1, Comparable key2) {
        if (key1 == null && key2 == null) {
            return 0;
        }
        if (key1 == null) {
            return 1;
        }
        if (key2 == null) {
            return -1;
        }
        return key1.compareTo(key2);
    }

    /**
     * 页面排序信息类
     */
    @Getter
    private static class PageOrderInfo {
        /**
         * 原始页面索引
         */
        private final int originalIndex;

        /**
         * 用于排序的键值
         */
        private final Comparable keyValue;

        /**
         * 匹配的行文本
         */
        private final String matchingLine;

        public PageOrderInfo(int originalIndex, Comparable keyValue, String matchingLine) {
            this.originalIndex = originalIndex;
            this.keyValue = keyValue;
            this.matchingLine = matchingLine;
        }
    }

    /**
     * 排序顺序枚举
     */
    public enum SortOrder {
        /**
         * 升序
         */
        ASC,

        /**
         * 降序
         */
        DESC
    }

    /**
     * 页面行匹配器接口
     */
    public interface PageLineMatcher {
        /**
         * 检查行是否匹配
         *
         * @param line 行文本
         * @return 是否匹配
         */
        boolean matches(String line);

        /**
         * 从匹配的行中提取用于排序的键值
         *
         * @param line 匹配的行文本
         * @return 用于排序的键值
         */
        default Comparable extractKeyValue(String line) {
            return line;
        }

        /**
         * 获取排序顺序
         *
         * @return 排序顺序
         */
        default SortOrder getSortOrder() {
            return PDFUtil.SortOrder.ASC;
        }
    }

    /**
     * 基于正则表达式的页面行匹配器
     */
    public static class RegexPageLineMatcher implements PageLineMatcher {
        private final Pattern pattern;
        private final int groupIndex;
        private final SortOrder sortOrder;

        public RegexPageLineMatcher(String regex, int groupIndex, SortOrder sortOrder) {
            this.pattern = Pattern.compile(regex);
            this.groupIndex = groupIndex;
            this.sortOrder = sortOrder;
        }

        @Override
        public boolean matches(String line) {
            return line != null && pattern.matcher(line).find();
        }

        @Override
        public Comparable extractKeyValue(String line) {
            if (line == null) {
                return null;
            }
            java.util.regex.Matcher matcher = pattern.matcher(line);
            if (matcher.find() && groupIndex <= matcher.groupCount()) {
                return matcher.group(groupIndex);
            }
            return null;
        }

        @Override
        public SortOrder getSortOrder() {
            return sortOrder;
        }
    }

    /**
     * 基于包含特定文本的页面行匹配器
     */
    public static class ContainsPageLineMatcher implements PageLineMatcher {
        private final String searchText;
        private final SortOrder sortOrder;

        public ContainsPageLineMatcher(String searchText, SortOrder sortOrder) {
            this.searchText = searchText;
            this.sortOrder = sortOrder;
        }

        @Override
        public boolean matches(String line) {
            return line != null && line.contains(searchText);
        }

        @Override
        public Comparable extractKeyValue(String line) {
            return line; // 使用整行作为排序键值
        }

        @Override
        public SortOrder getSortOrder() {
            return sortOrder;
        }
    }

    /**
     * 基于数字提取的页面行匹配器
     */
    public static class NumericPageLineMatcher implements PageLineMatcher {
        private final Pattern pattern;
        private final int groupIndex;
        private final SortOrder sortOrder;

        public NumericPageLineMatcher(String regex, int groupIndex, SortOrder sortOrder) {
            this.pattern = Pattern.compile(regex);
            this.groupIndex = groupIndex;
            this.sortOrder = sortOrder;
        }

        @Override
        public boolean matches(String line) {
            return line != null && pattern.matcher(line).find();
        }

        @Override
        public Comparable extractKeyValue(String line) {
            if (line == null) {
                return null;
            }
            java.util.regex.Matcher matcher = pattern.matcher(line);
            if (matcher.find() && groupIndex <= matcher.groupCount()) {
                try {
                    return Double.parseDouble(matcher.group(groupIndex));
                } catch (NumberFormatException e) {
                    return Double.MAX_VALUE; // 无法解析为数字时放在最后
                }
            }
            return Double.MAX_VALUE;
        }

        @Override
        public SortOrder getSortOrder() {
            return sortOrder;
        }
    }

    /**
     * 基于日期提取的页面行匹配器
     */
    public static class DatePageLineMatcher implements PageLineMatcher {
        private final Pattern pattern;
        private final int groupIndex;
        private final String dateFormat;
        private final SortOrder sortOrder;
        private final java.text.SimpleDateFormat sdf;

        public DatePageLineMatcher(String regex, int groupIndex, String dateFormat, SortOrder sortOrder) {
            this.pattern = Pattern.compile(regex);
            this.groupIndex = groupIndex;
            this.dateFormat = dateFormat;
            this.sortOrder = sortOrder;
            this.sdf = new java.text.SimpleDateFormat(dateFormat);
        }

        @Override
        public boolean matches(String line) {
            return line != null && pattern.matcher(line).find();
        }

        @Override
        public Comparable extractKeyValue(String line) {
            if (line == null) {
                return null;
            }
            java.util.regex.Matcher matcher = pattern.matcher(line);
            if (matcher.find() && groupIndex <= matcher.groupCount()) {
                try {
                    return sdf.parse(matcher.group(groupIndex));
                } catch (java.text.ParseException e) {
                    return new Date(Long.MAX_VALUE); // 无法解析为日期时放在最后
                }
            }
            return new Date(Long.MAX_VALUE);
        }

        @Override
        public SortOrder getSortOrder() {
            return sortOrder;
        }
    }

    /**
     * PDF文档信息类
     */
    @Getter
    @Setter
    public static class PDFDocumentInfo {
        /**
         * 总页数
         */
        private int totalPages;

        /**
         * 每页文本内容列表
         */
        private List<String> pageTexts = new java.util.ArrayList<>();

        /**
         * 获取指定页面的文本内容
         *
         * @param pageIndex 页面索引（从0开始）
         * @return 页面文本内容
         */
        public String getPageText(int pageIndex) {
            if (pageIndex < 0 || pageIndex >= pageTexts.size()) {
                return null;
            }
            return pageTexts.get(pageIndex);
        }

        /**
         * 获取指定页面的文本内容（按页码）
         *
         * @param pageNum 页码（从1开始）
         * @return 页面文本内容
         */
        public String getPageTextByNumber(int pageNum) {
            return getPageText(pageNum - 1);
        }
    }

    /**
     * 文本查找器
     */
    private static class TextFinder extends PDFTextStripper {
        private final String searchText;
        @Getter
        private final List<Integer> pagesContainingText = new java.util.ArrayList<>();
        private int currentPage;

        public TextFinder(String searchText) throws IOException {
            this.searchText = searchText;
            this.setSortByPosition(true);
        }

        @Override
        public void startPage(PDPage page) throws IOException {
            currentPage = getCurrentPageNo();
            super.startPage(page);
        }

        @Override
        protected void writeString(String string, List<org.apache.pdfbox.text.TextPosition> textPositions) throws IOException {
            if (string.contains(searchText) && !pagesContainingText.contains(currentPage)) {
                pagesContainingText.add(currentPage);
            }
            super.writeString(string, textPositions);
        }

    }

    /**
     * 文本配置类
     */
    /**
     * 文本配置类 - 增强版支持字体加粗
     */
    @Data
    public static class TextConfig {
        private String text;
        private float x;
        private float y;
        private PDFont font;
        private float fontSize = 12;
        private Color color = Color.BLACK;
        private String fontPath;
        private boolean isBold = false; // 新增加粗属性

        public TextConfig() {
        }


        // 链式调用方法
        public TextConfig text(String text) {
            this.text = text;
            return this;
        }

        public TextConfig position(float x, float y) {
            this.x = x;
            this.y = y;
            return this;
        }

        public TextConfig font(PDFont font) {
            this.font = font;
            return this;
        }

        public TextConfig fontSize(float fontSize) {
            this.fontSize = fontSize;
            return this;
        }

        public TextConfig color(Color color) {
            this.color = color;
            return this;
        }

        public TextConfig fontPath(String fontPath) {
            this.fontPath = fontPath;
            return this;
        }

        public TextConfig isBold(boolean bold) {
            this.isBold = bold;
            return this;
        }

        // 为了兼容您代码中的用法
        public TextConfig position(TextPositionInfo x, TextPositionInfo y) {
            this.x = x.getX();
            this.y = y.getY();
            return this;
        }
    }


    /**
     * 掩盖配置类
     */
    @Setter
    @Getter
    public static class CoverConfig {
        // 掩盖区域坐标
        private float x;
        private float y;
        // 掩盖区域尺寸
        private float width;
        private float height;
        // 掩盖颜色（默认白色）
        private Color coverColor = Color.WHITE;
        // 替代文本（可选）
        private String replacementText;
        // 替代文本字体
        private PDFont font;
        // 替代文本大小
        private float fontSize = 12;
        // 替代文本颜色
        private Color textColor = Color.BLACK;

        public CoverConfig() {
        }

        public CoverConfig(float x, float y, float width, float height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }

        // 链式调用方法
        public CoverConfig position(float x, float y) {
            this.x = x;
            this.y = y;
            return this;
        }

        public CoverConfig size(float width, float height) {
            this.width = width;
            this.height = height;
            return this;
        }

        public CoverConfig coverColor(Color coverColor) {
            this.coverColor = coverColor;
            return this;
        }

        public CoverConfig replacementText(String replacementText) {
            this.replacementText = replacementText;
            return this;
        }

        public CoverConfig font(PDFont font) {
            this.font = font;
            return this;
        }

        public CoverConfig fontSize(float fontSize) {
            this.fontSize = fontSize;
            return this;
        }

        public CoverConfig textColor(Color textColor) {
            this.textColor = textColor;
            return this;
        }
    }

    /**
     * 隐藏PDF中的指定文字（使用文件路径）
     *
     * @param inputPath   输入PDF文件路径
     * @param outputPath  输出PDF文件路径
     * @param textsToHide 要隐藏的文本列表
     * @param hideConfig  隐藏配置
     */
    public static void hideTextInPDF(String inputPath, String outputPath, List<String> textsToHide, HideTextConfig hideConfig) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            hideTextInPDF(doc, textsToHide, hideConfig);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("隐藏PDF文本失败", e);
        }
    }

    /**
     * 隐藏PDF中的指定文字（使用输入输出流）
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @param textsToHide  要隐藏的文本列表
     * @param hideConfig   隐藏配置
     */
    public static void hideTextInPDF(InputStream inputStream, OutputStream outputStream, List<String> textsToHide, HideTextConfig hideConfig) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            hideTextInPDF(doc, textsToHide, hideConfig);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("隐藏PDF文本失败", e);
        }
    }

    /**
     * 隐藏PDF中的指定文字（使用PDDocument对象）
     *
     * @param doc         PDF文档对象
     * @param textsToHide 要隐藏的文本列表
     * @param hideConfig  隐藏配置
     * @throws IOException IO异常
     */
    public static void hideTextInPDF(PDDocument doc, List<String> textsToHide, HideTextConfig hideConfig) throws IOException {
        if (doc.getNumberOfPages() == 0) {
            throw new IllegalArgumentException("文档中没有页面，无法隐藏文本。");
        }

        if (textsToHide == null || textsToHide.isEmpty()) {
            throw new IllegalArgumentException("要隐藏的文本列表不能为空。");
        }

        if (hideConfig == null) {
            hideConfig = new HideTextConfig(); // 使用默认配置
        }

        // 为每一页添加隐藏内容
        int totalPages = doc.getNumberOfPages();
        for (int i = 0; i < totalPages; i++) {
            try {
                hideTextOnPage(doc, i, textsToHide, hideConfig);
            } catch (Exception e) {
                System.err.println("隐藏第 " + (i + 1) + " 页文本时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 在指定页面上隐藏文本
     *
     * @param doc         PDF文档对象
     * @param pageIndex   页面索引
     * @param textsToHide 要隐藏的文本列表
     * @param hideConfig  隐藏配置
     * @throws IOException IO异常
     */
    private static void hideTextOnPage(PDDocument doc, int pageIndex, List<String> textsToHide, HideTextConfig hideConfig) throws IOException {
        // 先提取页面文本位置信息
        List<TextPositionInfo> textPositions = findTextPositionsOnPage(doc, pageIndex, textsToHide, hideConfig);

        if (textPositions.isEmpty()) {
            return; // 没有找到要隐藏的文本
        }

        // 然后在页面上绘制隐藏内容
        PDPage page = doc.getPage(pageIndex);
        try (PDPageContentStream content = new PDPageContentStream(
                doc, page,
                PDPageContentStream.AppendMode.APPEND,
                true)) {

            for (TextPositionInfo position : textPositions) {
                hideTextAtPosition(content, position, hideConfig);
            }
        }
    }

    /**
     * 查找页面上的文本位置信息
     *
     * @param doc         PDF文档对象
     * @param pageIndex   页面索引
     * @param textsToHide 要隐藏的文本列表
     * @param hideConfig  隐藏配置
     * @return 文本位置信息列表
     */
    private static List<TextPositionInfo> findTextPositionsOnPage(PDDocument doc, int pageIndex, List<String> textsToHide, HideTextConfig hideConfig) {
        List<TextPositionInfo> positions = new ArrayList<>();

        try {
            TextPositionFinder finder = new TextPositionFinder(textsToHide, hideConfig.isMatchExactly());
            finder.setStartPage(pageIndex + 1);
            finder.setEndPage(pageIndex + 1);
            finder.getText(doc);
            positions = finder.getTextPositions();
        } catch (Exception e) {
            System.err.println("查找第 " + (pageIndex + 1) + " 页文本位置时出错: " + e.getMessage());
        }

        return positions;
    }


    /**
     * 在指定位置隐藏文本
     *
     * @param content    内容流
     * @param position   文本位置信息
     * @param hideConfig 隐藏配置
     * @throws IOException IO异常
     */
    private static void hideTextAtPosition(PDPageContentStream content, TextPositionInfo position, HideTextConfig hideConfig) throws IOException {
        // 设置隐藏颜色
        Color hideColor = hideConfig.getHideColor() != null ? hideConfig.getHideColor() : Color.WHITE;
        content.setNonStrokingColor(hideColor);

        // 绘制矩形隐藏区域
        float x = position.getX();
        float y = position.getY();
        float width = position.getWidth();
        float height = position.getHeight();

        // 添加边距
        float margin = hideConfig.getMargin();
        x -= margin;
        y -= margin;
        width += 2 * margin;
        height += 2 * margin;

        content.addRect(x, y, width, height);
        content.fill();

        // 如果需要添加替代文本
        if (hideConfig.getReplacementText() != null && !hideConfig.getReplacementText().isEmpty()) {
            // 重置颜色为黑色或其他指定颜色
            Color textColor = hideConfig.getTextColor() != null ? hideConfig.getTextColor() : Color.BLACK;
            content.setNonStrokingColor(textColor);

            // 设置字体和大小
            float fontSize = hideConfig.getFontSize() > 0 ? hideConfig.getFontSize() : 12;
            content.beginText();
            content.setFont(PDType1Font.HELVETICA, fontSize);
            content.newLineAtOffset(x + margin, y + margin + (height - 2 * margin - fontSize) / 2);
            content.showText(hideConfig.getReplacementText());
            content.endText();
        }
    }

    /**
     * 文本位置查找器 - 改进版，支持查找同一行中的所有相同文字
     */
    private static class TextPositionFinder extends PDFTextStripper {
        private final List<String> textsToFind;
        private final boolean matchExactly;
        private final List<TextPositionInfo> textPositions = new ArrayList<>();

        public TextPositionFinder(List<String> textsToFind, boolean matchExactly) throws IOException {
            this.textsToFind = textsToFind != null ? textsToFind : new ArrayList<>();
            this.matchExactly = matchExactly;
            this.setSortByPosition(true);
        }

        public List<TextPositionInfo> getTextPositions() {
            return new ArrayList<>(textPositions);
        }

        @Override
        protected void writeString(String string, List<TextPosition> textPositions) throws IOException {
            for (String textToFind : textsToFind) {
                if (matchExactly) {
                    // 精确匹配
                    if (string.equals(textToFind)) {
                        processExactMatch(string, textPositions, textToFind);
                    }
                } else {
                    // 部分匹配 - 查找所有出现的位置
                    findAllOccurrences(string, textPositions, textToFind);
                }
            }
            super.writeString(string, textPositions);
        }

        /**
         * 处理精确匹配
         */
        private void processExactMatch(String string, List<TextPosition> textPositions, String textToFind) {
            if (textPositions.size() >= textToFind.length()) {
                addTextPosition(textToFind, textPositions, 0, textToFind.length());
            }
        }

        /**
         * 查找所有出现的位置
         */
        private void findAllOccurrences(String string, List<TextPosition> textPositions, String textToFind) {
            int fromIndex = 0;
            int index;
            while ((index = string.indexOf(textToFind, fromIndex)) != -1) {
                if (index + textToFind.length() <= textPositions.size()) {
                    addTextPosition(textToFind, textPositions, index, index + textToFind.length());
                }
                fromIndex = index + 1;
            }
        }

        /**
         * 添加文本位置信息
         */
        private void addTextPosition(String text, List<TextPosition> textPositions, int startIndex, int endIndex) {
            float minX = Float.MAX_VALUE;
            float minY = Float.MAX_VALUE;
            float maxX = Float.MIN_VALUE;
            float maxY = Float.MIN_VALUE;

            // 计算匹配文本的边界框
            for (int i = startIndex; i < endIndex; i++) {
                TextPosition pos = textPositions.get(i);
                float x = pos.getX();
                float y = pos.getEndY();
                float width = pos.getWidth();
                float height = pos.getHeight();

                minX = Math.min(minX, x);
                minY = Math.min(minY, y - height);
                maxX = Math.max(maxX, x + width);
                maxY = Math.max(maxY, y);
            }

            float width = maxX - minX;
            float height = maxY - minY;

            this.textPositions.add(new TextPositionInfo(text, minX, maxY, width, height));
        }
    }


    /**
     * 文本位置信息类
     */
    @Getter
    @ToString
    public static class TextPositionInfo {
        private final String text;
        private final float x;
        private final float y;
        private final float width;
        private final float height;

        public TextPositionInfo(String text, float x, float y, float width, float height) {
            this.text = text;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
    }

    /**
     * 隐藏文本配置类
     */
    @Getter
    @Setter
    public static class HideTextConfig {
        /**
         * 隐藏颜色（默认白色）
         */
        private Color hideColor = Color.WHITE;

        /**
         * 替代文本（可选）
         */
        private String replacementText;

        /**
         * 替代文本颜色
         */
        private Color textColor = Color.BLACK;

        /**
         * 替代文本大小
         */
        private float fontSize = 12;

        /**
         * 边距
         */
        private float margin = 1.5f;

        /**
         * 是否精确匹配
         */
        private boolean matchExactly = false;

        public HideTextConfig() {
        }

        // 链式调用方法
        public HideTextConfig hideColor(Color hideColor) {
            this.hideColor = hideColor;
            return this;
        }

        public HideTextConfig replacementText(String replacementText) {
            this.replacementText = replacementText;
            return this;
        }

        public HideTextConfig textColor(Color textColor) {
            this.textColor = textColor;
            return this;
        }

        public HideTextConfig fontSize(float fontSize) {
            this.fontSize = fontSize;
            return this;
        }

        public HideTextConfig margin(float margin) {
            this.margin = margin;
            return this;
        }

        public HideTextConfig matchExactly(boolean matchExactly) {
            this.matchExactly = matchExactly;
            return this;
        }
    }

    /**
     * 根据正则表达式隐藏PDF中的文字（使用文件路径）
     *
     * @param inputPath     输入PDF文件路径
     * @param outputPath    输出PDF文件路径
     * @param regexPatterns 要隐藏的文本正则表达式列表
     * @param hideConfig    隐藏配置
     */
    public static void hideTextByRegexInPDF(String inputPath, String outputPath, List<String> regexPatterns, HideTextConfig hideConfig) {
        try (PDDocument doc = PDDocument.load(new File(inputPath))) {
            hideTextByRegexInPDF(doc, regexPatterns, hideConfig);
            doc.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("根据正则表达式隐藏PDF文本失败", e);
        }
    }

    /**
     * 根据正则表达式隐藏PDF中的文字（使用输入输出流）
     *
     * @param inputStream   输入流
     * @param outputStream  输出流
     * @param regexPatterns 要隐藏的文本正则表达式列表
     * @param hideConfig    隐藏配置
     */
    public static void hideTextByRegexInPDF(InputStream inputStream, OutputStream outputStream, List<String> regexPatterns, HideTextConfig hideConfig) {
        try (PDDocument doc = PDDocument.load(inputStream)) {
            hideTextByRegexInPDF(doc, regexPatterns, hideConfig);
            doc.save(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("根据正则表达式隐藏PDF文本失败", e);
        }
    }

    /**
     * 根据正则表达式隐藏PDF中的文字（使用PDDocument对象）
     *
     * @param doc           PDF文档对象
     * @param regexPatterns 要隐藏的文本正则表达式列表
     * @param hideConfig    隐藏配置
     * @throws IOException IO异常
     */
    public static void hideTextByRegexInPDF(PDDocument doc, List<String> regexPatterns, HideTextConfig hideConfig) throws IOException {
        if (doc.getNumberOfPages() == 0) {
            throw new IllegalArgumentException("文档中没有页面，无法隐藏文本。");
        }

        if (regexPatterns == null || regexPatterns.isEmpty()) {
            throw new IllegalArgumentException("正则表达式列表不能为空。");
        }

        if (hideConfig == null) {
            hideConfig = new HideTextConfig(); // 使用默认配置
        }

        // 编译正则表达式模式
        List<Pattern> patterns = new ArrayList<>();
        for (String regex : regexPatterns) {
            try {
                patterns.add(Pattern.compile(regex));
            } catch (Exception e) {
                System.err.println("编译正则表达式失败: " + regex + ", 错误: " + e.getMessage());
            }
        }

        // 为每一页添加隐藏内容
        int totalPages = doc.getNumberOfPages();
        for (int i = 0; i < totalPages; i++) {
            try {
                hideTextByRegexOnPage(doc, i, patterns, hideConfig);
            } catch (Exception e) {
                System.err.println("隐藏第 " + (i + 1) + " 页文本时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 在指定页面上根据正则表达式隐藏文本
     *
     * @param doc        PDF文档对象
     * @param pageIndex  页面索引
     * @param patterns   正则表达式模式列表
     * @param hideConfig 隐藏配置
     * @throws IOException IO异常
     */
    private static void hideTextByRegexOnPage(PDDocument doc, int pageIndex, List<Pattern> patterns, HideTextConfig hideConfig) throws IOException {
        // 先提取页面文本位置信息
        List<TextPositionInfo> textPositions = findTextPositionsByRegexOnPage(doc, pageIndex, patterns);

        if (textPositions.isEmpty()) {
            return; // 没有找到要隐藏的文本
        }

        // 然后在页面上绘制隐藏内容
        PDPage page = doc.getPage(pageIndex);
        try (PDPageContentStream content = new PDPageContentStream(
                doc, page,
                PDPageContentStream.AppendMode.APPEND,
                true)) {

            for (TextPositionInfo position : textPositions) {
                hideTextAtPosition(content, position, hideConfig);
            }
        }
    }

    /**
     * 根据正则表达式查找页面上的文本位置信息
     *
     * @param doc       PDF文档对象
     * @param pageIndex 页面索引
     * @param patterns  正则表达式模式列表
     * @return 文本位置信息列表
     */
    private static List<TextPositionInfo> findTextPositionsByRegexOnPage(PDDocument doc, int pageIndex, List<Pattern> patterns) {
        List<TextPositionInfo> positions = new ArrayList<>();

        try {
            RegexTextPositionFinder finder = new RegexTextPositionFinder(patterns);
            finder.setStartPage(pageIndex + 1);
            finder.setEndPage(pageIndex + 1);
            finder.getText(doc);
            positions = finder.getTextPositions();
        } catch (Exception e) {
            System.err.println("查找第 " + (pageIndex + 1) + " 页正则表达式匹配文本位置时出错: " + e.getMessage());
        }

        return positions;
    }

    /**
     * 正则表达式文本位置查找器
     */
    private static class RegexTextPositionFinder extends PDFTextStripper {
        private final List<Pattern> patterns;
        @Getter
        private final List<TextPositionInfo> textPositions = new ArrayList<>();

        public RegexTextPositionFinder(List<Pattern> patterns) throws IOException {
            this.patterns = patterns != null ? patterns : new ArrayList<>();
            this.setSortByPosition(true);
        }

        @Override
        protected void writeString(String string, List<TextPosition> textPositions) throws IOException {
            for (Pattern pattern : patterns) {
                java.util.regex.Matcher matcher = pattern.matcher(string);
                while (matcher.find()) {
                    // 为每个匹配的文本创建位置信息
                    for (TextPosition textPosition : textPositions) {
                        String matchedText = matcher.group();
                        if (string.contains(matchedText)) {
                            this.textPositions.add(new TextPositionInfo(
                                    matchedText,
                                    textPosition.getX(),
                                    textPosition.getY(),
                                    textPosition.getWidth(),
                                    textPosition.getHeight()
                            ));
                        }
                    }
                }
            }
            super.writeString(string, textPositions);
        }
    }

    public static void main(String[] args) {
        try {

//            PDFUtil.hideTextInPDF("/Users/<USER>/Downloads/FBA1906MW1XB-1755683971603.pdf", "/Users/<USER>/Downloads/testkk.pdf", Lists.newArrayList("PLEASE LEAVE THIS LABEL UNCOVERED"),new HideTextConfig());
//            PDFUtil.insertBlankPage("/Users/<USER>/Downloads/FBA1906MW1XB-1755683971603.pdf",
//                    "/Users/<USER>/Downloads/testkk.pdf", 0);
//            PDFUtil.TextConfig textConfig = new PDFUtil.TextConfig()
//                    .text("dsadasd")
//                    .position(150, 800)
//                    .fontSize(20)
//                    .color(Color.black);
//            PDFUtil.addTextToPDF("/Users/<USER>/Downloads/testkk.pdf",
//                    "/Users/<USER>/Downloads/testkk.pdf", Lists.newArrayList(textConfig), new PDFUtil.TextConfig().isBold(true));


//            PDFUtil.insertBlankPage("/Users/<USER>/Downloads/FBA1906MW1XB-1755683971603.pdf",
//                    "/Users/<USER>/Downloads/testkk.pdf", 0);
//            // 在第一页添加居中文本
//            addCenteredTextToPage(
//                    "/Users/<USER>/Downloads/testkk.pdf",
//                    "/Users/<USER>/Downloads/test_centered.pdf",
//                    "这是居中的文本",
//                    30,
//                    true,
//                    Color.BLACK,
//                    0
//            );

            // 使用完整参数版本
            addTextBasedOnExistingTextPositionAllPages(
                    "/Users/<USER>/Downloads/FBA1906MW1XB-1755683971603.pdf",
                    "/Users/<USER>/Downloads/testkk.pdf",
                    "FBA1906MW1XBU000003",  // 查找的文本
                    "MADE IN CHINA",                      // 要添加的文本
                    15,   // xOffset
                    -100,  // yOffset
                    16,   // fontSize
                    true, // isBold
                    Color.BLACK // color
            );

//            mergePDFs(
//                    Lists.newArrayList("/Users/<USER>/Downloads/FBA1906MW1XB-1755683971603.pdf"
//                            , "/Users/<USER>/Downloads/testkk.pdf")
//                    , "/Users/<USER>/Downloads/merge.pdf"
//            );
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
