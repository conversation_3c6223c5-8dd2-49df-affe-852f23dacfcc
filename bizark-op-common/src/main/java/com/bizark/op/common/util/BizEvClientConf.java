package com.bizark.op.common.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class BizEvClientConf {
    @Value("${bizevcli.enable}")
    private Boolean enable;
    @Value("${bizevcli.env}")
    private String env;
    @Value("${bizevcli.token}")
    private String token;
    @Value("${bizevcli.url}")
    private String url;

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
