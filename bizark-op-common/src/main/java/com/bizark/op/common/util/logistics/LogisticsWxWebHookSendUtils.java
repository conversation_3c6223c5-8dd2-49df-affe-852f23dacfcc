package com.bizark.op.common.util.logistics;

import com.alibaba.fastjson.JSONObject;
import com.bizark.op.common.util.AssertUtil;
import com.bizark.op.common.util.HttpUtils;
import com.bizark.op.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 物流模块微信群消息推送工具类
 * <AUTHOR>
 * @create: 2024-06-05 11:29
 */
@Slf4j
public class LogisticsWxWebHookSendUtils {

    /**
     * 船公司报警群供应链确认收货报警机器人
     */
    public static final String WE_CHAT_SHIP_COMPANY_ALARM_WEB_HOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=00e18857-afec-4ca9-950f-058df9624b95";
    //测试环境推送
//    public static final String WE_CHAT_SHIP_COMPANY_ALARM_WEB_HOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7f3780e1-57d5-430a-8776-8553726d89d4";

    /**
     * 根据webHook推送文本消息
     *
     * @param webHookUrl webHook地址
     * @param msg 消息内容
     * @param mentionedList userid的列表，提醒群中的指定成员(@某个成员)，@all表示提醒所有人，如果开发者获取不到userid，可以使用mentioned_mobile_list
     * @param mentionedMobileList 手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人
     * <AUTHOR>
     * @date 2024-06-05 11:36:13
     */
    public static void sendTextMsg(String webHookUrl,String msg, List<String> mentionedList,List<String> mentionedMobileList) {
        AssertUtil.isFailCheck(StringUtils.isEmpty(webHookUrl),"微信消息推送异常,webHookUrl不能为空!");
        AssertUtil.isFailCheck(StringUtils.isEmpty(msg),"微信消息推送异常,消息内容不能为空!");
        //构建请求体
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msgtype", "text");
        JSONObject text = new JSONObject();
        text.put("content", msg);
        if(StringUtils.isNotEmpty(mentionedList)){
            text.put("mentioned_list", mentionedList);
        }
        if(StringUtils.isNotEmpty(mentionedMobileList)){
            text.put("mentioned_mobile_list", mentionedMobileList);
        }
        jsonObject.put("text", text);
        send(webHookUrl,jsonObject);
    }

    /**
     * 根据webHook推送markdown消息
     *
     * @param webHookUrl webHook地址
     * @param msg 消息内容 markdown内容，最长不超过4096个字节，必须是utf8编码
     * <AUTHOR>
     * @date 2024-06-05 11:36:13
     */
    public static void sendMarkDownMsg(String webHookUrl,String msg) {
        AssertUtil.isFailCheck(StringUtils.isEmpty(webHookUrl),"微信消息推送异常,webHookUrl不能为空!");
        AssertUtil.isFailCheck(StringUtils.isEmpty(msg),"微信消息推送异常,消息内容不能为空!");
        AssertUtil.isFailCheck(msg.length()>4096,"微信消息推送异常,消息内容不能超过4096个字节!");
        //构建请求体
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msgtype", "markdown");
        JSONObject text = new JSONObject();
        text.put("content", msg);
        jsonObject.put("markdown", text);
        send(webHookUrl,jsonObject);
    }

    /**
     * 根据webHook推送 image 消息
     *
     * @param webHookUrl webHook地址
     * @param base64 消息内容 图片内容的base64编码
     * @param md5   图片内容（base64编码前）的md5值
     * <AUTHOR>
     * @date 2024-06-05 11:36:13
     */
    public static void sendImageMsg(String webHookUrl,String base64,String md5) {
        AssertUtil.isFailCheck(StringUtils.isEmpty(webHookUrl),"微信消息推送异常,webHookUrl不能为空!");
        AssertUtil.isFailCheck(StringUtils.isEmpty(base64),"微信消息推送异常,消息内容不能为空!");
        //构建请求体
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msgtype", "image");
        JSONObject text = new JSONObject();
        text.put("base64", base64);
        text.put("md5", md5);
        jsonObject.put("image", text);
        send(webHookUrl,jsonObject);
    }

    /**
     * 根据webHook推送 图文 消息
     *
     * @param webHookUrl webHook地址
     * @param wxNewsTypeMsgDTOList 消息内容
     * <AUTHOR>
     * @date 2024-06-05 11:36:13
     */
    public static void sendNewsMsg(String webHookUrl,List<WxNewsTypeMsgDTO> wxNewsTypeMsgDTOList) {
        AssertUtil.isFailCheck(StringUtils.isEmpty(webHookUrl),"微信消息推送异常,webHookUrl不能为空!");
        AssertUtil.isFailCheck(StringUtils.isEmpty(wxNewsTypeMsgDTOList),"微信消息推送异常,消息内容不能为空!");
        //构建请求体
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msgtype", "news");
        List<JSONObject> msgList = new ArrayList<>();
        wxNewsTypeMsgDTOList.forEach(item->{
            JSONObject text = new JSONObject();
            text.put("title", item.getTitle());
            text.put("description", item.getDescription());
            text.put("url", item.getUrl());
            text.put("picurl", item.getPicurl());
            msgList.add(text);
        });
        JSONObject articles = new JSONObject();
        articles.put("articles", msgList);
        jsonObject.put("news", articles);
        send(webHookUrl,jsonObject);
    }

    /**
     * 发送消息(需自行构建好消息内容)
     *
     * @param webHookUrl webHook地址
     * @param jsonObject 消息体
     * <AUTHOR>
     * @date 2024-06-05 11:44:50
     */
    private static void send(String webHookUrl,JSONObject jsonObject){
        AssertUtil.isFailCheck(StringUtils.isEmpty(webHookUrl),"微信消息推送异常,webHookUrl不能为空!");
        AssertUtil.isFailCheck(StringUtils.isNull(jsonObject),"微信消息推送异常,消息体不能为空!");
        try {
            JSONObject resultJson = HttpUtils.httpPost(webHookUrl,jsonObject,"application/json");
            AssertUtil.isFailCheck(StringUtils.isNull(resultJson),"微信消息推送异常,webHookUrl:"+webHookUrl+",消息体:"+jsonObject.toJSONString()+",返回结果为空!");
            if(resultJson.getInteger("errcode") == 0){
                log.info("微信消息推送成功,webHookUrl:"+webHookUrl+",消息体:"+jsonObject.toJSONString());
            }else{
                throw new RuntimeException("微信消息推送失败,webHookUrl:"+webHookUrl+",消息体:"+jsonObject.toJSONString()+",错误码:"+resultJson.getInteger("errcode")+",错误信息:"+resultJson.getString("errmsg"));
            }
        } catch (Exception e) {
            throw new RuntimeException("微信消息推送异常,webHookUrl:"+webHookUrl+",消息体:"+jsonObject.toJSONString(),e);
        }
    }
}
