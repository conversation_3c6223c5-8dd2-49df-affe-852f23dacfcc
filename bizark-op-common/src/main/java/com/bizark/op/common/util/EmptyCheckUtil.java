package com.bizark.op.common.util;

import java.util.Collection;
import java.util.Map;

/**
 * @Classname EmptyCheckeUtil
 * @Description TODO
 * @Date 2025/6/5 18:32
 * @Created by 14167
 */
public class EmptyCheckUtil {

    /**
     * 判断对象是否为 null 或空。
     *
     * @param obj 要检查的对象
     * @return 如果对象为 null 或空，则返回 true，否则返回 false
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }

        if (obj instanceof String) {
            return ((String) obj).isEmpty();
        } else if (obj instanceof Collection<?>) {
            return ((Collection<?>) obj).isEmpty();
        } else if (obj instanceof Map<?, ?>) {
            return ((Map<?, ?>) obj).isEmpty();
        } else if (obj.getClass().isArray()) {
            return java.lang.reflect.Array.getLength(obj) == 0;
        } else if (obj instanceof Boolean) {
            return !(Boolean) obj;
        } else if (obj instanceof Number) {
            return ((Number) obj).doubleValue() == 0.0;
        }

        return false;
    }
}
