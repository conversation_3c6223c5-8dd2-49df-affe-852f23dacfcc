package com.bizark.op.common.core.domain;

import java.util.List;


/**
 * @ClassName CascaderSelect
 * @Description  层级分类通用Entity
 * <AUTHOR>
 * @Date 2023/8/2 9:57
 */
public class CascaderSelect {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 节点值
     */
    private String value;

    private List<CascaderSelect> children;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public List<CascaderSelect> getChildren() {
        return children;
    }

    public void setChildren(List<CascaderSelect> children) {
        this.children = children;
    }
}
