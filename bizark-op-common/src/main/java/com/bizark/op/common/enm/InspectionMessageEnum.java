package com.bizark.op.common.enm;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 验货申请消息相关枚举
 * <AUTHOR>
 * @create: 2024-03-19 15:16
 */

public class InspectionMessageEnum {


    /**
     * 验货消息通知模块枚举
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionMessageModelEnum {

        /**
         * 生产商
         */
        MANUFACTURER(0,   "生产商"),

        /**
         *管理端
         */
        ROOT(1,  " 管理端 "),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionMessageModelEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static InspectionMessageEnum.InspectionMessageModelEnum getByValue(Integer value) {
            for (InspectionMessageEnum.InspectionMessageModelEnum e : InspectionMessageEnum.InspectionMessageModelEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 验货消息通知模块枚举
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionRelatedTypeEnum {

        /**
         * 验货订单
         */
        INSPECTION_ORDER(1,   "验货订单"),

        /**
         *验货申请
         */
        INSPECTION_APPLICATION(2,  " 验货申请 "),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionRelatedTypeEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static InspectionMessageEnum.InspectionRelatedTypeEnum getByValue(Integer value) {
            for (InspectionMessageEnum.InspectionRelatedTypeEnum e : InspectionMessageEnum.InspectionRelatedTypeEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 验货消息状态
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionMessageReadStatusEnum {

        /**
         * 已读
         */
        READ(1,"已读"),

        /**
         *未读
         */
        UNREAD(0,  " 未读 "),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionMessageReadStatusEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static InspectionMessageEnum.InspectionMessageReadStatusEnum getByValue(Integer value) {
            for (InspectionMessageEnum.InspectionMessageReadStatusEnum e : InspectionMessageEnum.InspectionMessageReadStatusEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 消息来源
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionSourceTerminalEnum {

        /**
         * PC
         */
        PC(0,   "PC"),

        /**
         *小程序
         */
        MINI(1,  " 小程序 "),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionSourceTerminalEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static InspectionMessageEnum.InspectionSourceTerminalEnum getByValue(Integer value) {
            for (InspectionMessageEnum.InspectionSourceTerminalEnum e : InspectionMessageEnum.InspectionSourceTerminalEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 消息来源
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionMessageTemplateEnum {

        /**
         * 通用消息模版
         */
        COMMON_TEMPLATE(0,   "/pages/home/<USER>","通用消息模版","zkb77XhHCc7H470TrOPXbx3n3mI8gUXl7v4_4IYkQ4w", new HashMap<String,Object>(){
            {
                put("thing9", "");
                put("time8", "");
            }
        }),

        /**
         *小程序验货日期变更
         */
        MINI_INSPECTION_DATE(1, "/producer/apply/detail?id=", " 小程序验货日期变更 ","UWq2lW-ZM-XBYka-wl-rMDAhIbAH9llS-IMR_5FEAVE",new HashMap<String,Object>(){
            {
                put("time1", "");
                put("time2", "");
                put("time3", "");
                put("thing4", "");
                put("character_string5", "");
            }
        }),
        /**
         *小程序验货结果
         */
        MINI_INSPECTION_RESULTS(2, "/producer/apply/detail?id=", " 小程序验货结果 ","od2Q61HQhLwbnEElmVVe-iFlaykbgFSSWIderi_WtC8",new HashMap<String,Object>(){
            {
                put("time9", "");
                put("character_string8", "");
                put("thing3", "");
            }
        }),

        /**
         * 小程序待审核通知
         */
        MINI_INSPECTION_PENDING_REVIEW(3,  "/producer/apply/detail?id="," 小程序待审核通知 ","zkb77XhHCc7H470TrOPXbx3n3mI8gUXl7v4_4IYkQ4w",new HashMap<String,Object>(){
            {
                put("thing9", "");
                put("time8", "");
            }
        }),
        UNKNOWN(-1,  "/pages/","未知类型","",new HashMap<>())

        ;

        /**
         * 标的值
         */
        private final Integer value;

        private final String page;

        /**
         * 描述
         */
        private final String describe;

        /**
         * 模版id
         */
        private final String templateId;

        /**
         * 模版的参数
         */
        private Map<String,Object> data;



        InspectionMessageTemplateEnum(Integer value,String page, String describe, String templateId, Map<String,Object>data){
            this.page = page;
            this.value = value;
            this.describe = describe;
            this.templateId = templateId;
            this.data = data;
        }

        public static InspectionMessageEnum.InspectionMessageTemplateEnum getByValue(Integer value) {
            for (InspectionMessageEnum.InspectionMessageTemplateEnum e : InspectionMessageEnum.InspectionMessageTemplateEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }


    /**
     * 通用校验状态枚举
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionCommonCheckEnum {

        /**
         * 等于
         */
        NO(0,   "否"),

        /**
         *
         */
        YES(1,  " 是 "),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionCommonCheckEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static InspectionMessageEnum.InspectionCommonCheckEnum getByValue(Integer value) {
            for (InspectionMessageEnum.InspectionCommonCheckEnum e : InspectionMessageEnum.InspectionCommonCheckEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 小程序配置信息枚举
     * <AUTHOR>
     * @date 2024-01-15 11:19:42
     */
    @Getter
    public enum WxConfigConstantEnum {

        /**
         * 分销平台供应商
         */
        APP_ID("wx4c63eb4f8e2e55e9",  "微信小程序APPID"),
        /**
         * 分销平台
         */
        APP_SECRET("e8fac866bcdf03545ec0257b8b87e9e3",  "微信小程序密钥"),
        DEFAULT("null",  "其他(非法请求参数)")
        ;

        /**
         * 标的值
         */
        private final String key;

        /**
         * 描述
         */
        private final String describe;



        WxConfigConstantEnum(String key, String describe) {
            this.key = key;
            this.describe = describe;
        }

        public static WxConfigConstantEnum getByValue(String key) {
            for (WxConfigConstantEnum e : WxConfigConstantEnum.values()) {
                if (e.getKey().equals(key)) {
                    return e;
                }
            }
            return null;
        }
    }

    /**
     * 通用校验状态枚举
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionmInIprogramStateEnum {

        /**
         * 等于
         */
        DEVELOPER("developer",   "开发版"),

        /**
         *
         */
        TRIAL("trial",  "体验版"),

        FORMAL("formal",  "正式版"),
        UNKNOWN("unknown",  "未知类型")

        ;

        /**
         * 标的值
         */
        private final String value;

        /**
         * 描述
         */
        private final String describe;



        InspectionmInIprogramStateEnum(String value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static InspectionMessageEnum.InspectionmInIprogramStateEnum getByValue(String value) {
            for (InspectionMessageEnum.InspectionmInIprogramStateEnum e : InspectionMessageEnum.InspectionmInIprogramStateEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 验货消息通知模块枚举
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum MessageTypeEnum {

        /**
         * 分配验货人节点
         */
        ASSIGN_INSPECTORS(0,   "分配验货人节点","验货申请: %s 调整安排验货日期，采购订单号: %s 安排验货日期为 :%s","您的验货日期已经变更，请及时查看!"),

        /**
         *验货合格
         */
        QUALIFIED_INSPECTION(1,  "验货合格","验货申请: %s 已验货，采购订单号: %s  验货合格!","您的验货申请: %s 已验货，验货合格"),

        /**
         * 验货不合格
         */
        UNQUALIFIED_INSPECTION(2,  "验货不合格","验货申请: %s 已验货，采购订单号: %s  验货不合格，备注原因: %s","您的验货申请: %s 已验货，验货不合格"),
        UNKNOWN(-1,  "未知类型","","")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;

        /**
         * pc端消息参数
         */
        private final String pcMessage;

        /**
         * 小程序端消息参数
         */
        private final String miniProgramMessage;



        MessageTypeEnum(Integer value, String describe, String pcMessage, String miniProgramMessage) {
            this.value = value;
            this.describe = describe;
            this.pcMessage = pcMessage;
            this.miniProgramMessage = miniProgramMessage;
        }

        public static InspectionMessageEnum.MessageTypeEnum getByValue(Integer value) {
            for (InspectionMessageEnum.MessageTypeEnum e : InspectionMessageEnum.MessageTypeEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }




}
