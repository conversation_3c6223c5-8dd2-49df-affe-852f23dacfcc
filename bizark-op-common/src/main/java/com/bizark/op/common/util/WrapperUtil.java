package com.bizark.op.common.util;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.common.contract.AuthUserDetails;

import java.util.List;

/**
 * wrapper更便捷的工具使用
 *<AUTHOR> 2024/2/29
 */

public class WrapperUtil {

    /**
     *  通过传入id 删除对应的实体类
     * @param id 实体类id
     * @param service 服务类
     * @param <T> 服务类对应的泛型
     */
    public static <T> void deleteWithWrapper(Integer id, IService<T> service) {
        AuthUserDetails user = UserUtils.getThisUser();
        UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();
        // 设置通用的删除属性
        updateWrapper
                .set("disabled_name", user.getName())
                .set("disabled_at", System.currentTimeMillis() / 1000)
                .set("disabled_by", user.getId())
                .eq("id", id);


        // 调用更新方法
        service.update(updateWrapper);
    }
    /**
     *  通过传入ids 删除对应的实体类
     * @param ids 实体类ids
     * @param service 服务类
     * @param <T> 服务类对应的泛型
     */
    public static <T> void deleteBatchWithWrapper(List<Integer> ids, IService<T> service) {
        AuthUserDetails user = UserUtils.getThisUser();
        UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();
        // 设置通用的删除属性
        updateWrapper
                .set("disabled_name", user.getName())
                .set("disabled_at", System.currentTimeMillis() / 1000)
                .set("disabled_by", user.getId())
                .in("id", ids);


        // 调用更新方法
        service.update(updateWrapper);
    }
}
