package com.bizark.op.common.core.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Description 分页参数基类
 * <AUTHOR>
 * @create: 2024-02-23 19:31
 */
@Setter
@Getter
public class BaseQueryEntity {

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    @Schema(description = "页码")
    private Integer page;

    @NotNull(message = "每页条数不能为空")
    @Range(min = 1, max = 1000, message = "每页条数，取值范围 1-1000")
    @Schema(description = "每页条数")
    private Integer size;

    @Schema(description = "时间开始日期")
    private String queryStartDateValue;

    @Schema(description = "时间结束日期")
    private String queryEndDateValue;

    @Schema(description = "查询时间类型")
    private Integer queryDateType;

    @Schema(description = "查询type")
    private Integer queryNoType;

    @Schema(description = "查询值")
    private String queryNoValue;

    @Schema(description = "组织id")
    private Integer contextId;

    @Schema(description = "排序字段")
    private String sidx;

    @Schema(description = "排序方式 ")
    private String sord;

}
