package com.bizark.op.common.core.page;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 表格分页数据对象(物流模块汇总使用)
 *
 * <AUTHOR>
 */
// T:返回对象所属类型，加泛型是为了Swagger 上responses可以展示出响应对象，并且展示swagger的注解
public class LogisticsTableDataInfo<T> extends TableDataInfo<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 总记录数 */
    private long total;

    private PageData<T> data;

    /** 消息状态码 */
    private int statusCode;

    /** 消息状态码
     * API_STATUS_OK = 0，
     * API_STATUS_ERROR = 1，
     * API_STATUS_INFO = 2
     * API_STATUS_WARN = 3
     * API_STATUS_CONF = 4
     **/
    private int status;

    /** 消息状态码 */
    private int code;

    /** 消息内容 */
    private String message;

    /**
     * 可追加的参数
     */
    private Map<String, Object> customParams = new HashMap<>();


    /**
     * 表格数据对象
     */
    public LogisticsTableDataInfo()
    {
    }

    public LogisticsTableDataInfo(TableDataInfo<T> tableDataInfo,Map<String,Object> map) {
        this.customParams = map;
        this.total = tableDataInfo.getTotal();
        this.data = tableDataInfo.getData();
        this.code = tableDataInfo.getCode();
        this.message = tableDataInfo.getMessage();
        this.status = tableDataInfo.getStatus();
        this.statusCode = tableDataInfo.getStatusCode();
    }

    public long getTotal()
    {
        return total;
    }

    public void setTotal(long total)
    {
        this.total = total;
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public PageData<T> getData() {
        return data;
    }

    public void setData(PageData<T> data) {
        this.data = data;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, Object> getCustomParams() {
        return customParams;
    }

    public void setCustomParams(Map<String, Object> customParams) {
        this.customParams = customParams;
    }
}
