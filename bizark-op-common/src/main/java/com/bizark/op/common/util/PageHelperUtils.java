package com.bizark.op.common.util;

import cn.hutool.core.collection.CollectionUtil;
import com.bizark.op.common.constant.HttpStatus;
import com.bizark.op.common.core.page.PageData;
import com.bizark.op.common.core.page.TableDataInfo;
import com.github.pagehelper.PageInfo;

import java.util.Collections;
import java.util.List;

/**
 * @Author: Ailill
 * @Date: 2025-05-30 15:34
 */
public class PageHelperUtils {

    public static <T> TableDataInfo<T> getPageInfo(List<T> list) {
        TableDataInfo<T> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setStatusCode(HttpStatus.SUCCESS);
        rspData.setStatus(0);
        rspData.setMessage("查询成功");
        PageData<T> pageData = new PageData<T>();
        pageData.setContent(list);
        PageInfo<T> pageInfo = new PageInfo<T>(list);
        pageData.setTotalElements(pageInfo.getTotal());
        pageData.setSize(pageInfo.getPageSize());
        pageData.setTotalPages(pageInfo.getPages());
        pageData.setFirst(pageInfo.isIsFirstPage());
        pageData.setLast(pageInfo.isIsLastPage());
        rspData.setData(pageData);
        rspData.setTotal(pageInfo.getTotal());
        return rspData;
    }

    public static <T> TableDataInfo<T> getPageInfo(PageInfo<T> pageInfo) {
        TableDataInfo<T> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setStatusCode(HttpStatus.SUCCESS);
        rspData.setStatus(0);
        rspData.setMessage("查询成功");
        PageData<T> pageData = new PageData<T>();
        pageData.setContent(CollectionUtil.isEmpty(pageInfo.getList()) ? Collections.emptyList() : pageInfo.getList());
        pageData.setTotalElements(pageInfo.getTotal());
        pageData.setSize(pageInfo.getPageSize());
        pageData.setTotalPages(pageInfo.getPages());
        pageData.setFirst(pageInfo.isIsFirstPage());
        pageData.setLast(pageInfo.isIsLastPage());
        rspData.setData(pageData);
        rspData.setTotal(pageInfo.getTotal());
        return rspData;
    }
}
