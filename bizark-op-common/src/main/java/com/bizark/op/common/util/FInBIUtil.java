package com.bizark.op.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.bizark.op.common.core.page.PageData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 财务对接bi枚举
 *
 * <AUTHOR>
 * @date 2024/7/3 - 17:01
 */
@Component
@Slf4j
public class FInBIUtil {

    @Value("${fin_bi_export_url}")
    private String finBiExportUrl;

    static String biUrl = "";

    //平台账单明细查询
    public static final String ORDER_PAYMENT_DETAIL_QUERY = "/api/v1/PlatformSettlementCenterData";

    //平台账单明细合计查询
    public static final String ORDER_PAYMENT_DETAIL_TOTAL_QUERY = "/api/v1/PlatformSettlementCenterDataSum";

    //平台项目汇总导出
    public static final String PLATFORM_SUMMARY_EXPORT = "/api/v1/PlatformSettlementCenterDataPaymenttTypeSum";

    //库存变动导出
    public static final String ORDER_PAYMENT_DETAIL_EXPORT= "/api/v1/InventoryChangeExport";


    public static final String ORDER_SHIP_TRACK_OUT_LIST="/api/v1/OrderShipmentBillingAndTrackingAnalysisOutboundData";
    public static final String ORDER_SHIP_TRACK_OUT_TOTAL="/api/v1/OrderShipmentBillingAndTrackingAnalysisOutboundDataSum";

    public static final String ORDER_SHIP_TRACK_ORDER_LIST="/api/v1/OrderShipmentBillingAndTrackingAnalysisOrderData";
    public static final String ORDER_SHIP_TRACK_ORDER_TOTAL="/api/v1/OrderShipmentBillingAndTrackingAnalysisOrderDataSum";

    @PostConstruct
    public void setUrl() {
        biUrl = finBiExportUrl;
    }


    /**
     * 分页请求
     *
     * @param url   地址
     * @param param 查询参数
     * @return 分页数据
     */
    public static PageData sendPageQuery(String url, Object param) {
        return JSON.parseObject(sendQuery(url, param), PageData.class);
    }

    /**
     * 分页请求
     *
     * @param url   地址
     * @param param 查询参数
     * @return 分页数据
     */
    public static <T> T sendObjectQuery(String url, Object param, Class<T> clazz) {
        return JSON.parseObject(sendQuery(url, param), clazz);
    }

    /**
     * 普通请求
     *
     * @param url   地址
     * @param param 查询参数
     * @return 返回结果
     */
    public static String sendQuery(String url, Object param) {
        AssertUtil.isTrue(ObjectUtil.isNotNull(param), "请求参数不能为空");
        String body = "";
        if (!(param instanceof String)) {
            body = JSON.toJSONString(param);
        }
        AssertUtil.isTrue(StrUtil.isNotBlank(biUrl), "bi地址未配置，请先配置！！！");
        String result = HttpUtil.createPost(biUrl + url)
                .body(body)
                .execute()
                .body();

        log.info("调用bi {} 接口返回结果: {}", url, result);
        return result;
    }

}
