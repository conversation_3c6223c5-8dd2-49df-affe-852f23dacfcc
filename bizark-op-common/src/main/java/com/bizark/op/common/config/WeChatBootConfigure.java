package com.bizark.op.common.config;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@ConfigurationProperties(prefix = "wechat.boot.webhook")
@Data
public class WeChatBootConfigure {

    private String webhookUrl;

    private Map<String, String> bootTokens;

    private String getToken(String webHookType) {
        if (MapUtil.isEmpty(bootTokens)){
            return "";
        }
        return bootTokens.getOrDefault(webHookType,"");
    }

    public String getWebHookUrl(String webHookType) {
        if (StrUtil.isBlank(webhookUrl)){
            return "";
        }
        String token = getToken(webHookType);
        return this.webhookUrl + token;
    }

}
