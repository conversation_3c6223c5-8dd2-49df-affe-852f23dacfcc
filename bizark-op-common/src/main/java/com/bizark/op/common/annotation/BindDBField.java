package com.bizark.op.common.annotation;

import java.lang.annotation.*;
import java.lang.reflect.Field;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface BindDBField {

    /**
     * 指定业务Service
     * @return
     */
    Class<?> service() default BindDBField.class;


    /**
     * 字典
     * @return
     */
    String dict() default "";

    /**
     * DB关联关系
     * @return
     */
    String relation() default "";

    /**
     * 对应映射字段名称
     * @return
     */
    String mappingField() default "";

    /**
     * 方法名称
     * @return
     */
    String method() default "list";

    /**
     * 参数个数
     * @return
     */
    int paramCount() default 1;

    /**
     * 参数字段名称
     * @return
     */
    String[] paramField() default {};
}
