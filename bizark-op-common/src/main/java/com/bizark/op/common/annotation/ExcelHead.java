package com.bizark.op.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelHead {
    /**
     * 表头名称
     */
    String name();

    /**
     * 单元格的宽度 （单位1~256个字符宽度）
     * 默认0:表示自动宽度
     */
    int width() default 0;

    /**
     * 是否合并单元格
     * 默认不合并
     */
    boolean merge() default false;

    /**
     * 是否隐藏
     * 默认不展示
     */
    boolean hidden() default true;

    /**
     * 是否必填
     * 默认非必填
     */
    boolean required() default false;

    /**
     * 标题后缀
     */
    boolean suffix() default false;
}
