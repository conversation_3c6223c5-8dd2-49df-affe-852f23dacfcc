package com.bizark.op.common.config;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.bizark.op.common.annotation.IgnoreDisabled;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * mybatisPlus 逻辑删除过滤器
 */

@Component
@Intercepts({@Signature(
    type = Executor.class,
    method = "query",
    args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}
)})
@Slf4j
public class SqlIgnoreDisabledInterceptor implements Interceptor {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameter = args[1];
        try {
            if (SqlCommandType.SELECT.equals(ms.getSqlCommandType())) {
                //获取配置文件中最原始的sql
                BoundSql boundSql = ms.getBoundSql(parameter);
                //找到mapper
                Class<?> clazz = Class.forName(ms.getId().substring(0, ms.getId().lastIndexOf(".")));
                String sql = boundSql.getSql();
                //当mapper上包含 自定义注解并且 sql中 where子句 不包含 disabled_name 字段时 添加 disabled_name 为 NULL 条件
                if (clazz.isAnnotationPresent(IgnoreDisabled.class) && StrUtil.isNotBlank(sql) && !StrUtil.containsIgnoreCase(sql, "join")) {
                    if (StrUtil.containsIgnoreCase(sql, "WHERE")) {
                        String[] sqlArr = sql.split("(?i)WHERE");
                        if (ArrayUtil.isNotEmpty(sqlArr) && !StrUtil.containsIgnoreCase(sqlArr[1], "disabled_name")) {
                            sql = sqlArr[0] + "WHERE disabled_name is NULL AND" + sqlArr[1];
                        }
                    } else {
                        sql = sql + "WHERE disabled_name is NULL";
                    }

                    //重新设置 sql
                    Field field = boundSql.getClass().getDeclaredField("sql");
                    field.setAccessible(true);
                    field.set(boundSql, sql);
                }
            }
        } catch (Exception e) {
            logger.error("replace sql error ", e);
        }

        return invocation.proceed();
    }

}
