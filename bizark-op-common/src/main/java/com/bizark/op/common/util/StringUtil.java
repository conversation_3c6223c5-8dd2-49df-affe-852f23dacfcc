package com.bizark.op.common.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class StringUtil {

    public static boolean isEmpty(String str) {
        if (str == null || str.isEmpty() || str.trim().length() == 0 || "undefined".equals(str) || "null".equals(str)) {
            return true;
        }
        return false;
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static boolean hasStr(String sourceStr, String str) {
        int result = sourceStr.indexOf(str);
        if (result != -1) {
            return true;
        }

        return false;
    }

    /**
     * 判断字符串是否相等
     *
     * @param str1
     * @param str2
     * @return
     */
    public static boolean isEquals(String str1, String str2) {
        if (isEmpty(str1) && isEmpty(str2)) {
            return true;
        }

        if (isNotEmpty(str1)) {
            return str1.equalsIgnoreCase(str2);
        }

        if (isNotEmpty(str2)) {
            return str2.equalsIgnoreCase(str1);
        }

        return false;
    }

    /**
     * 获取传入时间所对应的年月日时间 .
     *
     * @param date 当前时间 .
     * @return 返回字符串格式 yyyy-MM-dd .
     */
    public static String getDateString(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(date);
        return dateString;
    }

    /**
     * 获取传入时间所对应的年月日时间 .
     *
     * @param date 当前时间 .
     * @return 返回字符串格式 yyyyMMdd .
     */
    public static String getRelevantDate(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateString = formatter.format(date);
        return dateString;
    }

    /**
     * 获取传入时间所对应的时分秒时间 .
     *
     * @param date 当前时间 .
     * @return 返回字符串格式 HHmmss .
     */
    public static String getRelevantTime(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("HHmmss");
        String dateString = formatter.format(date);
        return dateString;
    }

    /**
     * 字符串补位.
     *
     * @param val   字符串 .
     * @param type  r右补位l左补位 .
     * @param size  补足长度 .
     * @param delim 补位用字符串 .
     * @return String .
     */
    public static String pad(String val, String type, int size, String delim) {
        if (isEmpty(val))
            return val;
        if ("r".equals(type)) {
            if (val.length() >= size) {
                return val.substring(0, size);
            } else {
                return val + repeat('r', delim, size - val.length());
            }
        } else {
            if (val.length() >= size) {
                return val.substring(val.length() - size);
            } else {
                return repeat('l', delim, size - val.length()) + val;
            }
        }
    }

    /**
     * 重复字符串 .
     *
     * @param type r右补位l左补位 .
     * @param val  字符串 .
     * @param len  长度 .
     * @return .
     */
    public static String repeat(char type, String val, int len) {
        if (isEmpty(val))
            return val;
        StringBuffer tBuffer = new StringBuffer();
        while (tBuffer.length() < len) {
            tBuffer.append(val);
        }

        if ('r' == type) {
            return tBuffer.substring(0, len);
        } else {
            return tBuffer.substring(tBuffer.length() - len);
        }

    }

    public static String paserMaptoStr(Map<String, Object> dataMap) {
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            sb.append("&").append(entry.getKey()).append("=").append(entry.getValue());
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(0);
        }
        return sb.toString();
    }

    /**
     * 将list转成字符串
     *
     * @param list
     * @return
     */
    public static String paserListToStr(List<String> list, String regex) {
        StringBuffer sb = new StringBuffer();
        if (null != list && !list.isEmpty()) {
            for (String str : list) {
                sb.append(str).append(regex);
            }
        } else {
            return null;
        }
        return sb.substring(0, sb.lastIndexOf(regex));
    }

    /**
     * 将字符串转成Map
     *
     * @param respStr
     * @return
     */
    public static Map<String, String> paserStrtoMap(String respStr) {
        Map<String, String> data = new HashMap<String, String>();
        if (isNotEmpty(respStr)) {
            String[] strs = respStr.split("&");
            for (String str : strs) {
                if (isEmpty(str)) {
                    continue;
                }
                int index = str.indexOf("=");
                data.put(str.substring(0, index), str.substring(index + 1));
            }
        }
        return data;
    }

    /**
     * 转换成url参数 .
     *
     * @param map       .
     * @param isSort    .
     * @param removeKey .
     * @return String .
     * <AUTHOR> @serial .
     * @since 2012-10-11 上午09:01:57 .
     */
    public static String getURLParam(Map map, boolean isSort, Set removeKey) {
        StringBuffer param = new StringBuffer();
        List msgList = new ArrayList();
        for (Iterator it = map.keySet().iterator(); it.hasNext(); ) {
            String key = (String) it.next();
            String value = (String) map.get(key);
            if (removeKey != null && removeKey.contains(key)) {
                continue;
            }
            msgList.add(key + "=" + value);
        }

        if (isSort) {
            // 排序
            Collections.sort(msgList);
        }

        for (int i = 0; i < msgList.size(); i++) {
            String msg = (String) msgList.get(i);
            if (i > 0) {
                param.append("&");
            }
            param.append(msg);
        }

        return param.toString();
    }

    /**
     * 将字符串转换为Unicode码
     *
     * @param zhStr
     * @return
     */
    public static String toUnicode(String zhStr) {
        StringBuffer unicode = new StringBuffer();
        for (int i = 0; i < zhStr.length(); i++) {
            char c = zhStr.charAt(i);
            unicode.append("\\u" + Integer.toHexString(c));
        }
        return unicode.toString();
    }

    /**
     * 将Unicode码转换为中文
     *
     * @param unicode
     * @return
     */
    public static String tozhCN(String unicode) {
        StringBuffer gbk = new StringBuffer();
        String hex[] = unicode.split("\\\\u");
        for (int i = 1; i < hex.length; i++) { // 注意要从 1 开始，而不是从0开始。第一个是空。
            int data = Integer.parseInt(hex[i], 16); // 将16进制数转换为 10进制的数据。
            gbk.append((char) data); // 强制转换为char类型就是我们的中文字符了。
        }
        return gbk.toString();
    }

    /**
     * 获取编码字符集
     *
     * @param request
     * @param response
     * @return String
     */

    public static String getCharacterEncoding(HttpServletRequest request, HttpServletResponse response) {

        if (null == request || null == response) {
            return "gbk";
        }

        String enc = request.getCharacterEncoding();
        if (null == enc || "".equals(enc)) {
            enc = response.getCharacterEncoding();
        }

        if (null == enc || "".equals(enc)) {
            enc = "gbk";
        }

        return enc;
    }

    public static String getParamsStr(Map paramMap) {
        StringBuffer stringBuffer = new StringBuffer();
        Set set = paramMap.entrySet();
        Iterator iterator = set.iterator();
        while (iterator.hasNext()) {
            Map.Entry entry = (Map.Entry) iterator.next();
            String k = (String) entry.getKey();
            String[] v = (String[]) entry.getValue();
            for (String s : v) {
                stringBuffer.append(k + "=" + s + "&");
            }
        }
        return stringBuffer.toString().substring(0, stringBuffer.toString().length() - 1);
    }

    public static String urlEncode(String url) {
        String urlEncode = "";
        try {
            urlEncode = URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return urlEncode;
    }

    /**
     * 随机生成由数字、字母组成的N位验证码
     *
     * @return 返回一个字符串
     */
    public static String getRandomCode(int n) {
        char arr[] = new char[n];
        int i = 0;
        while (i < n) {
            char ch = (char) (int) (Math.random() * 124);
            if (ch >= 'A' && ch <= 'Z' || ch >= 'a' && ch <= 'z' || ch >= '0' && ch <= '9') {
                arr[i++] = ch;
            }
        }
        //将数组转为字符串
        return new String(arr);
    }

    /**
     * 将boolean转成string
     *
     * @param bl
     * @return
     */
    public static String getBooleanStr(Boolean bl) {
        return null != bl ? bl.toString() : null;
    }

    /**
     * 将字符串的首字母转大写
     *
     * @param str 需要转换的字符串
     * @return
     */
    public static String captureName(String str) {
        // 进行字母的ascii编码前移，效率要高于截取字符串进行转换的操作
        char[] cs = str.toLowerCase().toCharArray();
        cs[0] -= 32;
        return String.valueOf(cs);
    }

    /**
     * 复制指定长度的字符串数组，不足部分赋值空
     *
     * @param strings
     * @param length
     * @return
     */
    public static String[] copyStringArray(String[] strings, int length) {
        String[] newStringArray = new String[length];
        if (strings.length == length) {
            return strings;
        } else {
            for (int i = 0; i < length; i++) {
                if (i < strings.length) {
                    newStringArray[i] = strings[i];
                } else {
                    newStringArray[i] = "";
                }
            }
            return newStringArray;
        }
    }

    public static String getMD5Str(String str) {
        return DigestUtils.md5DigestAsHex(str.getBytes());
    }

    /**
     * 生成文件名使用日期+4位随机数
     *
     * @param fileName 文件名称
     */
    public static String createObsFileName(String fileName) {
        String fileSuffix = fileName.substring(fileName.lastIndexOf("."));
        String time = getRelevantDate(new Date());
        Integer num = new Random().nextInt(9999);
        return time + num + fileSuffix;
    }

    /**
     * 获取网页源代码
     *
     * @param pageUrl
     * @return
     */
    public static String getPageSource(String pageUrl) {
        StringBuffer sb = new StringBuffer();
        try {
            //构建一URL对象
            URL url = new URL(pageUrl);
            HttpURLConnection uc = (HttpURLConnection) url.openConnection();
            String encoding = uc.getContentType();
            System.out.println(encoding);
            encoding = encoding.substring(encoding.indexOf("charset=") + 8).trim();
            System.out.println(encoding);
            //uc.setRequestProperty("User-Agent", "Mozilla/4.0 compatible; MSIE 5.0;Windows NT; DigExt)");
            //使用openStream得到一输入流并由此构造一个BufferedReader对象
            BufferedReader in = new BufferedReader(new InputStreamReader(uc.getInputStream(), encoding));
            String line;
            //读取www资源
            while ((line = in.readLine()) != null) {
                sb.append(line);
            }
            in.close();
            uc.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    public static String getPageSourceWithoutHtml(String pageUrl) {
        final String regEx_script = "";
        final String regEx_style = "";
        final String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式
        final String regEx_space = "\\s*|\t|\r|\n";//定义空格回车换行符
        String htmlStr = getPageSource(pageUrl);
        Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
        Matcher m_script = p_script.matcher(htmlStr);
        htmlStr = m_script.replaceAll(""); // 过滤script标签
        Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
        Matcher m_style = p_style.matcher(htmlStr);
        htmlStr = m_style.replaceAll(""); // 过滤style标签
        Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
        Matcher m_html = p_html.matcher(htmlStr);
        htmlStr = m_html.replaceAll(""); // 过滤html标签
        Pattern p_space = Pattern.compile(regEx_space, Pattern.CASE_INSENSITIVE);
        Matcher m_space = p_space.matcher(htmlStr);
        htmlStr = m_space.replaceAll(""); // 过滤空格回车标签
        htmlStr = htmlStr.trim(); // 返回文本字符串
        htmlStr = htmlStr.replaceAll(" ", "");
        htmlStr = htmlStr.substring(0, htmlStr.indexOf("。") + 1);
        return htmlStr;
    }

    /**
     * String字符串相加，并返回为String类型
     *
     * @param
     * @return
     */
    public static String stringNumberSum(String num, String numtwo) {
        try {
            Integer sumResult = Integer.parseInt(StrUtil.nullToDefault(num, "0")) + Integer.parseInt(StrUtil.nullToDefault(numtwo, "0"));
            return sumResult.toString();
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * String字符串相加，并返回为String类型
     *
     * @param
     * @return
     */
    public static String stringNumberSum(String num, String numTwo, String numThree) {
        try {
            Integer sumResult = Integer.parseInt(StrUtil.nullToDefault(num, "0")) + Integer.parseInt(StrUtil.nullToDefault(numTwo, "0")) + Integer.parseInt(StrUtil.nullToDefault(numThree, "0"));
            return sumResult.toString();
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public static void main(String[] args) throws Exception {
        System.out.println(getPageSource("https://www.amazon.com/gp/customer-reviews/R2AW1BGILWGBOX"));
        // System.out.println(urlEncode("http://app.jr-info.cn/ld_dev/modules/wx/wx1050/bargin_center.screen"));
        // String encodeStr = DigestUtils.md5Hex("LEDAO2018");
        /*MessageDigest md5 = MessageDigest.getInstance("MD5");
        BASE64Encoder base64en = new BASE64Encoder();
        String str = "LEDAO2018";
        String encodeStr = base64en.encode(md5.digest(str.getBytes("utf-8")));
        System.out.println("MD5加密后的字符串为:encodeStr=" + encodeStr);*/
    }

    /**
     *  去除某一对象字属性中的字符串的前后空格
     */
    public static void trimStringFields(Object object) {
        if (object == null) {
            return;
        }
        Class<?> clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (field.getType() == String.class) {
                try {
                    field.setAccessible(true);
                    String value = (String) field.get(object);
                    if (value != null) {
                        field.set(object, value.trim());
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     *  判断集装箱号是否合法
     * @param containerNo 判断集装箱号是否合法
     * @return
     */
    public static boolean verifyContainerNumber(String containerNo) {
        return containerNo != null && containerNo.matches("^[A-Z]{4}\\d{7}$");
    }

    /**
     * 对某对象中的特殊字符进行转义
     * @param obj 任意对象
     */
    public static void escapeXmlFields(Object obj) {
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.getType() == String.class) {
                try {
                    String value = (String) field.get(obj);
                    if (value != null) {
                        value = escapeXmlCharacters(value);
                        field.set(obj, value);
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                    log.error("escapeXmlFields error", e);
                }
            }
        }
    }

    private static String escapeXmlCharacters(String input) {
        if (input == null) {
            return null;
        }
        return input.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&apos;");
    }
}
