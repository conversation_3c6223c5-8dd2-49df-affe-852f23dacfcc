package com.bizark.op.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.bizark.op.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * BigDecimal 工具类
 *
 * <AUTHOR>
 * @date 2023/6/7 - 16:47
 */
@Slf4j
public class BigDecimalUtils {

    private static final int scale = 8;

    /**
     * 比较
     */
    public static Integer compareTo(BigDecimal value1, BigDecimal value2) {
        if (ObjectUtil.isNull(value1)) {
            value1 = BigDecimal.ZERO;
        }

        if (ObjectUtil.isNull(value2)) {
            value2 = BigDecimal.ZERO;
        }
        return value1.compareTo(value2);
    }

    /**
     * 比较 是否为0（null 默认为0）
     */
    public static boolean isZero(BigDecimal value) {
        return compareTo(value, BigDecimal.ZERO) == 0;
    }

    /**
     * 加
     */
    public static BigDecimal add(BigDecimal value1, BigDecimal value2) {
        if (ObjectUtil.isNull(value1)) {
            value1 = BigDecimal.ZERO;
        }

        if (ObjectUtil.isNull(value2)) {
            value2 = BigDecimal.ZERO;
        }
        return value1.add(value2);
    }

    /**
     * 加
     */
    public static BigDecimal add(BigDecimal value1, BigDecimal value2, Integer scale) {
        return add(value1, value2).setScale(ObjectUtil.isNull(scale) ? scale : 2, RoundingMode.HALF_UP);
    }

    /**
     * 减
     */
    public static BigDecimal subtract(BigDecimal value1, BigDecimal value2) {
        if (ObjectUtil.isNull(value1)) {
            value1 = BigDecimal.ZERO;
        }

        if (ObjectUtil.isNull(value2)) {
            value2 = BigDecimal.ZERO;
        }
        return value1.subtract(value2);
    }

    /**
     * 乘
     */
    public static BigDecimal multiply(BigDecimal value1, BigDecimal value2) {
        return multiply(value1, value2, null);
    }

    /**
     * 乘
     */
    public static BigDecimal multiply(BigDecimal value1, BigDecimal value2, Integer scale) {
        scale = ObjectUtil.isNull(scale) ? BigDecimalUtils.scale : scale;
        if (ObjectUtil.isNull(value1)) {
            return BigDecimal.ZERO;
        }

        if (ObjectUtil.isNull(value2)) {
            return BigDecimal.ZERO;
        }
        return value1.multiply(value2).setScale(scale, RoundingMode.HALF_UP);
    }

    public static BigDecimal multiply(BigDecimal decimal, Integer num) {
        if (null == decimal || BigDecimal.ZERO.compareTo(decimal) == 0) {
            return BigDecimal.ZERO;
        }
        if (null == num || 0 == num) {
            return BigDecimal.ZERO;
        }

        return decimal.multiply(new BigDecimal(num)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 转化为bigdecimal
     */
    public static BigDecimal intConvertToBigDecimal(Integer value) {
        if (ObjectUtil.isNull(value)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(value);
    }

    /**
     * 转化为bigdecimal
     */
    public static BigDecimal strConvertToBigDecimal(String value) {
        if (StrUtil.isBlank(value)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(value);
    }

    /**
     * 除
     */
    public static BigDecimal divide(BigDecimal value1, BigDecimal value2) {
        return divide(value1, value2, null);
    }

    /**
     * 除
     *
     * @param scale 保留的位数 默认8位
     */
    public static BigDecimal divide(BigDecimal value1, BigDecimal value2, Integer scale) {
        scale = ObjectUtil.isNull(scale) ? BigDecimalUtils.scale : scale;
        if (ObjectUtil.isNull(value1)) {
            value1 = BigDecimal.ZERO;
        }

        if (ObjectUtil.isNull(value2) || BigDecimal.ZERO.compareTo(value2) == 0) {
            return BigDecimal.ZERO;
        }
        return value1.divide(value2, scale, RoundingMode.HALF_UP);
    }

    /**
     * 除
     */
    public static BigDecimal negate(BigDecimal value) {
        if (ObjectUtil.isNull(value)) {
            return BigDecimal.ZERO;
        }

        return value.negate();
    }

    /**
     * 除
     */
    public static BigDecimal abs(BigDecimal value) {
        if (ObjectUtil.isNull(value)) {
            return BigDecimal.ZERO;
        }

        return value.abs();
    }

    /**
     * 保留位数
     */
    public static BigDecimal setScale(BigDecimal value) {
        if (ObjectUtil.isNull(value)) {
            return BigDecimal.ZERO;
        }
        return setScale(value, 2, RoundingMode.HALF_UP);
    }

    /**
     * 保留位数
     */
    public static BigDecimal setScale(BigDecimal value, int scale, RoundingMode roundingMode) {
        if (ObjectUtil.isNull(value)) {
            return BigDecimal.ZERO;
        }
        return value.setScale(scale, roundingMode);
    }


    /**
     * 避免空指针计算
     *
     * @param bigDecimal
     * @return {@link BigDecimal}
     */
    public static BigDecimal removeNullBigDecimal(BigDecimal bigDecimal) {
        if (StringUtils.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    /**
     * 正数校验
     *
     * @param number
     * @return boolean
     */
    public static BigDecimal isPositiveNumber(String number, Boolean isPositive) {
        try {
            if (StringUtils.isNotEmpty(number)) {
                BigDecimal bd = new BigDecimal(number);
                //是否允许为负数
                if (StringUtils.isNotNull(isPositive) && Boolean.TRUE.equals(isPositive)) {
                    return bd;
                } else {
                    if (bd.compareTo(BigDecimal.ZERO) < 0) {
                        throw new CustomException("参数不可为负数!");
                    } else {
                        return bd;
                    }
                }
            } else {
                return BigDecimal.ZERO;
            }
        } catch (Exception e) {
            throw new CustomException("参数无法转换,请检查参数!");
        }
    }

    /**
     * 正数校验
     *
     * @param number
     * @return boolean
     */
    public static BigDecimal isPositiveNumber(BigDecimal number, Boolean isPositive) {
        try {
            if (StringUtils.isNotNull(number)) {
                //是否允许为负数
                if (StringUtils.isNotNull(isPositive) && Boolean.TRUE.equals(isPositive)) {
                    return number;
                } else {
                    if (number.compareTo(BigDecimal.ZERO) < 0) {
                        throw new CustomException("参数不可为负数!");
                    } else {
                        return number;
                    }
                }
            } else {
                return BigDecimal.ZERO;
            }
        } catch (Exception e) {
            throw new CustomException("参数无法转换,请检查参数!");
        }
    }

    /**
     * 校验是否为0或者为负数
     *
     * @param data
     * @return {@link BigDecimal}
     */
    public static Boolean checkBigDecimalGreaterThanZero(String data) {
        if (StringUtils.isNull(data)) {
            return Boolean.FALSE;
        }
        try {
            BigDecimal bigDecimal = new BigDecimal(data);
            if (bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            return Boolean.FALSE;
        }

    }

    /**
     * 校验是否为0或者为负数
     *
     * @param data
     * @return {@link BigDecimal}
     */
    public static Boolean checkBigDecimalGreaterThanZero(BigDecimal data) {
        if (StringUtils.isNull(data)) {
            return Boolean.FALSE;
        }
        try {
            if (data.compareTo(BigDecimal.ZERO) <= 0) {
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

    /**
     * string 转化为 BigDecimal( 如果为空或value无法转换则默认 0 )
     */
    public static BigDecimal strConvertBigDecimal(String value) {
        if (StringUtils.isEmpty(value)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }


    /**
     * 根据占比分配数据
     *
     * @param totalAmount 总计需要分配的
     * @param volumes     需要分配的占比 key为对应的唯一标识,用于获取分配后的数据 value为计算占比的数据
     * @return {@link Map}<{@link String}, {@link BigDecimal}> key为对应的唯一标识,用于获取分配后的数据 value为分配的金额
     */
    public static Map<String, BigDecimal> allocateAmount(BigDecimal totalAmount, Map<String, BigDecimal> volumes) {
        // 计算总体积
        BigDecimal totalVolume = volumes.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        // 分配金额的结果Map
        Map<String, BigDecimal> allocatedAmounts = new HashMap<>();

        // 遍历每个体积，根据体积占比计算分配金额
        for (Map.Entry<String, BigDecimal> entry : volumes.entrySet()) {
            String key = entry.getKey();
            BigDecimal volume = entry.getValue();

            BigDecimal percentage = volume.divide(totalVolume, 2, RoundingMode.HALF_EVEN);
            BigDecimal allocatedAmount = totalAmount.multiply(percentage);

            allocatedAmounts.put(key, allocatedAmount);
        }

        return allocatedAmounts;
    }


    /**
     * 将某个对象中包含BigDecimal的属性使用特定模式保留指定位数
     * 且会递归设置里面的列表对象 直到没有下一层
     *
     * @param obj   指定对象
     * @param scale 位数
     * @param mode  模式
     */
    public static void roundBigDecimalProperties(Object obj, int scale, RoundingMode mode) {
        if (obj == null) {
            return;
        }
        // 针对对象是列表的情况 遍历+递归
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object element : list) {
                roundBigDecimalProperties(element, scale, mode);
            }
        } else if (obj instanceof Set) {// Set会有npe异常 暂时不做处理
            return;
        } else {
            Class<?> clazz = obj.getClass();
            Field[] fields = clazz.getDeclaredFields();
            // 如果直接是一个对象 则遍历属性
            for (Field field : fields) {
                // 直接可访问到BigDecimal的属性 则保留指定位数
                if (BigDecimal.class.isAssignableFrom(field.getType())) {
                    try {
                        field.setAccessible(true);
                        BigDecimal value = (BigDecimal) field.get(obj);
                        if (value != null) {
                            BigDecimal roundedValue = value.setScale(scale, mode);
                            field.set(obj, roundedValue);
                        }
                    } catch (IllegalAccessException e) {
                        log.error("设置位数出现错误,{}", e.getMessage());
                        throw new CustomException(e.getMessage());
                    }
                } else {
                    // 不能直接访问的 排除掉java.lang包下的 可能是我们自定义的对象或者集合类 则进入该对象 遍历属性
                    if (!field.getType().isPrimitive() && !field.getType().getPackage().getName().startsWith("java.lang")) {
                        try {
                            field.setAccessible(true);
                            Object fieldValue = field.get(obj);
                            if (fieldValue != null) {
                                roundBigDecimalProperties(fieldValue, scale, mode);
                            }
                        } catch (IllegalAccessException e) {
                            log.error("设置位数出现错误,{}", e.getMessage());
                            throw new CustomException(e.getMessage());
                        }
                    }
                }
            }
        }
    }

    /**
     * 移除对象列表中为全为0的对象
     *
     * @param list 对象列表
     * @param <T>  类型
     */
    public static <T> void removeIfAllZero(List<T> list) {
        Iterator<T> iterator = list.iterator();
        while (iterator.hasNext()) {
            T obj = iterator.next();
            if (isAllFieldsZero(obj)) {
                iterator.remove();
            }
        }
    }

    private static <T> boolean isAllFieldsZero(T obj) {
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(obj);
                if (value instanceof BigDecimal) {
                    if (BigDecimal.ZERO.compareTo((BigDecimal) value) != 0) {
                        return false;
                    }
                } else if (value instanceof Integer) {
                    if ((Integer) value != 0) {
                        return false;
                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return true;
    }


    public static int removeNullInteger(Integer data) {
        return StringUtils.isNull(data) ? 0 : data;
    }
}
