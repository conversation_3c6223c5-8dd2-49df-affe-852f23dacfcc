package com.bizark.op.common.constant;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @remarks: xxx
 * @Author: kyle
 * @Date: 2025/1/7 14:16
 */
public class QuarterTimeDTO implements Serializable {
    private LocalDate beginDate;
    private LocalDate endDate;
    private LocalDate lastBeginLocalDate;
    private LocalDate lastEndLocalDate;
    private LocalDateTime lastBeginDate;
    private LocalDateTime lastEndDate;

    public QuarterTimeDTO() {
    }

    public LocalDate getBeginDate() {
        return this.beginDate;
    }

    public void setBeginDate(LocalDate beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDate getEndDate() {
        return this.endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public LocalDateTime getLastBeginDate() {
        return this.lastBeginDate;
    }

    public void setLastBeginDate(LocalDateTime lastBeginDate) {
        this.lastBeginDate = lastBeginDate;
    }

    public LocalDateTime getLastEndDate() {
        return this.lastEndDate;
    }

    public void setLastEndDate(LocalDateTime lastEndDate) {
        this.lastEndDate = lastEndDate;
    }

    public LocalDate getLastBeginLocalDate() {
        return this.lastBeginLocalDate;
    }

    public void setLastBeginLocalDate(LocalDate lastBeginLocalDate) {
        this.lastBeginLocalDate = lastBeginLocalDate;
    }

    public LocalDate getLastEndLocalDate() {
        return this.lastEndLocalDate;
    }

    public void setLastEndLocalDate(LocalDate lastEndLocalDate) {
        this.lastEndLocalDate = lastEndLocalDate;
    }
}