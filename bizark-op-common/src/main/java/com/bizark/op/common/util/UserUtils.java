package com.bizark.op.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.framework.security.AuthContextHolder;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * 获取当前用户
 */
public class UserUtils {

    private static final Logger logger = LoggerFactory.getLogger(UserUtils.class);

    public static AuthUserDetails getThisUser(Integer contextId) {
        AuthUserDetails principal = getThisUser();
        try {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(principal));
            jsonObject.put("orgId", contextId);
            return jsonObject.toJavaObject(AuthUserDetails.class);
        }catch (Exception e){
            return principal;
        }
    }
    public static AuthUserDetails getThisUser() {
        //默认返回
        AuthUserDetails authUserDetails = new AuthUserDetails() {
            @Override
            public Integer getId() {
                return 0;
            }

            @Override
            public Integer getOrgId() {
                return null;
            }

            @Override
            public String getEmail() {
                return null;
            }

            @Override
            public String getPhone() {
                return null;
            }

            @Override
            public String getName() {
                return "SYSTEM";
            }

            @Override
            public Integer getIsSuper() {
                return null;
            }

            @Override
            public Integer getIsOps() {
                return null;
            }

            @Override
            public String getRoleLabel() {
                return null;
            }

            @Override
            public Integer getDisabledAt() {
                return null;
            }
        };
        try{
            return ObjectUtils.isEmpty(SecurityUtils.getSubject().getPrincipal())? authUserDetails :(AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
        }catch (Exception e){
            logger.error("获取当前用户失败!",e);
            return authUserDetails;
        }
    }

    public static String getCurrentUserName() {
        AuthUserDetails userDetails = AuthContextHolder.getAuthUserDetails();
        if (userDetails == null) {
            return null;
        }
        return userDetails.getName();
    }
    public static String getCurrentUserName(String defaultUserName) {
        AuthUserDetails userDetails = AuthContextHolder.getAuthUserDetails();
        if (userDetails == null) {
            return defaultUserName;
        }
        return userDetails.getName();
    }
    public static Integer getCurrentUserId() {
        AuthUserDetails userDetails = AuthContextHolder.getAuthUserDetails();
        if (userDetails == null) {
            return null;
        }
        return userDetails.getId();
    }
    public static Integer getCurrentUserId(Integer defaultUserId) {
        AuthUserDetails userDetails = AuthContextHolder.getAuthUserDetails();
        if (userDetails == null) {
            return defaultUserId;
        }
        return userDetails.getId();
    }
}
