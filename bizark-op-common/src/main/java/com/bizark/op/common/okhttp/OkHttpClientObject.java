package com.bizark.op.common.okhttp;

import okhttp3.OkHttpClient;

import java.util.concurrent.TimeUnit;

/**
 * @description: 采用枚举的方式创建线程安全的okHttpClient单例
 * @<PERSON> huang<PERSON>hui
 * @Date 2022/6/9 15:14
 */
public enum OkHttpClientObject {

    /** client */
    CLIENT;

    private final OkHttpClient clientInstance;

    OkHttpClientObject() {
        int connectTimeoutTime = 10;
        int writeTimeoutTime = 10;
        int readTimeoutTime = 30;
        clientInstance = new OkHttpClient.Builder()
                .connectTimeout(connectTimeoutTime, TimeUnit.SECONDS)
                .writeTimeout(writeTimeoutTime, TimeUnit.SECONDS)
                .readTimeout(readTimeoutTime, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    public OkHttpClient getClientInstance() {
        return clientInstance;
    }
}
