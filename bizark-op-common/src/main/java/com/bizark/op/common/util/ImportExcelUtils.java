package com.bizark.op.common.util;

import com.bizark.op.common.exception.CustomException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @Date 2023/5/29 11:43
 */
public class ImportExcelUtils {

    private static final Logger logger = LoggerFactory.getLogger(ImportExcelUtils.class);

    /**
     * @param excelFile
     * @param clazz
     * @return
     */
    public static <T> List<T> checkExcelAndGetData(MultipartFile excelFile, Class<T> clazz) {
        String fileName = excelFile.getOriginalFilename();
        String suffixName = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!"xls".equals(suffixName) && !"xlsx".equals(suffixName)) {
            throw new CustomException("请选择xls或xlsx后缀名的文件!");
        }

        List<T> importFormList = new ArrayList<>();
        try {
            ImportExcel excel = new ImportExcel();
            InputStream inputStream = excelFile.getInputStream();
            importFormList = excel.parseExcelToClass(inputStream, clazz, 1, 0, 0);
        } catch (Exception e) {
            logger.error("导入报错",e);
            throw new CustomException("导入报错：" + clazz.getName() + e.getMessage());
        }
        if (CollectionUtils.isEmpty(importFormList)) {
            throw new CustomException("excel数据为空，未解析到填写的数据!");
        }
        return importFormList;
    }
}
