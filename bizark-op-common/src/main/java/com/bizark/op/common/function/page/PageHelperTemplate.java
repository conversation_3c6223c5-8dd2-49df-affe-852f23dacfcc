package com.bizark.op.common.function.page;

import com.bizark.op.common.constant.HttpStatus;
import com.bizark.op.common.core.page.PageData;
import com.bizark.op.common.core.page.PageInfoDomain;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.core.page.TableSupportInfo;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.sql.SqlUtil;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 分页模板
 * <AUTHOR>
 * @Date 2023/3/18 16:53
 */
@Component
public class PageHelperTemplate<DTO, VO> {

    public TableDataInfo<VO> execute(PageHelper<DTO, VO> pageHelper) {
        PageInfoDomain pageInfoDomain = TableSupportInfo.buildPageRequest();
        Integer page = pageInfoDomain.getPage();
        Integer rows = pageInfoDomain.getRows();

        if (StringUtils.isNotNull(page) && StringUtils.isNotNull(rows))
        {
            String orderBy = SqlUtil.escapeOrderBySql(pageInfoDomain.getOrderBy());
            com.github.pagehelper.PageHelper.startPage(page, rows, orderBy);
        }
        TableDataInfo<VO> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setStatusCode(HttpStatus.SUCCESS);
        rspData.setStatus(0);
        rspData.setMessage("查询成功");

        PageData<VO> pageData = new PageData<>();

        List<VO> execute = pageHelper.execute((p) -> {
            PageInfo<DTO> pageInfo = new PageInfo<>(p);
            pageData.setTotalElements(pageInfo.getTotal());
            pageData.setSize(pageInfo.getPageSize());
            pageData.setTotalPages(pageInfo.getPages());
            pageData.setFirst(pageInfo.isIsFirstPage());
            pageData.setLast(pageInfo.isIsLastPage());
            rspData.setData(pageData);
            rspData.setTotal(pageInfo.getTotal());
        });
        pageData.setContent(execute);
        return rspData;
    }
}
