package com.bizark.op.common.okhttp;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @Date 2022/6/15 11:09
 */
public class OkhttpRequestBase {

    protected static final Logger logger = LoggerFactory.getLogger(OkhttpRequestBase.class);

    /**
     * json传输方式
     */
    private final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    /**
     * xml传输方式
     */
    private final MediaType XML = MediaType.parse("application/xml");

    /**
     * 获取okHttpClient对象
     */
    private final OkHttpClient client = OkHttpClientObject.CLIENT.getClientInstance();

    /**
     * get形式,同步执行
     */
    public String getCall(String url, Map<String, String> headers) throws IOException {
        //创建请求
        Request.Builder builder = new Request.Builder();
        builder.header("User-Agent", "*****")
                .header("Content-Type", "application/json");
        if (!CollectionUtils.isEmpty(headers)) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                builder.header(entry.getKey(), entry.getValue());
            }
        }
        //同步执行请求，将响应结果存放到response中
        return client.newCall(builder.url(url).get().build()).execute().body().string();
    }

    /**
     * post形式
     */
    public String postCall(String url, String json, Map<String, String> headers) throws IOException {
        //创建请求
        Request.Builder builder = new Request.Builder();
        builder.header("User-Agent", "*****")
                .header("Content-Type", "application/json");
        if (!CollectionUtils.isEmpty(headers)) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                builder.header(entry.getKey(), entry.getValue());
            }
        }
        //同步请求
        return client.newCall(
                builder.url(url).post(RequestBody.create(JSON, json)).build()).execute().body().string();
    }

    /**
     * post形式 xml
     */
    public String postXmlCall(String url, String requestXml) throws IOException {
        //创建请求
        Request.Builder builder = new Request.Builder();
        builder.header("User-Agent", "*****")
                .header("Content-Type", "application/xml")
                .addHeader("Accept-Charset", "charset=urf-8")
                .addHeader("SOAPAction", "application/soap+xml")
                .addHeader("Cookie", "EASSESSIONID=300777419; JSESSIONID=yMgBOhrsZDZaC9M8nWr_LkBBkvTUschTKnQA; NAPRoutID=300777419");
        //同步请求
        return client.newCall(
                builder.url(url).post(RequestBody.create(XML, requestXml)).build()).execute().body().string();
    }
}
