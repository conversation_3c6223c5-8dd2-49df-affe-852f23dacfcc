package com.bizark.op.common.annotation;

import java.lang.annotation.*;

/**
 * 自定义注解，Excel导入多表字段映射使用
 * 该注解调用 Excel.readExcel(MultipartFile file,Class<T> clazz)时生效
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelMapping {

    /**
     * Service类名称，查询数据库使用
     * @return
     */
    Class<?> service() ;

    /**
     * 当前类与查询类关联关系
     * @return
     */
    String relation();

    /**
     * 查询类需要获取的字段
     * @return
     */
    String mappingField();




    String orgField() default "";

}
