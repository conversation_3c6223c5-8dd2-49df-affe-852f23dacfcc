package com.bizark.op.common.annotation;



import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义输出接口调用请求参数(支持控制层与业务层方法)
 * <AUTHOR>
 * @date 2024-03-06 14:29:01
 */

@Target({ ElementType.PARAMETER, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogLocalPrint
{
    /**
     * 输出的标识语句
     */
     String title() default "";


    /**
     * 需要过滤的敏感数据
     */
     String[] excludeParamNames() default {};
}
