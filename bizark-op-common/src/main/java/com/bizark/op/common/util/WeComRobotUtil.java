package com.bizark.op.common.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.bizark.op.common.config.WeChatBootConfigure;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 23/12/2022  上午11:27
 * @description: 企业微信 群聊机器人工具类
 */
public class WeComRobotUtil {
    private static Logger log = LoggerFactory.getLogger(WeComRobotUtil.class);

    private static final String ROBOT_FOLLOW_WEB_HOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=82f066be-0b43-44ae-8ed7-1e1436b517ab";
    private static final String ROBOT_ATD_WEB_HOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0024d528-fddb-47d2-889b-1ec0ad7d9269";
    private static final String ROBOT_PROFIT_WEB_HOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1118fc1b-5548-400e-bef5-6f3556257b27";
    private static final String ROBOT_PROFIT_OPERATION_HOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=249fee45-cb69-4061-9df8-f0785ebd50cb";

    private static final String ROBOT_PROFIT_OPERATION_EXTRA_HOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4c9f31b8-429f-47e9-ad1f-f0adb8623277";

    private static final String ROBOT_ORDER_TRACK_HOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bf859128-d8f0-4632-b1d0-30307086ba3d";



    /**
     * 发送群聊文本信息
     *
     * @param msg
     * @param webHookType 类型
     */
    @Deprecated
    public static void sendTextMsg(String msg,String webHookType) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("msgtype", "text");
            JSONObject jsonObjectContent = new JSONObject();
            jsonObjectContent.put("content", msg);
            jsonObject.put("text", jsonObjectContent);
            if (webHookType.equals("FOLLOW")){
                HttpUtils.doPostByForm(ROBOT_FOLLOW_WEB_HOOK, jsonObject.toJSONString());
            }else if (webHookType.equals("ATD")){
                HttpUtils.doPostByForm(ROBOT_ATD_WEB_HOOK, jsonObject.toJSONString());
            } else if (webHookType.equals("PROFIT")) {
                HttpUtils.doPostByForm(ROBOT_PROFIT_WEB_HOOK, jsonObject.toJSONString());
            }else if(webHookType.equals("PROFIT_OPERATION")){
                HttpUtils.doPostByForm(ROBOT_PROFIT_OPERATION_HOOK, jsonObject.toJSONString());
            }else if(webHookType.equals("ORDER_TRACK")){
                HttpUtils.doPostByForm(ROBOT_ORDER_TRACK_HOOK, jsonObject.toJSONString());
            }else if (webHookType.equals("PROFIT_OPERATION_EXTRA")){
                HttpUtils.doPostByForm(ROBOT_PROFIT_OPERATION_EXTRA_HOOK, jsonObject.toJSONString());
            }
        } catch (Exception e) {
            log.error("发送企业微信出错!", e);
        }
    }

    /**
     * 发送群聊文本信息 指定艾特群里的人员，可指定多个
     *
     * @param msg                       发送的文本信息
     * @param mentionedListString       群聊中userId列表 "@all" 表示提醒所有人  非必填
     * @param mentionedMobileListString 手机号列表 "@all" 表示提醒所有人  非必填
     * @param webHookType 类型
     */
    @Deprecated
    public static void sendTextMsgWithAt(String msg, String mentionedListString, String mentionedMobileListString,String webHookType) {
        log.info("跟卖群消息:{}",msg);
        log.info("@用户:{}",mentionedListString);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("msgtype", "text");
            JSONObject jsonObjectContent = new JSONObject();
            jsonObjectContent.put("content", msg);
            List<String> mentionedList = null;
            List<String> mentionedMobileList = null;
            if (StringUtils.isNotBlank(mentionedListString)) {
                mentionedList = Arrays.asList(mentionedListString.split(","));
            }
            if (StringUtils.isNotEmpty(mentionedMobileListString)) {
                mentionedMobileList = Arrays.asList(mentionedMobileListString.split(","));
            }
            jsonObjectContent.put("mentioned_list", mentionedList);
            jsonObjectContent.put("mentioned_mobile_list", mentionedMobileList);
            jsonObject.put("text", jsonObjectContent);
            if (webHookType.equals("FOLLOW")){
                HttpUtils.doPostByForm(ROBOT_FOLLOW_WEB_HOOK, jsonObject.toJSONString());
            }else if (webHookType.equals("ATD")){
                HttpUtils.doPostByForm(ROBOT_ATD_WEB_HOOK, jsonObject.toJSONString());
            } else if (webHookType.equals("PROFIT")) {
                HttpUtils.doPostByForm(ROBOT_PROFIT_WEB_HOOK, jsonObject.toJSONString());
            }else if(webHookType.equals("PROFIT_OPERATION")){
                HttpUtils.doPostByForm(ROBOT_PROFIT_OPERATION_HOOK, jsonObject.toJSONString());
            }else if (webHookType.equals("PROFIT_OPERATION_EXTRA")){
                HttpUtils.doPostByForm(ROBOT_PROFIT_OPERATION_EXTRA_HOOK, jsonObject.toJSONString());
            }
        } catch (Exception e) {
            log.error("发送企业微信出错!", e);
        }
    }

    public static void sendTextMsgNew(String msg, String webHookType, WeChatBootConfigure chatBootConfigure) {
        if (chatBootConfigure == null || StrUtil.isBlank(chatBootConfigure.getWebHookUrl(webHookType))){
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("msgtype", "text");
            JSONObject jsonObjectContent = new JSONObject();
            jsonObjectContent.put("content", msg);
            jsonObject.put("text", jsonObjectContent);
            HttpUtils.doPostByForm(chatBootConfigure.getWebHookUrl(webHookType), jsonObject.toJSONString());
        } catch (Exception e) {
            log.error("发送企业微信出错!", e);
        }
    }

    public static void sendTextMsgWithAtNew(String msg, String mentionedListString, String mentionedMobileListString,String webHookType,WeChatBootConfigure chatBootConfigure) {
        if (chatBootConfigure == null || StrUtil.isBlank(chatBootConfigure.getWebHookUrl(webHookType))){
            return;
        }
        log.info("跟卖群消息:{}",msg);
        log.info("@用户:{}",mentionedListString);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("msgtype", "text");
            JSONObject jsonObjectContent = new JSONObject();
            jsonObjectContent.put("content", msg);
            List<String> mentionedList = null;
            List<String> mentionedMobileList = null;
            if (StringUtils.isNotBlank(mentionedListString)) {
                mentionedList = Arrays.asList(mentionedListString.split(","));
            }
            if (StringUtils.isNotEmpty(mentionedMobileListString)) {
                mentionedMobileList = Arrays.asList(mentionedMobileListString.split(","));
            }
            jsonObjectContent.put("mentioned_list", mentionedList);
            jsonObjectContent.put("mentioned_mobile_list", mentionedMobileList);
            jsonObject.put("text", jsonObjectContent);
            HttpUtils.doPostByForm(chatBootConfigure.getWebHookUrl(webHookType), jsonObject.toJSONString());
        } catch (Exception e) {
            log.error("发送企业微信出错!", e);
        }
    }


}
