/**
 * Copyright © 2015 - 2017 EntDIY JavaEE Development Framework
 * <p>
 * Site: https://www.entdiy.com, E-Mail: <EMAIL>
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.bizark.op.common.util;

//import com.entdiy.core.service.Validation;
//import com.entdiy.core.web.AppContextHolder;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bizark.common.exception.CommonException;
import com.bizark.op.common.constant.QuarterTimeDTO;
import com.bizark.op.common.exception.CheckException;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;

public class DateUtils {

    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDD = "yyyy/MM/dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private final static Logger logger = LoggerFactory.getLogger(DateUtils.class);

    public final static String DEFAULT_TIMEZONE = "GMT+8";

    public final static String DEFAULT_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public final static String DEFAULT_TIME_FORMAT_HOUR = "yyyy/MM/dd HH:mm";

    public final static String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    public final static String MM_DD = "MM-dd";

    public final static String SHORT_TIME_FORMAT = "yyyy-MM-dd HH:mm";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(DEFAULT_DATE_FORMAT).format(date);
    }

    public static String formatDate(Date date, String format) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(format).format(date);
    }

    public static Integer formatDateToInt(Date date, String format) {
        if (date == null) {
            return null;
        }
        return Integer.valueOf(new SimpleDateFormat(format).format(date));
    }

    public static Long formatDateToLong(Date date, String format) {
        if (date == null) {
            return null;
        }
        return Long.valueOf(new SimpleDateFormat(format).format(date));
    }

    public static String formatTime(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DEFAULT_TIME_FORMAT).format(date);
    }

    public static String formatShortTime(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(SHORT_TIME_FORMAT).format(date);
    }

    public static Date parseDate(String date, String format) {
        if (date == null) {
            return null;
        }
        try {
            return new SimpleDateFormat(format).parse(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static Date parseDate(String date, String format, Locale locale) {
        if (date == null) {
            return null;
        }
        try {
            return new SimpleDateFormat(format, locale).parse(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static Date parseTime(String date, String format) {
        if (date == null) {
            return null;
        }
        try {
            return new SimpleDateFormat(format).parse(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static Date parseDate(String date) {
        return parseDate(date, DEFAULT_DATE_FORMAT);
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return org.apache.commons.lang3.time.DateUtils.parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * @Title:getDiffDay
     * @Description:获取日期相差天数
     * @param:@param beginDate  字符串类型开始日期
     * @param:@param endDate    字符串类型结束日期
     * @param:@return
     * @return:Long 日期相差天数
     * @author:谢
     * @thorws:
     */
    public static Long getDiffDay(String beginDate, String endDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Long checkday = 0L;
        //开始结束相差天数
        try {
            checkday = (formatter.parse(endDate).getTime() - formatter.parse(beginDate).getTime()) / (1000 * 24 * 60 * 60);
        } catch (ParseException e) {

            e.printStackTrace();
            checkday = null;
        }
        return checkday;
    }

    /**
     * @Title:getDiffDay
     * @Description:获取日期相差天数
     * @param:@param beginDate Date类型开始日期
     * @param:@param endDate   Date类型结束日期
     * @param:@return
     * @return:Long 相差天数
     * @author: 谢
     * @thorws:
     */
    public static Long getDiffDay(Date beginDate, Date endDate) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String strBeginDate = format.format(beginDate);

        String strEndDate = format.format(endDate);
        return getDiffDay(strBeginDate, strEndDate);
    }

    /**
     * N天之后
     *
     * @param n
     * @param date
     * @return
     */
    public static Date nDaysAfter(Integer n, Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, cal.get(Calendar.DAY_OF_MONTH) + n);
        return cal.getTime();
    }

    /**
     * N分钟之后
     *
     * @param date
     * @param minsRange
     */
    public static String nMinsAfter(Date date, int minsRange) {

        Calendar calendar = Calendar.getInstance();
        if (null != date) {
            calendar.setTime(date);
        }
        calendar.add(Calendar.MINUTE, minsRange);

        return DateUtils.formatDate(calendar.getTime(), DEFAULT_TIME_FORMAT);
    }

    /**
     * N分钟之后
     *
     * @param minsRange
     * @return
     */
    public static String nMinsAfter(int minsRange) {
        return nMinsAfter(null, minsRange);
    }

    /**
     * N天之前
     *
     * @param n
     * @param date
     * @return
     */
    public static Date nDaysAgo(Integer n, Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, cal.get(Calendar.DAY_OF_MONTH) - n);
        return cal.getTime();
    }

    private static LocalDateTime currentDateTime;

    public static void setCurrentDateTime(LocalDateTime localDateTime) {
//        Validation.isTrue(AppContextHolder.isDevMode(), "当前操作只能在开发测试运行模式才可用");
        if (localDateTime == null) {
            currentDateTime = null;
        } else {
            currentDateTime = localDateTime;
        }
    }

    /**
     * 为了便于在模拟数据程序中控制业务数据获取到的当前时间
     * 提供一个帮助类处理当前时间，为了避免误操作，只有在devMode开发模式才允许“篡改”当前时间
     *
     * @return
     */
    public static LocalDateTime currentDateTime() {
        if (currentDateTime == null) {
            return LocalDateTime.now();
        }
        return currentDateTime;
//        if (AppContextHolder.isDevMode()) {
//            return currentDateTime;
//        } else {
//            return LocalDateTime.now();
//        }
    }

    /**
     * 蒋Date类型转换为LocalDateTime用于兼容处理一些低版本API组件
     *
     * @param date
     * @return
     */
    public static LocalDateTime convertDateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * 判断字符串格式是否正确
     *
     * @param str
     * @return
     */
    public static boolean checkStringToDate(String str) {
        boolean success = true;
        SimpleDateFormat time = new SimpleDateFormat("yyyy/MM/dd");
        try {
            time.setLenient(false);
            time.parse(str);
            String now = time.format(new Date());
            Duration duration = Duration.between(LocalDate.parse(now.replace("/", "-")).atStartOfDay(),
                    LocalDate.parse(str.replace("/", "-")).atStartOfDay());
            if (duration.toDays() < 0) {
                success = false;
            }
        } catch (ParseException e) {
            success = false;
        }
        return success;
    }

    public static boolean checkDate(LocalDate begin, LocalDate end) {
        boolean success = true;
        Duration start = Duration.between(LocalDate.now().atStartOfDay(),
                begin.atStartOfDay());
        if (start.toDays() < 0)
            success = false;
        Duration finish = Duration.between(LocalDate.now().atStartOfDay(),
                end.atStartOfDay());
        if (finish.toDays() < 0)
            success = false;
        if (Duration.between(begin.atStartOfDay(), end.atStartOfDay()).toDays() < 0)
            success = false;
        return success;
    }

    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取年
     *
     * @return Integer 年份
     */
    public static Integer getYear(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.YEAR);//获取年
    }

    /**
     * 获取月份
     *
     * @return Integer 月份
     */
    public static Integer getMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.MONTH) + 1;//获取年
    }

    /**
     * 获取日
     *
     * @return Integer 日
     */
    public static Integer getDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_MONTH);//获取日
    }

    /**
     * 获取每月天数
     *
     * @return Integer 每月天数
     */
    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 一周的第一天
     *
     * @param localDate 当地日期
     * @return {@link LocalDate}
     */
    public static LocalDate firstDayOfWeek(LocalDate localDate) {
        return localDate.with(DayOfWeek.MONDAY);
    }

    /**
     * 一周的最后一天
     *
     * @param localDate 当地日期
     * @return {@link LocalDate}
     */
    public static LocalDate lastDayOfWeek(LocalDate localDate) {
        return localDate.with(DayOfWeek.SUNDAY);
    }

    /**
     * 月的第一天
     *
     * @param localDate 当地日期
     * @return {@link LocalDate}
     */
    public static LocalDate firstDayOfMonth(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.firstDayOfMonth());
    }

    /**
     * 月的最后一天
     *
     * @param localDate 当地日期
     * @return {@link LocalDate}
     */
    public static LocalDate lastDayOfMonth(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 每年的第一天
     *
     * @param localDate 当地日期
     * @return {@link LocalDate}
     */
    public static LocalDate firstDayOfYear(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.firstDayOfYear());
    }

    /**
     * 每年的最后一天
     *
     * @param localDate 当地日期
     * @return {@link LocalDate}
     */
    public static LocalDate lastDayOfYear(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.lastDayOfYear());
    }


    /**
     * 每周的所有日期  即周一到周日
     *
     * @param localDate 当地日期
     * @return {@link List<LocalDate>}
     */
    public static List<LocalDate> allDaysOfWeek(LocalDate localDate) {
        List<LocalDate> allDays = new ArrayList<>();
        allDays.add(localDate.with(DayOfWeek.MONDAY));
        allDays.add(localDate.with(DayOfWeek.TUESDAY));
        allDays.add(localDate.with(DayOfWeek.WEDNESDAY));
        allDays.add(localDate.with(DayOfWeek.THURSDAY));
        allDays.add(localDate.with(DayOfWeek.FRIDAY));
        allDays.add(localDate.with(DayOfWeek.SATURDAY));
        allDays.add(localDate.with(DayOfWeek.SUNDAY));
        return allDays;
    }

    /**
     * 每月的所有日期  即1日到31日
     *
     * @param localDate 当地日期
     * @return {@link List<LocalDate>}
     */
    public static List<LocalDate> allDaysOfMonth(LocalDate localDate) {
        List<LocalDate> allDays = new ArrayList<>();
        LocalDate firstDayOfMonth = firstDayOfMonth(localDate);
        LocalDate lastDayOfMonth = lastDayOfMonth(localDate);
        allDays.add(firstDayOfMonth);
        int i = 1;
        LocalDate temp = firstDayOfMonth;
        while (!temp.isEqual(lastDayOfMonth)) {
            LocalDate day = firstDayOfMonth.plusDays(i);
            allDays.add(day);
            temp = day;
            i++;
        }
        return allDays;
    }

    /**
     * 将date 转化为 LocalDate
     */
    public static LocalDate dateConvertLocalDate(Date date) {
        if (ObjectUtil.isNull(date)) {
            return null;
        }
        return LocalDate.parse(formatDate(date));
    }

    /**
     * 将date 转化为 LocalDateTime
     */
    public static LocalDateTime dateConvertLocalDateTime(Date date) {
        if (ObjectUtil.isNull(date)) {
            return null;
        }
        return LocalDateTime.parse(formatDate(date, DEFAULT_TIME_FORMAT), DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT));
    }

    /**
     * 将 LocalDate 转化为 date
     */
    public static Date localDateTimeConvertDate(LocalDateTime date) {
        if (ObjectUtil.isNull(date)) {
            return null;
        }
        return parseDate(date.format(DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT)));
    }

    /**
     * 将 LocalDate 转化为 date
     */
    public static Date localDateConvertDate(LocalDate date) {
        if (ObjectUtil.isNull(date)) {
            return null;
        }
        return parseDate(date.format(DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT)));
    }

    /**
     * 将 LocalDate 转化为 date
     */
    public static String localDateConvertStr(LocalDate date) {
        if (ObjectUtil.isNull(date)) {
            return null;
        }
        return date.format(DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT));
    }

    /**
     * localdatetime -> date
     * @param localDate
     * @return
     */
    public static Date localDateTimeToDate(LocalDateTime localDate) {
        Date date = Date.from(localDate.atZone(ZoneId.systemDefault()).toInstant());
        return date;
    }

    /**
     * 判断两个时间的是否同月
     */
    public static boolean compareIsCurrentMonth(Date date1, Date date2) {
        if (ObjectUtil.isNull(date1) || ObjectUtil.isNull(date2)) {
            return false;
        }
        LocalDate localDate1 = dateConvertLocalDate(date1);
        LocalDate localDate2 = dateConvertLocalDate(date2);
        return localDate1.getYear() == localDate2.getYear() && localDate1.getMonth().getValue() == localDate2.getMonth().getValue();
    }

    /**
     * @param length 时间字符长度
     * @param sDate  时间字符串
     * @param format 校验格式
     * @return 结果
     */
    public static boolean isLegalDate(int length, String sDate, String format) {
        int legalLen = length;
        if ((sDate == null) || (sDate.length() != legalLen)) {
            return false;
        }
        DateFormat formatter = new SimpleDateFormat(format);
        try {
            Date date = formatter.parse(sDate);
            return sDate.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 对时间补0 返回对应格式
     *
     * @param date       时间字符串
     * @param split      当前时间格式
     * @param splitValue 转换格式
     * @return
     */
    public static String dateToAddZero(String date, String split, String splitValue) {
        // 分割输入字符串
        String[] parts = date.split(split);
        if (parts.length == 3) {
            String year = parts[0];
            String month = parts[1];
            String day = parts[2];

            // 对月份进行补零
            month = String.format("%02d", Integer.parseInt(month));

            // 对日期进行补零
            day = String.format("%02d", Integer.parseInt(day));

            // 重新组合为格式化的日期字符串
            String formattedDate = year + splitValue + month + splitValue + day;

            return formattedDate;
        }
        return date;
    }

    /**
     * UTC时间太平洋时区 时间
     *
     * @param datetime 字符 yyyy-MM-dd  HH:mm:ss
     * @return 结果
     */
    public static Date utcToPst(Date datetime) {
        if (datetime == null) {
            return null;
        }
        String dateTime = cn.hutool.core.date.DateUtil.format(datetime, DatePattern.NORM_DATETIME_PATTERN);
        String DateTimeStr = utcToPst(dateTime);
        if (!StringUtils.isEmpty(DateTimeStr)) {
            return DateUtil.parse(DateTimeStr);
        }
        return null;
    }

    /**
     * UTC时间太平洋时区 时间
     *
     * @param dateStr 字符 yyyy-MM-dd  HH:mm:ss
     * @return 结果
     */
    public static String utcToPst(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }
        try {
            ZoneId newZone = ZoneId.of("America/Los_Angeles");
            DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
            LocalDateTime parse = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
            ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("UTC"));
            parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
            return dtf2.format(parse);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * UTC时间转美东时间
     *
     * @param datetime 字符 yyyy-MM-dd  HH:mm:ss
     * @return 结果
     */
    public static Date utcToEstOrEdt(Date datetime) {
        if (datetime == null) {
            return null;
        }
        String dateTime = cn.hutool.core.date.DateUtil.format(datetime, DatePattern.NORM_DATETIME_PATTERN);
        String DateTimeStr = utcToEstOrEdt(dateTime);
        if (!StringUtils.isEmpty(DateTimeStr)) {
            return DateUtil.parse(DateTimeStr);
        }
        return null;
    }

    /**
     * UTC时间太平洋时区 时间
     *
     * @param dateStr 字符 yyyy-MM-dd  HH:mm:ss
     * @return 结果
     */
    public static String utcToEstOrEdt(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }
        try {
            ZoneId newZone = ZoneId.of("America/New_York");
            DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
            LocalDateTime parse = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
            ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("UTC"));
            parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
            return dtf2.format(parse);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 计算两个时间相差天数 时间1 - 时间2的天数,如果时间1 在时间2之前的话则为负数(当前天数不计入)
     *
     * @param nowDate 时间1
     * @param oldDate 时间2
     * @return {@link Long}
     */
    public static Long calculateTheNumberOfDaysDifference(Date nowDate, Date oldDate) {
        long nd = 1000 * 24 * 60 * 60;
        // 获得两个时间的毫秒时间差异
        long diff = nowDate.getTime() - oldDate.getTime();
        // 计算差多少天
        return diff / nd;
    }

    public static LocalDateTime utcToBj(Date utcDate) {
        // 获取当前的UTC时间
        LocalDateTime utcTime = dateConvertLocalDateTime(utcDate);
        LocalDateTime bjTime = utcTime.plusHours(8);
        return bjTime;
    }

    /**
     * 将localdate 转换为 时间戳
     *
     * @param date 时间
     * @return 时间戳格式
     */
    public static Long localDateConvertTimestamp(LocalDate date) {
        if (ObjectUtil.isNull(date)) {
            return -1L;
        }
        return localDateTimeConvertTimestamp(date.atStartOfDay());
    }

    /**
     * 将localdateTime 转换为 时间戳
     *
     * @param date 时间
     * @return 时间戳格式
     */
    public static Long localDateTimeConvertTimestamp(LocalDateTime date) {
        if (ObjectUtil.isNull(date)) {
            return -1L;
        }
        return date.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 将localdateTime 转换为 Unix时间戳
     *
     * @param date 时间
     * @return 时间戳格式
     */
    public static Long localDateTimeConvertUnixTimestamp(LocalDateTime date) {
        if (ObjectUtil.isNull(date)) {
            return -1L;
        }
        return localDateTimeConvertTimestamp(date) / 1000;
    }

    /**
     * 获取今天结束的时间,也就是今天的23.59.59
     *
     * @return {@link Date}
     */
    public static Date getNowMaxDate() {
        // 获取当前日期
        LocalDateTime currentDateTime = LocalDateTime.now();

        // 获取今天的结束时间，即今天的23:59:59
        LocalDateTime endOfDay = currentDateTime.with(LocalTime.MAX);

        // 将LocalDateTime转换为Date
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }


    /**
     * 获取今天结束的时间,也就是今天的23.59.59
     *
     * @return {@link Date}
     */
    public static Date getMaxDate(Date date) {
        // 获取当前日期
        LocalDateTime currentDateTime = DateUtils.convertDateToLocalDateTime(date);

        // 获取今天的结束时间，即今天的23:59:59
        LocalDateTime endOfDay = currentDateTime.with(LocalTime.MAX);

        // 将LocalDateTime转换为Date
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }


    /**
     * 判断某个时间是否在今天之后
     *
     * @return boolean
     */
    public static boolean checkDateAfterNowMaxDate(Date date) {
        if (StringUtils.isNotNull(date)) {
            return date.after(getNowMaxDate());
        }
        return false;
    }

    /**
     * n秒之后
     *
     * @param date
     * @param second
     * @return
     */
    public static String nSecondAfter(Date date, int second) {

        Calendar calendar = Calendar.getInstance();
        if (null != date) {
            calendar.setTime(date);
        }
        calendar.add(Calendar.SECOND, second);

        return DateUtils.formatDate(calendar.getTime(), DEFAULT_TIME_FORMAT);
    }


    /**
     * 计算两个时间的天数差
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static long calculateDaysDifference(Date startDate, Date endDate) {
        if (StringUtils.isNull(startDate) || StringUtils.isNull(endDate)) {
            throw new CheckException("Calculation parameters cannot be empty");
        }
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return ChronoUnit.DAYS.between(startLocalDate, endLocalDate);
    }

    /**
     * 计算两个时间之间相差的天数 (此结果永远为正数,请选择合适场景使用)
     *
     * @param date1 第一个时间
     * @param date2 第二个时间
     * @return 相差的天数
     */
    public static int daysBetween(Date date1, Date date2) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        long diff = Math.abs(date1.getTime() - date2.getTime());
        return (int) (diff / (1000 * 60 * 60 * 24));
    }


    /**
     * 某个时间加上固定天数
     *
     * @param date 时间
     * @param daysToAdd 需要增加的天数
     * @return
     */
    public static Date addDays(Date date, long daysToAdd) {
        if (StringUtils.isNull(date)) {
            throw new CheckException("Calculation parameters cannot be empty");
        }
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate newLocalDate = localDate.plusDays(daysToAdd);
        return Date.from(newLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    @SneakyThrows
    public static Date addDays(String date, String format, long daysToAdd) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return addDays(sdf.parse(date), daysToAdd);
    }


    /**
     * 获取一个时间区间内的所有日期列表
     * 例：startTime = 2021-01-01 18:58:28 endTime = 2021-01-05 18:58:28
     * 返回 [2021-01-01, 2021-01-02, 2021-01-03, 2021-01-04, 2021-01-05]
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 日期列表
     */
    private static List<String> getBetweenDateList(String startTime, String endTime) {
        Date start = DateUtil.parseDate(startTime);
        Date end = DateUtil.parseDate(endTime);

        List<String> list = Lists.newArrayList();

        long betweenDay = DateUtil.between(start, end, DateUnit.DAY);
        for (long i = 0L; i <= betweenDay; i++) {
            Date date = DateUtil.offsetDay(start, (int) i);
            list.add(DateUtil.formatDate(date));
        }

        return list;
    }


    /**
     * 获取昨天开始历史六个月的月份
     *
     * @return {@link List}<{@link String}>
     */
    public static List<String> getMonthsInTheLastSixMonths() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);

        // 获取昨天开始历史六个月的日期
        LocalDate sixMonthsAgo = yesterday.minusMonths(5).withDayOfMonth(1);

        // 逐月遍历日期，将月份格式化为字符串并添加到列表中
        List<String> months = new ArrayList<>();
        LocalDate date = sixMonthsAgo;
        while (date.isBefore(yesterday.plusDays(1))) {
            months.add(date.format(formatter));
            date = date.plusMonths(1);
        }
        return months;
    }


    /**
     * 获取近指定几个月的日期(不包含今日)
     *
     * @return {@link List}<{@link String}>
     */
    public static List<String> getTheDateOfTheLastSixMonths(Integer monthCount) {

        List<String> dateList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 逐天遍历日期，将日期格式化为字符串并添加到列表中
        LocalDate date = today.minusMonths(monthCount);
        while (date.isBefore(today)) {
            dateList.add(date.format(formatter));
            date = date.plusDays(1);
        }
        return dateList;
    }

    /**
     * 获取指定日期 x 天内的日期
     * @param designatedTime 指定时间
     * @param days 指定天数
     * @param doesItIncludeToday 是否包含今天
     * @return {@link List}<{@link String}>
     */
    public static List<String> getTheDateOfTheLastDays(Date designatedTime,Integer days,Boolean doesItIncludeToday) {

        List<String> dateList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 获取当前日期
        LocalDate today = DateUtils.dateConvertLocalDate(designatedTime);
        if (StringUtils.isNull(today)) {
            throw new CheckException("日期转换失败，请检查日期格式是否正确");
        }
        //是否包含今天的日期,包含的话则减去days-1天，不包含则减去days天
        int daysToSubtract = doesItIncludeToday ? days - 1 : days;
        // 获取指定天数前的日期
        LocalDate yesterday = today.minusDays(daysToSubtract);
        // 逐天遍历日期，将日期格式化为字符串并添加到列表中
        for (int i = 0; i < days; i++) {
            dateList.add(yesterday.plusDays(i).format(formatter));
        }
        return dateList;
    }

    /**
     * 获取最近七天的日期列表（包括今天）
     * @create: July
     * @return 最近七天的日期列表
     */
    public static List<String> getLastSevenDays() {
        List<String> dates = new ArrayList<>();
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (int i = 0; i < 7; i++) {
            dates.add(today.minusDays(i).format(formatter));
        }
        return dates;
    }

    /**
     * 获取本周的日期列表（包括今天）
     * @create: July
     * @return 本周的日期列表
     */
    public static List<String> getCurrentWeek() {
        List<String> dates = new ArrayList<>();
        LocalDate today = LocalDate.now();
        //获取本周的开始日期即周一
        LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (int i = 0; i < 7; i++) {
            dates.add(startOfWeek.plusDays(i).format(formatter));
        }
        return dates;
    }

    /**
     * 获取本月的日期列表
     * @create: July
     * @return 本月的日期列表
     */
    public static List<String> getCurrentMonth() {
        List<String> dates = new ArrayList<>();
        LocalDate today = LocalDate.now();
        //获取本月第一天的日期
        LocalDate startOfMonth = today.with(TemporalAdjusters.firstDayOfMonth());
        //最后一天的日期
        LocalDate endOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        while (startOfMonth.isBefore(endOfMonth)) {
            dates.add(startOfMonth.format(formatter));
            startOfMonth = startOfMonth.plusDays(1);
        }
        //需要额外把最后一天加上
        dates.add(endOfMonth.format(formatter));
        return dates;
    }

    /**
     * 获取下一周日期的整周日期
     *
     * @return 下一周日期的整周日期
     */
//    public static List<String> getNextWeekDays() {
//        List<String> weekDays = new ArrayList<>();
//        Calendar cal = Calendar.getInstance();
//        //从周一开始
//        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
//        cal.add(Calendar.DATE, 7);
//        for (int i = 0; i < 7; i++) {
//            String date = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
//            weekDays.add(date);
//            cal.add(Calendar.DATE, 1);
//        }
//        return weekDays;
//    }

    public static List<String> getNextWeekDays() {
        LocalDate today = LocalDate.now();
        LocalDate startOfNextWeek = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY))
                .plusWeeks(today.getDayOfWeek().equals(DayOfWeek.MONDAY) ? 1 : 0);

        List<String> weekDates = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            weekDates.add(startOfNextWeek.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            startOfNextWeek = startOfNextWeek.plusDays(1);
        }

        return weekDates;
    }

    /**
     * 获取当前时间时间戳 物流模块删除时间后续使用时间戳做为逻辑删除区分标识
     * @create: July
     * @return {@link Long}
     */
    public static Long getNowDateTimeStamp() {
        //获取当前时间时间戳
        return System.currentTimeMillis();
    }


    /**
     * 将时间戳转换为日期格式
     *
     * @param timestamp 时间戳
     * @return 日期格式
     */
    public static Date timestampToDate(Long timestamp, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return parseDate(sdf.format(new Date(timestamp)), format);
    }

    /**
     * 根据对应的索引返回月份
     * @param index 从1开始 输入1 返回当前月 2为未来月 以此类推
     * @return 实际月份
     */
    public static Integer getFutureMonth(Integer index) {
        if (index == null) {
            return null;
        }
        LocalDate currentDate = LocalDate.now();
        int currentMonth = currentDate.getMonthValue();

        int targetMonth = currentMonth + index - 1;
        targetMonth = (targetMonth - 1) % 12 + 1;

        return targetMonth;
    }

    /**
     * 传入两个日期 如果是同一周的 则返回true
     * @param date1 日期1
     * @param date2 日期2
     * @return 是否是同一周
     */
    public static boolean isSameWeek(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        // 设置周一为一周开始
        cal1.setFirstDayOfWeek(Calendar.MONDAY);
        cal2.setFirstDayOfWeek(Calendar.MONDAY);
        cal1.setTime(date1);
        cal2.setTime(date2);
        int subYear = cal1.get(Calendar.YEAR) - cal2.get(Calendar.YEAR);
        // subYear == 0 说明是同一年
        boolean isSameWeek = cal1.get(Calendar.WEEK_OF_YEAR) == cal2.get(Calendar.WEEK_OF_YEAR);
        if (subYear == 0) {
            return isSameWeek;
        } else if (subYear == 1 && cal2.get(Calendar.MONTH) == Calendar.DECEMBER) {
            // subYear == 1 说明cal1比cal2大一年
            return isSameWeek;
        } else if (subYear == -1 && cal1.get(Calendar.MONTH) == Calendar.DECEMBER) {
            // subYear == 1 说明cal1比cal2小一年
            return isSameWeek;
        }
        return false;
    }


    /**
     * 是否是一周的最后一天
     *
     * @param date 当地日期
     */
    public static Boolean isItTheLastDayOfTheWeek(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate.with(DayOfWeek.SUNDAY).compareTo(localDate) == 0;
    }

    /**
     * 获取所在日期的周期
     */
    public static String getWeekPeriod(Date inputDate) {
        LocalDate localDate = inputDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        LocalDate startOfWeek = localDate.with(DayOfWeek.MONDAY);
        LocalDate endOfWeek = localDate.with(DayOfWeek.SUNDAY);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");

        return startOfWeek.format(formatter) + "~" + endOfWeek.format(formatter);
    }

    /**
     * 判断两个日期是否是同一个月的
     */
    public static boolean isSameMonth(Date date1, Date date2) {
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate1.getMonth() == localDate2.getMonth();
    }


    /**
     * 获取当前日期指定天数之前的时间。
     *
     * @param daysBefore 当前日期之前的天数，正数表示未来，负数表示过去。
     * @return 指定天数之前的时间日期。
     */
    public static LocalDate getDaysBeforeDate(int daysBefore) {
        return LocalDate.now().minusDays(daysBefore);
    }

    /**
     * 获取当前日期指定天数之前的时间（返回java.util.Date类型）。
     *
     * @param daysBefore 当前日期之前的天数，正数表示未来，负数表示过去。
     * @return 指定天数之前的时间日期（Date类型）。
     */
    public static Date getDateBeforeNow(int daysBefore) {
        Calendar calendar = Calendar.getInstance();
        // 减去天数
        calendar.add(Calendar.DAY_OF_MONTH, daysBefore);
        // 返回Date类型的时间
        return calendar.getTime();
    }

    public static Date formatter(String dateTimeString) {
        // 解析日期时间字符串为 ZonedDateTime 对象
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));

        try {
            // 解析日期时间字符串为 Date 对象
            Date date = formatter.parse(dateTimeString);
            return  utcToPst(date);
        } catch (Exception e) {
            throw new CommonException("转换异常");
        }
    }

    /**
     * 柏林(德国)转美西
     * @param berlinTime 传入美西时间
     * @return
     */
    public static LocalDateTime berlinToPst(LocalDateTime berlinTime) {
        // 定义柏林时区
        ZoneId berlinZone = ZoneId.of("Europe/Berlin");

        // 将输入的 LocalDateTime 转换为柏林时区的 ZonedDateTime
        ZonedDateTime berlinZonedDateTime = berlinTime.atZone(berlinZone);

        // 定义美西时区（PST/PDT）
        ZoneId pstZone = ZoneId.of("America/Los_Angeles");

        // 将柏林时区的 ZonedDateTime 转换为美西时区的 ZonedDateTime
        ZonedDateTime pstZonedDateTime = berlinZonedDateTime.withZoneSameInstant(pstZone);

        // 提取美西时间的 LocalDateTime
        LocalDateTime pstTime = pstZonedDateTime.toLocalDateTime();

        return pstTime;
    }


    /**
     * 日本转美西
     * @param jstTime
     * @return
     */
    public static LocalDateTime jstToPst(LocalDateTime jstTime) {
        // 定义日本时区
        ZoneId jstZone = ZoneId.of("Asia/Tokyo");

        // 将输入的 LocalDateTime 转换为日本时区的 ZonedDateTime
        ZonedDateTime jstZonedDateTime = jstTime.atZone(jstZone);

        // 定义美西时区（PST/PDT）
        ZoneId pstZone = ZoneId.of("America/Los_Angeles");

        // 将日本时区的 ZonedDateTime 转换为美西时区的 ZonedDateTime
        ZonedDateTime pstZonedDateTime = jstZonedDateTime.withZoneSameInstant(pstZone);

        // 提取美西时间的 LocalDateTime
        LocalDateTime pstTime = pstZonedDateTime.toLocalDateTime();

        return pstTime;
    }

    public static String getFormat(LocalDate localDate, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return localDate.format(formatter);
    }

    public static List<String> getDayByLocalDate(LocalDate beginTime, LocalDate endTime, String format) {
        List<String> list = new ArrayList<>();
        long day = endTime.toEpochDay() - beginTime.toEpochDay();
        String value = getFormat(beginTime, format);
        list.add(value);
        for (int i = 0; i< day; i++) {
            LocalDate date = beginTime.plusDays(1);
            String dateValue = getFormat(date, format);
            list.add(dateValue);
            beginTime = date;
        }
        return list;
    }

    public static List<String> getWeekStringList(LocalDate startDate, LocalDate endDate, String format) {
        List<String> weekStringList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        LocalDate startMonday = startDate.with(DayOfWeek.MONDAY);
        LocalDate startSunday = startDate.with(DayOfWeek.SUNDAY);

        // 获取 Locale 来定义周从周一开始
        WeekFields weekFields = WeekFields.ISO;

        // 将开始日期调整到该周的周一，结束日期调整到该周的周日
        LocalDate startOfWeek = startDate.with(weekFields.dayOfWeek(), 1);  // 开始日期所在周的周一
        LocalDate endOfWeek = endDate.with(weekFields.dayOfWeek(), 7);      // 结束日期所在周的周日

        // 计算跨越的周数，起始周为1
        Long weekendLong = ChronoUnit.WEEKS.between(startOfWeek, endOfWeek) + 1;

        Integer weekend = weekendLong.intValue();
        if (weekend == 0) {
            weekend = 1;
        }


        LocalDate current = startMonday;
        for (int i = 1; i <= weekend; i++) {
            if (i == 1) {
                if (startSunday.isAfter(endDate) || startDate.isEqual(endDate)) {
                    weekStringList.add(startDate.format(formatter) + "~" + endDate.format(formatter));
                } else {
                    weekStringList.add(startDate.format(formatter) + "~" + startSunday.format(formatter));
                }
            } else if (i == weekend) {
                current = current.plusWeeks(1);
                if (current.isEqual(endDate)) {
                    weekStringList.add(endDate.format(formatter) + "~" + endDate.format(formatter));
                } else {
                    weekStringList.add(current.format(formatter) + "~" + endDate.format(formatter));
                }
            } else {
                current = current.plusWeeks(1);
                weekStringList.add(current.format(formatter) + "~" +  current.with(DayOfWeek.SUNDAY).format(formatter));
            }
        }
        return weekStringList;
    }

    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }
    public static String localDateFormat(LocalDate localDate,String format) {
        return DateUtils.formatDate(DateUtils.localDateToDate(localDate), format);
    }

    public static String localDateTimeFormat(LocalDateTime localDate,String format) {
        return DateUtils.formatDate(DateUtils.localDateTimeToDate(localDate), format);
    }
    public static List<String> getMonthByLocalDate(LocalDate beginTime, LocalDate endTime) {
        if (null == beginTime || null == endTime) {
            return new ArrayList<>();
        }
        String strBeginTime = localDateFormat(beginTime, DateUtils.YYYY_MM);
        String strEbdTime = localDateFormat(endTime, DateUtils.YYYY_MM);
        List<String> yearMonths = new ArrayList<>();

        LocalDate startMonth = LocalDate.parse(strBeginTime + "-01");
        LocalDate endMonth = LocalDate.parse(strEbdTime + "-01");
        while (!startMonth.isAfter(endMonth)) {
            yearMonths.add(startMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            startMonth = startMonth.plusMonths(1);
        }
        return yearMonths;
    }

    public static List<QuarterTimeDTO> getLocalDateToQuarterToBetween(LocalDate startDate, LocalDate endDate) {
        if (null == startDate || null == endDate) {
            return null;
        }
        List<QuarterTimeDTO> quarterTimeDTOList = new ArrayList<>();
        while (startDate.isBefore(endDate) || startDate.equals(endDate)) {
            QuarterTimeDTO quarterTimeDTO = new QuarterTimeDTO();
            int currentMonth = startDate.getMonthValue();
            int quarterStartMonth = ((currentMonth - 1) / 3) * 3 + 1; // 季度开始月份
            int quarterEndMonth = quarterStartMonth + 2; // 季度结束月份
            LocalDate startQuarterStart = LocalDate.of(startDate.getYear(), quarterStartMonth, 1);
            LocalDate startQuarterEnd = startQuarterStart.plusMonths(2).withDayOfMonth(startQuarterStart.plusMonths(2).lengthOfMonth());
            if (startQuarterStart.isEqual(startDate) || startQuarterStart.isBefore(startDate)) {
                startQuarterStart = startDate;
            }
            if (startQuarterEnd.isAfter(endDate)) {
                startQuarterEnd = endDate;
            }

            quarterTimeDTO.setBeginDate(startQuarterStart);
            quarterTimeDTO.setEndDate(startQuarterEnd);
            quarterTimeDTOList.add(quarterTimeDTO);
            startDate = startQuarterEnd.plusDays(1);
        }
        return quarterTimeDTOList;
    }

    public static LocalDate getMonthStartTime(LocalDate startTime) {
        return startTime.with(TemporalAdjusters.firstDayOfMonth());
    }

    public static LocalDate getMonthEndTime(LocalDate startTime) {
        return startTime.with(TemporalAdjusters.lastDayOfMonth());
    }

    public static List<String> getBetweenTimeToTimeRadius(LocalDate beginTime, LocalDate endTime, Integer time, String format) {
        if (null == format) {
            format = DateUtils.YYYY_MM_DD;
        }
        List<String> timeList = new ArrayList<>();
        //天
        if (time.equals(2)) {
            List<String> dayByLocalDate = getDayByLocalDate(beginTime, endTime, format);
            timeList.addAll(dayByLocalDate);
        }
        //周
        if (time.equals(3)) {
            timeList.addAll(getWeekStringList(beginTime, endTime, format));
        }
        //月
        if (time.equals(4)) {
            //获取月得时间集合
            List<String> monthByLocalDate = getMonthByLocalDate(beginTime, endTime);
            for (int i = 0; i < monthByLocalDate.size(); i++) {
                String key = monthByLocalDate.get(i);
                /*String key = null;
                //如果只有一个，即是同一个月时间
                if (monthByLocalDate.size() == 1) {
                    key = DateUtils.getFormat(beginTime, format) + "~" + DateUtils.getFormat(endTime, format);
                } else {
                    //如果是最后一个
                    if (i + 1 == monthByLocalDate.size()) {
                        key = DateUtils.getFormat(DateUtils.getMonthStartTime(endTime), format) + "~" + DateUtils.getFormat(endTime, format);

                    } else if (i == 0) {
                        //如果是第一个
                        key = DateUtils.getFormat(beginTime, format) + "~" + DateUtils.getFormat(DateUtils.getMonthEndTime(beginTime), format);
                    } else {
                        //不是最后一个，不是第一个
                        String date = monthByLocalDate.get(i) + "-01";
                        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        LocalDate newStartDate = LocalDate.parse(date, fmt);
                        key = DateUtils.getFormat(DateUtils.getMonthStartTime(newStartDate), format) + "~" + DateUtils.getFormat(DateUtils.getMonthEndTime(newStartDate), format);
                    }
                }*/
                timeList.add(key);
            }


            return timeList;
        }
        //季度
        if (time.equals(5)) {
            //两个时间的所有
            List<QuarterTimeDTO> quarterToBetween = getLocalDateToQuarterToBetween(beginTime, endTime);

            for (QuarterTimeDTO timeDTO : quarterToBetween) {
                String key = DateUtils.getFormat(timeDTO.getBeginDate(), format) + "~" + DateUtils.getFormat(timeDTO.getEndDate(), format);
                timeList.add(key);
            }
        }
        return timeList;
    }

    public static Long convertDateStringToTimestamp(String createdDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        Date date = null;
        try {
            date = sdf.parse(createdDate);
        } catch (ParseException e) {
            return null;
        }
        long timestamp = date.getTime();
        return timestamp;
    }

    public static Date convertDateStringToDate(String createdDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        Date date = null;
        try {
            date = sdf.parse(createdDate);
        } catch (ParseException e) {
            return null;
        }
        return date;
    }
}
