package com.bizark.op.common.util;

import com.bizark.op.common.exception.CustomException;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.LinkedList;
import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @Date 2023/5/29 11:43
 */

public class ImportExcel {

    private static final Logger logger = LoggerFactory.getLogger(ImportExcel.class);


    /**
     * @param excelInputStream 文件输入流
     * @param clazz            解析参照对象
     * @param valueStartRow    值开始的行
     * @param valueStartColumn 值开始的列
     * @param sheetIndex       工作簿
     * @return 返回 对象的集合
     * @throws Exception
     */
    public <T> List<T> parseExcelToClass(InputStream excelInputStream, Class<T> clazz, int valueStartRow,
                                         int valueStartColumn, int sheetIndex) throws Exception {

        //验证输入值，并转化Workbook对象
        Workbook work = this.getWorkbook(excelInputStream);
        //获取指定的工作簿
        Sheet sheet = work.getSheetAt(sheetIndex);
        //通过反射获取到类的属性值
        Field[] fields = clazz.getDeclaredFields();
        int k = 0;//属性值集合下标
        Row row;//行
        Cell cell;//单元格
        List<T> objects = new LinkedList<T>();
        int fieldLength = fields.length;
        for (int i = valueStartRow; i < sheet.getLastRowNum() + 1; i++) {
            row = sheet.getRow(i);//行
            if (row == null) {
                continue;
            }
            T obj = clazz.newInstance();

            for (int j = valueStartColumn; j < fieldLength + valueStartColumn; j++) {
                try {
                    cell = row.getCell(j);
                    Object value = getCellValue(cell);
                    AssertUtil.isTrue(k + 1 <= fieldLength, "单元格值的个数，与返回对象的属性个数不符!");
                    Field f = clazz.getDeclaredField(fields[k].getName());
                    f.setAccessible(true);
                    if (value == null || "".equals(value.toString().trim())) {
                        f.set(obj, null);
                    } else if ("class java.lang.String".equals(
                            fields[k].getGenericType().toString())) {
                        f.set(obj, value.toString().trim());
                    } else if ("class java.lang.Integer".equals(
                            fields[k].getGenericType().toString())) {
                        if (value.toString().contains(".")) {
                            f.set(obj, Integer.valueOf(value.toString().trim().substring(0, value.toString().indexOf("."))));
                        } else {
                            f.set(obj, Integer.valueOf(value.toString().trim()));
                        }

                    } else if ("class java.lang.Double".equals(
                            fields[k].getGenericType().toString())) {
                        f.set(obj, Double.valueOf(value.toString().trim()));
                    } else if ("class java.math.BigDecimal".equals(
                            fields[k].getGenericType().toString())) {
                        f.set(obj, new BigDecimal(value.toString().trim()));
                    } else if ("class java.util.Date".equals(
                            fields[k].getGenericType().toString())) {
                        f.set(obj, DateUtils.parseDate(value.toString().trim(),DateUtils.YYYYMMDD));
                    }
                    k++;
                } catch (Exception e) {
                    logger.error("序列化excel数据值失败:",e);
                    throw new CustomException("第" + (i + 1) + "行,第" + (j + 1) + "列的数据有问题，请检查数据格式!");
                }
            }
            boolean flag = false;
            Field[] fieldArr = clazz.getDeclaredFields();
            for (int a = 0; a < fieldArr.length; a++) {
                Field f = fieldArr[a];
                f.setAccessible(true);
                Object val = f.get(obj);
                if (val != null && !"".equals(val.toString().trim())) {
                    flag = true;
                }
            }
            if (flag) {
                objects.add(obj);
                flag = false;
            }
            k = 0;//属性名称的序号
        }

        return objects;
    }

    /**
     * 获取单元格值
     *
     * @param cell
     * @return
     */
    private Object getCellValue(Cell cell) {
        if (ObjectUtils.isEmpty(cell)) {
            return null;
        }
        Object value;
        switch (cell.getCellType()) {
            case HSSFCell.CELL_TYPE_STRING: // 字符串
                value = cell.getStringCellValue();
                break;
            case HSSFCell.CELL_TYPE_NUMERIC: // 数字
                if (HSSFDateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(HSSFDateUtil.getJavaDate(cell.getNumericCellValue()));
                }
                value = cell.getNumericCellValue();
                break;
            case HSSFCell.CELL_TYPE_BOOLEAN: // Boolean
                value = cell.getBooleanCellValue();
                break;
            case HSSFCell.CELL_TYPE_FORMULA: // 公式
                value = cell.getNumericCellValue();
                break;
            case HSSFCell.CELL_TYPE_BLANK: // 空值
                value = "";
                break;
            case HSSFCell.CELL_TYPE_ERROR: // 故障
                value = "";
                break;
            default:
                value = "ERROR";
                break;
        }
        return value;
    }

    /**
     * 获取文件
     *
     * @param inStr
     * @return
     * @throws Exception
     */
    private Workbook getWorkbook(InputStream inStr) throws Exception {
        Assert.notNull(inStr, "未获取到导入文件的内容!");
        return WorkbookFactory.create(inStr);
    }
}
