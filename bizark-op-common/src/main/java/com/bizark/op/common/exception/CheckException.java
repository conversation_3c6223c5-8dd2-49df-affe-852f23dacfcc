package com.bizark.op.common.exception;

import lombok.Getter;

/**
 * 自定义异常-校验(为了避免影响原业务的Transactional注解事务,添加新的自定义异常)
 *
 * <AUTHOR>
 */
public class CheckException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

    @Getter
    private Integer code;

    private String message;

    private Object data;

    public CheckException(String message) {
        this.message = message;
    }
    public CheckException(String message, Object data) {

        this.message = message;
        this.data = data;
    }

    public CheckException(String message, Integer code)
    {
        this.message = message;
        this.code = code;
    }

    public CheckException(String message, Throwable e)
    {
        super(message, e);
        this.message = message;
    }

    @Override
    public String getMessage()
    {
        return message;
    }


    public Object getData() {
        return data;
    }
}

