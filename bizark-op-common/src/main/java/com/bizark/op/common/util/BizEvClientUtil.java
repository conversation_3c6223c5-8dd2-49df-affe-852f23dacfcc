package com.bizark.op.common.util;

import com.bizark.bizevcli.common.constants.AppConstants;
import com.bizark.bizevcli.common.parameter.api.BizEvCliRequest;
import com.bizark.bizevcli.common.parameter.api.BizEvCliResponse;
import com.bizark.bizevcli.common.util.BizEvColUtil;
import com.bizark.common.constant.PropertiesConfig;
import com.bizark.op.common.enm.BizEvClientEnum;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

public class BizEvClientUtil {
    private static Logger logger = LoggerFactory.getLogger(BizEvClientUtil.class);

    public static final Boolean BIZEVCLI_ENABLE;
    public static final String BIZEVCLI_ENV;
    public static final String BIZEVCLI_TOKEN;
    public static final String BIZEVCLI_URL;

    static {
        PropertiesConfig config = new PropertiesConfig("properties/setting");
        BIZEVCLI_ENABLE = config.getPropByKey("bizevcli.enable").equals("true");
        BIZEVCLI_ENV = config.getPropByKey("bizevcli.env");
        BIZEVCLI_TOKEN = config.getPropByKey("bizevcli.token");
        BIZEVCLI_URL = config.getPropByKey("bizevcli.url");
    }

    /**
     * 简单发送 标识，消息
     * @param label 标识
     * @param msg 消息
     */
    public static void bizEvCliSimpleSend(String label,String msg){
        bizEvCliSimpleSend(label, msg,"");
    }

    /**
     * 简单发送 标识，消息，内容
     * @param label 标识
     * @param msg 消息
     * @param content 内容
     */
    public static void bizEvCliSimpleSend(String label,String msg,String content){
        bizEvCliSimpleSend(label, msg, content, AppConstants.LEVEL_UNSPECIFIED);
    }

    /**
     * 简单发送 标识，消息，内容，级别
     * @param label 标识
     * @param msg 消息
     * @param content 内容
     * @param level 级别
     */
    public static void bizEvCliSimpleSend(String label,String msg,String content,Integer level){
        BizEvCliRequest bizEvCliRequest = new BizEvCliRequest();
        bizEvCliRequest.setLabel(label);
        bizEvCliRequest.setMsg(msg);
        bizEvCliRequest.setContent(content);
        bizEvCliRequest.setLevel(level);
        BizEvCliResponse bizEvCliResponse = bizEventCollectEnvGetResponse(bizEvCliRequest);
        if(Objects.requireNonNull(bizEvCliResponse).getCode()!=200){
            logger.warn("bizEventCollect failed ",bizEvCliResponse.getCode(),bizEvCliResponse.getMessage());
        }
    }

    /**
     * 简单发送 标识，消息，代码，内容
     * @param label 标识
     * @param msg 消息
     * @param code 代码
     * @param content 内容
     */
    public static void bizEvCliSimpleSend(String label,String msg,String code,String content){
        bizEvCliSimpleSend(label, msg, code, content, AppConstants.LEVEL_UNSPECIFIED);
    }
    /**
     * 简单发送 标识，消息，代码，内容，级别
     * @param label 标识
     * @param msg 消息
     * @param code 代码
     * @param content 内容
     * @param level 级别
     */
    public static void bizEvCliSimpleSend(String label,String msg,String code,String content,Integer level){
        BizEvCliRequest bizEvCliRequest = new BizEvCliRequest();
        bizEvCliRequest.setLabel(label);
        bizEvCliRequest.setMsg(msg);
        bizEvCliRequest.setCode(code);
        bizEvCliRequest.setContent(content);
        bizEvCliRequest.setLevel(level);
        BizEvCliResponse bizEvCliResponse = bizEventCollectEnvGetResponse(bizEvCliRequest);
        if(Objects.requireNonNull(bizEvCliResponse).getCode()!=200){
            logger.warn("bizEventCollect failed ",bizEvCliResponse.getCode(),bizEvCliResponse.getMessage());
        }
    }

    /**
     * 采集并获取采集响应结果
     * @param bizEvCliRequest 事件采集客户端请求
     * @return
     */
    public static BizEvCliResponse bizEventCollectEnvGetResponse(BizEvCliRequest bizEvCliRequest)
    {
        try {
            return BizEvColUtil.bizEventCollectEnvGetResponse(bizEvCliRequest,BIZEVCLI_TOKEN,BIZEVCLI_ENV,BIZEVCLI_ENABLE);
        } catch (Exception e) {
            logger.error("bizEventCollect error ", e);
        }
        return null;
    }

    /**
     * 单向发送采集数据
     * @param bizEvCliRequest 事件采集客户端请求
     */
    public static void bizEventCollectSend(BizEvCliRequest bizEvCliRequest) {
        try {
            BizEvColUtil.bizEventCollectSend(bizEvCliRequest,BIZEVCLI_TOKEN,BIZEVCLI_ENV,BIZEVCLI_ENABLE);
        } catch (Exception e) {
            logger.error("bizEventCollect error ", e);
        }
    }

    public static String buildExceptionContentByException(Throwable throwable){
        String referenceMessage = ExceptionUtils.getMessage(throwable);
        StackTraceElement[] stackTraces = throwable.getStackTrace();
        String stackTrace = null;
        if (ArrayUtils.isNotEmpty(stackTraces)) {
            for (StackTraceElement traceElement : stackTraces) {
                if (traceElement.toString().contains(BizEvClientEnum.PROJECT_PACKAGE_PREFIX)) {
                    stackTrace = traceElement.toString();
                    break;
                }
            }
        }
        return referenceMessage + " \nTRACE:\n\n"+stackTrace;
    }
}
