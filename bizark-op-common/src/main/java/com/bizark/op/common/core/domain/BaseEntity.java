package com.bizark.op.common.core.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.common.util.DateUtils;
import com.bizark.framework.security.AuthContextHolder;
import com.fasterxml.jackson.annotation.JsonFormat;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.shiro.SecurityUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
public class BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 搜索值
     */
    @TableField(exist = false)
    private String searchValue;


    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createdBy;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updatedBy;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedAt;

    /**
     * 更新者
     */
    private Integer disabledBy;

    /**
     * 更新者
     */
    private String disabledName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date disabledAt;

    /**
     * 备注
     */
    private String remark;


    /**
     * 是否跳过日志
     */
    @TableField(exist = false)
    private Boolean skipInterceptor;

    /**
     * 请求参数
     */
    @TableField(exist = false)
    private Map<String, Object> params;

    public String getSearchValue() {
        return searchValue;
    }

    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getDisabledBy() {
        return disabledBy;
    }

    public void setDisabledBy(Integer disabledBy) {
        this.disabledBy = disabledBy;
    }

    public String getDisabledName() {
        return disabledName;
    }

    public void setDisabledName(String disabledName) {
        this.disabledName = disabledName;
    }

    public Date getDisabledAt() {
        return disabledAt;
    }

    public void setDisabledAt(Date disabledAt) {
        this.disabledAt = disabledAt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Boolean getSkipInterceptor() {
        return skipInterceptor;
    }

    public void setSkipInterceptor(Boolean skipInterceptor) {
        this.skipInterceptor = skipInterceptor;
    }

    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }


    public void settingDefaultValue() {
        AuthUserDetails authUserDetails = AuthContextHolder.getAuthUserDetails();
        String updatedName = authUserDetails == null ? "system" : authUserDetails.getName();
        Integer updatedBy = authUserDetails == null ? -1 : authUserDetails.getId();
        Date now = DateUtils.getNowDate();
        this.setCreatedAt(getCreatedAt() == null ? now : getCreatedAt());
        this.setCreatedBy(getCreatedAt() == null ? updatedBy : getCreatedBy());
        this.setCreatedName(getCreatedName() == null ? updatedName : getCreatedName());
        this.setUpdatedBy(updatedBy);
        this.setUpdatedName(updatedName);
        this.setUpdatedAt(now);
    }

    public void settingUpdated(){
        try {
            AuthUserDetails userDetails = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
            this.updatedBy = userDetails.getId();
            this.updatedName = userDetails.getName();
            this.updatedAt = new Date();
        } catch (Exception e) {
            this.updatedAt = new Date();
        }
    }

    public void settingCreated(){
        try {
            AuthUserDetails userDetails = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
            this.createdBy = userDetails.getId();
            this.createdName = userDetails.getName();
            this.createdAt = new Date();
        } catch (Exception e) {
            this.createdAt = new Date();
        }
    }

    public void settingDefaultCreate() {
        AuthUserDetails authUserDetails = null;
        try {
            authUserDetails = AuthContextHolder.getAuthUserDetails();
        } catch (Exception e) {
            e.printStackTrace();
        }
        this.setCreatedAt(DateUtils.getNowDate());
        this.setCreatedBy(authUserDetails == null ? -1 : authUserDetails.getId());
        this.setCreatedName(authUserDetails == null ? "system" : authUserDetails.getName());
    }

    public void settingDefaultUpdate() {
        AuthUserDetails authUserDetails = null;
        try {
            authUserDetails = AuthContextHolder.getAuthUserDetails();
        } catch (Exception e) {
            e.printStackTrace();
        }
        this.setUpdatedAt(DateUtils.getNowDate());
        this.setUpdatedBy(authUserDetails == null ? -1 : authUserDetails.getId());
        this.setUpdatedName(authUserDetails == null ? "system" : authUserDetails.getName());
    }


    /**
     * 设置默认创建人为系统
     */
    public void settingDefaultSystemCreate() {
        this.setCreatedAt(DateUtils.getNowDate());
        this.setCreatedBy(-1);
        this.setCreatedName("system");
    }


    public void settingDefaultSystemUpdate() {
        this.setUpdatedAt(DateUtils.getNowDate());
        this.setUpdatedBy(-1);
        this.setUpdatedName("system");
    }
}
