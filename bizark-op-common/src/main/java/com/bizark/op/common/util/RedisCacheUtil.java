package com.bizark.op.common.util;


import com.bizark.op.common.core.redis.RedisCache;
import com.bizark.op.common.util.spring.SpringUtils;

import java.util.concurrent.TimeUnit;

public class RedisCacheUtil {

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(String configKey) {
        return configKey;
    }

    /**
     * 获取缓存内容
     *
     * @param key
     * @return
     */
    public static String getStringCache(String key) {
        Object cacheObj = SpringUtils.getBean(RedisCache.class).getCacheObject(getCacheKey(key));
        if (StringUtils.isNotNull(cacheObj)) {
            String verifyCode = StringUtils.cast(cacheObj);
            return verifyCode;
        }
        return null;
    }

    /**
     * 设置缓存内容
     *
     * @param key
     * @param cacheContent 缓存内容
     * @param minutes      缓存分钟
     */
    public static void setStringCacheTime(String key, String cacheContent, int minutes) {
        SpringUtils.getBean(RedisCache.class).setCacheObject(getCacheKey(key), cacheContent, minutes, TimeUnit.MINUTES);
    }
}
