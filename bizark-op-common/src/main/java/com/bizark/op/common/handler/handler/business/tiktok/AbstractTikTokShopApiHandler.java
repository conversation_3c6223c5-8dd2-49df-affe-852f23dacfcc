package com.bizark.op.common.handler.handler.business.tiktok;

import org.springframework.beans.factory.InitializingBean;

import java.util.Map;

/**
 * lty notes tiktok 模版 ,mustData 为过鉴权必需的字段,类似于 shopId
 *
 * <AUTHOR> Theo
 * @create 2023/10/16 17:50
 */
public abstract class AbstractTikTokShopApiHandler<E> implements InitializingBean {
    /**
     * 获得api调用结果
     *
     * @param reqParam  req param
     * @param url       网址
     * @param path      路径
     * @param respParam Resp参数
     * @param mustData
     * @return {@link T}
     */
    public abstract <T> T getTikTokShopReturn(Object reqParam, String url, String path, Class<T> respParam, String mustData);

    public final void tikTokApiPostNeedMustData() {

    }

    /**
     * 获得api调用结果
     * sendGet
     *
     * @param reqParam  req param
     * @param url       网址
     * @param path      路径
     * @param respParam Resp参数
     * @param mustData
     * @return {@link T}
     */
    public abstract <T> T getTikTokShopReturnByGet(Object reqParam, String url, String path, Class<T> respParam, String mustData);

    /**
     * 用于计算sign预数据query
     *
     * @return {@link E}
     */
    public abstract E preDataForCalculateTheSign(String mustData);

    /**
     * 常见参数的装饰器
     *
     * @param commonParam 常见参数
     * @return {@link E}
     */
    public abstract E dataWrapper(E commonParam);

    /**
     * 数据包装器
     * 常见参数的装饰器
     *
     * @param commonParam 常见参数
     * @param base        基础
     * @return {@link T}
     */
    public abstract <T> T getTByObjectToJson(E commonParam, Class<T> base);

    /**
     * 用于计算sign预数据query
     *
     * @return {@link E}
     */
    public abstract <T> T preDataForCalculateTheSign(String mustData, Class<T> query);

    /**
     * 发送前
     * notes :url+api+shop_id这样固定的类型返回命名commonParamsMap sign内部加密的mustData 等 返回 queryMap
     *
     * @param o o
     * @return {@link Map}
     */
    public abstract Map sendBefore(Object o);
}
