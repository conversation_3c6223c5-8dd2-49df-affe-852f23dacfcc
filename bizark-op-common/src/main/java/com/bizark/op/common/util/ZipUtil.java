package com.bizark.op.common.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <NAME_EMAIL>
 * 2018/12/25
 */
public class ZipUtil {
    /**
     * 压缩多个文件
     * @param srcFiles
     * @param zipFile
     */
    public static void zipFiles(File[]srcFiles, File zipFile){
        if(!zipFile.exists()){
            try{
                zipFile.createNewFile();
            }catch (IOException e){
                e.printStackTrace();
            }
        }
        FileOutputStream fileOutputStream = null;
        ZipOutputStream zipOutputStream = null;
        FileInputStream fileInputStream = null;
        try{
            fileOutputStream = new FileOutputStream(zipFile);
            zipOutputStream = new ZipOutputStream(fileOutputStream);
            ZipEntry zipEntry = null;
            for (File srcFile : srcFiles) {
                fileInputStream = new FileInputStream(srcFile);
                zipEntry = new ZipEntry(srcFile.getName());
                zipOutputStream.putNextEntry(zipEntry);
                int len;
                byte[]buffer = new byte[1024];
                while ((len=fileInputStream.read(buffer))>0){
                    zipOutputStream.write(buffer,0,len);
                }
            }
        }catch (IOException e){
            e.printStackTrace();
        }finally {
            try {
                zipOutputStream.closeEntry();
                zipOutputStream.close();
                fileInputStream.close();
                fileOutputStream.close();
            }catch (IOException e){
                e.printStackTrace();
            }
        }
    }
}
