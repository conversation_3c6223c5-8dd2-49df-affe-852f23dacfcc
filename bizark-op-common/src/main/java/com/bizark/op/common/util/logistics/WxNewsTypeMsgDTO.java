package com.bizark.op.common.util.logistics;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 微信消息图文类型参数
 * <AUTHOR>
 * @create: 2024-06-05 12:10
 */
@Data
public class WxNewsTypeMsgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  标题，不超过128个字节，超过会自动截断
     */
    private String title;

    /**
     * 描述，不超过512个字节，超过会自动截断
     */
    private String description;

    /**
     * 点击后跳转的链接
     */
    private String url;

    /**
     * 图文消息的图片链接，支持JPG、PNG格式，较好的效果为大图 1068*455，小图150*150。
     */
    private String picurl;
}
