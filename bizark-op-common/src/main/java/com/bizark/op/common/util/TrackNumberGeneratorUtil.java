package com.bizark.op.common.util;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 订单跟踪号生成工具
 *
 * <AUTHOR> 2023/11/23
 */

public class TrackNumberGeneratorUtil {

    /**
     * 根据传入的字符串数组 生成下一个订单跟踪号
     * @param strings 订单跟踪号数组
     * @param baseString  基准字段
     * @return 下一个跟踪号
     */
    public static String generateNextTrackNumber(List<String> strings, String baseString) {
        if (strings == null || strings.isEmpty() || baseString == null) {
            return baseString + "A"; // 默认起始值
        }

        Set<String> stringSet = new HashSet<>(strings);

        // 生成所有可能的后缀
        String[] suffixes = generateSuffixes();

        for (String suffix : suffixes) {
            String potentialString = baseString + suffix;
            if (!stringSet.contains(potentialString)) {
                return potentialString; // 找到第一个不在集合中的后缀
            }
        }

        return null; // 如果所有可能的后缀都用完了
    }


    /**
     * 提前生成订单跟踪号
     */
    private static String[] generateSuffixes() {
        String[] suffixes = new String[26 * 27]; // 最大可能的后缀数量
        int index = 0;

        for (char c = 'A'; c <= 'Z'; c++) {
            suffixes[index++] = String.valueOf(c);
        }

        for (char c1 = 'A'; c1 <= 'Z'; c1++) {
            for (char c2 = 'A'; c2 <= 'Z'; c2++) {
                suffixes[index++] = "" + c1 + c2;
            }
        }

        return suffixes;
    }

}
