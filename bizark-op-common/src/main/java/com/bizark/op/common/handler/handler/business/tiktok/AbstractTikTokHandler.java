package com.bizark.op.common.handler.handler.business.tiktok;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

/**
 * lty notes
 * 定义saas模型决策,其实是适合整体业务已经明朗了,我这里只是一个api调用 并不适合使用,后期扩展的时候可以将整套的脱货换货流程进行策略工厂升级
 *
 * <AUTHOR> Theo
 * @create 2023/10/12 11:49
 */
@Slf4j
public abstract class AbstractTikTokHandler<T,M> implements InitializingBean {




    /**
     * notes: 总结一下逻辑
     * 调用者调用完接口,
     * 1.根据业务需求,拿固定参数 url-path
     * 2.包装常见参数 包装除了access_token这一类必需的参数以外的query参数
     * 3.调用okhttp
     * 4.返回数据进行数据的转换 按照传入的.class文件类型拿包装结束的类型
     * */
    public abstract void getParamsByBusinessDirection();
    public abstract void getParamsForAuth();
    public abstract void callBusinessApi();
    public abstract void responseDataWrapper();

    public final void generalProcess(String platform, String channel,String businessType){
        if(isEnable()){
            log.info("业务钩子通过,saas用户:{},调用平台:{},业务类型:{}",channel,platform,businessType);

        }

    }


    /**
     * 是否启用
     *
     * @return boolean
     */
    public abstract boolean isEnable();

    /**
     * notes 该工厂确定 saas 用户, 然后是 saas用户接下来的选择,比如 要走抖音,走抖音的什么接口 例如 shop内容-getXXXXX
     *
     * @param platform     平台
     * @param businessType 业务类型
     */
    public final void thirdPartyInterfaceCall(String platform, String businessType){
        if(isEnable()){
            log.info("业务钩子通过,三方调用接口记录,调用平台:{},业务类型:{}",platform,businessType);

        }

    }



}
