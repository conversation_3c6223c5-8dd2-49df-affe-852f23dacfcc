package com.bizark.op.common.util;

import org.apache.commons.codec.binary.Hex;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class Sha1Utils {
    //    public static void main(String[] args) throws Exception {
//        String s = "abc";
//        byte[] b = s.getBytes();
//        String ss = sha1(b);
//        System.out.println(ss);
//    }
    public static String sha1(byte[] data) throws NoSuchAlgorithmException {
        MessageDigest sha1 = MessageDigest.getInstance("SHA-1");
        byte[] bytes = sha1.digest(data);
        return Hex.encodeHexString(bytes);
    }




}
