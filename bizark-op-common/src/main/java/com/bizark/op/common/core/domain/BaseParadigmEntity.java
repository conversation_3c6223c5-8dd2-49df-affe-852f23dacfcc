package com.bizark.op.common.core.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 根据设计规范  --  删除时间变为int
 * 用于逻辑删除
 */
@Data
public class BaseParadigmEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createdBy;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updatedBy;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedAt;

    /**
     * 更新者
     */
    private Integer disabledBy;

    /**
     * 更新者
     */
    private String disabledName;

    /**
     * 删除时间
     */
    @TableLogic(value = "0",delval = "1")
    private Integer disabledAt;

    /**
     * 备注
     */
    private String remark;
}
