package com.bizark.op.common.util;

import com.bizark.op.common.core.text.StrFormatter;
import com.bizark.op.common.exception.CustomException;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils
{
    /** 空字符串 */
    private static final String NULLSTR = "";

    /** 下划线 */
    private static final char SEPARATOR = '_';

    /**
     * 获取参数不为空值
     *
     * @param value defaultValue 要判断的value
     * @return value 返回值
     */
    public static <T> T nvl(T value, T defaultValue)
    {
        return value != null ? value : defaultValue;
    }

    /**
     * * 判断一个Collection是否为空， 包含List，Set，Queue
     *
     * @param coll 要判断的Collection
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Collection<?> coll)
    {
        return isNull(coll) || coll.isEmpty();
    }

    /**
     * * 判断一个Collection是否非空，包含List，Set，Queue
     *
     * @param coll 要判断的Collection
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Collection<?> coll)
    {
        return !isEmpty(coll);
    }

    /**
     * * 判断一个对象数组是否为空
     *
     * @param objects 要判断的对象数组
     ** @return true：为空 false：非空
     */
    public static boolean isEmpty(Object[] objects)
    {
        return isNull(objects) || (objects.length == 0);
    }

    /**
     * * 判断一个对象数组是否非空
     *
     * @param objects 要判断的对象数组
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Object[] objects)
    {
        return !isEmpty(objects);
    }

    /**
     * * 判断一个Map是否为空
     *
     * @param map 要判断的Map
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Map<?, ?> map)
    {
        return isNull(map) || map.isEmpty();
    }

    /**
     * * 判断一个Map是否为空
     *
     * @param map 要判断的Map
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Map<?, ?> map)
    {
        return !isEmpty(map);
    }

    /**
     * * 判断一个字符串是否为空串
     *
     * @param str String
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(String str)
    {
        return isNull(str) || NULLSTR.equals(str.trim());
    }

    /**
     * * 判断一个字符串是否为非空串
     *
     * @param str String
     * @return true：非空串 false：空串
     */
    public static boolean isNotEmpty(String str)
    {
        return !isEmpty(str);
    }

    /**
     * * 判断一个对象是否为空
     *
     * @param object Object
     * @return true：为空 false：非空
     */
    public static boolean isNull(Object object)
    {
        return object == null;
    }

    /**
     * * 判断一个对象是否非空
     *
     * @param object Object
     * @return true：非空 false：空
     */
    public static boolean isNotNull(Object object)
    {
        return !isNull(object);
    }

    /**
     * * 判断ID是否为空
     *
     * @param  id
     * @return true：为空 false：非空
     */
    public static boolean isNull(Long id)
    {
        Serializable id1 = (Serializable)id;
        return id == null || StringUtils.isBlank(String.valueOf(id1));
    }

    /**
     * * 判断一个对象是否是数组类型（Java基本型别的数组）
     *
     * @param object 对象
     * @return true：是数组 false：不是数组
     */
    public static boolean isArray(Object object)
    {
        return isNotNull(object) && object.getClass().isArray();
    }

    /**
     * 去空格
     */
    public static String trim(String str)
    {
        return (str == null ? "" : str.trim());
    }

    /**
     * 截取字符串
     *
     * @param str 字符串
     * @param start 开始
     * @return 结果
     */
    public static String substring(final String str, int start)
    {
        if (str == null)
        {
            return NULLSTR;
        }

        if (start < 0)
        {
            start = str.length() + start;
        }

        if (start < 0)
        {
            start = 0;
        }
        if (start > str.length())
        {
            return NULLSTR;
        }

        return str.substring(start);
    }

    /**
     * 截取字符串
     *
     * @param str 字符串
     * @param start 开始
     * @param end 结束
     * @return 结果
     */
    public static String substring(final String str, int start, int end)
    {
        if (str == null)
        {
            return NULLSTR;
        }

        if (end < 0)
        {
            end = str.length() + end;
        }
        if (start < 0)
        {
            start = str.length() + start;
        }

        if (end > str.length())
        {
            end = str.length();
        }

        if (start > end)
        {
            return NULLSTR;
        }

        if (start < 0)
        {
            start = 0;
        }
        if (end < 0)
        {
            end = 0;
        }

        return str.substring(start, end);
    }

    /**
     * 格式化文本, {} 表示占位符<br>
     * 此方法只是简单将占位符 {} 按照顺序替换为参数<br>
     * 如果想输出 {} 使用 \\转义 { 即可，如果想输出 {} 之前的 \ 使用双转义符 \\\\ 即可<br>
     * 例：<br>
     * 通常使用：format("this is {} for {}", "a", "b") -> this is a for b<br>
     * 转义{}： format("this is \\{} for {}", "a", "b") -> this is \{} for a<br>
     * 转义\： format("this is \\\\{} for {}", "a", "b") -> this is \a for b<br>
     *
     * @param template 文本模板，被替换的部分用 {} 表示
     * @param params 参数值
     * @return 格式化后的文本
     */
    public static String format(String template, Object... params)
    {
        if (isEmpty(params) || isEmpty(template))
        {
            return template;
        }
        return StrFormatter.format(template, params);
    }

    /**
     * 字符串转set
     *
     * @param str 字符串
     * @param sep 分隔符
     * @return set集合
     */
    public static final Set<String> str2Set(String str, String sep)
    {
        return new HashSet<String>(str2List(str, sep, true, false));
    }

    /**
     * 字符串转list
     *
     * @param str 字符串
     * @param sep 分隔符
     * @param filterBlank 过滤纯空白
     * @param trim 去掉首尾空白
     * @return list集合
     */
    public static final List<String> str2List(String str, String sep, boolean filterBlank, boolean trim)
    {
        List<String> list = new ArrayList<String>();
        if (StringUtils.isEmpty(str))
        {
            return list;
        }

        // 过滤空白字符串
        if (filterBlank && StringUtils.isBlank(str))
        {
            return list;
        }
        String[] split = str.split(sep);
        for (String string : split)
        {
            if (filterBlank && StringUtils.isBlank(string))
            {
                continue;
            }
            if (trim)
            {
                string = string.trim();
            }
            list.add(string);
        }

        return list;
    }

    /**
     * 下划线转驼峰命名
     */
    public static String toUnderScoreCase(String str)
    {
        if (str == null)
        {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        // 前置字符是否大写
        boolean preCharIsUpperCase = true;
        // 当前字符是否大写
        boolean curreCharIsUpperCase = true;
        // 下一字符是否大写
        boolean nexteCharIsUpperCase = true;
        for (int i = 0; i < str.length(); i++)
        {
            char c = str.charAt(i);
            if (i > 0)
            {
                preCharIsUpperCase = Character.isUpperCase(str.charAt(i - 1));
            }
            else
            {
                preCharIsUpperCase = false;
            }

            curreCharIsUpperCase = Character.isUpperCase(c);

            if (i < (str.length() - 1))
            {
                nexteCharIsUpperCase = Character.isUpperCase(str.charAt(i + 1));
            }

            if (preCharIsUpperCase && curreCharIsUpperCase && !nexteCharIsUpperCase)
            {
                sb.append(SEPARATOR);
            }
            else if ((i != 0 && !preCharIsUpperCase) && curreCharIsUpperCase)
            {
                sb.append(SEPARATOR);
            }
            sb.append(Character.toLowerCase(c));
        }

        return sb.toString();
    }

    /**
     * 是否包含字符串
     *
     * @param str 验证字符串
     * @param strs 字符串组
     * @return 包含返回true
     */
    public static boolean inStringIgnoreCase(String str, String... strs)
    {
        if (str != null && strs != null)
        {
            for (String s : strs)
            {
                if (str.equalsIgnoreCase(trim(s)))
                {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 将下划线大写方式命名的字符串转换为驼峰式。如果转换前的下划线大写方式命名的字符串为空，则返回空字符串。 例如：HELLO_WORLD->HelloWorld
     *
     * @param name 转换前的下划线大写方式命名的字符串
     * @return 转换后的驼峰式命名的字符串
     */
    public static String convertToCamelCase(String name)
    {
        StringBuilder result = new StringBuilder();
        // 快速检查
        if (name == null || name.isEmpty())
        {
            // 没必要转换
            return "";
        }
        else if (!name.contains("_"))
        {
            // 不含下划线，仅将首字母大写
            return name.substring(0, 1).toUpperCase() + name.substring(1);
        }
        // 用下划线将原始字符串分割
        String[] camels = name.split("_");
        for (String camel : camels)
        {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if (camel.isEmpty())
            {
                continue;
            }
            // 首字母大写
            result.append(camel.substring(0, 1).toUpperCase());
            result.append(camel.substring(1).toLowerCase());
        }
        return result.toString();
    }

    /**
     * 驼峰式命名法 例如：user_name->userName
     */
    public static String toCamelCase(String s)
    {
        if (s == null)
        {
            return null;
        }
        s = s.toLowerCase();
        StringBuilder sb = new StringBuilder(s.length());
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++)
        {
            char c = s.charAt(i);

            if (c == SEPARATOR)
            {
                upperCase = true;
            }
            else if (upperCase)
            {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            }
            else
            {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    @SuppressWarnings("unchecked")
    public static <T> T cast(Object obj)
    {
        return (T) obj;
    }



    /**
     * 用于校验指定分割符分割的字符串是否包含指定参数(必填参数为空则返回false)
     * @param inputValue 原字符串
     * @param checkValue 校验的参数
     * @param separator 分隔符号
     * @return {@link Boolean} 结果
     */
    public static Boolean checkStatusContains(String inputValue,String checkValue,String separator){
        //必填参数为空则返回空字符串
        if (isEmpty(inputValue) || isEmpty(checkValue) || isEmpty(separator)){
            return Boolean.FALSE;
        }
        List<String> typeList = new ArrayList<>(Arrays.asList(inputValue.split(separator)));
        if (StringUtils.isEmpty(typeList)){
            return Boolean.FALSE;
        }
        return typeList.contains(checkValue);
    }


    /**
     * 用于删除指定分割符分割的字符串指定参数(必填参数为空则返回空字符串)
     * @param inputString 原字符串
     * @param valueToRemove 需要删除的参数
     * @param separator 指定的分割符号
     * @return {@link String} 返回参数
     */
    public static String removeValue(String inputString, String valueToRemove,String separator) {
        //必填参数为空则返回空字符串
        if (isEmpty(inputString) || isEmpty(valueToRemove) || isEmpty(separator)){
            return "";
        }
        // 将字符串分割成数组
        String[] values = inputString.split(separator);

        if (isEmpty(values)){
            return "";
        }

        // 创建一个列表，用于存储不包含要删除值的元素
        List<String> updatedValues = new ArrayList<>();

        // 遍历数组，将不是要删除值的元素加入列表
        for (String value : values) {
            if (!valueToRemove.equals(value)) {
                updatedValues.add(value);
            }
        }

        // 将列表中的元素连接成字符串，用逗号分隔
        return String.join(separator, updatedValues);
    }

    /**
     * 用于添加指定分割符分割的字符串指定参数(必填参数为空则返回空字符串)
     * @param inputString 原字符串
     * @param valueToAdd 需要添加的参数
     * @param separator 指定的分割符号
     * @return {@link String} 返回参数
     */
    public static String addValue(String inputString, String valueToAdd,String separator) {
        //必填参数为空则返回空字符串
        if (isEmpty(valueToAdd) || isEmpty(separator)){
            return "";
        }
        // 将字符串分割成数组
        String[] values = inputString.split(separator);

        // 创建一个列表，用于存储原始元素
        List<String> updatedValues = new ArrayList<>(Arrays.asList(values));

        // 将要添加的值转换为字符串，并添加到列表中
        updatedValues.add(valueToAdd);

        // 将列表中的元素连接成字符串，用逗号分隔
        return String.join(separator, updatedValues);
    }

    /**
     * 用于添加指定分割符分割的字符串指定参数(必填参数为空则返回空字符串)
     * @param inputString 原字符串
     * @param valueToAdd 需要添加的参数
     * @param separator 指定的分割符号
     * @return {@link String} 返回参数
     */
    public static String addValueReduction(String inputString, String valueToAdd, String separator) {
        // 必填参数为空则返回空字符串
        if (isEmpty(valueToAdd) || isEmpty(separator)) {
            return "";
        }

        // 将字符串分割成数组
        String[] values = inputString.split(separator);

        // 创建一个集合，用于存储不重复的元素
        Set<String> uniqueValues = new HashSet<>(Arrays.asList(values));

        // 如果要添加的值不存在于集合中，则将其添加进去
        uniqueValues.add(valueToAdd);
        // 或者更简洁地，直接从集合构建字符串
         return String.join(separator, uniqueValues);
    }
    /**
     * 分割自定字符串
     * @param splitValue 需要分割的源数据
     * @param identification 分割字符 默认为 ,
     * @return {@link List}<{@link String}> 分割结果
     */
    public static List<String> splitStringValue(String splitValue,String identification){
        if (StringUtils.isEmpty(splitValue)){
            return new ArrayList<>();
        }
        return new ArrayList<>(Arrays.asList(splitValue
                .replaceAll("\t","")
                .replaceAll(" ","")
                .split(StringUtils.isEmpty(identification)?",":identification)));
    }

    /**
     * 分割自定字符串
     * @param splitValue 需要分割的源数据
     * @param identification 分割字符 默认为 ,
     * @return {@link List}<{@link String}> 分割结果
     */
    public static List<String> trimStringValue(String splitValue,String identification){
        if (StringUtils.isEmpty(splitValue)){
            return Collections.emptyList();
        }
        return Arrays.stream(splitValue
                        .replaceAll("\t", "")
                        .split(StringUtils.isEmpty(identification) ? "," : identification))
                .map(String::trim)
                .collect(Collectors.toList());
    }

    /**
     * 去除字符串转义符号与前后空格
     * @param value 字符串
     * @return
     */
    public static String getStringValue(String value){
        if (StringUtils.isEmpty(value)){
            return value
                    .trim()
                    .replaceAll("\t","")
                    .replaceAll(" ","");
        }
        return value;
    }


    /**
     * 去除字符串空格
     */
    public static String removeStringValueSpence(String splitValue){
        if (StringUtils.isEmpty(splitValue)){
            return "";
        }
        return splitValue
                .trim()
                .replaceAll("\t","")
                .replaceAll(" ","");
    }

    /**
     * 去除字符串空格
     */
    public static List<String> removeStringValueSpence(List<String> splitValue){
        if (StringUtils.isEmpty(splitValue)){
            return Collections.emptyList();
        }
        return splitValue.stream().map(StringUtils::removeStringValueSpence).collect(Collectors.toList());
    }

    // 定义一些常见的XSS攻击模式正则表达式
    private static final Pattern XSS_PATTERNS = Pattern.compile(
            "<(script|xss|iframe|form|meta|style|html|body|title|link|img|base|input|textarea|select|button|embed|object|applet|bgsound|body|basefont|div|span|layer|iframe|frameset|ilayer|layer|b|font|xml|blink|marquee|html|meta|link|style|script|embed|object|applet|img|base|input|textarea|select|button|iframe|frameset|ilayer|layer|bgsound|embed|object|applet|meta|xml|blink|marquee|meta|link|style|script|iframe|frameset|ilayer|layer|bgsound|embed|object|applet|img|base|input|textarea|select|button|body|html|meta|link|style|script|embed|object|applet|img|base|input|textarea|select|button|iframe|frameset|ilayer|layer|bgsound|embed|object|applet|xml|blink|marquee|meta|link|style|script|iframe|frameset|ilayer|layer|bgsound|embed|object|applet|img|base|input|textarea|select|button|form|meta|xml|blink|marquee|title|body|html|meta|link|style|script|embed|object|applet|img|base|input|textarea|select|button|iframe|frameset|ilayer|layer|bgsound|embed|object|applet|xml|blink|marquee|meta|link|style|script|iframe|frameset|ilayer|layer|bgsound|embed|object|applet|img|base|input|textarea|select|button|form|xml|blink|marquee|)",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL
    );

    /**
     * 检查字符串是否包含可疑的XSS注入。
     *
     * @param input 要检查的字符串
     * @return 如果字符串包含可疑的XSS注入，则返回true；否则返回false。
     */
    public static boolean containsSuspiciousXss(String input) {
        if (input == null) {
            return false;
        }
        // 使用正则表达式检查字符串是否包含可疑的XSS攻击模式
        return XSS_PATTERNS.matcher(input).find();
    }

    /**
     * 校验排序字段是否合法
     * 传入任意类型和sidx排序字段 sord排序方式
     * 如果等于某个对象的下划线形式 则校验成功否则失败
     */
    public static void validateSort(Class<?> clazz, String sidx, String sord) {
        // Check if sord is either "asc" or "desc"
        if (!("asc".equalsIgnoreCase(sord) || "desc".equalsIgnoreCase(sord))) {
            throw new CustomException("传参方式字段有误！");
        }
        boolean flag = false;
        // Get all fields of the class
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            // Convert the field name to the underscore format
            String underscoreFieldName = toUnderscoreCase(field.getName());
            // 如果说相等则校验通过
            if (underscoreFieldName.equals(sidx)) {
                flag = true;
                break;
            }
        }
        // 如果没有相等的字段则校验失败
        if (!flag) {
            throw new CustomException("传参字段有误！");
        }
    }


    private static String toUnderscoreCase(String camelCase) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char ch = camelCase.charAt(i);
            if (Character.isUpperCase(ch)) {
                result.append('_').append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }
        return result.toString();
    }

    /**
     * 分割列表
     * @param list 源列表
     * @param batchSize 每批的数据
     */
    public static  <T> List<List<T>> splitList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            batches.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return batches;
    }

}

