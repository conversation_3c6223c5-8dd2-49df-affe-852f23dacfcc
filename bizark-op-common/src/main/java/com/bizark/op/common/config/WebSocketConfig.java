package com.bizark.op.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * @Description WebSocket配置类。开启WebSocket的支持
 * <AUTHOR>
 * @create: 2024-03-26 15:55
 */
@Configuration
public class WebSocketConfig {
    private static final Logger log = LoggerFactory.getLogger(WebSocketConfig.class);

    /**
     * bean注册：会自动扫描带有@ServerEndpoint注解声明的Websocket Endpoint(端点)，注册成为Websocket bean。
     * 要注意，如果项目使用外置的servlet容器，而不是直接使用springboot内置容器的话，就不要注入ServerEndpointExporter，因为它将由容器自己提供和管理。
     * 这里根据配置文件配置的spring.websocket.enabled属性来决定是否启用WebSocket功能。
     */
    @Bean
    @ConditionalOnMissingBean(name = "serverEndpointExporter")
    @ConditionalOnProperty(name = "spring.websocket.enabled", havingValue = "true")
    public ServerEndpointExporter serverEndpointExporter() {
        log.info("开始注入ServerEndpointExporter");
        return new ServerEndpointExporter();

    }


}

