package com.bizark.op.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/11/30 10:12
 */
@Component
@Slf4j
public class RedisUtils {

    @Resource
    private RedisTemplate redisTemplate;

    private static final List<Object> VALUE_REDIS_KEY_LIST = CollUtil.newArrayList();

    public Boolean isExist(List<String> keys, List<String> values) {
        String script =
                "if redis.call('exists', KEYS[1],600) == 1 then\n" +
                        "    return true\n" +
                        "else\n" +
                        "    redis.call('setex', KEYS[1], 600, ARGV[1])\n" +
                        "    return false\n" +
                        "end";
        Boolean result = (Boolean) redisTemplate.execute(new DefaultRedisScript<>(script, Boolean.class), keys, values);
        return result;
    }

    /**
     * 取消链接
     *
     * @param keys   钥匙
     * @param values 值
     * @return {@link Boolean }
     * <AUTHOR>
     */
    public Boolean unlink(List<String> keys, List<String> values) {
        redisTemplate.unlink(keys);
        return Boolean.TRUE;
    }

    public Boolean hasKey(Object key) {
        return redisTemplate.hasKey(key);
    }


    public Boolean delete(Object key) {
        if (ObjectUtil.isNull(key)) {
            return Boolean.TRUE;
        }
        if (key instanceof Collection) {
            Collection keys = (Collection) key;
            if (CollUtil.isNotEmpty(keys)) {
                redisTemplate.delete(keys);
                VALUE_REDIS_KEY_LIST.removeIf(keys::contains);
            }
        } else {
            redisTemplate.delete(key);
            VALUE_REDIS_KEY_LIST.remove(key);
        }
        return Boolean.TRUE;
    }

    public Boolean valueSet(Object key, Object value) {
        redisTemplate.opsForValue().set(key, value);
        VALUE_REDIS_KEY_LIST.add(key);
        return Boolean.TRUE;
    }

    public Object get(Object key) {
        return redisTemplate.opsForValue().get(key);
    }

    public Boolean valueSet(Object key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
        VALUE_REDIS_KEY_LIST.add(key);
        return Boolean.TRUE;
    }

    @PostConstruct
    public void deleteRedisKey() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("系统重启，删除缓存的redis key {}", VALUE_REDIS_KEY_LIST);
            if (CollUtil.isNotEmpty(VALUE_REDIS_KEY_LIST)) {
                redisTemplate.unlink(VALUE_REDIS_KEY_LIST);
            }
        }));
    }
}
