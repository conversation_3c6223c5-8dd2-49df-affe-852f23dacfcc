package com.bizark.op.common.util;

import net.sf.cglib.beans.BeanCopier;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 用于拷贝值
 */
public class BeanCopyUtils {
    private BeanCopyUtils() {

    }

    /**
     * 单个对象的拷贝方法
     *
     * @param source 源数据
     * @param clazz  泛型
     * @return V
     */
    public static <V> V copyBean(Object source, Class<V> clazz) {
        V result = null;
        try {
            result = clazz.newInstance();
            BeanUtils.copyProperties(source, result);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    /**
     * List集合的拷贝方法
     *
     * @param list  源数据
     * @param clazz 泛型
     * @return java.util.List<V>
     */
    public static <O, V> List<V> copyBeanList(List<O> list, Class<V> clazz) {
        return list.stream()
                //先将stream流中的每一个元素进行拷贝，然后再收集为集合
                .map(o -> copyBean(o, clazz))
                .collect(Collectors.toList());
    }

    public static <S, D> List<D> asmCopyList(List<S> source, Class<D> dClass) {
        List<D> result = new ArrayList<>(source.size());
        try {
            for (S s : source) {
                D d = (D) dClass.newInstance();
                BeanCopier copier = BeanCopier.create(s.getClass(), dClass, false);
                copier.copy(s, d, null);
                result.add(d);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static <S, D> D asmCopyBean(S source, Class<D> dClass) {
        try {
            D d = (D) dClass.newInstance();
            BeanCopier copier = BeanCopier.create(source.getClass(), dClass, false);
            copier.copy(source, d, null);
            return d;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
