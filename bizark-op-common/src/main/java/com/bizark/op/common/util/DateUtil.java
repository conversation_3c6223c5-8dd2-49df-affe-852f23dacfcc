package com.bizark.op.common.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.bizark.op.common.enm.MonthAbbreviationEnum;
import com.github.pagehelper.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class DateUtil {
    private static final Logger log = LoggerFactory.getLogger(DateUtil.class);

    // 一天是多少毫秒
    public static final long DAY = 24L * 60L * 60L * 1000L;

    public static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd");
    public static final SimpleDateFormat YMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static final String ISO_PATTERN_YMDHMSSZ = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final String ORIGINAL_PATTERN_MDYKMSA = "MMM d, yyyy K:m:s a";
    public static final String ORIGINAL_PATTERN_MDYKMS = "MMM d, yyyy K:m:s";
    public static final String ORIGINAL_PATTERN_MDY = "MMM d, yyyy";

    /**
     * 根据兩個个日期，取得相隔的天数
     *
     * @param d1 前一个日期
     * @param d2 后一个日期
     * @return 相差的天数
     */
    public static int getBetweenDayNumber(Date d1, Date d2) {
        // 根据兩個个日期，取得相隔的天数
        long dayNumber = 0;
        try {
            // 计算两个日期的时间差（以毫秒记）
            dayNumber = (d2.getTime() - d1.getTime()) / DAY;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (int) dayNumber;
    }
    public static Date convertPSTtoCST(Date date) {
         if (Objects.isNull(date)) {
             return null;
         }
        // 1. 获取这个Date在当前系统时区的本地时间
        LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());

        // 2. 将这个本地时间解释为美西时间
        ZonedDateTime pstTime = localDateTime.atZone(ZoneId.of("America/Los_Angeles"));

        // 3. 转换为北京时间
        ZonedDateTime cstTime = pstTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));

        // 4. 返回转换后的北京时间作为Date对象
        return Date.from(cstTime.toInstant());
    }
    /**
     * 根据兩個个日期，取得相隔的天数
     *
     * @param date1 前一个日期字符串
     * @param date2 后一个日期字符串
     * @return 相差的天数
     */
    public static int getBetweenDayNumber(String date1, String date2) {
        // 根据兩個个日期，取得相隔的天数
        long dayNumber = 0;
        try {
            Date d1 = SDF.parse(date1);
            Date d2 = SDF.parse(date2);
            // 计算两个日期的时间差（以毫秒记）
            dayNumber = (d2.getTime() - d1.getTime()) / DAY;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (int) dayNumber;
    }

    /**
     * 根据兩個个日期，取得相隔的分钟数
     *
     * @param date1 前一个日期字符串
     * @param date2 后一个日期字符串
     * @return 相差的分钟数
     */
    public static int getBetweenMinute(Date date1, Date date2) {
        // 根据兩個个日期，取得相隔的分钟数
        long minuteNumber = 0;
        try {
            // 计算两个日期的时间差（以毫秒记）
            minuteNumber = (date2.getTime() - date1.getTime()) / (60L * 1000L);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (int) minuteNumber;
    }

    /**
     * 根据兩個个日期，取得相隔的月数
     *
     * @param date1 前一个日期（小的）
     * @param date2 后一个日期（大的）
     * @return 相差的月数
     */
    public static int getBetweenMountNumber(Date date1, Date date2) {
        Calendar cal1 = new GregorianCalendar();
        cal1.setTime(date1);
        Calendar cal2 = new GregorianCalendar();
        cal2.setTime(date2);
        int c = (cal1.get(Calendar.YEAR) - cal2.get(Calendar.YEAR)) * 12 + cal1.get(Calendar.MONTH) - cal2.get(Calendar.MONTH);
        return c;
    }

    /**
     * 说明：比较两个日期大小,Calendar格式<br>
     * 备注：cal1>cal2 返回1 ,cal1 = cal2 返回0 , cal1 <cal2 返回 -1<br>
     *
     * @param cal1 日期1
     * @param cal2 日期2
     * @return 比较结果
     */
    public static int compareDate(Calendar cal1, Calendar cal2) {
        if (cal1.getTimeInMillis() > cal2.getTimeInMillis()) {
            return 1;
        } else if (cal1.getTimeInMillis() == cal2.getTimeInMillis()) {
            return 0;
        } else {
            return -1;
        }
    }

    /**
     * 说明：比较两个日期大小,Date格式<br>
     * 备注：date1>date2 返回1 ,date1 = date2 返回0 , date1 <date2 返回 -1<br>
     *
     * @param date1 日期1
     * @param date2 日期2
     * @return 比较结果
     */
    public static int compareDate(Date date1, Date date2) {
        if (date1.getTime() > date2.getTime()) {
            return 1;
        } else if (date1.getTime() == date2.getTime()) {
            return 0;
        } else {
            return -1;
        }
    }

    /**
     * 说明：比较两个日期大小,YYYY-MM-DD格式<br>
     * 备注：d1>d2 返回1 ,d1 = d2 返回0 , d1 <d2 返回 -1<br>
     *
     * @param d1 日期1
     * @param d2 日期2
     * @return 比较结果
     * @throws ParseException 格式转化异常
     */
    public static int compareDate(String d1, String d2) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = null;
        Date date2 = null;
        try {
            date1 = sdf.parse(d1);
            date2 = sdf.parse(d2);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return compareDate(date1, date2);
    }

    /**
     * 说明：比较两个日期大小,YYYY-MM-DD格式<br>
     * 备注：d1>d2 返回1 ,d1 = d2 返回0 , d1 <d2 返回 -1<br>
     *
     * @param d1 日期1
     * @param d2 日期2
     * @return 比较结果
     * @throws ParseException 格式转化异常
     */
    public static int compareTime(String d1, String d2)
            throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date1 = sdf.parse(d1);
        Date date2 = sdf.parse(d2);
        return compareDate(date1, date2);
    }

    /**
     * 计算一个日期加上payment_freq*payment_unit后的日期 payment_freq 收付息周期
     *
     * @param d            时间
     * @param payment_freq 数字
     * @param payment_unit 单位
     * @return 下一个收付息日
     */
    public static Date addDate(Date d, int payment_freq, String payment_unit) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        if (payment_unit.equals("D")) {
            cal.add(Calendar.DAY_OF_MONTH, payment_freq);
        } else if (payment_unit.equals("M")) {
            cal.add(Calendar.MONTH, payment_freq);
        } else if (payment_unit.equals("Y")) {
            cal.add(Calendar.YEAR, payment_freq);
        } else if (payment_unit.equals("H")) {
            cal.add(Calendar.HOUR, payment_freq);
        } else if (payment_unit.equals("MI")) {
            cal.add(Calendar.MINUTE, payment_freq);
        } else if (payment_unit.equals("W")) {
            cal.add(Calendar.WEEK_OF_MONTH, payment_freq);
        }
        return cal.getTime();
    }

    /**
     * 说明：计算一个日期加上payment_freq*payment_unit后的日期 payment_freq 收付息周期<br>
     * 备注：<br>
     *
     * @param d            计算日
     * @param payment_freq 数字
     * @param payment_unit 单位
     * @return 下一个收付息日的yyyy-mm-dd字符串
     */
    public static String addDate(String d, int payment_freq, String payment_unit) {
        String result = null;
        result = convertDateToString(addDate(convertStringToDate(d), payment_freq, payment_unit));
        return result;
    }

    /**
     * 根据String类型的date得到月份
     *
     * @param date 时间
     * @return 当前时间的月份
     * @throws ParseException 数字转换异常
     */
    public static int getMonth(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        int ke = 0;
        try {
            Date d = sdf.parse(date);
            Calendar cal = Calendar.getInstance();
            cal.setTime(d);
            // 月份从0-11
            ke = cal.get(Calendar.MONTH) + 1;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return ke;
    }

    /**
     * 根据String类型的date得到年份<br>
     * 备注：<br>
     *
     * @param date 时间
     * @return 当前时间的年份
     * @throws ParseException 数字转换异常
     */
    public static int getYear(String date)
            throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date d = sdf.parse(date);
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        int ke = cal.get(Calendar.YEAR);
        return ke;
    }

    /**
     * 根据String类型的date得到天<br>
     * 备注：<br>
     *
     * @param date 时间
     * @return 当前时间的日期
     * @throws ParseException 数字转换异常
     */
    public static int getDay(String date)
            throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date d = sdf.parse(date);
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        int ke = cal.get(Calendar.DATE);
        return ke;
    }

    /**
     * 计算一个日期加上day天后的日期 date为“yyyy-MM-dd”的形式
     *
     * @param date 日期字符串
     * @param day  天数
     * @return day天后的日期
     */
    public static String addDate(String date, int day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date d = null;
        try {
            d = sdf.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.DAY_OF_MONTH, day);
        return sdf.format(cal.getTime());
    }

    /**
     * 计算一个日期加上day天后的日期 date为“yyyy-MM-dd”的形式
     *
     * @param date 日期
     * @param day  天数
     * @return day天后的日期
     */
    public static Date addDate(Date date, int day) {
        Date d = date;
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.DAY_OF_MONTH, day);
        return cal.getTime();
    }

    /**
     * 计算一个日期加上day天后的日期 date为“yyyy-MM-dd”的形式
     *
     * @param cal 日期
     * @param day 天数
     * @return day天后的日期
     */
    public static Calendar addDate(Calendar cal, int day) {
        cal.add(Calendar.DAY_OF_MONTH, day);
        return cal;
    }

    /**
     * 计算一个日期加上year年后的日期 date为“yyyy-MM-dd”的形式
     *
     * @param date 日期
     * @param year 年数
     * @return year年后的日期
     */
    public static Date addYear(Date date, int year) {
        Date d = date;
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.YEAR, year);
        return cal.getTime();
    }

    /**
     * 计算一个日期加上year年后的日期 date为“yyyy-MM-dd”的形式
     *
     * @param cal  时间
     * @param year 年数
     * @return year年后的日期
     */
    public static Calendar addYear(Calendar cal, int year) {
        cal.add(Calendar.YEAR, year);
        return cal;
    }

    /**
     * 获取当前系统时间
     *
     * @return 返回系统日期的字符串
     */
    public static String getSysDate() {
        return new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(Calendar.getInstance().getTime());
    }

    /**
     * 说明： 判断两个日期段是否交叉<br>
     * 备注：算头不算尾<br>
     *
     * @param date1F 第一个日期区间起始日
     * @param date1T 第一个日期区间截止日
     * @param date2F 第二个日期区间起始日
     * @param date2T 第二个日期区间截止日
     * @return true or false
     * @throws ParseException 数字转换异常
     */
    public static boolean isDateCross(String date1F, String date1T, String date2F, String date2T)
            throws ParseException {
        // 日期段2的起始日介于日期段1区间内
        if (compareDate(date1F, date2F) <= 0 && compareDate(date2F, date1T) < 0) {
            return true;
            // 日期段1的起始日介于日期段2区间内
        } else if (compareDate(date2F, date1F) <= 0 && compareDate(date1F, date2T) < 0) {
            return true;
            // 其余场合
        } else {
            return false;
        }
    }

    /**
     * 将Date转换成String
     *
     * @param todayDate 当前日期,Date类型
     * @return 转换成字符串类型, yyyy-mm-dd
     */
    public static String convertDateToString(Date todayDate) {
        // 返回的字符串
        String tempStr = SDF.format(todayDate);
        return tempStr;
    }

    /**
     * 将Date转换成String
     *
     * @param todayDate 当前日期,Date类型
     * @param format    转换格式的类型
     * @return 转换成字符串类型
     */
    public static String convertDateToString(Date todayDate, String format) {
        // 日期格式转换
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        // 返回的字符串
        String tempStr = sdf.format(todayDate);
        return tempStr;
    }

    /**
     * 将字符串类型的转换成Date类型
     *
     * @param dateStr 字符串类型的日期 yyyy-MM-dd
     * @return Date类型的日期
     * @throws ParseException
     */
    public static Date convertStringToDate(String dateStr) {
        // 返回的日期
        Date resultDate = null;
        try {
            // 日期格式转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            resultDate = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return resultDate;
    }

    /**
     * @param dateStr   字符串类型的日期
     * @param formatStr 格式化类型
     * @return 时间格式的日期
     * @since 2014-12-30 14:09
     */
    public static Date convertStringToDate(String dateStr, String formatStr) {
        // 返回的日期
        Date resultDate = null;
        try {
            // 日期格式转换
            SimpleDateFormat sdf = new SimpleDateFormat(formatStr);
            resultDate = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return resultDate;
    }

    /**
     * 将字符串类型的转换成Date类型
     *
     * @param dateStr 字符串类型的日期 yyyyMMdd
     * @return Date类型的日期
     * @throws ParseException 数字转换异常
     */
    public static Date convertStringToDate_Other(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        // 返回的日期
        Date resultDate = null;
        try {
            resultDate = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return resultDate;
    }

    /**
     * 将字符串类型的转换成Date类型
     *
     * @param dateStr 字符串类型的日期 dd-MMM-yyyy
     * @return Date类型的日期
     * @throws ParseException 数字转换异常
     */
    public static String convertStringToDate_dmy(String dateStr) {
        String resultDate = "";
        try {
            String[] strings = dateStr.split("-");
            String nDateStr = strings[2] + "-" + MonthAbbreviationEnum.getName(strings[1]) + "-" + strings[0];
            resultDate = convertDateToString(convertStringToDate(nDateStr, DatePattern.NORM_DATE_PATTERN));
        } catch (Exception e) {
            return dateStr;
        }
        return resultDate;
    }

    /**
     * 转换Jul 8, 2020 12:00:00 AM 格式的时间
     *
     * @param amzDate Jul 8, 2020 12:00:00 AM 格式的时间
     * @return
     */
    public static String convertOriginalDate(String amzDate) {
        if (StringUtil.isEmpty(amzDate)) {
            return null;
        }
        String pattern = DatePattern.NORM_DATE_PATTERN;
        String originalPattern = DateUtil.ORIGINAL_PATTERN_MDY;
        if (amzDate.indexOf(":") > 0) {
            pattern = DatePattern.NORM_DATETIME_PATTERN;
            int count = StringUtils.countMatches(amzDate, ":");
            if (count > 0) {
                originalPattern = DateUtil.ORIGINAL_PATTERN_MDYKMSA;
                if (count < 2) {
                    amzDate = amzDate + "00:00";
                    originalPattern = DateUtil.ORIGINAL_PATTERN_MDYKMS;
                }
            }
        } else {
            amzDate = amzDate.trim();
            int spaceCount = StringUtils.countMatches(amzDate, " ");
            if (spaceCount < 1) {
                return amzDate;
            } else if (spaceCount < 2) {
                amzDate = amzDate.replace(",", ", ");
            }
        }
        return parseOriginalDate(amzDate, originalPattern, pattern);
    }

    /**
     * 把 Jul 8, 2020 12:00:00 AM 格式的时间转换为 2020-07-08 12:00:00 格式的时间
     *
     * @param date    Jul 8, 2020 12:00:00 AM 格式的时间
     * @param pattern 日期模式
     */
    public static String parseOriginalDate(String date, String originalPattern, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(originalPattern, Locale.ENGLISH);
        Date d2 = null;
        try {
            //把Jul 8, 2020 12:00:00 AM格式转换为常规的Date格式
            d2 = sdf.parse(date);
        } catch (ParseException e) {
            log.error("原始日期格式转换错误 {}", e.getMessage());
            return date;
        } catch (Exception e) {
            log.error("原始日期格式转换异常 {}", e.getMessage());
            return date;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        //再把Date的时间转换为需要的格式
        String format = simpleDateFormat.format(d2);
        return format;
    }

    /**
     * 获取月末日期
     *
     * @param todayDate 任意日期
     * @return 获取月末日期
     */
    public static Date getLastDay(Date todayDate) {
        Calendar calendar = Calendar.getInstance();
        // 把平台日期赋值给Calendar
        calendar.setTime(todayDate);
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
        int endday = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, endday);
        return calendar.getTime();
    }

    /**
     * 判断字符串是否为日期
     *
     * @param str 任意字符串
     * @return true or false
     */
    public static boolean isDateStr(String str) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            format.parse(str);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 日期按月相加，
     *
     * @param date 原日期
     * @param num  相加月数，正数为加，负数为减
     * @return Date 计算后的日期
     */
    public static Date addMonthDate(Date date, Integer num) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, num);// 日期减月
        Date dt1 = calendar.getTime();
        String reStr = format.format(dt1);
        return convertStringToDate(reStr);
    }

    /**
     * 日期按月相加，
     *
     * @param str 原日期字符串
     * @param num 相加月数，正数为加，负数为减
     * @return Date 计算后的日期
     */
    public static Date addMonthDateStr(String str, Integer num) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(convertStringToDate(str));
        calendar.add(Calendar.MONTH, num);// 日期减1个月
        Date dt1 = calendar.getTime();
        String reStr = format.format(dt1);
        return convertStringToDate(reStr);
    }

    /**
     * 日期按月相加
     *
     * @param str 原日期字符串
     * @param num 相加月数，正数为加，负数为减
     * @return String 计算后的日期字符串
     */
    public static String addMonthDate(String str, Integer num) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(convertStringToDate(str));
        calendar.add(Calendar.MONTH, num);// 日期减1个月
        Date dt1 = calendar.getTime();
        String reStr = format.format(dt1);
        return reStr;
    }

    /**
     * 根据兩個个日期，取得相隔的实际年数，精确到4位小数
     *
     * @param date1 任意日期1
     * @param date2 任意日期2
     * @return 两日期相隔年数
     */
    public static BigDecimal getBetweenYearNumberBig(Date date1, Date date2) {
        Calendar cal1 = new GregorianCalendar();
        cal1.setTime(date1);
        Calendar cal2 = new GregorianCalendar();
        cal2.setTime(date2);

        int year = cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR);
        int mount = cal2.get(Calendar.MONTH) - cal1.get(Calendar.MONTH);
        int day = cal2.get(Calendar.DATE) - cal1.get(Calendar.DATE);

        // 年>0,月、天=0
        if (year == 0 && mount == 0 && day == 0) {
            return BigDecimal.ZERO;
        } else {
            if (year > 0 && mount == 0 && day == 0) {
                return new BigDecimal(year);
            } else {
                // 根据兩個个日期，取得相隔的天数
                long dayNumber = 0;
                try {
                    // 计算两个日期的时间差（以毫秒记）
                    dayNumber = (date2.getTime() - date1.getTime()) / DAY;
                } catch (Exception e) {
                    e.printStackTrace();
                }
                BigDecimal yearbig = new BigDecimal(dayNumber).divide(new BigDecimal(365), 4, RoundingMode.HALF_UP);
                return yearbig;
            }
        }
    }

    /**
     * 根据兩個个日期，取得相隔的实际月数，精确到4位小数
     *
     * @param date1 任意日期1
     * @param date2 任意日期2
     * @return 两日期相隔的月数
     */
    public static BigDecimal getBetweenMountNumberBig(Date date1, Date date2) {
        Calendar cal1 = new GregorianCalendar();
        cal1.setTime(date1);
        Calendar cal2 = new GregorianCalendar();
        cal2.setTime(date2);

        int mount = (cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR)) * 12
                + cal2.get(Calendar.MONTH) - cal1.get(Calendar.MONTH);
        int day = cal2.get(Calendar.DATE) - cal1.get(Calendar.DATE);

        // 月>0,天=0
        if (mount == 0 && day == 0) {
            return BigDecimal.ZERO;
        } else {
            if (mount > 0 && day == 0) {
                return new BigDecimal(mount);
            } else {
                // 根据兩個个日期，取得相隔的天数
                long dayNumber = 0;
                try {
                    // 计算两个日期的时间差（以毫秒记）
                    dayNumber = (date2.getTime() - date1.getTime()) / DAY;
                } catch (Exception e) {
                    e.printStackTrace();
                }
                BigDecimal mountbig = new BigDecimal(dayNumber).divide(new BigDecimal(30), 4, RoundingMode.HALF_UP);
                return mountbig;
            }
        }
    }

    /**
     * 根据兩個个日期，取得相隔的实际年数，精确到4位小数
     *
     * @param date1 任意时间1
     * @param date2 任意时间2
     * @return 两个时间相隔的年数
     */
    public static BigDecimal getBetweenDayNumberBig(Date date1, Date date2) {
        Calendar cal1 = new GregorianCalendar();
        cal1.setTime(date1);
        Calendar cal2 = new GregorianCalendar();
        cal2.setTime(date2);

        // 根据兩個个日期，取得相隔的天数
        long dayNumber = 0;
        try {
            // 计算两个日期的时间差（以毫秒记）
            dayNumber = (date2.getTime() - date1.getTime()) / DAY;
        } catch (Exception e) {
            e.printStackTrace();
        }
        BigDecimal yearbig = new BigDecimal(dayNumber);
        return yearbig;
    }

    /**
     * 将时间转换成时间值
     *
     * @param date 任意时间
     * @return 时间值
     */
    public static long parse(String date) {
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(SDF.parse(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return calendar.getTimeInMillis();
    }

    /**
     * 获得指定日期的前n个工作日(周一到周五)
     *
     * @param toDay 指定日期
     * @param days  前几个工作日
     * @return 返回前days个工作日的日期
     */
    public static Date getLastWeekDay(Date toDay, int days) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(toDay);
        int result = 0;
        while (result < days) {
            // 不等于周一 && 不等于周日
            if (cal.get(Calendar.DAY_OF_WEEK) != 2 && cal.get(Calendar.DAY_OF_WEEK) != 1) {
                result++;
                // 日期-1
                cal.add(Calendar.DAY_OF_MONTH, -1);

            } else {
                // 日期-1
                cal.add(Calendar.DAY_OF_MONTH, -1);
            }
        }
        return cal.getTime();
    }

    /**
     * 获得指定日期的后n个工作日(周一到周五)
     *
     * @param toDay 指定日期
     * @param days  后几个工作日
     * @return 返回后days个工作日的日期
     */
    public static Date getNextWeekDay(Date toDay, int days) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(toDay);

        int result = 0;
        while (result < days) {
            // 不等于周五&&不等于周六
            if (cal.get(Calendar.DAY_OF_WEEK) != 6 && cal.get(Calendar.DAY_OF_WEEK) != 7) {
                result++;
                cal.add(Calendar.DAY_OF_MONTH, 1);
            } else {
                cal.add(Calendar.DAY_OF_MONTH, 1);
            }
        }
        return cal.getTime();
    }

    /**
     * 获取指定日期后间隔n个工作日
     *
     * @param d        待调整的日期
     * @param holidays 节假日表
     * @param days     间隔天数
     * @return 调整以后的日期
     */
    public static String getNextWeekDay(String d, List<String> holidays, int days) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(d));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (holidays == null || holidays.size() == 0) {
            cal.add(Calendar.DATE, days);
            d = sdf.format(cal.getTime());
            return d;
        } else {
            int num = 0;
            while (true) {
                if (num == days) {
                    break;
                } else {
                    cal.add(Calendar.DATE, 1);
                    d = sdf.format(cal.getTime());
                    if (!holidays.contains(d)) {
                        num++;
                    }
                }
            }
            return d;
        }
    }

    /**
     * 获取指定日期前间隔n个工作日
     *
     * @param d        待调整的日期
     * @param holidays 节假日表
     * @param days     间隔天数
     * @return 调整以后的日期
     */
    public static String getLastWeekDay(String d, List<String> holidays, int days) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(d));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (holidays == null || holidays.size() == 0) {
            cal.add(Calendar.DATE, days * (-1));
            d = sdf.format(cal.getTime());
            return d;
        } else {
            int num = 0;
            while (true) {
                if (num == days) {
                    break;
                } else {
                    cal.add(Calendar.DATE, -1);
                    d = sdf.format(cal.getTime());
                    if (!holidays.contains(d)) {
                        num++;
                    }
                }
            }
            return d;
        }
    }

    /**
     * 判断某天是否是月末
     *
     * @param date 任意日期
     * @return true or false
     */
    public static boolean isLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE, (calendar.get(Calendar.DATE) + 1));
        if (calendar.get(Calendar.DAY_OF_MONTH) == 1) {
            return true;
        }
        return false;
    }

    /**
     * 获取某月的最后一天
     *
     * @param date 任意日期
     * @return 返回当前月份的最后一天
     */
    public static Date getLastDayOfMonth(Date date) {
        Calendar day = Calendar.getInstance();
        day.setTime(date);
        int lastDay = day.getActualMaximum(Calendar.DAY_OF_MONTH);
        day.set(Calendar.DAY_OF_MONTH, lastDay);
        Date lastDate = day.getTime();
        return lastDate;
    }

    /**
     * 根据String类型的date得到时<br>
     * 备注：<br>
     *
     * @return 当前时间的日期
     */
    public static int getCurrentHour() {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        return hour;
    }

    /**
     * 将字符串类型的转换成ISO日期类型<br>
     * 备注：<br>
     *
     * @return ISO日期类型字符串
     */
    public static String convertStringToISODate(String dateStr) {
        if (StringUtil.isEmpty(dateStr)) {
            return null;
        }
        //String pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
        String pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'";
        return convertStringToISODate(dateStr, pattern);
    }

    /**
     * 将字符串类型的转换成ISO日期类型<br>
     * 备注：<br>
     *
     * @return ISO日期类型字符串
     */
    public static String convertStringToISODate(String dateStr, String pattern) {
        if (StringUtil.isEmpty(dateStr)) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(pattern);
        //df.setTimeZone(TimeZone.getTimeZone("UTC"));
        if (isDateStr(dateStr)) {
            return df.format(convertStringToDate(dateStr, "yyyy-MM-dd HH:mm:ss"));
        } else {
            return dateStr;
        }
    }

    public static String convertTimestampToStrDate(String timestampStr) {
        if (StringUtil.isEmpty(timestampStr)) {
            return null;
        }
        try {
            long msl = Long.parseLong(timestampStr);
            Date date = new Date();
            date.setTime(msl);
            return convertDateToString(date, DatePattern.NORM_DATETIME_PATTERN);
        } catch (Exception e) {
            return timestampStr;
        }
    }

    /**
     * 获取日期半年最后一天
     *
     * @param date
     * @return
     */
    public static Calendar endOfHalfYear(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int year = cal.get(Calendar.YEAR);
        int mon = cal.get(Calendar.MONTH);
        Calendar c1 = Calendar.getInstance();
        c1.set(Calendar.YEAR, year);
        if (mon < 6) {
            c1.set(Calendar.MONTH, 5);
            c1.set(Calendar.DATE, 30);
        } else {
            c1.set(Calendar.MONTH, 11);
            c1.set(Calendar.DATE, 31);
        }
        return c1;
    }

    /**
     * 将localDate类型的转换成Date类型
     *
     * @param localDate
     * @return
     */
    public static Date transLocalDateToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将localDateTime类型的转换成Date类型
     *
     * @param localDateTime
     * @return
     */
    public static Date transLocalDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将Date类型的转换成LocalDate类型
     *
     * @param date
     * @return
     */
    public static LocalDate transDateToLocalDate(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 将Date类型的转换成LocalDateTime类型
     *
     * @param date
     * @return
     */
    public static LocalDateTime transDateToLocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * @param length 时间字符长度
     * @param sDate  时间字符串
     * @param format 校验格式
     * @return 判断是否为指定格式时间数据
     */
    public static boolean isLegalDate(int length, String sDate, String format) {
        int legalLen = length;
        if ((sDate == null) || (sDate.length() != legalLen)) {
            return false;
        }
        DateFormat formatter = new SimpleDateFormat(format);
        try {
            Date date = formatter.parse(sDate);
            return sDate.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * @param ustime 美西时间 yyyy-MM-dd HH:mm:ss
     * @return  美西时间转UTC时间 yyyy-MM-dd HH:mm:ss
     */
    public static String pacificTimeToUtcTime(String ustime) {
        ZoneId newZone = ZoneId.of("UTC");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(ustime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("America/Los_Angeles"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }


    /**
     * @param date 美西时间 yyyy-MM-dd HH:mm:ss
     * @return  美西时间转UTC时间 yyyy-MM-dd HH:mm:ss
     */
    public static Date pacificTimeToUtcDate(Date date) {
        return convertStringToDate(pacificTimeToUtcTime(DateUtil.convertDateToString(date,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }


    /**
     * @param utcTime UTC时间 yyyy-MM-dd HH:mm:ss
     * @return  美西时间转UTC时间 yyyy-MM-dd HH:mm:ss
     */
    public static String UtcToPacificTime(String utcTime) {
        ZoneId newZone = ZoneId.of("America/Los_Angeles");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(utcTime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("UTC"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }


    /**
     * @param utcDate UTC时间 yyyy-MM-dd HH:mm:ss
     * @return  美西时间转UTC时间 yyyy-MM-dd HH:mm:ss
     */
    public static Date UtcToPacificDate(Date utcDate) {
        return convertStringToDate(UtcToPacificTime(DateUtil.convertDateToString(utcDate,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }


    /**
     * @param ustime 美西时间 yyyy-MM-dd HH:mm:ss
     * @return  美西时间转北京时间 yyyy-MM-dd HH:mm:ss
     */
    public static String pacificTimeToBjTime(String ustime) {
        ZoneId newZone = ZoneId.of("Asia/Shanghai");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(ustime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("America/Los_Angeles"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }


    /**
     * @param date 美西时间 yyyy-MM-dd HH:mm:ss
     * @return  美西时间转北京时间 yyyy-MM-dd HH:mm:ss
     */
    public static Date pacificTimeToBjDate(Date date) {
        return convertStringToDate(pacificTimeToBjTime(DateUtil.convertDateToString(date,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * @param timestamp 时间戳（BJ）
     * @return  PST时间
     */
    public static Date BjToPstByTimestamp(Long timestamp) {
        if (null == timestamp) {
            return null;
        }
        String bjTime = cn.hutool.core.date.DateUtil.format(new Date(timestamp * 1000L), DatePattern.NORM_DATETIME_FORMAT);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  //返回为北京时间
        sdf.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
        String pstTime = sdf.format(cn.hutool.core.date.DateUtil.parse(bjTime));  //转换为PST时间
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(pstTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 判断某个日期是否在特定的时间范围内
     * @param dateStr 目标日期
     * @param startDateStr 开始日期
     * @param endDateStr 结束日期
     * @return
     */
    public static boolean isDateInRange(String dateStr, String startDateStr, String endDateStr) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date targetDate = dateFormat.parse(dateStr);
            Date startDate = dateFormat.parse(startDateStr);
            Date endDate = dateFormat.parse(endDateStr);
            return !targetDate.before(startDate) && !targetDate.after(endDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * @param bjtime 北京时间 yyyy-MM-dd HH:mm:ss
     * @return  北京时间转美西时间 yyyy-MM-dd HH:mm:ss
     */
    public static String bjTimeToPacificTime(String bjtime) {
        ZoneId newZone = ZoneId.of("America/Los_Angeles");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(bjtime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("Asia/Shanghai"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }

    /**
     * @param date 北京时间 yyyy-MM-dd HH:mm:ss
     * @return  北京时间转美西时间 yyyy-MM-dd HH:mm:ss
     */
    public static Date bjDateToPacificTime(Date date) {
        return convertStringToDate(bjTimeToPacificTime(DateUtil.convertDateToString(date,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * 时间加上多少小时
     * @param date
     * @param hour
     * @return
     */
    public static Date addHour(Date date, int hour) {
        Date d = date;
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.HOUR, hour);
        return cal.getTime();
    }

    /**
     * dd MMMM yyyy HH:mm:ss zzz  格式时间转换 成yyyy-MM-dd HH:mm:ss
     * 16 August 2024 08:19:05 CEST
     * @param date
     * @return
     */
    public static String getDateToStr(String date) {
        String[] split = date.split(" ");
        String year = split[2];
        String month = split[1];
        int value = Month.valueOf(month.toUpperCase()).getValue();
        if (value < 10) {
            month = "0" + value;
        }else {
            month = String.valueOf(value);
        }
        String day = split[0];
        String hms = split[3];
        String result = year + "-" + month + "-" + day + " " + hms;
        return result;
    }

    /**
     * utc时间转CEST时间
     * @param utcTime UTC时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String UtcCestTime(String utcTime) {
        ZoneId newZone = ZoneId.of("Europe/Paris");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(utcTime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("UTC"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }


    /**
     * utc时间转CEST时间
     * @param utcDate UTC时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Date UtcToCestDate(Date utcDate) {
        return convertStringToDate(UtcCestTime(DateUtil.convertDateToString(utcDate,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * @param timestamp 时间戳（BJ）
     * @return  PST时间
     */
    public static Date BjToPstByTimestampMillSecond(Long timestamp) {
        if (null == timestamp) {
            return null;
        }
        String bjTime = cn.hutool.core.date.DateUtil.format(new Date(timestamp), DatePattern.NORM_DATETIME_FORMAT);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  //返回为北京时间
        sdf.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
        String pstTime = sdf.format(cn.hutool.core.date.DateUtil.parse(bjTime));  //转换为PST时间
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(pstTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * utc时间转东京时间
     * @param utcDate UTC时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Date UtcToTokyoDate(Date utcDate) {
        return convertStringToDate(UtcToTokyoTime(DateUtil.convertDateToString(utcDate,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * utc时间转东京时间
     * @param utcTime UTC时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String UtcToTokyoTime(String utcTime) {
        ZoneId newZone = ZoneId.of("Asia/Tokyo");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(utcTime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("UTC"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }


    /**
     * 判断日期是否在跨年范围内
     *
     * @param targetDate 目标日期
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 是否在范围内
     */
    public static boolean isInCrossYearRange(Date targetDate, Date startDate, Date endDate) {
        // 判断是否跨年
        if (endDate.before(startDate)) {
            // 跨年范围：targetDate 在 startDate 之后 或 endDate 之前
            return !targetDate.before(startDate) || !targetDate.after(endDate);
        } else {
            // 普通范围
            return !targetDate.before(startDate) && !targetDate.after(endDate);
        }
    }


    /**
     * March 4, 2025 12:00:00 AM PST  转成 2025-03-04 00:00:00
     * March 23, 2025 11:59:59 PM PDT 转成 2025-03-23 23:59:59
     * January 22, 2025 12:00:00 AM CET 转成 2025-01-22 00:00:00
     * January 24, 2025 11:59:59 PM CET 转成 2025-01-24 23:59:59
     * @param date
     * @return
     */
    public static String getDateStr(String date) {

        try {
            // 定义输入的时间格式
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("MMMM d, yyyy hh:mm:ss a z", Locale.ENGLISH);
           // 解析输入时间
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(date, inputFormatter);

            // 定义输出格式
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 格式化输出
            String formattedDate = zonedDateTime.format(outputFormatter);
            return formattedDate;
        } catch (Exception e) {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("MMMM d, yyyy h:mm:ss a z", Locale.ENGLISH);
            // 解析输入时间
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(date, inputFormatter);

            // 定义输出格式
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 格式化输出
            String formattedDate = zonedDateTime.format(outputFormatter);
            return formattedDate;
        }
    }


    /**
     * pst时间转时间戳
     * @param pstTime
     * @return
     */
    public static long pstToTimeStamp(String pstTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        formatter.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
        try {
            Date date = formatter.parse(pstTime);
            return date.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }


    /**
     * CEST时间转pst时间
     * @param cestTime  yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String cestToPstTime(String cestTime) {
        ZoneId newZone = ZoneId.of("America/Los_Angeles");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(cestTime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("Europe/Paris"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }


    /**
     * CEST时间转pst时间
     * @param cestDate cest时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Date cestToPstDate(Date cestDate) {
        return convertStringToDate(cestToPstTime(DateUtil.convertDateToString(cestDate,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }



    /**
     * pst时间转cest时间
     * @param cestTime 时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String pstToCestTime(String cestTime) {
        ZoneId newZone = ZoneId.of("Europe/Paris");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(cestTime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("America/Los_Angeles"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }


    /**
     * pst时间转cest时间
     * @param pstDate 时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Date pstToCestDate(Date pstDate) {
        return convertStringToDate(pstToCestTime(DateUtil.convertDateToString(pstDate,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }
    /**
     * 日本Tokyo时间转pst时间
     * @param cestTime  yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String tokyoToPstTime(String cestTime) {
        ZoneId newZone = ZoneId.of("America/Los_Angeles");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(cestTime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("Asia/Tokyo"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }


    /**
     * 日本Tokyo时间转pst时间
     * @param tokyoDate  yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Date tokyoToPstDate(Date tokyoDate) {
        return convertStringToDate(tokyoToPstTime(DateUtil.convertDateToString(tokyoDate,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }


    /**
     * pst时间转日本Tokyo时间
     * @param cestTime  yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String pstToTokyoTime(String cestTime) {
        ZoneId newZone = ZoneId.of("Asia/Tokyo");
        DateTimeFormatter formatFlag = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime parse = LocalDateTime.parse(cestTime, formatFlag);
        ZonedDateTime zonedDateTime1 = ZonedDateTime.of(parse, ZoneId.of("America/Los_Angeles"));
        parse = LocalDateTime.ofInstant(zonedDateTime1.toInstant(), newZone);
        return  parse.format(formatFlag);
    }


    /**
     * pst时间转日本Tokyo时间
     * @param pstDate  yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Date pstToTokyoDate(Date pstDate) {
        return convertStringToDate(pstToTokyoTime(DateUtil.convertDateToString(pstDate,DatePattern.NORM_DATETIME_PATTERN)),DatePattern.NORM_DATETIME_PATTERN);
    }



    /**
     * 根据兩個个日期，取得相隔的秒数
     *
     * @param date1 前一个日期
     * @param date2 后一个日期
     * @return 相差的秒数
     */
    public static int getBetweenSeconds(Date date1, Date date2) {
        // 根据兩個个日期，取得相隔的秒数
        long minuteNumber = 0;
        try {
            // 计算两个日期的时间差（以毫秒记）
            minuteNumber = (date2.getTime() - date1.getTime()) / 1000L;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (int) minuteNumber;
    }


    /**
     * MMMM d, yyyy 格式转化为 yyyy-MM-dd
     * @return
     */
    public static String MMMMddyyyyToyyyyMMdd(String inputDate) {
        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.ENGLISH);
            LocalDate date = LocalDate.parse(inputDate, inputFormatter);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String outputDate = date.format(outputFormatter);
            return outputDate;
        }catch (Exception e) {
            return null;
        }
    }

}
