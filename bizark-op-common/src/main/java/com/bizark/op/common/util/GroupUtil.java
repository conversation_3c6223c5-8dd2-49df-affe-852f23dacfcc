package com.bizark.op.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 动态分组工具类
 *
 * <AUTHOR>
 * @date 2023/3/29 - 18:05
 */
public class GroupUtil {


    /**
     * 根据字段名进行动态分组 以 _ 隔开
     *
     * @param clazz      将要分组的类
     * @param properties 将要分组类的属性（也是分组完成后 map的key）
     * @param list       分组集合
     */
    public static <T> Map<String, List<T>> groupingByDynamicProperties(Class<?> clazz, List<T> list, List<String> properties) {
        if (CollUtil.isEmpty(list) || CollUtil.isEmpty(properties) || ObjectUtil.isNull(clazz)) {
            return CollUtil.newHashMap();
        }
        return list.stream().filter(ObjectUtil::isNotNull).collect(Collectors.groupingBy(t -> groupingByDynamicProperties(t, getReadMethodList(properties, clazz))));
    }

    /**
     * 根据字段名进行动态分组 以 _ 隔开
     */
    public static <T> String groupingByDynamicProperties(T t, List<Method> readMethods) {
        if (CollUtil.isEmpty(readMethods) || ObjectUtil.isNull(CollUtil.getFirst(readMethods))) {
            return StrUtil.EMPTY;
        } else if (readMethods.size() == 1) {
            return getResult(t, CollUtil.getFirst(readMethods));
        } else {
            return readMethods.stream().filter(ObjectUtil::isNotNull)
                .map(readMethod -> getResult(t, readMethod))
                .reduce((str1, str2) -> str1 + StrUtil.UNDERLINE + str2).orElse(StrUtil.EMPTY);
        }
    }

    private static <T> String getResult(T t, Method readMethod) {
        if (ObjectUtil.isNull(readMethod) || ObjectUtil.isNull(t)) {
            return StrUtil.EMPTY;
        }
        Object result = DozerUtils.getInvokeResult(t, readMethod);
        if (ObjectUtil.isNotNull(result)) {
            return result.toString();
        }
        return StrUtil.EMPTY;
    }

    /**
     * 获取 clazz 里面的 get属性 方法
     */
    private static List<Method> getReadMethodList(List<String> properties, Class<?> clazz) {
        return properties.stream().filter(StrUtil::isNotBlank).distinct().map(propertie -> DozerUtils.getReadMethod(clazz, propertie)).collect(Collectors.toList());
    }

}


