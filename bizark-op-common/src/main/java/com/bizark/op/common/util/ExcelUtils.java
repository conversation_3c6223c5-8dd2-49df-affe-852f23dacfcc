
package com.bizark.op.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.*;
import cn.hutool.poi.excel.*;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.common.exception.CommonException;
import com.bizark.op.common.annotation.ExcelHead;
import com.bizark.op.common.annotation.ExcelMapping;
import com.bizark.op.common.config.ExcelDataHandler;
import com.bizark.op.common.core.domain.ExcelCellStyleEntity;
import com.bizark.op.common.core.domain.ExcelMergeEntity;
import com.bizark.op.common.enm.ContentTypeEnum;
import com.bizark.op.common.enm.ExcelErrorEnum;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.spring.SpringUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 导入导出excel Utils
 **/
public class ExcelUtils {


    private final static Logger logger = LoggerFactory.getLogger(ExcelUtils.class);

    /**
     * 别名导出
     *
     * @param data     数据
     * @param response 返回对象
     */
    public static void excelHeaderAliasExport(List<?> data, String fileName, Class clazz, HttpServletResponse response) throws IOException {
        excelHeaderAliasExport(data, response, fileName, null, true, false, clazz, null);
    }

    /**
     * 下载模板
     *
     * @param sheetMap key-sheet名 value 表头信息
     * @param response 返回对象
     */
    public static void downloadTemplate(Map<String, Class> sheetMap,
                                        Map<String, List<ExcelCellStyleEntity>> styleMap,
                                        String fileName, HttpServletResponse response) throws IOException {
        if (CollUtil.isEmpty(sheetMap)) {
            return;
        }

        ServletOutputStream outputStream = response.getOutputStream();
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=\"" + URLEncoder.encode(fileName, "utf8") + ".xlsx" + "\"");
        ExcelWriter writer = getExcelWriter(null);

        Set<String> keySet = sheetMap.keySet();
        int sheetIndex = 0;
        for (String sheetName : keySet) {
            if (sheetIndex == 0) {
                writer.renameSheet(sheetIndex, sheetName);
            } else {
                writer.setSheet(sheetName);
            }
            Class clazz = sheetMap.get(sheetName);
            //设置列宽
            setCloum(writer, getFields(clazz));
            //设置表头
            writer.writeHeadRow(CollUtil.newArrayList(setExcelTitle(clazz)));
            //设置单元格格式
            setCellStyle(writer, styleMap.get(sheetName));
            sheetIndex++;
        }
        writer.flush(outputStream);
        writer.close();
        IoUtil.close(outputStream);
    }

    private static void setSelectData(ExcelWriter writer, ExcelCellStyleEntity cellStyleEntity) {
        if (ArrayUtil.isEmpty(cellStyleEntity.getSelectData())) {
            return;
        }
        Sheet sheet = writer.getSheet();
        int firstRow = cellStyleEntity.getRowIndex(), lastRow = 5000, firstCol = cellStyleEntity.getCellIndex(), lastCol = cellStyleEntity.getCellIndex();
        // 单元格范围地址列表
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        //单元格样式
        StyleSet styleSet = writer.getStyleSet();
        CellStyle cellStyle = styleSet.getCellStyle();
        cellStyle.setDataFormat((short) BuiltinFormats.getBuiltinFormat("text"));

        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(cellStyleEntity.getSelectData());
        //数据验证
        DataValidation dataValidation = helper.createValidation(constraint, addressList);
        writer.addValidationData(dataValidation);
    }

    /**
     * 别名导出下载
     *
     * @param data 数据
     * @return 下载路径
     */
    public static String excelHeaderAliasExportDownload(List<?> data, String filePath, Class clazz) {
        ExcelWriter writer = getExcelWriter(data, null, true, true, filePath + ContentTypeEnum.xlsx.value(), clazz, null);
        IoUtil.close(writer);
        return filePath;
    }

    /**
     * 别名大批量导出
     *
     * @param data     数据
     * @param response 返回对象
     */
    public static void excelHeaderAliasBigExport(List<?> data, String fileName, Class clazz, HttpServletResponse response) throws IOException {
        excelHeaderAliasExport(data, response, fileName, null, true, true, clazz, null);
    }


    /**
     * 简单导出
     *
     * @param data     <Key: 表头名，Value : 数据 >数据
     * @param response 返回对象
     */
    public static void easyExcelExport(List<?> data, String fileName, HttpServletResponse response) throws IOException {
        excelHeaderAliasExport(data, response, fileName, null, false, false, null, null);
    }

    /**
     * 简单合并导出
     *
     * @param data     <Key: 表头名，Value : 数据 >数据
     * @param response 返回对象
     */
    public static void easyMergeExcelExport(List<?> data, String fileName, List<ExcelMergeEntity> listNum, List<ExcelCellStyleEntity> excelCellStyleList, HttpServletResponse response) throws IOException {
        excelHeaderAliasExport(data, response, fileName, listNum, false, false, null, excelCellStyleList);
    }

    /**
     * 导出
     *
     * @param data     数据
     * @param response 返回对象
     * @param listNum  合并行数list
     * @param isAlias  是否使用别名
     */
    public static void excelHeaderAliasExport(List<?> data,
                                              HttpServletResponse response,
                                              String fileName,
                                              List<?> listNum,
                                              Boolean isAlias,
                                              Boolean isBigExcel,
                                              Class clazz,
                                              List<ExcelCellStyleEntity> excelCellStyleList) throws IOException {
        ServletOutputStream outputStream = response.getOutputStream();
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf8") + ".xlsx");
        ExcelWriter writer = getExcelWriter(data, listNum, isAlias, isBigExcel, null, clazz, excelCellStyleList);
        writer.flush(outputStream);
        writer.close();
        IoUtil.close(outputStream);
    }

    /**
     * @param data    数据
     * @param listNum 合并行数list
     */
    private static ExcelWriter getExcelWriter(List<?> data,
                                              List<?> listNum,
                                              Boolean isAlias,
                                              Boolean isBigExcel,
                                              String path,
                                              Class clazz,
                                              List<ExcelCellStyleEntity> excelCellStyleList) {
        ExcelWriter writer = apdterExcelWriter(isBigExcel, path);
        //获取 类属性上的自定义注解信息 并 设置别名
        Field[] fields = getFieldsAndSetAlias(clazz, writer, isAlias);
        if (CollUtil.isNotEmpty(data)) {
            writer.write(data);
            if (!isBigExcel) {
                writer.autoSizeColumnAll();
            }
            //合并单元格
            mergeColumn(listNum, writer, fields);
            //设置单元格格式
            setCellStyle(writer, excelCellStyleList);
            //设置列宽
            setCloum(writer, fields);
        }
        return writer;
    }

    /**
     * 设置单元格格式
     */
    private static void setCellStyle(ExcelWriter writer, List<ExcelCellStyleEntity> excelCellStyleList) {
        if (CollUtil.isEmpty(excelCellStyleList)) {
            return;
        }
        Sheet sheet = writer.getSheet();
        Workbook workbook = writer.getWorkbook();
        excelCellStyleList.forEach(excelCellStyle -> setRowAndCell(writer, sheet, workbook, excelCellStyle));
    }

    private static ExcelWriter apdterExcelWriter(Boolean isBigExcel, String path) {
        ExcelWriter writer = null;
        if (isBigExcel) {
            writer = getBigExcelWriter(path);
        } else {
            writer = getExcelWriter(path);
        }
        return writer;
    }

    public static ExcelWriter getExcelWriter(String path) {
        ExcelWriter writer;
        if (StrUtil.isNotBlank(path)) {
            writer = ExcelUtil.getWriter(path);
        } else {
            writer = ExcelUtil.getWriter(true);
        }
        return writer;
    }

    public static BigExcelWriter getBigExcelWriter(String path) {
        BigExcelWriter writer;
        if (StrUtil.isNotBlank(path)) {
            writer = new BigExcelWriter(path);
        } else {
            writer = new BigExcelWriter(-1);
        }
        return writer;
    }

    public static void writeBigExcel(List<?> data, boolean isWriteHeader, BigExcelWriter writer, int sheetIndex, Boolean isAlias) {
        writeBigExcel(data, isWriteHeader, writer, sheetIndex, isAlias, null);
    }


    public static void writeBigExcel(List<?> data, boolean isWriteHeader, BigExcelWriter writer, int sheetIndex, Boolean isAlias, List<?> mergeList) {
        try {
            if (CollUtil.isEmpty(data)) {
                return;
            }
            if (isWriteHeader) {
                writer.setSheet(sheetIndex);
                getFieldsAndSetAlias(CollUtil.getFirst(data).getClass(), writer, isAlias);
            }
            data.removeIf(ObjectUtil::isNull);
            writer.write(data);

            if (CollUtil.isNotEmpty(mergeList)) {
                //设置单元格格式
                mergeColumn(mergeList, writer, getFields(CollUtil.getFirst(data).getClass()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置列宽
     */
    private static void setCloum(ExcelWriter writer, Field[] fields) {
        if (ArrayUtil.isEmpty(fields)) {
            return;
        }
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            if (field.isAnnotationPresent(ExcelHead.class)) {
                ExcelHead excelHeadAnnotation = field.getAnnotation(ExcelHead.class);
                int width = excelHeadAnnotation.width();
                if (width > 0) {
                    writer.setColumnWidth(i, width);
                }
            }
        }
    }

    /**
     * 合并单元格
     */
    private static void mergeColumn(List<?> listNum, ExcelWriter writer, Field[] fields) {
        if (CollUtil.isEmpty(listNum)) {
            return;
        }
        if (CollUtil.getFirst(listNum) instanceof ExcelMergeEntity) {
            listNum.forEach(obj -> {
                ExcelMergeEntity entity = (ExcelMergeEntity) obj;
                writer.merge(entity.getStartRow(), entity.getEndRow(), entity.getStartColumn(), entity.getEndColumn(), entity.getContent(), true);
            });
        } else if (CollUtil.getFirst(listNum) instanceof Integer && ArrayUtil.isNotEmpty(fields)) {
            int rowNum = 0;
            for (Object obj : listNum) {
                Integer num = (Integer) obj;
                rowNum = rowNum + 1;
                int firstRow = rowNum;
                rowNum = rowNum + num - 1;
                int lastRow = rowNum;
                for (int i = 0; i < fields.length; i++) {
                    Field field = fields[i];
                    if (field.isAnnotationPresent(ExcelHead.class)) {
                        ExcelHead excelHeadAnnotation = field.getAnnotation(ExcelHead.class);
                        if (excelHeadAnnotation.merge() && firstRow != lastRow) {
                            writer.merge(firstRow, lastRow, i, i, null, false);
                        }
                    }
                }
            }
        }
    }

    /**
     * @param sheet                sheet sheet
     * @param workbook             workbook
     * @param excelCellStyleEntity 格式信息
     * @Description: 设置对齐方式
     */
    private static void setRowAndCell(ExcelWriter writer, Sheet sheet, Workbook workbook, ExcelCellStyleEntity excelCellStyleEntity) {
        if (ObjectUtil.isNull(excelCellStyleEntity)) {
            return;
        }
        //设置下拉框数据 默认5000行
        setSelectData(writer, excelCellStyleEntity);
        //设置样式
        setCellStyle(sheet, workbook, excelCellStyleEntity);
    }

    private static void setCellStyle(Sheet sheet, Workbook workbook, ExcelCellStyleEntity excelCellStyleEntity) {
        if (ObjectUtil.isNull(excelCellStyleEntity.getRowIndex())
                || ObjectUtil.isNull(excelCellStyleEntity.getCellIndex())) {
            return;
        }
        // 创建row
        Row row = sheet.getRow(excelCellStyleEntity.getRowIndex());
        if (ObjectUtil.isNull(row)) {
            return;
        }
        // 创建cell, 设置样式
        Cell cell = row.getCell(excelCellStyleEntity.getCellIndex());
        CellStyle cellStyle = workbook.createCellStyle();
        HorizontalAlignment horizontalAlignment = HorizontalAlignment.CENTER;
        VerticalAlignment verticalAlignment = VerticalAlignment.CENTER;
        if (ObjectUtil.isNotNull(excelCellStyleEntity.getHorizontalAlignment())) {
            horizontalAlignment = excelCellStyleEntity.getHorizontalAlignment();

        }
        if (ObjectUtil.isNotNull(excelCellStyleEntity.getVerticalAlignment())) {
            verticalAlignment = excelCellStyleEntity.getVerticalAlignment();
        }
        cellStyle.setAlignment(horizontalAlignment);
        cellStyle.setVerticalAlignment(verticalAlignment);
        // 设置上边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        // 设置下边框
        cellStyle.setBorderBottom(BorderStyle.THIN);
        // 设置左边框
        cellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置右边框
        cellStyle.setBorderRight(BorderStyle.THIN);
        cell.setCellStyle(cellStyle);
    }


    private static Field[] getParentField(Class clazz) {
        Class<?> parentClass = clazz.getSuperclass();
        if (parentClass == null) {
            return new Field[0];
        }
        return parentClass.getDeclaredFields();
    }

    public static Field[] getFieldsAndSetAlias(Class clazz, ExcelWriter writer, Boolean isAlias) {
        if (ObjectUtil.isNull(clazz) || !isAlias) {
            return null;
        }

        Field[] newFields = getFields(clazz);

        Map<String, String> headerAlias = new LinkedHashMap<>();
        for (Field field : newFields) {
            if (field.isAnnotationPresent(ExcelHead.class)) {
                ExcelHead excelHeadAnnotation = field.getAnnotation(ExcelHead.class);
                if (excelHeadAnnotation.hidden()) {
                    continue;
                }
                headerAlias.put(field.getName(), excelHeadAnnotation.name());
            }
        }
        if (CollUtil.isEmpty(headerAlias)) {
            return null;
        }
        writer.setHeaderAlias(headerAlias);
        writer.setOnlyAlias(true);
        return newFields;
    }

    private static Field[] getFields(Class clazz) {
        if (ObjectUtil.isNull(clazz)) {
            return new Field[]{};
        }
        Field[] parentField = getParentField(clazz);
        // 获取 "属性变量" 上的注解的值
        Field[] fields = clazz.getDeclaredFields();
        Field[] newFields = new Field[parentField.length + fields.length];
        System.arraycopy(parentField, 0, newFields, 0, parentField.length);

        // 将第二个数组拷贝到新数组中
        System.arraycopy(fields, 0, newFields, parentField.length, fields.length);
        return newFields;
    }


    /**
     * excel导入 （过滤无注解项）
     * 重要：文件标题不可重复
     *
     * @param file  文件
     * @param clazz 列对应的对象
     * @return 返回数据集合
     */
    public static <T> List<T> excelImportFilterAnnotation(MultipartFile file, Class<T> clazz) {
        checkFile(file);
        //将数据封装到list<Map>中
        List<T> dataList = new ArrayList<>();
        try {
            //读取数据
            dataList = excelImportFilteNoAnnotation(file.getInputStream(), clazz);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            AssertUtil.isFail(ExcelErrorEnum.FILE_ERROR, e);
        }
        return dataList;
    }

    /**
     * excel导入
     * 重要：文件标题不可重复
     *
     * @param file      文件
     * @param classList 列对应的对象
     * @return 返回数据集合
     */
    public static Map<Class, List<?>> excelMultiSheetImport(MultipartFile file, List<Class> classList) {
        checkFile(file);
        try {
            //读取数据
            return excelMultiSheetImport(file.getInputStream(), classList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            AssertUtil.isFail(ExcelErrorEnum.FILE_ERROR, e);
        }
        return CollUtil.newHashMap();
    }

    /**
     * excel 多sheet导入
     *
     * @param inputStream 文件流
     * @param classList   对应的类数组
     * @return 返回数据集合
     * @throws Exception
     */
    private static HashMap<Class, List<?>> excelMultiSheetImport(InputStream inputStream, List<Class> classList) throws Exception {
        //将数据封装到list<Map>中
        HashMap<Class, List<?>> dataMap = CollUtil.newHashMap();
        ExcelReader reader = ExcelUtil.getReader(inputStream, false);
        List<Sheet> sheetList = reader.getSheets();
        AssertUtil.isTrue(sheetList.size() == classList.size(), ExcelErrorEnum.FILE_PATTERN_ERROR);

        for (int i = 0; i < classList.size(); i++) {
            ExcelReader excelReader = null;
            try {
                Class clazz = classList.get(i);
                excelReader = new ExcelReader(sheetList.get(i));
                List<Map<String, Object>> list = excelReader.readAll();
                dataMap.put(clazz, bulidDataList(clazz, list));
            } finally {
                IoUtil.close(excelReader);
            }
        }
        IoUtil.close(reader);
        return dataMap;
    }

    /**
     * excel导入
     * 重要：文件标题不可重复
     *
     * @param file  文件
     * @param clazz 列对应的对象
     * @return 返回数据集合
     */
    public static <T> List<T> excelImport(MultipartFile file, Class<T> clazz) {
        checkFile(file);
        //将数据封装到list<Map>中
        List<T> dataList = new ArrayList<>();
        try {
            //读取数据
            dataList = excelImport(file.getInputStream(), clazz);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            AssertUtil.isFail(ExcelErrorEnum.FILE_ERROR, e);
        }
        return dataList;
    }

    public static <T> List<T> excelImport(String url, Class<T> clazz) {
        //将数据封装到list<Map>中
        List<T> dataList = new ArrayList<>();
        try {
            InputStream input = fileUrlToByte(url);
            //读取数据
            dataList = excelImport(input, clazz);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            AssertUtil.isFail(ExcelErrorEnum.FILE_ERROR, e);
        }
        return dataList;
    }


    /**
     * 直接读取url转成 二进制流
     *
     * @param strUrl url
     */
    public static InputStream fileUrlToByte(String strUrl) {
        try {
            URL url = new URL(strUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(10 * 1000);
            conn.setReadTimeout(50 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("user-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            return conn.getInputStream();
        } catch (Exception e) {
            logger.error("gen exception", e);
        }
        return null;
    }


    private static <T> List<T> excelImportFilteNoAnnotation(InputStream input, Class<T> clazz) throws Exception {
        //读取数据
        ExcelReader excelReader = ExcelUtil.getReader(input, 0, true);
        List<Map<String, Object>> list = excelReader.readAll();
        return bulidDataList(clazz, list);
    }

    /**
     * 封装数据信息
     *
     * @param clazz 对应的类
     * @param list  读取的数据集合
     * @param <T>   泛型
     */
    private static <T> List<T> bulidDataList(Class<T> clazz, List<Map<String, Object>> list) throws InstantiationException, IllegalAccessException {
        //将数据封装到list<Map>中
        List<T> dataList = new ArrayList<>();
        AssertUtil.isTrue(CollUtil.isNotEmpty(list), ExcelErrorEnum.NO_DATA);

        logger.info("读取结果：" + list.size());

        Field[] fields = clazz.getDeclaredFields();
        fields = Arrays.stream(fields).filter(item -> item.isAnnotationPresent(ExcelHead.class)).toArray(Field[]::new);

        AssertUtil.isTrue(ArrayUtil.isNotEmpty(fields), ExcelErrorEnum.FILE_PATTERN_ERROR);

        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            AssertUtil.isTrue(map.size() == fields.length, ExcelErrorEnum.FILE_PATTERN_ERROR);
            T t = clazz.newInstance();

            for (Map.Entry<String, Object> stringObjectEntry : map.entrySet()) {
                String key = stringObjectEntry.getKey();
                Object value = stringObjectEntry.getValue();
                Field field = Arrays.stream(fields).filter(item -> item.getAnnotation(ExcelHead.class).name().equals(key)).findFirst().orElse(null);
                if (field == null) {
                    continue;
                }
                checkImportFieldIsNotNull(value, field);
                if (value == null) {
                    continue;
                }
                String strValue = value.toString().trim();
                if (StrUtil.isBlank(strValue)) {
                    continue;
                }
                value = convertObj(value, field, strValue);
                field.setAccessible(true);
                field.set(t, value);
            }
            dataList.add(t);
        }

        return dataList;
    }


    private static <T> List<T> excelImport(InputStream input, Class<T> clazz) throws Exception {
        //将数据封装到list<Map>中
        List<T> dataList = new ArrayList<>();
        //读取数据
        ExcelReader excelReader = ExcelUtil.getReader(input, 0, true);
        List<Map<String, Object>> list = excelReader.readAll();
        logger.info("读取结果：" + list.size());
        AssertUtil.isTrue(CollUtil.isNotEmpty(list), ExcelErrorEnum.NO_DATA);
        Field[] fields = clazz.getDeclaredFields();
        AssertUtil.isTrue(ArrayUtil.isNotEmpty(fields), ExcelErrorEnum.FILE_PATTERN_ERROR);
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            map.remove(StrUtil.EMPTY);
            AssertUtil.isTrue(map.size() == fields.length, ExcelErrorEnum.FILE_PATTERN_ERROR);
            T t = clazz.newInstance();
            int j = 0;
            for (Object value : map.values()) {
                Field field = fields[j];
                j++;

                checkImportFieldIsNotNull(value, field);

                if (value == null) {
                    continue;
                }

                String strValue = value.toString().trim();
                if (StrUtil.isBlank(strValue)) {
                    continue;
                }
                value = convertObj(value, field, strValue);
                field.setAccessible(true);
                field.set(t, value);
            }
            dataList.add(t);
        }
        return dataList;
    }

    public static Object convertObj(Object value, Field field, String strValue) {
        if (String.class.equals(field.getType())) {
            value = strValue;
        }
        if (boolean.class.equals(field.getType()) || boolean.class.equals(field.getType())) {
            value = strValue.equals("是") ? true : false;
        }
        if (byte.class.equals(field.getType()) || Byte.class.equals(field.getType())) {
            value = Byte.parseByte(strValue);
        }
        if (int.class.equals(field.getType()) || Integer.class.equals(field.getType())) {
            value = Integer.parseInt(strValue);
        }
        if (long.class.equals(field.getType()) || Long.class.equals(field.getType())) {
            value = Long.parseLong(strValue);
        }
        if (double.class.equals(field.getType()) || Double.class.equals(field.getType())) {
            value = Double.valueOf(strValue);
        }
        if (Date.class.equals(field.getType())) {
            String datePattern = "\\d{1,2}/\\d{1,2}/\\d{4}";
            boolean match = ReUtil.isMatch(datePattern, strValue);
            if (match) {
                value = DateUtil.parse(strValue, "MM/dd/yyyy");
            } else {
                value = DateUtil.parse(strValue);
            }

        }
        if (LocalDateTime.class.equals(field.getType())) {
            value = LocalDateTime.parse(strValue);

        }
        if (LocalDate.class.equals(field.getType())) {
            value = LocalDate.parse(strValue);

        }
        if (BigDecimal.class.equals(field.getType()) && !strValue.isEmpty()) {
            Number parse;
            try {
                parse = NumberFormat.getInstance().parse(strValue);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            value = BigDecimal.valueOf(parse.doubleValue());
        }
        if (strValue.equals("true") || strValue.equals("false")) {
            value = new Boolean(strValue);
        }
        return value;
    }

    /**
     * 校验字段值是否允许为空
     *
     * @param value 字段值
     * @param field 字段
     */
    private static void checkImportFieldIsNotNull(Object value, Field field) {
        if (field.isAnnotationPresent(ExcelHead.class)) {
            ExcelHead excelHead = field.getAnnotation(ExcelHead.class);
            if (excelHead.required()) {
                AssertUtil.isTrue(ObjectUtil.isNotNull(value) && StrUtil.isNotBlank(value.toString()), excelHead.name() + "为必填项");
            }
        }
    }


    private static void checkFile(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            AssertUtil.isFail(ExcelErrorEnum.NO_FILE);
        }
        //上传文件大小为10M数据
        if (file.getSize() > 1024 * 1024 * 10) {
            logger.error("upload | 上传失败: 文件大小超过10M，文件大小为：{}", file.getSize());
            AssertUtil.isFail(ExcelErrorEnum.NO_SIZE);
        }
        // 上传文件名格式不正确
        String suffix = FileUtil.extName(file.getOriginalFilename()).toLowerCase();
        if (!"xls".equals(suffix) && !"xlsx".equals(suffix)) {
            logger.error("upload | 上传失败: 文件名格式为：{}", suffix);
            AssertUtil.isFail(ExcelErrorEnum.NO_FORMAT);
        }
    }

    /**
     * excel导入
     *
     * @param file 文件
     * @return 返回数据集合
     */
    public static List<Map<String, Object>> excelImport(MultipartFile file) {
        checkFile(file);
        //将数据封装到list<Map>中
        List<Map<String, Object>> dataList = new ArrayList<>();
        List<String> head = new ArrayList<>();
        try {
            //读取数据
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, Object>>() {
                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                        head.add(entry.getValue());
                    }
                }

                @Override
                public void invoke(Map<Integer, Object> dataMap, AnalysisContext analysisContext) {
                    HashMap<String, Object> map = new HashMap<>();
                    for (Map.Entry<Integer, Object> entry : dataMap.entrySet()) {
                        String key = head.get(entry.getKey());
                        map.put(key, entry.getValue());
                    }
                    dataList.add(map);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                }
            }).sheet(0).doRead();
//            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream(), 0, true);
//            dataList = excelReader.readAll();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            AssertUtil.isFail(ExcelErrorEnum.FILE_ERROR, e);
        }
        return dataList;
    }


    /**
     * 设置动态表头是否展示
     */
    public static <T> T setExcelHeadFeild(Class<T> clazz, Collection<String> fields) {
        T t = null;
        try {
            t = ReflectUtil.newInstance(clazz);
            if (CollUtil.isEmpty(fields)) {
                return t;
            }

            List<Field> fieldList = CollUtil.newArrayList(clazz.getDeclaredFields());
            if (!Object.class.equals(clazz)) {
                List<Field> superFieldList = CollUtil.newArrayList(clazz.getSuperclass().getDeclaredFields());
                if (CollUtil.isNotEmpty(superFieldList)) {
                    fieldList.addAll(superFieldList);
                }
            }

            for (Field field : fieldList) {
                if (field.isAnnotationPresent(ExcelHead.class)) {
                    ExcelHead excelHead = field.getAnnotation(ExcelHead.class);
                    boolean hidden = excelHead.hidden();
                    if (fields.contains(field.getName())) {
                        hidden = false;
                    }

                    InvocationHandler handler = Proxy.getInvocationHandler(excelHead);
                    // 获取 AnnotationInvocationHandler 的 memberValues 字段
                    Field hField = handler.getClass().getDeclaredField("memberValues");
                    // 打开权限
                    hField.setAccessible(true);
                    // 获取 memberValues
                    Map<String, Object> memberValues = (Map<String, Object>) hField.get(handler);
                    // 修改 hidden 属性值
                    memberValues.put("hidden", hidden);
                }
            }

            for (String s : fields) {

                Optional<Field> optional = fieldList.stream().filter(obj -> obj.getName().equals(s)).findFirst();

                if (optional.isPresent()) {
                    Field field = optional.get();

                }
            }
        } catch (Exception e) {
            logger.error("update annotation field error", e);
        } finally {
            try {
                t = ObjectUtil.isNotNull(t) ? t : clazz.newInstance();
            } catch (Exception e) {
                logger.error("newInstance error", e);
            }
        }
        return t;
    }

    /**
     * 表头添加后缀
     */
    public static <T> T setExcelHeadField(Class<T> clazz, String suffix) {
        T t = null;
        try {
            t = ReflectUtil.newInstance(clazz);
            if (StrUtil.isBlank(suffix)) {
                return t;
            }

            List<Field> fieldList = CollUtil.newArrayList(clazz.getDeclaredFields());
            if (!Object.class.equals(clazz)) {
                List<Field> superFieldList = CollUtil.newArrayList(clazz.getSuperclass().getDeclaredFields());
                if (CollUtil.isNotEmpty(superFieldList)) {
                    fieldList.addAll(superFieldList);
                }
            }

            for (Field field : fieldList) {
                if (field.isAnnotationPresent(ExcelHead.class)) {
                    ExcelHead excelHead = field.getAnnotation(ExcelHead.class);
                    String name = excelHead.name();
                    if (excelHead.suffix()) {
                        name = suffix;
                    }

                    InvocationHandler handler = Proxy.getInvocationHandler(excelHead);
                    // 获取 AnnotationInvocationHandler 的 memberValues 字段
                    Field hField = handler.getClass().getDeclaredField("memberValues");
                    // 打开权限
                    hField.setAccessible(true);
                    // 获取 memberValues
                    Map<String, Object> memberValues = (Map<String, Object>) hField.get(handler);
                    // 修改 hidden 属性值
                    memberValues.put("name", name);
                }
            }
        } catch (Exception e) {
            logger.error("update annotation field error", e);
        } finally {
            try {
                t = ObjectUtil.isNotNull(t) ? t : clazz.newInstance();
            } catch (Exception e) {
                logger.error("newInstance error", e);
            }
        }
        return t;
    }


    /**
     * 获取字段上的注解值设置为表头，并且设置必填项
     */
    public static String[] setExcelTitle(Class<?> clazz) {
        //获取字段上的注解，并且设置必填项
        Field[] fields = ReflectUtil.getFields(clazz);

        String[] titleArr = new String[fields.length];

        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            if (field.isAnnotationPresent(ExcelHead.class)) {
                ExcelHead excelHead = field.getAnnotation(ExcelHead.class);
                String title = excelHead.name();
                if (excelHead.required()) {
                    title = "*" + title;
                }
                titleArr[i] = title;
            }
        }
        return titleArr;
    }


    public static <T> void writeExcel(List<T> t, HttpServletResponse response) throws IllegalAccessException, IOException {
        List<Map<String, String>> exportData = getExportData(t);

        ExcelWriter excelWriter = new ExcelWriter(true);
        excelWriter.setDefaultRowHeight(15);
        excelWriter.setColumnWidth(-1, 25);
        excelWriter.write(exportData);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=test.xlsx");
        ServletOutputStream out = response.getOutputStream();
        excelWriter.flush(out);
        excelWriter.close();
    }

    public static <T> List<Map<String, String>> getExportData(List<T> t) throws IllegalAccessException {
        if (CollectionUtil.isEmpty(t)) {
            throw new CommonException("导出数据为空！");
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Class<?> clazz = t.get(0).getClass();
        List<Map<String, String>> exportData = new ArrayList<>();

        Field[] fields = clazz.getDeclaredFields();
        List<Field> list = Stream.of(fields)
                .filter(f -> f.isAnnotationPresent(ExcelProperty.class))
                .sorted(Comparator.comparing(f -> {
                    f.setAccessible(true);
                    ExcelProperty annotation = f.getAnnotation(ExcelProperty.class);
                    return annotation.order();
                })).collect(Collectors.toList());

        for (T data : t) {
            Map<String, String> dataMap = new LinkedHashMap<>();
            for (Field field : list) {
                if (field.isAnnotationPresent(ExcelProperty.class)) {
                    field.setAccessible(true);
                    ExcelProperty property = field.getAnnotation(ExcelProperty.class);
                    Object o = field.get(data);
                    if (o instanceof Date) {
                        dataMap.put(property.value()[0], format.format(o));
                    } else if (o instanceof Boolean) {
                        Boolean flag = (Boolean) o;
                        dataMap.put(property.value()[0], flag ? "是" : "否");
                    } else {
                        dataMap.put(property.value()[0], o == null ? "" : String.valueOf(o));
                    }
                }
            }
            exportData.add(dataMap);
        }
        return exportData;
    }


    public static <T> void writeExcel(List<T> t, HttpServletResponse response, int index) throws IllegalAccessException, IOException {
        List<Map<String, String>> exportData = getExportData(t, index);
        ExcelWriter excelWriter = ExcelUtil.getWriter();
        excelWriter.write(exportData);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=test.xlsx");
        ServletOutputStream out = response.getOutputStream();
        excelWriter.flush(out);
        excelWriter.close();
    }


    public static <T> List<Map<String, String>> getExportData(List<T> t, int index) throws IllegalAccessException {
        if (CollectionUtil.isEmpty(t)) {
            throw new CommonException("导出数据为空！");
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Class<?> clazz = t.get(0).getClass();
        List<Map<String, String>> exportData = new ArrayList<>();

        Field[] fields = clazz.getDeclaredFields();
        List<Field> list = Stream.of(fields)
                .filter(f -> f.isAnnotationPresent(ExcelProperty.class))
                .sorted(Comparator.comparing(f -> {
                    f.setAccessible(true);
                    ExcelProperty annotation = f.getAnnotation(ExcelProperty.class);
                    return annotation.order();
                })).collect(Collectors.toList());
        for (T data : t) {
            Map<String, String> dataMap = new LinkedHashMap<>();
            for (Field field : list) {
                if (field.isAnnotationPresent(ExcelProperty.class)) {
                    field.setAccessible(true);
                    ExcelProperty property = field.getAnnotation(ExcelProperty.class);
                    if (property.index() == index) {
                        Object o = field.get(data);
                        if (o instanceof Date) {
                            dataMap.put(property.value()[0], format.format(o));
                        } else if (o instanceof Boolean) {
                            Boolean flag = (Boolean) o;
                            dataMap.put(property.value()[0], flag ? "是" : "否");
                        } else {
                            dataMap.put(property.value()[0], o == null ? "" : String.valueOf(o));
                        }
                    }
                }
            }
            exportData.add(dataMap);
        }
        return exportData;
    }

    /**
     * 读取Excel
     *
     * @param file
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> readExcel(MultipartFile file, Class<T> clazz, Integer contextId) throws NoSuchMethodException, NoSuchFieldException, InvocationTargetException, IllegalAccessException {
        List<Map<String, Object>> maps = excelImport(file);
        Map<String, Field> fieldMapping = new HashMap<>();
        Map<String, ExcelMapping> excelMappingMap = new HashMap<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                field.setAccessible(true);
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                fieldMapping.put(excelProperty.value()[0], field);
            }
            if (field.isAnnotationPresent(ExcelMapping.class)) {
                field.setAccessible(true);
                ExcelMapping excelMapping = field.getAnnotation(ExcelMapping.class);
                excelMappingMap.put(field.getName(), excelMapping);
            }
        }

        if (CollectionUtil.isNotEmpty(maps) && CollectionUtil.isNotEmpty(fieldMapping)) {
            Constructor<T> constructor = clazz.getConstructor();
            constructor.setAccessible(true);
            List<T> list = maps.stream()
                    .map(item -> {
                        try {
                            T instance = constructor.newInstance();
                            for (Map.Entry<String, Object> entry : item.entrySet()) {
                                if (fieldMapping.containsKey(entry.getKey()) && !String.valueOf(entry.getValue()).equals("null")) {
                                    Field field = fieldMapping.get(entry.getKey());
                                    Object value = entry.getValue();
                                    if (StrUtil.isNotBlank(String.valueOf(value)) && !String.valueOf(value).equals("null")) {
                                        Object obj = convertObj(value, field, String.valueOf(value));
                                        if (!String.valueOf(obj).equals("null") && !String.valueOf(obj).isEmpty()) {
                                            field.set(instance, obj);
                                        }
                                    }
                                }
                            }
                            return instance;
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toList());
            // 处理多表字段映射
            if (CollectionUtil.isNotEmpty(excelMappingMap)) {
                for (Map.Entry<String, ExcelMapping> entry : excelMappingMap.entrySet()) {
                    try {
                        String key = entry.getKey();
                        Field field = clazz.getDeclaredField(key);
                        field.setAccessible(true);
                        ExcelMapping mapping = entry.getValue();
                        String relation = mapping.relation();
                        String[] relationStr = relation.split("=");
                        String orgedField = mapping.orgField();
                        String cuurentFieldName = relationStr[0];
                        Field currentField = clazz.getDeclaredField(cuurentFieldName);
                        currentField.setAccessible(true);
                        List<Object> values = list.stream()
                                .map(t -> {
                                    try {
                                        return currentField.get(t);
                                    } catch (IllegalAccessException e) {
                                        return "";
                                    }
                                }).filter(t -> null != t && StrUtil.isNotBlank(t.toString()))
                                .collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(values)) {
                            Class<?> serviceClass = mapping.service();
                            Object service = SpringUtils.getBean(serviceClass);
                            Method[] methods = serviceClass.getMethods();
                            for (Method method : methods) {
                                if (method.getName().equals("list") && method.getParameterCount() == 1 && method.getParameterTypes()[0] == Wrapper.class) {
                                    QueryWrapper<Object> wrapper = new QueryWrapper<>();
                                    wrapper.in(StrUtil.toUnderlineCase(relationStr[1]), values);
                                    if (StrUtil.isNotBlank(orgedField) && contextId != null) {
                                        wrapper.eq(StrUtil.toUnderlineCase(orgedField), contextId);
                                    }
                                    Object invoke = method.invoke(service, wrapper);
                                    List<Object> invokeData = null;
                                    if (invoke != null && invoke instanceof List && (invokeData = (List) invoke).size() > 0) {
                                        Class<?> invokeClass = invokeData.get(0).getClass();
                                        Field mappingField = invokeClass.getDeclaredField(mapping.mappingField());
                                        Field current = invokeClass.getDeclaredField(relationStr[1]);
                                        current.setAccessible(true);
                                        mappingField.setAccessible(true);
                                        // 构"建数据关系映射
                                        Map<String, String> dataMapping = new HashMap<>();
                                        for (Object value : values) {
                                            for (Object datum : invokeData) {
                                                Object o = current.get(datum);
                                                if (String.valueOf(value).equals(String.valueOf(o))) {
                                                    dataMapping.put(String.valueOf(value), String.valueOf(mappingField.get(datum)));
                                                }
                                            }
                                        }
                                        for (T t : list) {
                                            Object currentValue = currentField.get(t);
                                            if (dataMapping.containsKey(String.valueOf(currentValue))) {
                                                String target = dataMapping.get(String.valueOf(currentValue));
                                                if (target != null && StrUtil.isNotBlank(target)) {
                                                    field.set(t, convertObj(null, field, target));
                                                }
                                            }
                                        }
                                    }

                                }
                            }
                        }


                    } catch (Exception e) {
                        continue;
                    }

                }

//                for (T t : list) {
//                    mapping:
//                    for (Map.Entry<String, ExcelMapping> entry : excelMappingMap.entrySet()) {
//                        Field field = clazz.getDeclaredField(entry.getKey());
//                        field.setAccessible(true);
//                        ExcelMapping excelMapping = entry.getValue();
//                        String relation = excelMapping.relation();
//                        String[] relationStr = relation.split("=");
//                        String currentClassFieldName = relationStr[0];
//                        String targetClassFieldName = relationStr[1];
//                        Field relationFiled = clazz.getDeclaredField(currentClassFieldName);
//                        relationFiled.setAccessible(true);
//                        Object relationData = relationFiled.get(t);
//                        if (relationData == null || String.valueOf(relationData).equals("null")) {
//                            continue;
//                        }
//                        Class<?> serviceClass = excelMapping.service();
//                        Object service = SpringUtils.getBean(serviceClass);
//                        Method[] methods = serviceClass.getMethods();
//                        for (Method method : methods) {
//                            if (method.getName().equals("list") && method.getParameterCount() == 1 && method.getParameterTypes()[0] == Wrapper.class) {
//                                QueryWrapper<Object> wrapper = new QueryWrapper<>();
//                                wrapper.eq(StrUtil.toUnderlineCase(targetClassFieldName), relationFiled.get(t));
//                                Object invoke = method.invoke(service, wrapper);
//                                List<Object> invokeData = null;
//                                if (invoke != null && invoke instanceof List && (invokeData = (List) invoke).size() > 0) {
//                                    Class<?> invokeClass = invokeData.get(0).getClass();
//                                    Field mappingField = invokeClass.getDeclaredField(excelMapping.mappingField());
//                                    mappingField.setAccessible(true);
//                                    Object mappingData = mappingField.get(invokeData.get(0));
//                                    field.set(t, convertObj(mappingData, field, String.valueOf(mappingData)));
//                                    continue mapping;
//                                }
//
//                            }
//                        }
//
//                    }
//                }
            }
            return list;
        }
        return new ArrayList<>();
    }

    public static void createDropDownList(Sheet sheet, String[] values, int firstRow, int lastRow, int firstCol, int lastCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();

        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        // 设置下拉框数据
        DataValidationConstraint constraint = helper.createExplicitListConstraint(values);
        DataValidation dataValidation = helper.createValidation(constraint, addressList);

        // Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
    }


    /**
     * 横向读取加竖向读取
     *
     * @param file
     * @param clazz
     * @param verticalBeginRow 指定从第几行开始竖向读取(起始行为1)
     * @param needVerifyHead   是否需要校验表头
     * @return
     */
    public static <T> Map<String, Object> readExcelCrossAndVertical(MultipartFile file, Class<T> clazz, int verticalBeginRow, boolean needVerifyHead,Integer index) throws NoSuchMethodException, NoSuchFieldException, InvocationTargetException, IllegalAccessException {
        checkFile(file);

        //最终结果(横向结果的key为实体类的属性名，竖向结果的key为常量)
        Map<String, Object> map = new HashMap<>();
        //竖向读取结果，将数据封装到list<Map>中
        List<Map<String, Object>> dataList = new ArrayList<>();
        //横向读取结果
        Map<String, List<Object>> crossListMap = new HashMap<>();

        //竖向读取的表头(校验时需要，不校验不需要)
        List<List<Object>> excelHead = null;

        try {
            //读取数据
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream(), 0, true);
            for (int result = 0; result < verticalBeginRow - 1; result++) {
                List<List<Object>> read = excelReader.read(result, result);
                if (CollectionUtil.isNotEmpty(read)) {
                    List<Object> objects = read.get(0);
                    crossListMap.put("row" + result, objects);
                }
            }
            if (needVerifyHead) {
                //竖向读取数据的表头
                excelHead = excelReader.read(verticalBeginRow - 1, verticalBeginRow - 1);
            }

            dataList = excelReader.read(verticalBeginRow - 1, verticalBeginRow, Integer.MAX_VALUE);

        } catch (Exception e) {
            throw new RuntimeException("读取数据异常");
        }
        List<Map<String, Object>> maps = dataList;
        Map<String, Field> fieldMapping = new HashMap<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                field.setAccessible(true);
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                fieldMapping.put(excelProperty.value()[index], field);
            }

        }

        if (excelHead != null && CollectionUtil.isNotEmpty(excelHead)) {
            //表头模板校验
            StringBuilder stringBuilder = new StringBuilder();
            if (!verifyExcelHead(excelHead, fieldMapping, stringBuilder)) {
                map.clear();
                map.put("couponImportVerifyExcelHeadFail", stringBuilder.toString());
                return map;
            }
        }

        for (int result = 0; result < verticalBeginRow - 1; result++) {
            List<Object> row = crossListMap.get("row" + result);
            if (CollectionUtil.isEmpty(row)) {
                continue;
            }
            for (int i = 0; i < row.size(); i++) {
                if (row.get(i) == null || row.get(i).toString().trim().equalsIgnoreCase("")) {
                    continue;
                }
                if (fieldMapping.containsKey(row.get(i).toString())) {
                    String string = fieldMapping.get(row.get(i).toString()).toString();
                    if (i % 2 == 0) {
                        if (i + 1 <= row.size() - 1) {
                            if (result == 1) {
                                //第二行的数据的key占据两个单元格，值从第三个单元格取
                                map.put(string.substring(string.lastIndexOf(".") + 1), row.get(i + 2).toString());
                            } else {
                                map.put(string.substring(string.lastIndexOf(".") + 1), row.get(i + 1).toString());
                            }
                        } else {
                            map.put(string.substring(string.lastIndexOf(".") + 1), null);
                        }
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(maps) && CollectionUtil.isNotEmpty(fieldMapping)) {
            Constructor<T> constructor = clazz.getConstructor();
            constructor.setAccessible(true);
            List<T> list = maps.stream()
                    .map(item -> {
                        try {
                            T instance = constructor.newInstance();
                            int nullCount = 0;
                            for (Map.Entry<String, Object> entry : item.entrySet()) {

                                if (fieldMapping.containsKey(entry.getKey()) && !String.valueOf(entry.getValue()).equals("null") && !String.valueOf(entry.getValue()).trim().equals("")) {
                                    Field field = fieldMapping.get(entry.getKey());
                                    Object value = entry.getValue();
                                    Object obj = convertObj(value, field, String.valueOf(value));
                                    field.set(instance, obj);
                                } else {
                                    nullCount ++;
                                }
                            }
                            if (nullCount == item.entrySet().size()) {
                                return null;
                            }
                            return instance;
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            map.put("list", list);
            return map;
        }
        return new HashMap<>();
    }

    public static boolean verifyExcelHead(List<List<Object>> excelHead, Map<String, Field> fieldMap, StringBuilder stringBuilder) {

        List<Object> head = excelHead.get(0);
        List<String> collect = head.stream().map(s -> s.toString()).collect(Collectors.toList());
        List<String> collect1 = fieldMap.keySet().stream().map(s -> s.toString()).collect(Collectors.toList());
        if (collect1.containsAll(collect)) {
            return true;
        } else {
            stringBuilder.append("上传文件非模板格式，请先下载模板");
            return false;
        }
    }

    /**
     * 表格导入
     *
     * @param file        文件路径
     * @param clazz       表格实体类
     * @param dataHandler 函数式接口 自定义表格数据校验
     * @return 实体对象
     */
    public static <T> List<T> importExcel(MultipartFile file, Class<T> clazz, ExcelDataHandler<T> dataHandler) {
        List<T> dataList = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            EasyExcel.read(inputStream, clazz, new AnalysisEventListener<T>() {
                @Override
                public void invoke(T data, AnalysisContext context) {
                    // 在invoke方法中调用自定义策略处理数据
                    dataHandler.handle(data);
                    // 将处理后的数据添加到列表
                    dataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 所有数据已读取完毕，可以在这里进行后续处理
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new CustomException("文件读取失败:" + e.getMessage());
        }

        return dataList;
    }


    /**
     *  使用该方法可以配合@ExcelProperty使用
     *  如果遇到日期处理可以配合
     *  com.bizark.op.common.converter下的注解使用
     * @param response 请求相应
     * @param fileName 文件名
     * @param excelModelClass 表格实体类
     * @param data 表格列表
     * @param <T> 特定类型
     */
    public static <T> void exportExcel(HttpServletResponse response, String fileName, Class<T> excelModelClass, List<T> data) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            // URLEncoder.encode可以防止中文乱码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
            // 使用EasyExcel进行实际的写操作
            EasyExcel.write(response.getOutputStream(), excelModelClass).sheet("sheet1").doWrite(data);
        } catch (Exception e) {
            logger.error(fileName + "导出出现错误", e);
            throw new CustomException("导出出现错误！");
        }
    }

    /**
     *  使用该方法可以配合@ExcelProperty使用
     *  如果遇到日期处理可以配合
     *  com.bizark.op.common.converter下的注解使用
     * @param response 请求相应
     * @param fileName 文件名
     * @param excelModelClass 表格实体类
     * @param data 表格列表
     * @param <T> 特定类型
     * @param consumer 对sheet的自定义操作
     */
    public static <T> void exportExcel(HttpServletResponse response, String fileName, Class<T> excelModelClass, List<T> data, Consumer<ExcelWriterSheetBuilder> consumer) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            // URLEncoder.encode可以防止中文乱码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
            // 使用EasyExcel进行实际的写操作
            ExcelWriterSheetBuilder sheet = EasyExcel.write(response.getOutputStream(), excelModelClass).sheet("sheet1");
            // 自定义对sheet的操作
            consumer.accept(sheet);
            sheet.doWrite(data);
        } catch (Exception e) {
            logger.error(fileName + "导出出现错误", e);
            throw new CustomException("导出出现错误！");
        }
    }


    /**
     *  sheet额外区域取值创建下拉列表
     * @param sheet
     * @param rowNum
     * @param length
     * @param firstRow
     * @param lastRow
     * @param firstCol
     * @param lastCol
     */
    public static void createDropDownListWith(HSSFSheet sheet, String rowNum, Integer length, int firstRow, int lastRow, int firstCol, int lastCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        String refers = "$" + rowNum + "$1:$" + rowNum + "$" + length;
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        DataValidationConstraint constraint = helper.createFormulaListConstraint(refers);
        DataValidation dataValidation = helper.createValidation(constraint, addressList);
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }
        sheet.addValidationData(dataValidation);
    }



    /**
     * excel多sheet导入（允许某个sheet全部为空）允许sheet里有非field的列，例如下拉填充数据来源
     * 某个sheet某一行允许全部为空，但只要该行有一列有值才做该行非空校验
     * 重要：文件标题不可重复
     *
     * @param file      文件
     * @param classList 列对应的对象
     * @return 返回数据集合
     */
    public static Map<Class, List<?>> excelMultiSheetImportNew(MultipartFile file, List<Class> classList) {
        checkFile(file);
        try {
            //读取数据
            return excelMultiSheetImportNew(file.getInputStream(), classList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            AssertUtil.isFail(ExcelErrorEnum.FILE_ERROR, e);
        }
        return CollUtil.newHashMap();
    }


    /**
     * excel 多sheet导入
     *
     * @param inputStream 文件流
     * @param classList   对应的类数组
     * @return 返回数据集合
     * @throws Exception
     */
    private static HashMap<Class, List<?>> excelMultiSheetImportNew(InputStream inputStream, List<Class> classList) throws Exception {
        //将数据封装到list<Map>中
        HashMap<Class, List<?>> dataMap = CollUtil.newHashMap();
        ExcelReader reader = ExcelUtil.getReader(inputStream, false);
        List<Sheet> sheetList = reader.getSheets();
        AssertUtil.isTrue(sheetList.size() == classList.size(), ExcelErrorEnum.FILE_PATTERN_ERROR);

        for (int i = 0; i < classList.size(); i++) {
            ExcelReader excelReader = null;
            try {
                Class clazz = classList.get(i);
                excelReader = new ExcelReader(sheetList.get(i));
                List<Map<String, Object>> list = excelReader.readAll();
                List value = bulidDataListNew(clazz, list);
                if (CollectionUtil.isNotEmpty(value)) {
                    dataMap.put(clazz, value);
                }

            } finally {
                IoUtil.close(excelReader);
            }
        }
        IoUtil.close(reader);
        return dataMap;
    }


    /**
     * 封装数据信息
     *
     * @param clazz 对应的类
     * @param list  读取的数据集合
     * @param <T>   泛型
     */
    private static <T> List<T> bulidDataListNew(Class<T> clazz, List<Map<String, Object>> list) throws InstantiationException, IllegalAccessException {
        //将数据封装到list<Map>中
        List<T> dataList = new ArrayList<>();
        AssertUtil.isTrue(CollUtil.isNotEmpty(list), ExcelErrorEnum.NO_DATA);

        logger.info("读取结果：" + list.size());

        Field[] fields = clazz.getDeclaredFields();
        fields = Arrays.stream(fields).filter(item -> item.isAnnotationPresent(ExcelHead.class)).toArray(Field[]::new);

        AssertUtil.isTrue(ArrayUtil.isNotEmpty(fields), ExcelErrorEnum.FILE_PATTERN_ERROR);

        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
//            AssertUtil.isTrue(map.size() == fields.length, ExcelErrorEnum.FILE_PATTERN_ERROR);
            T t = clazz.newInstance();

            boolean needSetValue = false;
            boolean oneFiledIsNotNull = false;

            //如果map的key和字段名不在fields中，则去除掉这个key
            Field[] finalFields = fields;
            map.entrySet().removeIf(item -> Arrays.stream(finalFields).noneMatch(field -> field.getAnnotation(ExcelHead.class).name().equals(item.getKey())));
            for (Map.Entry<String, Object> stringObjectEntry : map.entrySet()) {
                String key = stringObjectEntry.getKey();
                Object value = stringObjectEntry.getValue();
                Field field = Arrays.stream(fields).filter(item -> item.getAnnotation(ExcelHead.class).name().equals(key)).findFirst().orElse(null);
                if (field == null) {
                    continue;
                }
                //如果某一行某一个列设置了值，才对该行做必填校验
                if (oneFiledIsNotNull) {
                checkImportFieldIsNotNull(value, field);
                }
                if (value == null) {
                    continue;
                }
                String strValue = value.toString().trim();
                if (StrUtil.isBlank(strValue)) {
                    continue;
                }
                value = convertObj(value, field, strValue);
                field.setAccessible(true);
                field.set(t, value);
                needSetValue = true;
                oneFiledIsNotNull = true;
            }
            if (needSetValue) {
                dataList.add(t);
            }

        }

        return dataList;
    }



    public static <T> Map<String, Object> readExcelCrossAndVertical(InputStream inputStream, Class<T> clazz, int verticalBeginRow, boolean needVerifyHead,Integer index) throws Exception {


        //最终结果(横向结果的key为实体类的属性名，竖向结果的key为常量)
        Map<String, Object> map = new HashMap<>();
        //竖向读取结果，将数据封装到list<Map>中
        List<Map<String, Object>> dataList = new ArrayList<>();
        //横向读取结果
        Map<String, List<Object>> crossListMap = new HashMap<>();

        //竖向读取的表头(校验时需要，不校验不需要)
        List<List<Object>> excelHead = null;

        try {
            //读取数据
            ExcelReader excelReader = ExcelUtil.getReader(inputStream, 0, true);
            for (int result = 0; result < verticalBeginRow - 1; result++) {
                List<List<Object>> read = excelReader.read(result, result);
                if (CollectionUtil.isNotEmpty(read)) {
                    List<Object> objects = read.get(0);
                    crossListMap.put("row" + result, objects);
                }
            }
            if (needVerifyHead) {
                //竖向读取数据的表头
                excelHead = excelReader.read(verticalBeginRow - 1, verticalBeginRow - 1);
            }

            dataList = excelReader.read(verticalBeginRow - 1, verticalBeginRow, Integer.MAX_VALUE);

        } catch (Exception e) {
            throw new RuntimeException("读取数据异常");
        }
        List<Map<String, Object>> maps = dataList;
        Map<String, Field> fieldMapping = new HashMap<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                field.setAccessible(true);
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                fieldMapping.put(excelProperty.value()[index], field);
            }

        }

        if (excelHead != null && CollectionUtil.isNotEmpty(excelHead)) {
            //表头模板校验
            StringBuilder stringBuilder = new StringBuilder();
            if (!verifyExcelHead(excelHead, fieldMapping, stringBuilder)) {
                map.clear();
                map.put("couponImportVerifyExcelHeadFail", stringBuilder.toString());
                return map;
            }
        }

        for (int result = 0; result < verticalBeginRow - 1; result++) {
            List<Object> row = crossListMap.get("row" + result);
            if (CollectionUtil.isEmpty(row)) {
                continue;
            }
            for (int i = 0; i < row.size(); i++) {
                if (row.get(i) == null || row.get(i).toString().trim().equalsIgnoreCase("")) {
                    continue;
                }
                if (fieldMapping.containsKey(row.get(i).toString())) {
                    String string = fieldMapping.get(row.get(i).toString()).toString();
                    if (i % 2 == 0) {
                        if (i + 1 <= row.size() - 1) {
                            if (result == 1) {
                                //第二行的数据的key占据两个单元格，值从第三个单元格取
                                map.put(string.substring(string.lastIndexOf(".") + 1), row.get(i + 2).toString());
                            } else {
                                map.put(string.substring(string.lastIndexOf(".") + 1), row.get(i + 1).toString());
                            }
                        } else {
                            map.put(string.substring(string.lastIndexOf(".") + 1), null);
                        }
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(maps) && CollectionUtil.isNotEmpty(fieldMapping)) {
            Constructor<T> constructor = clazz.getConstructor();
            constructor.setAccessible(true);
            List<T> list = maps.stream()
                    .map(item -> {
                        try {
                            T instance = constructor.newInstance();
                            int nullCount = 0;
                            for (Map.Entry<String, Object> entry : item.entrySet()) {

                                if (fieldMapping.containsKey(entry.getKey()) && !String.valueOf(entry.getValue()).equals("null") && !String.valueOf(entry.getValue()).trim().equals("")) {
                                    Field field = fieldMapping.get(entry.getKey());
                                    Object value = entry.getValue();
                                    Object obj = convertObj(value, field, String.valueOf(value));
                                    field.set(instance, obj);
                                } else {
                                    nullCount ++;
                                }
                            }
                            if (nullCount == item.entrySet().size()) {
                                return null;
                            }
                            return instance;
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            map.put("list", list);
            return map;
        }
        return new HashMap<>();
    }
}
