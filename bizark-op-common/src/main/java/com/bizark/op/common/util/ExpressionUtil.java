package com.bizark.op.common.util;

import lombok.extern.slf4j.Slf4j;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表达式工具类
 *
 * @Author: Ailill
 * @Date: 2025-08-04 14:00
 */
@Slf4j
public class ExpressionUtil {

    /**
     * 判断表达式是否可解析
     */

    public static boolean isExpressionValid(String expression, List<String> replaceList) {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");
        Map<String, BigDecimal> variables = new HashMap<>();
        for (String var : replaceList) {
            variables.put(var, new BigDecimal("1.0"));
        }
        try {
            engine.eval("function validate() { return " + expression + "; }");
            variables.forEach(engine::put);
            engine.eval(expression);
            return true;
        } catch (ScriptException e) {
            log.error("表达式--{}--变量--{}解析失败:{} ", expression, replaceList, e.getMessage());
            return false;
        }
    }

    /**
     * 获取表达式的值
     *
     * @param expression
     * @param replaceList
     * @return
     */
    public static BigDecimal getExpressionValue(String expression, Map<String, BigDecimal> replaceList) {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");
        replaceList.forEach(engine::put);
        try {
            BigDecimal result = (new BigDecimal(engine.eval(expression).toString())).setScale(2, RoundingMode.HALF_UP);
            log.info("表达式计算结果: " + result);
            return result;
        } catch (Exception e) {
            log.error("表达式{}--变量--{}计算失败: {}", expression, replaceList, e.getMessage());
            return null;
        }
    }
}
