package com.bizark.op.common.core.domain;

import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.io.Serializable;
import java.util.List;

/**
 * excel 行格式实体类
 *
 * <AUTHOR>
 * @date 2023/12/25 - 17:58
 */
@Data
public class ExcelCellStyleEntity implements Serializable {

    /**
     * 行
     */
    private int rowIndex;

    /**
     * 列
     */
    private int cellIndex;


    /**
     * 水平格式
     */
    private HorizontalAlignment horizontalAlignment;

    /**
     * 垂直格式
     */
    private VerticalAlignment verticalAlignment;

    /**
     * 下拉框数据
     */
    private String[] selectData;

    public ExcelCellStyleEntity() {
    }

    public ExcelCellStyleEntity(int rowIndex, int cellIndex, HorizontalAlignment horizontalAlignment, VerticalAlignment verticalAlignment) {
        this.rowIndex = rowIndex;
        this.cellIndex = cellIndex;
        this.horizontalAlignment = horizontalAlignment;
        this.verticalAlignment = verticalAlignment;
    }

    public ExcelCellStyleEntity(int rowIndex, int cellIndex, String[] selectData) {
        this.rowIndex = rowIndex;
        this.cellIndex = cellIndex;
        this.selectData = selectData;
    }
}
