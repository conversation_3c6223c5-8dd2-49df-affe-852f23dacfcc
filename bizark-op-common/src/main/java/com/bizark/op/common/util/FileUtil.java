package com.bizark.op.common.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.ObjIntConsumer;
import java.util.function.Supplier;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileUtil {

    private static volatile Map<String, Class<?>> classMap = new HashMap<>();
    private static volatile Map<String, Constructor<?>> constructorMap = new HashMap<>();
    private static volatile Map<String, Method> methodMap = new HashMap<>();
    private static volatile Map<String, Object> objectMap = new HashMap<>();


    /**
     * 文件写入内容
     * @param path
     * @param context
     */
    public static void writeFile(String path, String context) {
        BufferedWriter out = null;
        try {
            File file = new File(path);
            if (file.exists()) {
                file.createNewFile();
            }
            out = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file, true)));
            out.write(context + "\n  \n");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    public static boolean downloadFile(String downloadUrl, String filePath) throws IOException {


        URL url = new URL(downloadUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestProperty("User-Agent", "Mozilla/5.0");
        connection.setInstanceFollowRedirects(false); // Disable automatic redirection.

        int responseCode = connection.getResponseCode();
        int count = 0;
        while ((responseCode == HttpURLConnection.HTTP_MOVED_PERM || responseCode == HttpURLConnection.HTTP_MOVED_TEMP) && ++count < 10) {
            // 获取重定向的URL
            String location = connection.getHeaderField("Location");
            url = new URL(location);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");
            responseCode = connection.getResponseCode();
        }

        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(filePath)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            return true;
        } else {
            log.info("File download error - {} - {}", downloadUrl, responseCode);
//            throw new RuntimeException("Server returned non-OK status: " + responseCode);
            return false;
        }

    }



    public static String downLoadAndUpload(String downloadUrl, String filePath, String folder, boolean delete) throws IOException {
        boolean result = downloadFile(downloadUrl, filePath);
        if (!result) {
            return null;
        }
        File file = new File(filePath);
        String path = AliyunOssClientUtil.uploadFile(file.getName(), Files.newInputStream(Paths.get(filePath)), folder);
        if (delete) {
            cn.hutool.core.io.FileUtil.del(filePath);
        }
        return path;
    }

    public static byte[] readStream(InputStream in) throws IOException {
        ByteArrayOutputStream bout = new ByteArrayOutputStream();
        readStream(in, (a, b) -> bout.write(a, 0, b));
        byte[] result = bout.toByteArray();
        return result;
    }

    public static void readStream(InputStream in, ObjIntConsumer<byte[]> consumer) throws IOException {
        InputStream fin;
        if (in instanceof ByteArrayInputStream) {
            fin = in;
        }
        else if (in instanceof BufferedInputStream) {
            fin = in;
        }
        else {
            fin = new BufferedInputStream(in);
        }


        byte[] buffer = new byte[8192];
        int read = 0;
        while (true) {
            read = fin.read(buffer);
            if (read < 1) {
                break;
            }
            consumer.accept(buffer, read);
        }
    }

    public static byte[] readFile(String fpath) throws IOException {
        InputStream fin = new BufferedInputStream(new FileInputStream(fpath));
        try {
            return readStream(fin);
        }
        finally {
            try {
                fin.close();
            }
            catch (IOException e) {

            }
        }
    }

    public static void putFile(String fpath, final byte[] data) throws IOException {
        OutputStream fout = null;
        try {
            fout = new BufferedOutputStream(new FileOutputStream(fpath));
            fout.write(data);
            fout.flush();
        }
        finally {
            if (fout != null) {
                try {
                    fout.close();
                }
                catch (IOException e) {

                }
            }
        }
    }

    public static void putFile(String fpath, InputStream data) throws IOException {
        OutputStream fout = new BufferedOutputStream(new FileOutputStream(fpath));
        InputStream fin;
        if (data instanceof ByteArrayInputStream) {
            fin = data;
        }
        else if (data instanceof BufferedInputStream) {
            fin = data;
        }
        else {
            fin = new BufferedInputStream(data);
        }

        byte[] buffer = new byte[8192];
        int read = 0;
        try {
            while (true) {
                read = fin.read(buffer);
                if (read < 1) {
                    break;
                }
                fout.write(buffer, 0, read);
            }
            fout.flush();
        }
        finally {
            try {
                fout.close();
            }
            catch (IOException e) {

            }
        }
    }

    public static byte[] getResourceData(final String path) throws Exception {
        InputStream inputStream = getResourceStream(path);
        try {
            return readStream(inputStream);
        }
        finally {
            try {
                inputStream.close();
            }
            catch (IOException e) {

            }
        }
    }

    public static URL getResource(final String path) throws Exception {
        URL res = null;
        if (path.startsWith("classpath:")) {
            res = getResource1(path);
            if (res != null) {
                log.debug("getResource " + path + ": use getResource1 success");
            }
        }
        if (res == null) {
            res = getResource2(path);
            if (res != null) {
                log.debug("getResource " + path + ": use getResource2 success");
            }
        }
        if (res == null) {
            throw new FileNotFoundException("resource " + path + "not exists");
        }
        return res;
    }

    private static URL getResource1(final String path) throws Exception {
        String fpath = path;
        if (fpath.startsWith("classpath:")) {
            fpath = fpath.substring("classpath:".length()).trim();
        }

        String className = "org.springframework.core.io.ClassPathResource";
        Class<?> clazz = getClazz(className);
        if (clazz == null) {
            return null;
        }

        Constructor<?> constructor = getConstructor(clazz, String.class);

        String classPathResourceObjectKey = "org.springframework.core.io.ClassPathResource_String";
        String fpath2 = fpath;
        List<Exception> exceptions = new ArrayList<>();
        Object classPathResource = getObject(classPathResourceObjectKey, () -> {
            try {
                return newInstance(constructor, fpath2);
            }
            catch (Exception e) {
                exceptions.add(e);
                return null;
            }
        });
        if (!exceptions.isEmpty()) {
            throw exceptions.iterator().next();
        }

        String methodName = "resolveURL";
        Method method = getMethod(clazz, methodName);
        if (method == null) {
            return null;
        }

        URL url = (URL) invoke(method, classPathResource);
        return url;
    }

    private static URL getResource2(final String path) {
        String fpath = path;
        if (fpath.startsWith("classpath:")) {
            fpath = fpath.substring("classpath:".length()).trim();
        }

        if (fpath.charAt(0) != '/') {
            fpath = "/" + fpath;
        }

        URL url = FileUtil.class.getResource(fpath);
        if (url == null) {
            if (!fpath.startsWith("/resources")) {
                url = FileUtil.class.getResource("/resources" + fpath); // resources目录, 兼容jar包
            }
        }
        return url;
    }

    public static InputStream getResourceStream(final String path) throws Exception {
        URL res = null;
        try {
            res = getResource(path);
        }
        catch (FileNotFoundException e) {
            throw e;
        }
        catch (Exception e) {
            throw e;
        }

        InputStream inputStream = res == null ? null : res.openStream();
        if (inputStream == null) {
            throw new FileNotFoundException("file " + path + " not found");
        }

        return inputStream;
    }

    private static Class<?> getClazz(String className) {
        Class<?> clazz = classMap.get(className);
        if (clazz != null) {
            return clazz;
        }
        synchronized(classMap) {
            clazz = classMap.get(className);
            if (clazz != null) {
                return clazz;
            }

            try {
                clazz = Class.forName(className);
            }
            catch (ClassNotFoundException e) {
                return null;
            }
            classMap.put(className, clazz);
            return clazz;
        }
    }

    private static Constructor<?> getConstructor(Class<?> clazz, Class<?>... parameterTypes) {
        StringBuilder keyBuidler = new StringBuilder(clazz.getName());
        if (parameterTypes != null && parameterTypes.length > 0) {
            for (int i = 0; i < parameterTypes.length; i++) {
                keyBuidler.append('_');
                keyBuidler.append(parameterTypes[i].getName());
            }
        }
        String key = keyBuidler.toString();

        Constructor<?> constructor = constructorMap.get(key);
        if (constructor != null) {
            return constructor;
        }

        synchronized(constructorMap) {
            constructor = constructorMap.get(key);
            if (constructor != null) {
                return constructor;
            }

            try {
                constructor = clazz.getConstructor(parameterTypes);
            }
            catch (NoSuchMethodException | SecurityException e) {
                try {
                    constructor = clazz.getDeclaredConstructor(parameterTypes);
                }
                catch (NoSuchMethodException | SecurityException e2) {
                    return null;
                }
            }
            constructor.setAccessible(true);
            constructorMap.put(key, constructor);
            return constructor;
        }
    }

    private static Method getMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        StringBuilder keyBuidler = new StringBuilder(clazz.getName());
        if (parameterTypes != null && parameterTypes.length > 0) {
            for (int i = 0; i < parameterTypes.length; i++) {
                keyBuidler.append('_');
                keyBuidler.append(parameterTypes[i].getName());
            }
        }
        String key = keyBuidler.toString();

        Method method = methodMap.get(key);
        if (method != null) {
            return method;
        }

        synchronized(methodMap) {
            method = methodMap.get(key);
            if (method != null) {
                return method;
            }

            try {
                method = clazz.getMethod(methodName, parameterTypes);
            }
            catch (NoSuchMethodException | SecurityException e) {
                try {
                    method = clazz.getDeclaredMethod(methodName, parameterTypes);
                }
                catch (NoSuchMethodException | SecurityException e2) {
                    return null;
                }
            }
            method.setAccessible(true);
            methodMap.put(key, method);
            return method;
        }
    }

    private static Object getObject(String key, Supplier<?> supplier) {
        Object object = objectMap.get(key);
        if (object != null) {
            return object;
        }
        if (supplier == null) {
            return null;
        }

        synchronized(objectMap) {
            object = objectMap.get(key);
            if (object != null) {
                return object;
            }

            object = supplier.get();
            if (object != null) {
                objectMap.put(key, object);
            }
            return object;
        }
    }

    private static Object newInstance(Constructor<?> constructor, Object ... initargs) throws Exception {
        try {
            return constructor.newInstance(initargs);
        }
        catch (RuntimeException e) {
            throw e;
        }
        catch (InvocationTargetException e) {
            Throwable t = e.getTargetException();
            if (t instanceof RuntimeException) {
                throw (RuntimeException) t;
            }
            else if (t instanceof Exception) {
                throw (Exception) t;
            }
            else {
                throw new Exception(t.getMessage(), t);
            }
        }
        catch (Exception e) {
            throw e;
        }
    }

    private static Object invoke(Method method, Object obj, Object... args) throws Exception {
        try {
            return method.invoke(obj, args);
        }
        catch (RuntimeException e) {
            throw e;
        }
        catch (InvocationTargetException e) {
            Throwable t = e.getTargetException();
            if (t instanceof RuntimeException) {
                throw (RuntimeException) t;
            }
            else if (t instanceof Exception) {
                throw (Exception) t;
            }
            else {
                throw new Exception(t.getMessage(), t);
            }
        }
        catch (Exception e) {
            throw e;
        }
    }
}
