package com.bizark.op.common.config;

import com.bizark.op.common.exception.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

/**
 * 手动事务管理
 */
@Component
public class TransactionManger {

    private final PlatformTransactionManager transactionManager;

    @Autowired
    public TransactionManger(PlatformTransactionManager transactionManager) {
        this.transactionManager = transactionManager;
    }

    /**
     * 通用手动事务处理 根细粒度的事务控制
     * @param action 这里传要事务管理的操作
     */
    public void doInTransaction(Runnable action) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            action.run();
            transactionManager.commit(status);
        } catch (Exception ex) {
            transactionManager.rollback(status);
            throw new CustomException(ex.getMessage());
        }
    }

    /**
     *  自定义事务状态
     */
    public void doInTransactionUseStatus(Runnable action,TransactionStatus status) {
        try {
            action.run();
            transactionManager.commit(status);
        } catch (Exception ex) {
            transactionManager.rollback(status);
            throw new CustomException(ex.getMessage());
        }
    }
}
