package com.bizark.op.common.util;

import org.apache.commons.collections.CollectionUtils;
import org.dozer.DozerBeanMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class DozerUtils {

    private final static Logger log = LoggerFactory.getLogger(DozerUtils.class);

    private static DozerBeanMapper mapper = new DozerBeanMapper();

    /**
     * 单个对象数据转换
     *
     * @param source
     * @param destinationClass
     * @param <T>
     * @return
     */
    public static <T> T convert(Object source, Class<T> destinationClass) {
        if (source != null) {
            return mapper.map(source, destinationClass);
        }
        return null;
    }

    /**
     * 列表数据转换
     *
     * @param sourceList
     * @param destinationClass
     * @param <T>
     * @param <S>
     * @return
     */
    public static <T, S> List<T> convertList(List<S> sourceList, Class<T> destinationClass) {
        if (CollectionUtils.isNotEmpty(sourceList)) {
            List<T> retList = new ArrayList<T>(sourceList.size());
            for (S source : sourceList) {
                retList.add(mapper.map(source, destinationClass));
            }
            return retList;
        }
        return new ArrayList<T>();
    }


    /**
     * 获取 get方法的执行结果
     */
    public static Object getInvokeResult(Object oo, Method readMethod) {
        Object result = null;
        try {
            result = readMethod.invoke(oo);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("invoke method fail", e);
        }
        return result;
    }

    /**

     * 获取 类中 get属性方法
     */
    public static Method getReadMethod(Class<?> clazz, String fieldName) {
        Method readMethod = null;
        try {
            PropertyDescriptor clazzProperty = new PropertyDescriptor(fieldName, clazz);
            readMethod = clazzProperty.getReadMethod();
        } catch (IntrospectionException e) {
            log.error("get property method fail", e);
        }
        return readMethod;
    }

    /**

     * 获取 类中 get属性方法
     */
    public static Method getWriteMethod(Class<?> clazz, String fieldName) {
        Method readMethod = null;
        try {
            PropertyDescriptor clazzProperty = new PropertyDescriptor(fieldName, clazz);
            readMethod = clazzProperty.getWriteMethod();
        } catch (IntrospectionException e) {
            log.error("get property method fail", e);
        }
        return readMethod;
    }

}
