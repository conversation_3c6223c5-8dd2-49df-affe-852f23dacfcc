package com.bizark.op.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomDateConverter implements Converter<Date> {

    // 需要的日期格式(我们需要的日期样式)
    private static final String PATTERN_YYYY_MM_DD = "yyyy-MM-dd HH:mm:ss ";

    @Override
    public Class<?> supportJavaTypeKey() {
        return Converter.super.supportJavaTypeKey();
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return Converter.super.supportExcelTypeKey();
    }

    @Override
    public Date convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return Converter.super.convertToJavaData(cellData, contentProperty, globalConfiguration);

    }

    @Override
    public Date convertToJavaData(ReadConverterContext<?> context) throws Exception {
        //获取字段的string值
        return getTime(context.getReadCellData().getNumberValue() + "");
    }

    @Override
    public WriteCellData<?> convertToExcelData(Date value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(PATTERN_YYYY_MM_DD);
        String dateValue = sdf.format(value);
        return new WriteCellData<>(dateValue);
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Date> context) throws Exception {
        return Converter.super.convertToExcelData(context);
    }

    /**
     * 将日期数字转为时间格式
     * daysDuration = 44745
     *
     * @param daysDuration
     * @return
     */
    public static Date getTime(String daysDuration) {
        //如果不是数字
        if(!isNumeric(daysDuration)){
            return null;
        }
        //如果是数字 小于0则 返回
        BigDecimal bd = new BigDecimal(daysDuration);
        //天数
        int days = bd.intValue();
        int mills = (int) Math.round(bd.subtract(new BigDecimal(days)).doubleValue() * 24 * 3600);
        //获取时间
        Calendar c = Calendar.getInstance();
        c.set(1900, Calendar.JANUARY, 1);
        c.add(Calendar.DATE, days - 2);
        int hour = mills / 3600;
        int minute = (mills - hour * 3600) / 60;
        int second = mills - hour * 3600 - minute * 60;
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);
        c.set(Calendar.SECOND, second);

        String format = new SimpleDateFormat(PATTERN_YYYY_MM_DD).format(c.getTime());
        DateFormat dateFormat = new SimpleDateFormat(PATTERN_YYYY_MM_DD);
        Date birthDate = new Date();
        try {
            birthDate = dateFormat.parse(format);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return birthDate;
    }

    /**
     * 校验是否数据含小数点
     *
     * @param str
     * @return
     */
    private static boolean isNumeric(String str){
        Pattern pattern = Pattern.compile("[0-9]+\\.*[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if(!isNum.matches()){
            return false;
        }
        return true;
    }


}
