package com.bizark.op.common.factory;

import com.bizark.op.common.handler.handler.platform.AbstractRefundHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * lty notes:拿到对应的第三方退换货工具包
 *
 * <AUTHOR> Theo
 * @create 2023/10/17 18:05
 */
@Component
public class RefundAndReturnFactory {

    @Resource
    private Map<String, AbstractRefundHandler> strategyMap;

    public AbstractRefundHandler getInvokeStrategy(String strategyName){
        return strategyMap.get(strategyName);
    }
}
