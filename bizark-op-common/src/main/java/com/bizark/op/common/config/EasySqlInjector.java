package com.bizark.op.common.config;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;

import java.util.List;

/**
 * 拓展mapper的批量插入方法
 * <AUTHOR> 2023/10/8
 */
public class EasySqlInjector extends DefaultSqlInjector {

	@Override
	public List<AbstractMethod> getMethodList(Class<?> mapperClass) {
		List<AbstractMethod> methodList = super.getMethodList(mapperClass);
		// 添加InsertBatchSomeColumn方法
		methodList.add(new InsertBatchSomeColumn());
		return methodList;
	}

}
