package com.bizark.op.common.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * excel 合并实体类
 *
 * <AUTHOR>
 * @date 2023/10/31 - 10:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExcelMergeEntity implements Serializable {


    /**
     * 开始行
     */
    private Integer startRow;

    /**
     * 结束行
     */
    private Integer endRow;

    /**
     * 开始列
     */
    private Integer startColumn;

    /**
     * 结束列
     */
    private Integer endColumn;

    /**
     * 合并内容:默认为合并的第一个单元格
     */
    private String content;

}
