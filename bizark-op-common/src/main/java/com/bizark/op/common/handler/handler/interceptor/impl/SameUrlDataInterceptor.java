package com.bizark.op.common.handler.handler.interceptor.impl;


import com.alibaba.fastjson.JSON;
import com.bizark.op.common.annotation.RepeatSubmit;
import com.bizark.op.common.core.redis.RedisCache;
import com.bizark.op.common.handler.handler.interceptor.RepeatSubmitInterceptor;
import com.bizark.op.common.handler.handler.interceptor.RepeatedlyRequestWrapper;
import com.bizark.op.common.util.HttpHelper;
import com.bizark.op.common.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 判断请求url和数据是否和上一次相同，
 *
 * <AUTHOR>
 */
@Component
public class SameUrlDataInterceptor extends RepeatSubmitInterceptor
{
    /**
     * 提交表单
     */
    public static final String REPEAT_PARAMS = "repeatParams";

    private static final String REPEAT_TIME = "repeatTime";

    private static final String REPEAT_SUBMIT_KEY = "repeat_submit:";


    // token的自定义标识
    private static final String header = "Authorization";

    @Autowired
    private RedisCache redisCache;

    private static final String PROCESSING_KEY_PREFIX = "processing_submit:";

    @SuppressWarnings("unchecked")
    @Override
    public boolean isRepeatSubmit(HttpServletRequest request, RepeatSubmit annotation)
    {
        String nowParams = "";
        if (request instanceof RepeatedlyRequestWrapper)
        {
            RepeatedlyRequestWrapper repeatedlyRequest = (RepeatedlyRequestWrapper) request;
            nowParams = HttpHelper.getBodyString(repeatedlyRequest);
        }

        // body参数为空，获取Parameter的数据
        if (StringUtils.isEmpty(nowParams))
        {
            nowParams = JSON.toJSONString(request.getParameterMap());
        }

        if (isProcessing(request, annotation)) {
            return true;
        }
        Map<String, Object> nowDataMap = new HashMap<String, Object>();
        nowDataMap.put(REPEAT_PARAMS, nowParams);
        nowDataMap.put(REPEAT_TIME, System.currentTimeMillis());

        // 请求地址（作为存放cache的key值）
        String url = request.getRequestURI();

        //是否需要同一用户的授权信息加入key
        boolean needAuthorization = annotation.needAuthorization();
        String submitKey = "";
        if (needAuthorization) {
            submitKey = StringUtils.trimToEmpty(request.getHeader(header));
        }
        // 唯一标识（指定key + url + 消息头）
        String cacheRepeatKey = REPEAT_SUBMIT_KEY + url + submitKey;

        markAsProcessing(request, annotation);
        try {
            Object sessionObj = redisCache.getCacheObject(cacheRepeatKey);
            if (sessionObj != null)
            {
                Map<String, Object> sessionMap = (Map<String, Object>) sessionObj;
                if (sessionMap.containsKey(url))
                {
                    Map<String, Object> preDataMap = (Map<String, Object>) sessionMap.get(url);
                    if (compareParams(nowDataMap, preDataMap) && compareTime(nowDataMap, preDataMap, annotation.interval()))
                    {
                        return true;
                    }
                }
            }
            Map<String, Object> cacheMap = new HashMap<String, Object>();
            cacheMap.put(url, nowDataMap);
            redisCache.setCacheObject(cacheRepeatKey, cacheMap, annotation.interval(), TimeUnit.MILLISECONDS);
            return false;
        } finally {
            clearProcessingMark(request, annotation);
        }

    }

    /**
     * 判断参数是否相同
     */
    private boolean compareParams(Map<String, Object> nowMap, Map<String, Object> preMap)
    {
        String nowParams = (String) nowMap.get(REPEAT_PARAMS);
        String preParams = (String) preMap.get(REPEAT_PARAMS);
        return nowParams.equals(preParams);
    }

    /**
     * 判断两次间隔时间
     */
    private boolean compareTime(Map<String, Object> nowMap, Map<String, Object> preMap, int interval)
    {
        long time1 = (Long) nowMap.get(REPEAT_TIME);
        long time2 = (Long) preMap.get(REPEAT_TIME);
        if ((time1 - time2) < interval)
        {
            return true;
        }
        return false;
    }



    private boolean isProcessing(HttpServletRequest request, RepeatSubmit annotation) {
        String url = request.getRequestURI();
        boolean needAuthorization = annotation.needAuthorization();
        String submitKey = needAuthorization ? StringUtils.trimToEmpty(request.getHeader(header)) : "";
        String processingKey = PROCESSING_KEY_PREFIX + url + submitKey;

        return redisCache.getCacheObject(processingKey) != null;
    }

    private void markAsProcessing(HttpServletRequest request, RepeatSubmit annotation) {
        String url = request.getRequestURI();
        boolean needAuthorization = annotation.needAuthorization();
        String submitKey = needAuthorization ? StringUtils.trimToEmpty(request.getHeader(header)) : "";
        String processingKey = PROCESSING_KEY_PREFIX + url + submitKey;
        redisCache.setCacheObject(processingKey, true, 1, TimeUnit.MINUTES);
    }

    private void clearProcessingMark(HttpServletRequest request, RepeatSubmit annotation) {
        String url = request.getRequestURI();
        boolean needAuthorization = annotation.needAuthorization();
        String submitKey = needAuthorization ? StringUtils.trimToEmpty(request.getHeader(header)) : "";
        String processingKey = PROCESSING_KEY_PREFIX + url + submitKey;
        redisCache.deleteObject(processingKey);
    }
}
