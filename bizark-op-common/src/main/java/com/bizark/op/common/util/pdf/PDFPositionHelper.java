package com.bizark.op.common.util.pdf;

import lombok.Getter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * PDF文本位置定位帮助类
 */
public class PDFPositionHelper {

    /**
     * PDF文本位置信息
     */
    @Getter
    public static class TextPositionInfo {
        // Getters
        private String text;
        private float x;
        private float y;
        private float width;
        private float height;

        public TextPositionInfo(String text, float x, float y, float width, float height) {
            this.text = text;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }

        @Override
        public String toString() {
            return String.format("Text: '%s', X: %.2f, Y: %.2f, Width: %.2f, Height: %.2f",
                    text, x, y, width, height);
        }
    }

    /**
     * 自定义PDF文本提取器，用于获取文本位置信息
     */
    public static class PositionTextStripper extends PDFTextStripper {
        @Getter
        private final List<TextPositionInfo> textPositions = new ArrayList<>();
        private final String searchText;

        public PositionTextStripper(String searchText) throws IOException {
            this.searchText = searchText;
            // 设置宽松的字体处理模式
            this.setSuppressDuplicateOverlappingText(true);
        }

        @Override
        protected void writeString(String string, List<TextPosition> textPositions) throws IOException {
            try {
                if (searchText == null || string.contains(searchText)) {
                    for (int i = 0; i < textPositions.size(); i++) {
                        if (i < string.length()) { // 确保索引有效
                            TextPosition textPosition = textPositions.get(i);
                            String character = string.substring(i, i + 1);

                            // 如果是搜索特定文本，只记录匹配的文本
                            if (searchText != null && !searchText.contains(character)) {
                                continue;
                            }

                            // 获取文本位置信息
                            float x = textPosition.getX();
                            float y = textPosition.getY();
                            float width = textPosition.getWidth();
                            float height = textPosition.getHeight();

                            this.textPositions.add(new TextPositionInfo(character, x, y, width, height));
                        }
                    }
                }
                super.writeString(string, textPositions);
            } catch (Exception e) {
                // 忽略字体相关的异常，继续处理
                System.err.println("处理文本时遇到字体问题，已跳过: " + e.getMessage());
            }
        }

    }

    /**
     * 查找PDF中特定文本的位置信息
     *
     * @param document   PDF文档
     * @param searchText 要查找的文本
     * @return 文本位置信息列表
     */
    public static List<TextPositionInfo> findTextPositions(PDDocument document, String searchText) {
        List<TextPositionInfo> positions;
        try {
            PositionTextStripper stripper = new PositionTextStripper(searchText);
            stripper.setStartPage(1);
            stripper.setEndPage(document.getNumberOfPages());
            stripper.getText(document);
            positions = stripper.getTextPositions();
        } catch (Exception e) {
            System.err.println("查找文本位置时出错: " + e.getMessage());
            // 使用备用方法
            positions = findTextPositionsWithFallback(document, searchText);
        }
        return positions;
    }

    /**
     * 备用文本位置查找方法
     */
    private static List<TextPositionInfo> findTextPositionsWithFallback(PDDocument document, String searchText) {
        List<TextPositionInfo> positions = new ArrayList<>();
        try {
            // 使用标准PDFTextStripper获取文本内容
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setStartPage(1);
            stripper.setEndPage(Math.min(1, document.getNumberOfPages())); // 只处理第一页

            String text = stripper.getText(document);

            if (searchText != null && text.contains(searchText)) {
                // 简化处理：返回估算位置
                // 在实际应用中，您可能需要更精确的位置估算逻辑
                System.out.println("找到匹配文本，使用估算位置");
                positions.add(new TextPositionInfo(searchText, 100, 700,
                        searchText.length() * 6, 12));
            }
        } catch (Exception e) {
            System.err.println("备用文本查找方法也失败: " + e.getMessage());
        }
        return positions;
    }

    /**
     * 根据文本内容查找并掩盖PDF中的文本
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param searchText 要掩盖的文本内容
     * @param coverColor 掩盖颜色
     */
    public static void coverTextByContent(String inputPath, String outputPath,
                                          String searchText, Color coverColor) {
        try (PDDocument document = PDDocument.load(new File(inputPath))) {
            coverTextByContent(document, searchText, coverColor);
            document.save(outputPath);
        } catch (Exception e) {
            throw new RuntimeException("根据文本内容掩盖PDF失败", e);
        }
    }

    /**
     * 根据文本内容查找并掩盖PDF中的文本
     *
     * @param document   PDF文档
     * @param searchText 要掩盖的文本
     * @param coverColor 掩盖颜色
     */
    public static void coverTextByContent(PDDocument document, String searchText, Color coverColor) {
        try {
            // 查找文本位置
            List<TextPositionInfo> positions = findTextPositions(document, searchText);

            if (positions.isEmpty()) {
                System.out.println("未找到文本: " + searchText + "，使用备用掩盖方法");
                // 使用备用掩盖方法
                coverWithEstimatedPosition(document, searchText, coverColor);
                return;
            }

            System.out.println("找到 " + positions.size() + " 个文本位置");

            // 为第一页创建掩盖（简化处理）
            if (document.getNumberOfPages() > 0 && !positions.isEmpty()) {
                PDPage page = document.getPage(0);

                try (PDPageContentStream contentStream = new PDPageContentStream(
                        document, page, PDPageContentStream.AppendMode.APPEND, true)) {

                    // 设置掩盖颜色
                    contentStream.setNonStrokingColor(coverColor != null ? coverColor : Color.WHITE);

                    // 为每个文本位置添加掩盖
                    for (TextPositionInfo position : positions) {
                        // 添加一些边距使掩盖更完整
                        float margin = 2.0f;
                        contentStream.addRect(
                                position.getX() - margin,
                                position.getY() - margin,
                                position.getWidth() + 2 * margin,
                                position.getHeight() + 2 * margin
                        );
                        contentStream.fill();
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("掩盖文本时出错: " + e.getMessage());
            // 出错时使用备用掩盖方法
            try {
                coverWithEstimatedPosition(document, searchText, coverColor);
            } catch (Exception ex) {
                throw new RuntimeException("掩盖文本失败", ex);
            }
        }
    }

    /**
     * 使用估算位置进行掩盖的备用方法
     */
    private static void coverWithEstimatedPosition(PDDocument document, String searchText, Color coverColor) {
        try {
            System.out.println("使用估算位置掩盖文本: " + searchText);

            if (document.getNumberOfPages() > 0) {
                PDPage page = document.getPage(0);

                try (PDPageContentStream contentStream = new PDPageContentStream(
                        document, page, PDPageContentStream.AppendMode.APPEND, true)) {

                    // 设置掩盖颜色
                    contentStream.setNonStrokingColor(coverColor != null ? coverColor : Color.WHITE);

                    // 在页面顶部添加一个通用的掩盖区域
                    // 这是一个示例，实际使用时需要根据具体需求调整
                    contentStream.addRect(50, 650, 500, 100);
                    contentStream.fill();
                }
            }
        } catch (Exception e) {
            System.err.println("备用掩盖方法也失败: " + e.getMessage());
            throw new RuntimeException("无法掩盖文本", e);
        }
    }

    /**
     * 根据坐标范围掩盖文本
     *
     * @param document   PDF文档
     * @param x         X坐标
     * @param y         Y坐标
     * @param width     宽度
     * @param height    高度
     * @param coverColor 掩盖颜色
     */
    public static void coverTextByCoordinates(PDDocument document, float x, float y,
                                              float width, float height, Color coverColor) {
        try {
            if (document.getNumberOfPages() > 0) {
                PDPage page = document.getPage(0); // 假设操作第一页

                try (PDPageContentStream contentStream = new PDPageContentStream(
                        document, page, PDPageContentStream.AppendMode.APPEND, true)) {

                    contentStream.setNonStrokingColor(coverColor != null ? coverColor : Color.WHITE);
                    contentStream.addRect(x, y, width, height);
                    contentStream.fill();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("根据坐标掩盖PDF失败", e);
        }
    }

    public static void main(String[] args) {
        try {
            String inputPath = "/Users/<USER>/Downloads/原始.pdf";
            String outputPath = "covered_output.pdf";

            // 根据文本内容掩盖
            coverTextByContent(inputPath, outputPath, "HJ", Color.BLACK);

            System.out.println("PDF文本掩盖完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
