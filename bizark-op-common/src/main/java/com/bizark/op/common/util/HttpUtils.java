package com.bizark.op.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.bizark.op.common.constant.Constants;
import com.bizark.op.common.exception.CustomException;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import okhttp3.*;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.BufferedHttpEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

import javax.net.ssl.*;
import java.io.*;
import java.net.*;
import java.security.cert.X509Certificate;
import java.util.*;

/**
 * 通用http发送方法
 *
 * <AUTHOR>
 */
public class HttpUtils {
    private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);

    /**
     * 超时时间 .
     */
    private static int SOCKET_TIMEOUT = 180000;

    /**
     * 连接超时时间 .
     */
    private static int CONNECT_TIMEOUT = 180000;


    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param) {
        return sendGet(url, param, Constants.UTF8, null);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @param param 请求头参数
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param, Map<String, String> headerMap) {
        return sendGet(url, param, Constants.UTF8, headerMap);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url          发送请求的 URL
     * @param param        请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @param encodingType 编码类型
     * @param param        请求头参数
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param, String encodingType, Map<String, String> headerMap) {
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        try {
            String urlNameString = url;
            if (StringUtil.isNotEmpty(param)) {
                urlNameString = url + "?" + param;
            }
            log.info("sendGet - {}", urlNameString);
            URL realUrl = new URL(urlNameString);
            URLConnection connection = realUrl.openConnection();
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36");

            if (null != headerMap && !headerMap.isEmpty()) {
                Set<String> keySet = headerMap.keySet();
                for (String keyStr : keySet) {
                    connection.setRequestProperty(keyStr, headerMap.get(keyStr));
                }
            }

            connection.connect();
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), encodingType));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            log.info("recv - {}", result);
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendGet ConnectException, url=" + url + ",param=" + param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendGet SocketTimeoutException, url=" + url + ",param=" + param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendGet IOException, url=" + url + ",param=" + param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendGet Exception, url=" + url + ",param=" + param, e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception ex) {
                log.error("调用in.close Exception, url=" + url + ",param=" + param, ex);
            }
        }
        return result.toString();
    }

    /**
     * post请求x-www-form-urlencoded方式传参
     *
     * @param urlStr  url
     * @param message 请求参数
     * @return
     */
    public static String doPostByForm(String urlStr, String message) {
        URLConnection conn = null;
        try {
            URL url = new URL(urlStr);
            conn = url.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) conn;
            httpUrlConnection.setRequestMethod("POST");
            byte[] data = message.getBytes("utf-8");
            httpUrlConnection.setDoOutput(true);
            httpUrlConnection.setDoInput(true);
            httpUrlConnection.setUseCaches(false);
            httpUrlConnection.setInstanceFollowRedirects(true);
            httpUrlConnection.setInstanceFollowRedirects(false);
            httpUrlConnection.setConnectTimeout(CONNECT_TIMEOUT);
            httpUrlConnection.setReadTimeout(SOCKET_TIMEOUT);
            httpUrlConnection.addRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
            httpUrlConnection.addRequestProperty("Content-Length", String.valueOf(data.length));
            OutputStream out = httpUrlConnection.getOutputStream();
            out.write(data);
            out.flush();
            out.close();
            InputStreamReader inputStreamReader = new InputStreamReader(conn.getInputStream(), "utf-8");
            BufferedReader in = new BufferedReader(inputStreamReader);
            String line = null;
            StringBuilder stringBuffer = new StringBuilder(255);
            while ((line = in.readLine()) != null) {
                stringBuffer.append(line);
                stringBuffer.append("\n");
            }
            String responseMessage = stringBuffer.toString();
            return responseMessage;
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendPost Exception, url=" + urlStr + ",param=" + message, e);
            return null;
        } finally {
            if ((conn != null) && conn instanceof HttpURLConnection)
                ((HttpURLConnection) conn).disconnect();
        }
    }

    /**
     * post请求
     *
     * @param urlStr  url地址
     * @param message json字符串请求报文
     * @return json字符串响应报文
     */
    public static String doPostByJson(String urlStr, String message) {
        URLConnection conn = null;
        try {
            URL url = new URL(urlStr);
            conn = url.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) conn;
            httpUrlConnection.setRequestMethod("POST");
            byte[] data = message.getBytes("utf-8");
            httpUrlConnection.setDoOutput(true);
            httpUrlConnection.setDoInput(true);
            httpUrlConnection.setUseCaches(false);
            httpUrlConnection.setInstanceFollowRedirects(true);
            httpUrlConnection.setInstanceFollowRedirects(false);
            httpUrlConnection.setConnectTimeout(CONNECT_TIMEOUT);
            httpUrlConnection.setReadTimeout(SOCKET_TIMEOUT);
            httpUrlConnection.addRequestProperty("Content-Type", "application/json;charset=utf-8");
            httpUrlConnection.addRequestProperty("Content-Length", String.valueOf(data.length));
            OutputStream out = httpUrlConnection.getOutputStream();
            out.write(data);
            out.flush();
            out.close();
            InputStreamReader inputStreamReader = new InputStreamReader(conn.getInputStream(), "utf-8");
            BufferedReader in = new BufferedReader(inputStreamReader);
            String line = null;
            StringBuilder stringBuffer = new StringBuilder(255);
            while ((line = in.readLine()) != null) {
                stringBuffer.append(line);
                stringBuffer.append("\n");
            }
            String responseMessage = stringBuffer.toString();
            return responseMessage;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if ((conn != null) && conn instanceof HttpURLConnection)
                ((HttpURLConnection) conn).disconnect();
        }
    }

    /**
     * post请求
     *
     * @param urlStr        url地址
     * @param message       json字符串请求报文
     * @param authorization 授权信息
     * @return json字符串响应报文
     */
    public static String doPostByJson(String urlStr, String message, String authorization) {
        URLConnection conn = null;
        try {
            URL url = new URL(urlStr);
            conn = url.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) conn;
            httpUrlConnection.setRequestMethod("POST");
            byte[] data = message.getBytes("utf-8");
            httpUrlConnection.setDoOutput(true);
            httpUrlConnection.setDoInput(true);
            httpUrlConnection.setUseCaches(false);
            httpUrlConnection.setInstanceFollowRedirects(true);
            httpUrlConnection.setInstanceFollowRedirects(false);
            httpUrlConnection.setConnectTimeout(CONNECT_TIMEOUT);
            httpUrlConnection.setReadTimeout(SOCKET_TIMEOUT);
            httpUrlConnection.setRequestProperty("Authorization", authorization);
            httpUrlConnection.addRequestProperty("Content-Type", "application/json;charset=utf-8");
            httpUrlConnection.addRequestProperty("Content-Length", String.valueOf(data.length));
            OutputStream out = httpUrlConnection.getOutputStream();
            out.write(data);
            out.flush();
            out.close();
            InputStreamReader inputStreamReader = new InputStreamReader(conn.getInputStream(), "utf-8");
            BufferedReader in = new BufferedReader(inputStreamReader);
            String line = null;
            StringBuilder stringBuffer = new StringBuilder(255);
            while ((line = in.readLine()) != null) {
                stringBuffer.append(line);
                stringBuffer.append("\n");
            }
            String responseMessage = stringBuffer.toString();
            return responseMessage;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if ((conn != null) && conn instanceof HttpURLConnection)
                ((HttpURLConnection) conn).disconnect();
        }
    }

    /**
     * POST请求,map参数
     *
     * @param url     .
     * @param dataMap .
     * @return CloseableHttpResponse .
     * @throws Exception
     */
    public static String sendPost(String url, Map<String, Object> dataMap) throws Exception {
        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse httpResonse = null;
        try {
            RequestConfig config = RequestConfig.custom().setSocketTimeout(SOCKET_TIMEOUT)
                    .setConnectTimeout(CONNECT_TIMEOUT).setAuthenticationEnabled(false).build();

            HttpPost post = new HttpPost(url);
            post.setProtocolVersion(org.apache.http.HttpVersion.HTTP_1_1);
            post.setConfig(config);

            HttpEntity entity = null;
            List<NameValuePair> formpair = new ArrayList<NameValuePair>();
            {
                for (String str : dataMap.keySet().toArray(new String[dataMap.size()])) {
                    formpair.add(new BasicNameValuePair(str, dataMap.get(str).toString()));
                }
            }

            entity = new UrlEncodedFormEntity(formpair, "UTF-8");
            if (entity != null) {
                post.setEntity(entity);
            }
            httpResonse = client.execute(post);
            if (httpResonse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return HttpHelper.parseResponseToStr(httpResonse);
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            httpResonse.close();
            client.close();
        }
        return null;
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            String urlNameString = url;
            log.info("sendPost - {}", urlNameString);
            URL realUrl = new URL(urlNameString);
            URLConnection conn = realUrl.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Accept-Charset", "utf-8");
            conn.setRequestProperty("contentType", "utf-8");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            out = new PrintWriter(conn.getOutputStream());
            out.print(param);
            out.flush();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            log.info("recv - {}", result);
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendPost ConnectException, url=" + url + ",param=" + param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendPost SocketTimeoutException, url=" + url + ",param=" + param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendPost IOException, url=" + url + ",param=" + param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendPost Exception, url=" + url + ",param=" + param, e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("调用in.close Exception, url=" + url + ",param=" + param, ex);
            }
        }
        return result.toString();
    }

    public static String sendSSLPost(String url, String param) {
        StringBuilder result = new StringBuilder();
        String urlNameString = url + "?" + param;
        try {
            log.info("sendSSLPost - {}", urlNameString);
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, new TrustManager[]{new TrustAnyTrustManager()}, new java.security.SecureRandom());
            URL console = new URL(urlNameString);
            HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Accept-Charset", "utf-8");
            conn.setRequestProperty("contentType", "utf-8");
            conn.setDoOutput(true);
            conn.setDoInput(true);

            conn.setSSLSocketFactory(sc.getSocketFactory());
            conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
            conn.connect();
            InputStream is = conn.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String ret = "";
            while ((ret = br.readLine()) != null) {
                if (ret != null && !"".equals(ret.trim())) {
                    result.append(new String(ret.getBytes("ISO-8859-1"), "utf-8"));
                }
            }
            log.info("recv - {}", result);
            conn.disconnect();
            br.close();
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendSSLPost ConnectException, url=" + url + ",param=" + param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendSSLPost SocketTimeoutException, url=" + url + ",param=" + param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendSSLPost IOException, url=" + url + ",param=" + param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendSSLPost Exception, url=" + url + ",param=" + param, e);
        }
        return result.toString();
    }

    private static class TrustAnyTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    private static class TrustAnyHostnameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }

    /**
     * 获取本机的所有的网卡信息(ipv4网卡) (兼容linux)
     */
    public static List<String> getLocalHostIp() {
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            List<String> localHostAddress = Lists.newArrayList();
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = allNetInterfaces.nextElement();
                Enumeration<InetAddress> address = networkInterface.getInetAddresses();
                while (address.hasMoreElements()) {
                    InetAddress inetAddress = address.nextElement();
                    if (inetAddress instanceof Inet4Address) {
                        localHostAddress.add(inetAddress.getHostAddress());
                    }
                }
            }
            return localHostAddress;
        } catch (SocketException e) {
            log.error("获取本地网卡信息失败", e);
        }
        return CollUtil.newArrayList();
    }

    /**
     * post请求传输json参数
     *
     * @param url       url地址
     * @param jsonParam 参数
     * @return 响应消息
     */
    public static JSONObject httpPost(String url, JSONObject jsonParam, org.apache.http.Header[] header, String contentType) {
        // post请求返回结果
        CloseableHttpClient httpClient = HttpClients.createDefault();
        JSONObject jsonResult = new JSONObject();
        HttpPost httpPost = new HttpPost(url);
        RequestConfig config = RequestConfig.custom().setSocketTimeout(SOCKET_TIMEOUT)
                .setConnectTimeout(CONNECT_TIMEOUT).setAuthenticationEnabled(false).build();

        // 设置请求和传输超时时请求
        httpPost.setConfig(config);
        try {
            log.info(jsonParam.toString());
            if (StringUtils.isNotNull(jsonParam)) {
                // 解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");
                entity.setContentEncoding("UTF-8");
                if (StringUtils.isNotEmpty(contentType)) {
                    entity.setContentType(contentType);
                } else {
                    entity.setContentType("application/json");
                }
                httpPost.setEntity(entity);
                httpPost.setHeaders(header);
            }
            log.info(jsonParam.toString());
            CloseableHttpResponse result = httpClient.execute(httpPost);
            // 请求发请求成功，并得到响应
            if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String str = "";
                try {
                    HttpEntity httpEntity = result.getEntity();
                    httpEntity = new BufferedHttpEntity(httpEntity);

                    // 读取服务器返回过来的json字符串数
                    str = EntityUtils.toString(httpEntity, "utf-8");
                    // 把json字符串转换成json对象
                    jsonResult = JSONObject.parseObject(str);
                } catch (Exception e) {
                    log.error("post请求提交失败:" + url, e);
                }
            } else {
                log.error("post请求提交失败:" + url, EntityUtils.toString(result.getEntity(), "utf-8"));
                log.error("post请求提交失败,状态码:{}", result.getStatusLine().getStatusCode());
            }
        } catch (IOException e) {
            log.error("post请求提交失败:" + url, e);
        } finally {
            httpPost.releaseConnection();
        }
        return jsonResult;
    }

    /**
     * post请求传输json参数
     *
     * @param url       url地址
     * @param jsonParam 参数
     * @return
     */
    public static JSONObject httpPost(String url, JSONObject jsonParam,String contentType) throws Exception{
        // post请求返回结果
        CloseableHttpClient httpClient = HttpClients.createDefault();
        JSONObject jsonResult = null;
        HttpPost httpPost = new HttpPost(url);
        RequestConfig config = RequestConfig.custom().setSocketTimeout(SOCKET_TIMEOUT)
                .setConnectTimeout(CONNECT_TIMEOUT).setAuthenticationEnabled(false).build();

        // 设置请求和传输超时时请求
        httpPost.setConfig(config);
        try {
            log.info(JSONObject.toJSONString(jsonParam));
            if (null != jsonParam) {
                // 解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType(StringUtils.isEmpty(contentType) ?"application/json":contentType);
                httpPost.setEntity(entity);
            }
            CloseableHttpResponse result = httpClient.execute(httpPost);
            // 请求发请求成功，并得到响应
            if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String str = "";
                try {
                    // 读取服务器返回过来的json字符串数
                    str = EntityUtils.toString(result.getEntity(), "utf-8");
                    // 把json字符串转换成json对象
                    jsonResult = JSONObject.parseObject(str);
                } catch (Exception e) {
                    log.error("post请求提交失败:" + url, e);
                    throw new CustomException(String.format("Post远程调用失败,转换异常: 请求参数 : %s ,失败原因: %s",JSONObject.toJSONString(jsonParam),e.getMessage()));
                }
            }else {
                log.error(String.format("Post远程调用 %s 失败,响应异常,状态码: %s" , url,result.getStatusLine().getStatusCode()) );
                throw new CustomException(String.format("Post远程调用 %s 失败,响应异常: 请求参数 : %s ,状态码: %s",url,JSONObject.toJSONString(jsonParam),result.getStatusLine().getStatusCode()));
            }
        } catch (IOException e) {
            log.error("post请求提交失败:" + url, e);
            throw new CustomException(String.format("Post远程调用失败: 请求参数 : %s ,失败原因: %s",JSONObject.toJSONString(jsonParam),e.getMessage()));
        } finally {
            httpPost.releaseConnection();
            httpClient.close();
        }
        return jsonResult;
    }

    /**
     * post请求传输json参数
     *
     * @param url       url地址
     * @param jsonParam 参数
     * @return
     */
    public static JSONObject httpPost(String url, String jsonParam,String contentType) throws Exception{
        // post请求返回结果
        CloseableHttpClient httpClient = HttpClients.createDefault();
        JSONObject jsonResult = null;
        HttpPost httpPost = new HttpPost(url);
        RequestConfig config = RequestConfig.custom().setSocketTimeout(SOCKET_TIMEOUT)
                .setConnectTimeout(CONNECT_TIMEOUT).setAuthenticationEnabled(false).build();

        // 设置请求和传输超时时请求
        httpPost.setConfig(config);
        try {
            log.info(JSONObject.toJSONString(jsonParam));
            if (null != jsonParam) {
                // 解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam, "utf-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType(StringUtils.isEmpty(contentType) ?"application/json":contentType);
                httpPost.setEntity(entity);
            }
            CloseableHttpResponse result = httpClient.execute(httpPost);
            // 请求发请求成功，并得到响应
            if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String str = "";
                try {
                    // 读取服务器返回过来的json字符串数
                    str = EntityUtils.toString(result.getEntity(), "utf-8");
                    // 把json字符串转换成json对象
                    jsonResult = JSONObject.parseObject(str);
                } catch (Exception e) {
                    log.error("post请求提交失败:" + url, e);
                    throw new CustomException(String.format("Post远程调用失败,转换异常: 请求参数 : %s ,失败原因: %s",JSONObject.toJSONString(jsonParam),e.getMessage()));
                }
            }else {
                log.error(String.format("Post远程调用 %s 失败,响应异常,状态码: %s" , url,result.getStatusLine().getStatusCode()) );
                throw new CustomException(String.format("Post远程调用 %s 失败,响应异常: 请求参数 : %s ,状态码: %s",url,JSONObject.toJSONString(jsonParam),result.getStatusLine().getStatusCode()));
            }
        } catch (IOException e) {
            log.error("post请求提交失败:" + url, e);
            throw new CustomException(String.format("Post远程调用失败: 请求参数 : %s ,失败原因: %s",JSONObject.toJSONString(jsonParam),e.getMessage()));
        } finally {
            httpPost.releaseConnection();
            httpClient.close();
        }
        return jsonResult;
    }

    /**
     * 发送get请求
     *
     * @param url 路径
     * @return
     */
    public static JSONObject httpGet(String url) {
        // get请求返回结果
        JSONObject jsonResult = null;
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000).setConnectionRequestTimeout(35000).setSocketTimeout(60000).build();
        CloseableHttpClient client = HttpClients.createDefault();
        // 发送get请求
        HttpGet request = new HttpGet(url);
        request.setConfig(requestConfig);
        try {
            CloseableHttpResponse response = client.execute(request);

            // 请求发送成功，并得到响应
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                // 读取服务器返回过来的json字符串数组
                HttpEntity entity = response.getEntity();
                String strResult = EntityUtils.toString(entity, "utf-8");
                // 把json字符串转换成json对象
                jsonResult = JSONObject.parseObject(strResult);
            } else {
                log.error("get请求提交失败:" + url);
            }
        } catch (IOException e) {
            log.error("get请求提交失败:" + url, e);
        } finally {
            request.releaseConnection();
        }
        return jsonResult;
    }


    public static String sendPostTiktok(String url, String token, String param) {
        OkHttpClient client = new OkHttpClient();
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;

        RequestBody body = RequestBody.create(JSON, param);
        try {
            Request.Builder builder = new Request.Builder().url(url)
                    .addHeader("Content-Type", "application/json")
                    .post(body);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(token)) {
                builder.addHeader("Access-Token", token);
            }
            Request request = builder.build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error(String.format("请求TIKTOK发送POST请求异常: %d and message: %s",
                        response.code(), response.message()));
            }
            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (IOException e) {
            log.error("调用TIKTOK发送POST请求失败:{}", e.getMessage());
        }

        return result.toString();
    }


    public static String sendGetTiktok(String url, String token) {
        OkHttpClient client = new OkHttpClient();
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;

        try {
            Request.Builder builder = new Request.Builder().url(url)
                    .addHeader("Content-Type", "application/json");
            if (StringUtils.isNotEmpty(token)) {
                builder.addHeader("Access-Token", token);
            }
            Request request = builder.build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error(String.format("请求TIKTOK发送GET请求异常: %d and message: %s",
                        response.code(), response.message()));
            }
            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (IOException e) {
            log.error("请求TIKTOK发送GET请求失败:{}", e.getMessage());
        }

        return result.toString();
    }


}
