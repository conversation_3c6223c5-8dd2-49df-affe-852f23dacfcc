///**
// * Copyright © 2015 - 2017 EntDIY JavaEE Development Framework
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// * http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
//package com.bizark.op.test.common;
//
//import cn.hutool.core.util.StrUtil;
//import com.bizark.common.util.JacksonUtils;
//import org.junit.Assert;
//import org.junit.Test;
//
//import java.util.*;
//
//public class JsonTest {
//
//    @Test
//    public void optionalTest() {
//        Assert.assertFalse(Optional.empty().isPresent());
//
//        Map<String,String> subdict = new HashMap<>();
//        subdict.put("sk1","v1");
//        subdict.put("sk2","v2");
//        Map<String,Object> mydict = new HashMap<>();
//        mydict.put("abc",1);
//        mydict.put("bbc","qqq");
//        mydict.put("cbd","weqwe");
//        mydict.put("csub",subdict);
//
//        String serialized = JacksonUtils.toJson(mydict);
//        System.out.println(serialized);
//        Map<String,Object> dedict = JacksonUtils.jsonToMap(serialized);
//
//        String mytpl = " my template %bbc% %abc% myasd   {   %csub% }      fasdfasdff";
//        String mycontent = mytpl;
//        if(dedict!=null){
//            for (Map.Entry<String, Object> param : dedict.entrySet()) {
//                mycontent = StrUtil.replace(mycontent,"%"+param.getKey()+"%",param.getValue().toString());
//            }
//        }
//        System.out.println(dedict);
//        System.out.println(mycontent);
//
//        List<Map<String,Object>> myList = new ArrayList<>();
//        Map<String,Object> row1 = new HashMap<>();
//        row1.put("k1",1);
//        row1.put("k2","v2");
//        myList.add(row1);
//        System.out.println(JacksonUtils.toJson(myList));
//    }
//}
