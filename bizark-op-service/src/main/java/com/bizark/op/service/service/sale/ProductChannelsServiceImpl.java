package com.bizark.op.service.service.sale;

import bizark.amz.catalog.*;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.HyperlinkData;
import com.alibaba.excel.metadata.data.RichTextStringData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.bizark.boss.api.enm.SellStatusEnum;
import com.bizark.boss.api.entity.dashboard.ProductLineEntity;
import com.bizark.boss.api.entity.dashboard.product.ProductChannelEntity;
import com.bizark.boss.api.entity.dashboard.product.ProductChannelRelateEntity;
import com.bizark.boss.api.entity.dashboard.stock.StockAccountAddressEntity;
import com.bizark.boss.api.service.ProductLineService;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.service.stock.StockAccountAddressService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.common.exception.CommonException;
import com.bizark.common.util.JacksonUtils;
import com.bizark.fbb.api.entity.fbb.InventoryListEntity;
import com.bizark.fbb.api.service.InventoryListService;
import com.bizark.framework.security.AuthContextHolder;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.cons.RedisCons;
import com.bizark.op.api.dto.sale.*;
import com.bizark.op.api.enm.config.SystemGlobalConfigEnum;
import com.bizark.op.api.enm.inventory.InventoryType;
import com.bizark.op.api.enm.log.LogEnums;
import com.bizark.op.api.enm.sale.*;
import com.bizark.op.api.enm.sale.temu.TemuShopTypeEnum;
import com.bizark.op.api.enm.sys.SysLabelConfigEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.account.ApprovalDTO;
import com.bizark.op.api.entity.op.inventory.InventorySyncBean;
import com.bizark.op.api.entity.op.inventory.ProductChannelsInventory;
import com.bizark.op.api.entity.op.inventory.ProductChannelsStockMessage;
import com.bizark.op.api.entity.op.inventory.SyncInventoryResult;
import com.bizark.op.api.entity.op.inventory.enums.InventoryStatus;
import com.bizark.op.api.entity.op.inventory.response.query.*;
import com.bizark.op.api.entity.op.log.ErpOperateLog;
import com.bizark.op.api.entity.op.mar.MarListingInfo;
import com.bizark.op.api.entity.op.product.ShopifyProductResponse;
import com.bizark.op.api.entity.op.product.SkuChildren;
import com.bizark.op.api.entity.op.product.SkuParents;
import com.bizark.op.api.entity.op.promotions.MarTemuPromotionPriceReport;
import com.bizark.op.api.entity.op.sale.*;
import com.bizark.op.api.entity.op.sale.dto.ProductChannelExportDTO;
import com.bizark.op.api.entity.op.sale.response.WalmartWfsInventoryResponse;
import com.bizark.op.api.entity.op.sale.vo.*;
import com.bizark.op.api.service.RpcAsyncExportService;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.account.AmazonMarketPlaceService;
import com.bizark.op.api.service.conf.SystemGlobalConfigService;
import com.bizark.op.api.service.document.ScDocumentMaterialService;
import com.bizark.op.api.service.finance.FinConfExchangeRateService;
import com.bizark.op.api.service.inventory.ProductChannelsInventoryService;
import com.bizark.op.api.service.log.ErpOperateLogService;
import com.bizark.op.api.service.mar.MarListingInfoService;
import com.bizark.op.api.service.product.SkuChildrenService;
import com.bizark.op.api.service.product.SkuParentsService;
import com.bizark.op.api.service.sale.*;
import com.bizark.op.api.service.stat.StatOrderSalesService;
import com.bizark.op.api.service.sys.ISysDictTypeService;
import com.bizark.op.api.service.sys.SysLabelConfigService;
import com.bizark.op.api.vo.mar.ListingVo;
import com.bizark.op.api.vo.product.*;
import com.bizark.op.api.vo.sale.CommonDataContext;
import com.bizark.op.api.vo.sale.ProductChannelEditResult;
import com.bizark.op.api.vo.sale.ProductChannelsImportVO;
import com.bizark.op.common.core.domain.sys.SysDictData;
import com.bizark.op.common.exception.CheckException;
import com.bizark.op.common.util.*;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.api.AmazonApi;
import com.bizark.op.service.api.InventorySelectApi;
import com.bizark.op.service.api.ProductSelectApi;
import com.bizark.op.service.api.WalmartApi;
import com.bizark.op.service.event.ProductChannelApprovalEvent;
import com.bizark.op.service.handler.inventory.sync.AbstractInventorySynchronizer;
import com.bizark.op.service.handler.inventory.sync.InventorySynchronizerFactory;
import com.bizark.op.service.handler.sale.ProductChannelsResultHandler;
import com.bizark.op.service.mapper.inventory.ProductChannelsInventoryHisMapper;
import com.bizark.op.service.mapper.inventory.ProductChannelsInventoryMapper;
import com.bizark.op.service.mapper.mar.MarListingInfoHisMapper;
import com.bizark.op.service.mapper.promotions.MarTemuPromotionPriceReportMapper;
import com.bizark.op.service.mapper.sale.ProductChannelsMapper;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.op.service.util.third.qywx.QywxUtil;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.bizark.usercenter.api.parameter.attachment.AttachItemRow;
import com.bizark.usercenter.api.parameter.attachment.ContextAttachMeta;
import com.bizark.usercenter.api.parameter.user.UserDeptVO;
import com.bizark.usercenter.api.service.UcPermissionService;
import com.bizark.usercenter.api.service.UcPostService;
import com.bizark.usercenter.api.service.UserService;
import com.squareup.okhttp.*;
import com.xxl.conf.core.annotation.XxlConf;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bizark.op.api.enm.sale.ProductChannelTypeEnum.TEMU;
import static com.bizark.op.api.enm.sale.ProductChannelTypeEnum.TIKTOK;
import static com.bizark.op.common.util.ExcelUtils.createDropDownList;

@Service
@Slf4j
public class ProductChannelsServiceImpl extends ServiceImpl<ProductChannelsMapper, ProductChannels> implements ProductChannelsService {


    @Value("${task.center.file.path}")
    private String filePath;

    @Autowired
    private AmazonApi amazonApi;


    @XxlConf(value = "bizark-erp.sellersku.change.notice")
    public static String NOTIFY_OPERATE;


    @Autowired
    private UcPermissionService ucPermissionService;

    @Autowired
    private UserService userService;

    @Autowired
    private ISysDictTypeService sysDictTypeService;


    @Value("${crm.api.ip}")
    private String crmApiUrl;
    @Autowired
    private ProductChannelsMapper productChannelsMapper;

    @Autowired(required = false)
    private StockAccountAddressService stockAccountAddressService;

    @Autowired(required = false)
    private com.bizark.boss.api.service.product.ProductChannelService bossProductChannels;

    @Autowired
    private InventoryListService inventoryListService;

    @Autowired
    private InventorySelectApi inventorySelectApi;


    @Autowired
    private ScDocumentMaterialService documentMaterialService;

    @Autowired
    private ScGoodsCategoryService scGoodsCategoryService;

    @Autowired
    private ProductChannelsRestrictService productChannelsRestrictService;

    @Autowired
    private ProductLineService productLineService;

    @Autowired
    private SaleOrdersMapper saleOrdersMapper;

    @Autowired
    private ProductChannelRelateService productChannelRelateService;

    @Autowired
    private ProductChannelsHistoryService productChannelsHistoryService;

    @Autowired
    private ErpOperateLogService erpOperateLogService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ProductChannelsSlaveService productChannelsSlaveService;

    @Autowired
    private TemuProductCategoryRelationService productCategoryRelationService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private AccountService accountService;

    @Autowired
    private ProductsService productsService;

    @Autowired
    private AmazonMarketPlaceService amazonMarketPlaceService;

    @Autowired
    private UcPostService ucPostService;

    @Autowired
    private ProductChannelsInventoryHisMapper productChannelsInventoryHisMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ProductSelectApi productSelectApi;

    @Autowired
    private InventorySynchronizerFactory inventorySynchronizerFactory;

    @Autowired
    private ProductChannelsInventoryMapper productChannelsInventoryMapper;

    @Autowired
    private ProductChannelsInventoryService productChannelsInventoryService;

    @Autowired
    private StatOrderSalesService statOrderSalesService;

    @Autowired
    private MarListingInfoService marListingInfoService;

    @Autowired
    private MarListingInfoHisMapper marListingInfoHisMapper;

    @Autowired
    private WalmartApi walmartApi;

    @Autowired
    private ProductChannelApprovalNodeService productChannelApprovalNodeService;
    @Autowired
    private TaskCenterService taskCenterService;

    @Autowired
    private RpcAsyncExportService rpcAsyncExportService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private TemuCategoryService temuCategoryService;

    @Autowired
    private FinConfExchangeRateService finConfExchangeRateService;

    @Autowired
    private ScAccessoriesCategoryService accessoriesCategoryService;

    @XxlConf(value = "bizark-erp.channels.export.batch")
    private static String PRODUCT_CHANNEL_EXPORT_BATCH;


    @Autowired
    private SysLabelConfigService labelConfigService;

    @Autowired
    private MarTemuPromotionPriceReportMapper priceReportMapper;
    @Autowired
    private SystemGlobalConfigService systemGlobalConfigService;
    @Autowired
    private SkuParentsService skuParentsService;
    @Autowired
    private SkuChildrenService skuChildrenService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public ProductChannels getOne(String sellerSku, String accountId) {
        return this.lambdaQuery()
                .eq(ProductChannels::getSellerSku, sellerSku)
                .eq(ProductChannels::getAccountId, accountId)
                .eq(ProductChannels::getDisabledName, "")
                .one();
    }

    @SneakyThrows
    @Override
    public List<ProductChannels> selectProductChannels(ProductChannels channels, Integer contextId) {
        channels.setOrgId(contextId);
        String accountId = channels.getAccountId();
        channels.settingQueryParam();


        long start = System.currentTimeMillis();
        List<ProductChannels> productChannels = productChannelsMapper.selectProductChannels(channels);
        log.info("SKU映射查询耗时 - {}", System.currentTimeMillis() - start);
        if (CollectionUtil.isEmpty(productChannels)) {
            return productChannels;
        }

        // 这只
        List<String> keys = productChannels.stream().map(channel -> RedisCons.INVENTORY_SYNC_PRE + channel.getSellerSku() + ":" + channel.getAccountId()).collect(Collectors.toList());
        List<Object> list = redisTemplate.opsForValue().multiGet(keys);

        Map<String, Object> map = new HashMap<>();

        if (CollectionUtil.isNotEmpty(list)) {
            Map<String, Object> objectMap = list.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(String::valueOf, Function.identity(), (a, b) -> a));
            map.putAll(objectMap);
        }

        Integer currentUserId = UserUtils.getCurrentUserId();

        Map<String, List<Integer>> approvalUserMap = new HashMap<>();
        Map<String, Integer> returnUserMap = new HashMap<>();

        // 处理受限数据
        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> restrictDataAssembly(productChannels), threadPoolTaskExecutor);
        // 是否可见审批按钮
        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> approvalDataAssembly(productChannels, approvalUserMap, returnUserMap), threadPoolTaskExecutor);
        // 父SKU数据查询
        CompletableFuture<Map<String, List<SkuChildren>>> f3 = CompletableFuture.supplyAsync(() -> childrenMapping(contextId, productChannels), threadPoolTaskExecutor);

        CompletableFuture.allOf(f1, f2, f3).get(10, TimeUnit.SECONDS);

        Map<String, List<SkuChildren>> childreMap = f3.get();


        // 获取字典sku_approval_privileges
        Map<String, SysDictData> dictMap = new HashMap<>();
        Map<String, List<SysDictData>> dicts = sysDictTypeService.selectDictDataByTypes(CollectionUtil.newArrayList("sku_approval_privileges", "currency_symbol"));
        if (CollectionUtil.isNotEmpty(dicts)) {
            for (List<SysDictData> dictData : dicts.values()) {
                      Map<String, SysDictData> dataMap = dictData.stream()
                    .collect(Collectors.toMap(SysDictData::getDictValue, Function.identity(), (v1, v2) -> v1));
                    dictMap.putAll(dataMap);
            }
        }

        boolean canApproved = dictMap.containsKey(String.valueOf(currentUserId));


        for (ProductChannels channel : productChannels) {

            if (StrUtil.isNotBlank(channel.getTags())) {
                channel.setTagArr(Stream.of(channel.getTags().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            }


            if (Objects.equals(channel.getIsParentSku(), 1)) {
                childrenAssembly(channel, childreMap);
            }

            if (StrUtil.isNotBlank(channel.getCurrencyCode()) && dictMap.containsKey(channel.getCurrencyCode()) && Objects.nonNull(channel.getSellerSkuPrice())) {
                channel.setCurrencyPrice(dictMap.get(channel.getCurrencyCode()).getDictLabel() + channel.getSellerSkuPrice());
            }

            if (StrUtil.isBlank(channel.getCurrencyPrice()) && Objects.nonNull(channel.getSellerSkuPrice())) {
                log.info("SKU映射币种符号为空 - ID:{} SellerSku:{} 供货价:{}", channel.getId(), channel.getSellerSku(), channel.getSellerSkuPrice());
                channel.setCurrencyPrice(channel.getSellerSkuPrice().toString());
            }


            channel.setCanApproved(canApproved);

            if (Objects.equals(channel.getApprovalStatus(), ApprovalEnum.SUBMIT.getValue()) && returnUserMap.containsKey(channel.getInstanceId())) {
                channel.setReturnable(Objects.equals(returnUserMap.get(channel.getInstanceId()), currentUserId));
            }

            if (!canApproved && StrUtil.isNotBlank(channel.getInstanceId()) && approvalUserMap.containsKey(channel.getInstanceId())) {
                List<Integer> userIds = approvalUserMap.get(channel.getInstanceId());
                channel.setCanApproved(userIds.contains(currentUserId));
            }
            if (StrUtil.isNotBlank(channel.getErpSkuQuantity())) {
                channel.setErpSkuList(Arrays.asList(channel.getErpSkuQuantity().split(",")));
            }


            if (!Objects.equals(channel.getOrgId(), 1000049)) {
                channel.setCanApproved(true);
                channel.setReturnable(true);
            }

            channel.setInventoryStatusLabel(InventoryStatus.getDesc(channel.getInventoryStatus()));
            channel.setInventoryInSync(map.containsKey(channel.getSellerSku()));

            if (StrUtil.isNotBlank(channel.getCategoryName())) {
                channel.setCategoryNameList(
                        channel.getCategoryName().split(",")
                );
            }

            // 只有组合产品需要展示
            if (!ProductRelateTypeEnum.BUNDLE.getValue().equals(channel.getType())) {
                channel.setProductRelates(null);
            }
            if ("N".equals(channel.getIsVine())) {
                channel.setIsVineDict("否");
            }
            if ("Y".equals(channel.getIsVine())) {
                channel.setIsVineDict("是");
            }
            if ("Y".equals(channel.getIsSeed())) {
                channel.setIsSeedDict("是");
            }
            if ("N".equals(channel.getIsSeed())) {
                channel.setIsSeedDict("否");
            }

        }

        return productChannels;
    }

    private static void childrenAssembly(List<ProductChannels> channels, Map<String, List<SkuChildren>> childreMap) {
        for (ProductChannels channel : channels) {
            childrenAssembly(channel, childreMap);
        }
    }

    private static void childrenAssembly(ProductChannels channel, Map<String, List<SkuChildren>> childreMap) {
        List<SkuChildren> children = Arrays.stream(channel.getErpSku().split(","))
                .map(childreMap::get)
                .filter(CollectionUtil::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(children)) {
            channel.setChildren(children);
        }
    }

    private void approvalDataAssembly(List<ProductChannels> productChannels, Map<String, List<Integer>> approvalUserMap, Map<String, Integer> returnUserMap) {
        List<String> instanceIds = productChannels.stream()
                .map(ProductChannels::getInstanceId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(instanceIds)) {
            List<ProductChannelApprovalNode> approvalNodes = productChannelApprovalNodeService.lambdaQuery()
                    .in(ProductChannelApprovalNode::getInstanceId, instanceIds)
                    .list();

            Map<String, List<Integer>> nodeMap = approvalNodes.stream()
                    .filter(c -> !Objects.equals(c.getApprovalStatus(), ApprovalEnum.APPROVED.getValue()))
                    .collect(Collectors.groupingBy(ProductChannelApprovalNode::getInstanceId,
                            Collectors.mapping(ProductChannelApprovalNode::getApprovalUserId, Collectors.toList()))
                    );
            approvalUserMap.putAll(nodeMap);


            Map<String, Integer> initUserMap = approvalNodes.stream()
                    .filter(c -> Objects.nonNull(c.getInitiatorUserId()))
                    .collect(Collectors.toMap(ProductChannelApprovalNode::getInstanceId, ProductChannelApprovalNode::getInitiatorUserId, (v1, v2) -> v1));

            returnUserMap.putAll(initUserMap);
        }
    }

    @Nullable
    private Map<String,List<SkuChildren>> childrenMapping(Integer contextId, List<ProductChannels> channels) {

        List<ProductChannels> parentChannels = channels.stream().filter(c -> Objects.equals(c.getIsParentSku(), 1)).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(parentChannels)) {
            return new HashMap<>();
        }


        List<String> parentSku = parentChannels.stream()
                .filter(c -> Objects.equals(c.getIsParentSku(), 1))
                .map(c -> c.getErpSku().split(","))
                .flatMap(Stream::of)
                .distinct().collect(Collectors.toList());

        if (CollectionUtil.isEmpty(parentSku)) {
            return null;
        }

        List<SkuChildren> skuChildren = skuChildrenService.selectEnableChildren(contextId, parentSku);

        return skuChildren.stream().collect(Collectors.groupingBy(SkuChildren::getParentSku));
    }

    public void approvalNodeQuerySetting(ProductChannels channels) {
        String approvalUserIds = channels.getApprovalUserIds();
        if (StrUtil.isNotBlank(approvalUserIds)) {

            List<String> instanceIds = new ArrayList<>();
            // 添加false条件，防止审批数据为空时查询不生效
            instanceIds.add("-1111");

            channels.setInstanceIds(instanceIds);

            List<Integer> approvalUserIdQuery = Arrays.stream(approvalUserIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());

            List<ProductChannelApprovalNode> approvalNodes = productChannelApprovalNodeService.lambdaQuery()
                    .in(ProductChannelApprovalNode::getApprovalUserId, approvalUserIdQuery)
                    .select(ProductChannelApprovalNode::getInstanceId)
                    .list();

            if (CollectionUtil.isNotEmpty(approvalNodes)) {
                instanceIds.addAll(
                        approvalNodes.stream().map(ProductChannelApprovalNode::getInstanceId).distinct().collect(Collectors.toList())
                );
            }

            // 无论有没有审批节点都设置，防止查询不生效
            channels.setInstanceIds(instanceIds);

        }
    }


    @Override
    public void skuInventorySetting(Integer contextId, List<ProductChannels> productChannels,boolean show) {
        List<String> erpSkus = productChannels.stream()
                .map(ProductChannels::getErpSku)
                .map(c -> c.split(","))
                .flatMap(Stream::of)
                .collect(Collectors.toList());


        List<ErpSkuStockVO> vos = productChannelsMapper.selectErpSkuInventory(contextId, erpSkus);
        if (CollectionUtil.isEmpty(vos)) {
            return;
        }

        Map<String, List<ErpSkuStockVO>> skuInventoryMap = vos.stream().collect(Collectors.groupingBy(ErpSkuStockVO::getSku));

        for (ProductChannels channel : productChannels) {
            if (StrUtil.isBlank(channel.getErpSku())) {
                continue;
            }
            channel.setSkuInventories(new ArrayList<>());
            Integer skuInventory = null;
            for (String sku : channel.getErpSku().split(",")) {
                if (!skuInventoryMap.containsKey(sku)) {
                    continue;
                }
                if (StrUtil.isEmpty(channel.getCountry())) {
                    continue;
                }
                List<ErpSkuStockVO> stockVOS = skuInventoryMap.get(sku);
                stockVOS = stockVOS.stream()
                        .filter(c -> Objects.equals(c.getRegionId(), channel.getCountry()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(stockVOS)) {
                    continue;
                }
                channel.getSkuInventories().addAll(stockVOS);

                Integer currentQty = stockVOS.stream()
                        .map(ErpSkuStockVO::getInventoryAvailable)
                        .reduce(Integer::sum).get();
                if (Objects.isNull(skuInventory)) {
                    skuInventory = currentQty;
                    continue;
                }
                skuInventory = currentQty > skuInventory ? skuInventory : currentQty;
            }
            channel.setSkuMinInventory(skuInventory);

            // 处理库存展示数据
            if (CollectionUtil.isNotEmpty(channel.getSkuInventories()) && show) {
                Map<String, List<ErpSkuStockVO>> warehouseCodeMap = channel.getSkuInventories().stream()
                        .collect(Collectors.groupingBy(ErpSkuStockVO::getOrgWarehouseCode));

                String erpSku = channel.getErpSku();
                channel.setOverShow(new ArrayList<>());

                for (Map.Entry<String, List<ErpSkuStockVO>> entry : warehouseCodeMap.entrySet()) {
                    SkuInventoryShowVO showVO = new SkuInventoryShowVO();

                    Map<String, Integer> qtyMap = entry.getValue().stream()
                            .collect(Collectors.toMap(ErpSkuStockVO::getSku, ErpSkuStockVO::getInventoryAvailable, (a, b) -> a));

                    for (String sku : erpSku.split(",")) {
                        showVO.appendLine(qtyMap.get(sku));
                    }
                    showVO.setWareHouse(entry.getKey());
                    channel.getOverShow().add(showVO);
                }

            }


        }
    }


    void handleProductChannelsInventory( ProductChannels channel, Map<String, List<ErpSkuStockVO>> skuInventoryMap, Map<String, List<SkuChildren>> parentMap, boolean show) {
        channel.setSkuInventories(new ArrayList<>());
        if (StrUtil.isBlank(channel.getErpSku()) || StrUtil.isEmpty(channel.getCountry())) {
            return;
        }

        boolean isParentSku = Objects.equals(channel.getIsParentSku(), 1);

        Integer minInventoryQty = null;

        for (String sku : channel.getErpSku().split(",")) {

            List<String> findInventorySkus = CollectionUtil.newArrayList(sku);

            // 如果是父SKU,需要获取父下所有子SKU库存
            List<SkuChildren> children = parentMap.get(sku);

            if (isParentSku && CollectionUtil.isEmpty(children)) {
                log.info("父SKU:{} 下不存在子SKU", sku);
                continue;
            }

            if (isParentSku) {
                findInventorySkus =  children.stream().map(SkuChildren::getSku).distinct().collect(Collectors.toList());
            }

            // 查询库存数据
            List<ErpSkuStockVO> stockVOS = findInventorySkus.stream()
                    .map(skuInventoryMap::get)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .filter(c -> Objects.equals(c.getRegionId(), channel.getCountry()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(stockVOS)) {
                log.info("SKU库存不存在 - {} - 是否是父SKU - {} - 无库存SKU:{}", sku, isParentSku, findInventorySkus);
                continue;
            }

            // 设置库存数据
            channel.getSkuInventories().addAll(stockVOS);

            Integer currentQty = stockVOS.stream()
                    .map(ErpSkuStockVO::getInventoryAvailable)
                    .reduce(Integer::sum).get();

            if (Objects.isNull(minInventoryQty)) {
                minInventoryQty = currentQty;
                continue;
            }

            minInventoryQty = Math.min(minInventoryQty, currentQty);
        }

        // 设置SKU最小库存数量
        channel.setSkuMinInventory(minInventoryQty);

        if (show) {
            // 处理库存展示数据
            handleInventoryShow(channel, parentMap);
        }
    }

    private static void handleInventoryShow(ProductChannels channel, Map<String, List<SkuChildren>> parentMap) {

        if (CollectionUtil.isEmpty(channel.getSkuInventories())) {
            return;
        }

        boolean isParentSku = Objects.equals(channel.getIsParentSku(), 1);


        Map<String, List<ErpSkuStockVO>> warehouseCodeMap = channel.getSkuInventories().stream()
                .collect(Collectors.groupingBy(ErpSkuStockVO::getOrgWarehouseCode));

        String erpSku = channel.getErpSku();
        channel.setOverShow(new ArrayList<>());

        for (Map.Entry<String, List<ErpSkuStockVO>> entry : warehouseCodeMap.entrySet()) {
            SkuInventoryShowVO showVO = new SkuInventoryShowVO();

            Map<String, Integer> qtyMap = entry.getValue().stream()
                    .collect(Collectors.toMap(ErpSkuStockVO::getSku, ErpSkuStockVO::getInventoryAvailable, (a, b) -> a));

            for (String sku : erpSku.split(",")) {

                List<String> findSkus = CollectionUtil.newArrayList(sku);

                List<SkuChildren> children = parentMap.get(sku);

                // 如果是父SKU，那么需要所有子的综合
                if (isParentSku) {
                    if (CollectionUtil.isEmpty(children)) {
                        continue;
                    }
                    findSkus = children.stream().map(SkuChildren::getSku).distinct().collect(Collectors.toList());
                }

                for (String sonSku : findSkus) {
                    Integer qty = qtyMap.get(sonSku);
                    showVO.appendSku(sonSku);
                    showVO.appendLine(qty);
                }

//                Integer line = findSkus.stream().map(qtyMap::get).filter(Objects::nonNull).reduce(Integer::sum).orElse(null);

            }
            showVO.setWareHouse(entry.getKey());
            channel.getOverShow().add(showVO);
        }
    }

    @Override
    public void skuInventorySettingWithMultipleVersionSku(Integer contextId,Map<String, List<SkuChildren>> childrenMap, List<ProductChannels> productChannels, boolean show) {


        Map<Integer, List<ProductChannels>> map = productChannels.stream().collect(Collectors.groupingBy(ProductChannels::getIsParentSku));

        for (Map.Entry<Integer, List<ProductChannels>> entry : map.entrySet()) {

            List<String> erpSkus = productChannels.stream().map(c -> c.getErpSku().split(","))
                    .flatMap(Stream::of).collect(Collectors.toList());

            // 需要查询库存的SKU数据
            List<String> skus = erpSkus;

            if (Objects.equals(entry.getKey(), 1)) {
                // 如果当前是父SKU，那么需要查询当前父SKU下所有子SKU下所有库存
                List<SkuChildren> currentChildren = erpSkus.stream().map(childrenMap::get).filter(CollectionUtil::isNotEmpty)
                        .flatMap(List::stream).collect(Collectors.toList());

                if (CollectionUtil.isEmpty(currentChildren)) {
                    continue;
                }
                skus = currentChildren.stream().map(SkuChildren::getSku).distinct().collect(Collectors.toList());
            }
            // 查询库存数据
            List<ErpSkuStockVO> vos = productChannelsMapper.selectErpSkuInventory(contextId, skus);

            if (CollectionUtil.isEmpty(vos)) {
                continue;
            }


            Map<String, List<ErpSkuStockVO>> skuInventoryMap = vos.stream().collect(Collectors.groupingBy(ErpSkuStockVO::getSku));


            for (ProductChannels channel : productChannels) {
                handleProductChannelsInventory(
                        channel,
                        skuInventoryMap,
                        childrenMap,
                        show
                );
            }


        }


        List<String> erpSkus = productChannels.stream()
                .map(ProductChannels::getErpSku)
                .map(c -> c.split(","))
                .flatMap(Stream::of)
                .collect(Collectors.toList());


        List<ErpSkuStockVO> vos = productChannelsMapper.selectErpSkuInventory(contextId, erpSkus);
        if (CollectionUtil.isEmpty(vos)) {
            return;
        }

        Map<String, List<ErpSkuStockVO>> skuInventoryMap = vos.stream().collect(Collectors.groupingBy(ErpSkuStockVO::getSku));

        for (ProductChannels channel : productChannels) {
            if (StrUtil.isBlank(channel.getErpSku())) {
                continue;
            }
            channel.setSkuInventories(new ArrayList<>());
            Integer skuInventory = null;
            for (String sku : channel.getErpSku().split(",")) {
                if (!skuInventoryMap.containsKey(sku)) {
                    continue;
                }
                if (StrUtil.isEmpty(channel.getCountry())) {
                    continue;
                }
                List<ErpSkuStockVO> stockVOS = skuInventoryMap.get(sku);
                stockVOS = stockVOS.stream()
                        .filter(c -> Objects.equals(c.getRegionId(), channel.getCountry()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(stockVOS)) {
                    continue;
                }
                channel.getSkuInventories().addAll(stockVOS);

                Integer currentQty = stockVOS.stream()
                        .map(ErpSkuStockVO::getInventoryAvailable)
                        .reduce(Integer::sum).get();
                if (Objects.isNull(skuInventory)) {
                    skuInventory = currentQty;
                    continue;
                }
                skuInventory = currentQty > skuInventory ? skuInventory : currentQty;
            }
            channel.setSkuMinInventory(skuInventory);

            // 处理库存展示数据
            if (CollectionUtil.isNotEmpty(channel.getSkuInventories()) && show) {
                Map<String, List<ErpSkuStockVO>> warehouseCodeMap = channel.getSkuInventories().stream()
                        .collect(Collectors.groupingBy(ErpSkuStockVO::getOrgWarehouseCode));

                String erpSku = channel.getErpSku();
                channel.setOverShow(new ArrayList<>());

                for (Map.Entry<String, List<ErpSkuStockVO>> entry : warehouseCodeMap.entrySet()) {
                    SkuInventoryShowVO showVO = new SkuInventoryShowVO();

                    Map<String, Integer> qtyMap = entry.getValue().stream()
                            .collect(Collectors.toMap(ErpSkuStockVO::getSku, ErpSkuStockVO::getInventoryAvailable, (a, b) -> a));

                    for (String sku : erpSku.split(",")) {
                        showVO.appendLine(qtyMap.get(sku));
                    }
                    showVO.setWareHouse(entry.getKey());
                    channel.getOverShow().add(showVO);
                }

            }


        }
    }

    private void restrictDataAssembly(List<ProductChannels> productChannels) {
        if (CollectionUtil.isEmpty(productChannels)) {
            return;
        }
        List<Integer> channelIds = productChannels.stream()
                .map(ProductChannels::getId)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(channelIds)) {
            return;
        }
        List<ProductChannelsRestrict> restricts = productChannelsRestrictService.lambdaQuery()
                .in(ProductChannelsRestrict::getChannelId, channelIds)
                .list();
        if (CollectionUtil.isEmpty(restricts)) {
            return;
        }
        Map<Integer, List<ProductChannelsRestrict>> map = restricts.stream()
                .collect(Collectors.groupingBy(ProductChannelsRestrict::getChannelId));

        for (ProductChannels channel : productChannels) {
            if (map.containsKey(channel.getId())) {
                channel.setRestricts(map.get(channel.getId()));
            }
        }

    }


    public void settingUserInfo(List<ProductChannels> productChannels) {
        List<Integer> userIds = productChannels.stream()
                .map(ProductChannels::getOperationUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(userIds)) {
            return;
        }
        List<UserDeptVO> deptVO = productChannelsMapper.selectOperateUserInfoByUserIds(userIds);

        if (CollectionUtil.isEmpty(deptVO)) {
            return;
        }

        Map<Integer, UserDeptVO> deptVOMap = deptVO.stream().collect(Collectors.toMap(UserDeptVO::getUserId, Function.identity(), (a, b) -> a));
        for (ProductChannels channel : productChannels) {
            if (Objects.nonNull(channel.getOperationUserId()) && deptVOMap.containsKey(channel.getOperationUserId())) {
                UserDeptVO vo = deptVOMap.get(channel.getOperationUserId());
                channel.setDeptName(vo.getDeptName());
                if (StrUtil.isBlank(channel.getOperationUserName())) {
                    channel.setOperationUserName(vo.getUserName());
                }
            }
        }
    }

    public void buildCategoryNameWithMultipleVersionSku(List<ProductChannels> productChannels, Map<String, List<SkuChildren>> childrenMap) {
        Integer orgId = productChannels.get(0).getOrgId();
        // 获取需要查询到的所有产品数据
        List<String> allSkus = productChannels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getErpSku()))
                .map(c -> c.getErpSku().split(","))
                .flatMap(Stream::of)
                .distinct().collect(Collectors.toList());

        if (CollectionUtil.isEmpty(allSkus)) {
            return;
        }

        if (CollectionUtil.isEmpty(childrenMap)) {
            // 把当前所有SKU作为父查询
            childrenMap = skuChildrenService.selectSkuByParentSku(orgId, allSkus);
        }



        List<String> childSkus = productChannels.stream().filter(c -> Objects.equals(c.getIsParentSku(), 1) && StrUtil.isNotBlank(c.getErpSku()))
                .map(c -> c.getErpSku().split(","))
                .flatMap(Stream::of)
                .map(childrenMap::get)
                .filter(CollectionUtil::isNotEmpty)
                .flatMap(List::stream)
                .map(SkuChildren::getSku)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(childSkus)) {
            allSkus.addAll(childSkus);
        }
        // 获取所有的产品数据
        List<Products> products = productsService.lambdaQuery()
                .eq(Products::getOrgId, orgId)
                .in(Products::getErpsku, allSkus)
                .list();

        if (CollectionUtil.isEmpty(products)) {
            return;
        }
        // 设置所有产品分类
        productsService.settingProductCategory(products);


        Map<String, Products> productMap = products.stream().collect(Collectors.toMap(Products::getErpsku, Function.identity(), (v1, v2) -> v1));

        Map<Integer, List<ProductChannels>> map = productChannels.stream().collect(Collectors.groupingBy(ProductChannels::getIsParentSku));

        Map<Integer, String> categoryNameMap = products.stream()
                .collect(Collectors.toMap(Products::getId, p -> p.getCategoryName() == null ? "" : p.getCategoryName(), (a, b) -> a));
        Map<String, String> skuCategoryMap = products.stream()
                .collect(Collectors.toMap(Products::getErpsku, p -> p.getCategoryName() == null ? "" : p.getCategoryName(), (a, b) -> a));

        for (ProductChannels channel : productChannels) {


            if (StrUtil.isBlank(channel.getErpSku())) {
                continue;
            }

            boolean isParentSku = Objects.equals(channel.getIsParentSku(), 1);

            List<String> categorySkus = CollectionUtil.newArrayList(channel.getErpSku().split(","));

            if (isParentSku) {
                categorySkus = categorySkus.stream()
                        .map(childrenMap::get).filter(CollectionUtil::isNotEmpty)
                        .flatMap(List::stream).map(SkuChildren::getSku)
                        .distinct().collect(Collectors.toList());
            }

            if (CollectionUtil.isEmpty(categorySkus)) {
                continue;
            }

            String categoryName = categorySkus.stream()
                    .map(skuCategoryMap::get)
                    .filter(StrUtil::isNotBlank).findFirst().orElse("");

            channel.setCategoryName(categoryName);

            List<ProductChannelRelate> relates = channel.getProductRelates();

            if (CollectionUtil.isEmpty(relates)) {
                continue;
            }

            for (ProductChannelRelate relate : relates) {


                List<String> skus = CollectionUtil.newArrayList(relate.getErpsku());

                if (isParentSku) {
                    List<SkuChildren> children = childrenMap.get(relate.getErpsku());
                    if (CollectionUtil.isNotEmpty(children)) {
                        skus = children.stream().map(SkuChildren::getSku).distinct().collect(Collectors.toList());
                    }
                }

                if (CollectionUtil.isEmpty(skus)) {
                    continue;
                }


                Set<Long> categoryIdSet = new HashSet<>();

                for (int i = 0; i < categorySkus.size(); i++) {
                    String sku = categorySkus.get(i);
                    String relateCategoryName = skuCategoryMap.get(sku);
                    Products p = productMap.get(sku);
                    if (StrUtil.isBlank(relateCategoryName) || Objects.isNull(p)) {
                        continue;
                    }

                    relate.setCategoryName(relateCategoryName);
                    if (Objects.isNull(relate.getCategoryType())) {
                        relate.setCategoryType(p.getProductType());
                    }

                    if (Objects.nonNull(p.getCategorySecondId())) {
                        categoryIdSet.add(p.getCategorySecondId());
                    }
                }

                if (CollectionUtil.isNotEmpty(categoryIdSet)) {
                    relate.setCategoryId(CollectionUtil.join(categoryIdSet, ","));
                }
            }
        }
    }

    @Override
    public void settingLineIdAndProductId(List<ProductChannels> channels) {

        ProductChannels productChannels = CollectionUtil.getFirst(channels);


        // 获取产品信息
        Map<Integer, Products> pMap = new HashMap<>();
        List<Integer> productsIds = channels.stream()
                .filter(c -> CollectionUtil.isNotEmpty(c.getProductRelates()))
                .map(ProductChannels::getProductRelates)
                .flatMap(List::stream)
                .map(ProductChannelRelate::getProductId)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(productsIds)) {
            List<Products> products = productsService.lambdaQuery()
                    .select(Products::getId, Products::getLineId)
                    .list();
            pMap = products.stream()
                    .collect(Collectors.toMap(Products::getId, Function.identity()));
        }

        List<String> singles = new ArrayList<>();
        for (ProductChannels channel : channels) {
            channel.settingDBDefaultValue();
            if (ProductRelateTypeEnum.SINGLE.getValue().equals(channel.getType()) && StrUtil.isNotBlank(channel.getErpSku())) {
                singles.add(channel.getErpSku());
            }
            List<ProductChannelRelate> relates = channel.getProductRelates();
            if (CollectionUtil.isNotEmpty(channel.getProductRelates())) {
                for (ProductChannelRelate relate : relates) {
                    channel.setProductId(relate.getProductId());
                    if (pMap.containsKey(relate.getProductId())) {
                        channel.setLineId(pMap.get(relate.getProductId()).getLineId());
                        break;
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(singles)) {
            List<Products> products = productsService.lambdaQuery()
                    .in(Products::getErpsku, singles)
                    .eq(Products::getOrgId, productChannels.getOrgId())
                    .select(Products::getId, Products::getErpsku, Products::getLineId)
                    .list();
            if (CollectionUtil.isNotEmpty(products)) {
                Map<String, Products> productsMap = products.stream()
                        .collect(Collectors.toMap(Products::getErpsku, Function.identity(), (a, b) -> a));
                for (ProductChannels channel : channels) {
                    if (productsMap.containsKey(channel.getErpSku())) {
                        channel.setProductId(productsMap.get(channel.getErpSku()).getId());
                        channel.setLineId(productsMap.get(channel.getErpSku()).getLineId());
                    }
                }
            }
        }
    }

    public void checkSaveData(Integer orgId, List<ProductChannels> channels, Map<String, Integer> configMap) {
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("sku映射数据不能为空！");
        }

        Integer status = configMap.get(SysLabelConfigEnum.OPERATE_REQUIRED.getLabelConfig());

        for (ProductChannels channel : channels) {
            if (StrUtil.isBlank(channel.getAccountId())) {
                throw new CommonException("店铺不能为空！");
            }
//            if (StrUtil.isBlank(channel.getSaleChannel())) {
//                throw new CommonException("销售渠道不能为空！");
//            }
            if (StrUtil.isBlank(channel.getSellerSku())) {
                throw new CommonException("SellerSku不能为空！");
            }
            if (StrUtil.isBlank(channel.getSellStatus())) {
                throw new CommonException("销售状态不能为空！");
            }
            if (StrUtil.isBlank(channel.getType())) {
                throw new CommonException("产品类型不能为空！");
            }
            if (Objects.equals(status, 1) && Objects.equals(orgId, 1000049) && Objects.isNull(channel.getOperationUserId())) {
                throw new CommonException("运营不能为空！");
            }
            if (CollectionUtil.isEmpty(channel.getProductRelates())) {
                throw new CommonException("erpSku配对信息不能为空");
            }
        }
    }

    @Override
    public void fixChannelsQuantity(Integer contextId) {
        List<ProductChannels> channels = this.lambdaQuery()
                .select(ProductChannels::getId, ProductChannels::getQuantity, ProductChannels::getType)
                .eq(ProductChannels::getOrgId, contextId)
                .list();
        if (CollectionUtil.isEmpty(channels)) {
            log.error("未获取到SKU映射数据！");
            return;
        }
        List<Integer> channelsIds = channels.stream()
                .map(ProductChannels::getId)
                .collect(Collectors.toList());

        List<ProductChannelRelate> relates = productChannelRelateService.lambdaQuery()
                .in(ProductChannelRelate::getProductChannelId, channelsIds)
                .list();
        if (CollectionUtil.isNotEmpty(relates)) {
            Map<Integer, List<ProductChannelRelate>> rMap = relates.stream()
                    .collect(Collectors.groupingBy(ProductChannelRelate::getProductChannelId));
            for (ProductChannels channel : channels) {
                if (rMap.containsKey(channel.getId())) {
                    channel.setProductRelates(rMap.get(channel.getId()));
                }
            }
        }

        for (ProductChannels channel : channels) {
            if (ProductRelateTypeEnum.SINGLE.getValue().equals(channel.getType())) {
                channel.setQuantity(BigDecimal.ONE);
            }
            if (ProductRelateTypeEnum.BUNDLE.getValue().equals(channel.getType()) && CollectionUtil.isNotEmpty(channel.getProductRelates())) {
                List<ProductChannelRelate> channelRelates = channel.getProductRelates();
                BigDecimal quantity = channelRelates.stream()
                        .map(t -> new BigDecimal(t.getQty() == null ? 1L : t.getQty()))
                        .reduce(BigDecimal::add)
                        .orElse(new BigDecimal(channelRelates.size()));
                channel.setQuantity(quantity);
            }
        }
        this.updateBatchById(channels);
    }

    @Override
    public List<String[]> buildString(Integer contextId) {

        List<String[]> downData = new ArrayList<>();
        List<Account> accounts = accountService.lambdaQuery()
                .select(Account::getTitle, Account::getId)
                .eq(Account::getOrgId, contextId)
                .list();
        Map<String, List<SysDictData>> map = SpringUtils.getBean(ISysDictTypeService.class)
                .selectDictDataByTypes(Arrays.asList("approval_status", "account_sale_channel", "sku_sale_status", "sku_mapping_type"));
        String[] account = accounts.stream().map(Account::getTitle).collect(Collectors.toList()).toArray(new String[accounts.size()]);
        downData.add(account);

        if (map.containsKey("sku_sale_status")) {
            List<SysDictData> sysDictData = map.get("sku_sale_status");
            String[] sellStatusLabels = sysDictData.stream().map(SysDictData::getDictLabel).collect(Collectors.toList()).toArray(new String[sysDictData.size()]);
            downData.add(sellStatusLabels);
        }
//        if (map.containsKey("approval_status")) {
//            List<SysDictData> sysDictData = map.get("approval_status");
//            String[] approvalLabels = sysDictData.stream().map(SysDictData::getDictLabel).collect(Collectors.toList()).toArray(new String[sysDictData.size()]);
//            downData.add(approvalLabels);
//        }
        downData.add(new String[]{"待处理", "待审批"});

//        List<String> approvalStatus = Arrays.asList("待处理", "退回");
//        if (map.containsKey("account_sale_channel")) {
//            List<SysDictData> sysDictData = map.get("account_sale_channel");
//            String[] saleChannelLabels = sysDictData.stream().map(SysDictData::getDictLabel).collect(Collectors.toList()).toArray(new String[sysDictData.size()]);
//            downData.add(saleChannelLabels);
//        }
        if (map.containsKey("sku_mapping_type")) {
            List<SysDictData> sysDictData = map.get("sku_mapping_type");
            String[] typeLables = sysDictData.stream().map(SysDictData::getDictLabel).collect(Collectors.toList()).toArray(new String[sysDictData.size()]);
            downData.add(typeLables);
        }

        downData.add(new String[]{"是", "否"});
        downData.add(new String[]{"是", "否"});

        return downData;


    }

    @Override
    @Transactional
    public void approvalWithdraw(Integer contextId, Integer[] ids) {
        List<ProductChannels> channels = this.listByIds(Arrays.asList(ids));
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到需要撤回审批的映射信息！");
        }
        List<String> errors = new ArrayList<>();
        for (ProductChannels channel : channels) {
            if (!ApprovalEnum.SUBMIT.getValue().equals(channel.getApprovalStatus())) {
                errors.add(channel.getSellerSku());
            }
        }
        if (CollectionUtil.isNotEmpty(errors)) {
            throw new CommonException("只有已提交的审批才可以撤回，请核对SellerSku为" + errors + "的数据项！");
        }

        List<ProductChannels> copyBeans = BeanCopyUtils.copyBeanList(channels, ProductChannels.class);

        this.lambdaUpdate()
                .in(ProductChannels::getId, ids)
                .set(ProductChannels::getApprovalStatus, ApprovalEnum.RETURN.getValue())
                .update();

        channels.forEach(c -> c.setApprovalStatus(ApprovalEnum.RETURN.getValue()));
        try {
            erpOperateLogService.logRecord(copyBeans, channels, "id","SKU映射", true, false, "approvalStatus");
        } catch (Exception e) {
            log.error("SKU映射撤回日志记录失败：{}", e.getMessage());
        }

        List<String> instanceIds = channels.stream()
                .map(ProductChannels::getInstanceId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(instanceIds)) {
            productChannelApprovalNodeService.lambdaUpdate()
                    .in(ProductChannelApprovalNode::getInstanceId, instanceIds)
                    .remove();
        }


        // 保存审批日志
        try {
            SpringUtils.getBean(ProductChannelApprovalService.class).recordApprovalLog(channels);
        } catch (Exception e) {

        }
    }


    @Override
    public AccountsSellerResponse queryAmazonBySellerSku(String flag, String sellerSku, Integer contextId) {
        AmazonApi amazonApi = SpringUtils.getBean(AmazonApi.class);
        Account account = accountService.lambdaQuery()
                .eq(Account::getOrgId, contextId)
                .eq(Account::getFlag, flag)
                .one();
        if (Objects.isNull(account)) {
            throw new CommonException("未获取到店铺信息！");
        }
        if (!Arrays.asList("Amazon_VC", "Amazon_SC").contains(account.getAccountType())) {
            return new AccountsSellerResponse();
        }
        ItemSearchResults searchResults = null;
        try {
            searchResults = amazonApi.searchCatalogItems(sellerSku, account);
        } catch (Exception e) {
            throw new RuntimeException("查询SellerSku失败：" + e.getMessage());
        }
        log.info("SellerSku查询:{} , 响应：{}", sellerSku, JSON.toJSONString(searchResults));
        if (Objects.isNull(searchResults)) {
            return null;
        }
        return convertResponse(contextId, searchResults, sellerSku);
    }

    @Override
    public void multiChannelProductChannels(ProductChannelTypeEnum typeEnum, AmazonProductChannelVO channelVO) {
        if (Objects.isNull(typeEnum)) {
            log.error("multiChannel 未获取到对应渠道，不做处理");
            throw new CommonException("multiChannel 未获取到对应渠道，不做处理");
        }
        if (Objects.isNull(channelVO)) {
            log.error("multiChannel 多渠道信息解析未获取到数据");
            throw new CommonException("multiChannel 多渠道信息解析未获取到数据");
        }
        // 获取店铺信息
        List<Account> accounts = accountService.lambdaQuery()
                .eq(Account::getFlag, channelVO.getAccountId())
                .list();
        if (CollectionUtil.isEmpty(accounts)) {
            log.error("multiChannel 未获取到店铺信息");
            throw new CommonException("未获取到店铺信息");
        }
        Account account = accounts.get(0);
        // 获取数据库对应数据
        ProductChannels dbChannel = this.lambdaQuery()
                .eq(ProductChannels::getSellerSku, channelVO.getSellerSku())
                .eq(ProductChannels::getOrgId, account.getOrgId())
                .eq(ProductChannels::getDisabledName, "")
                .eq(ProductChannels::getAccountId, channelVO.getAccountId())
                .one();
//        log.info("multiChannel 是否获取到DB数据：{}", Objects.nonNull(dbChannel));
        // 拷贝原始对象，日志记录使用
        ProductChannels channels = channelVO.convert();
        // 查询汇率
        settingCurrencyExchangeRate(account, channels);

        if (StrUtil.isBlank(channels.getSellerSku())) {
            log.error(" multiChannel SellerSku为空不做处理");
            throw new CommonException(" multiChannel SellerSku为空不做处理");
        }
        channels.settingDBDefaultValue();
        channels.setOrgId(account.getOrgId());
        // 手动记录修改日志
        channels.setSkipInterceptor(true);
        ProductChannels copyBean = null;
        if (Objects.nonNull(dbChannel)) {
            copyBean = BeanCopyUtils.copyBean(dbChannel, ProductChannels.class);

            dbChannel.setCurrencyExchangeRate(channels.getCurrencyExchangeRate());


            if (Objects.nonNull(channels.getSellerSkuPrice())) {
                if (StrUtil.isBlank(channels.getCurrencyCode())) {
                    channels.setCurrencyCode(account.getCurrency());
                }
                dbChannel.setCurrencyCode(channels.getCurrencyCode());
            }

            // 订单消息处理
            if (Objects.equals(channelVO.getMessageType(), 2)) {
                handleChannelOrderMessage(dbChannel);
            }
        }
        boolean needUpdate = false;
        switch (typeEnum) {
            case AMAZON:
                handleAmazonMessage(channelVO, dbChannel, channels,  copyBean);
                break;
            case AMAZON_VC:
                hanldeVcMessage(dbChannel, channels, account,copyBean);
                break;
            case WAYFAIR:
                handleWayfairMessage(dbChannel, channels,  copyBean);
                break;
            case WALMART_DSV:
                handleWalmartDsvMessage(dbChannel, channels, copyBean);
                break;
            case WALMART:
                // 新增状态为在售/待处理SKU映射信息
                handleWalmartMessage(dbChannel, channels, copyBean);
                break;
            case OVER_STOCK:
                handleOverStockMessage(account,channelVO, dbChannel, channels, copyBean);
                break;
            case EBAY:
                handleEbayMessage(copyBean,dbChannel, channels);
                break;
            case SHOPIFY:
                handleShopifyMessage(dbChannel, channels, copyBean);
                break;
            case TIKTOK:
                handleTiktokMessage(dbChannel, channels, copyBean);

                break;
            case MICROSOFT:
                handleMicrosoftMessage(dbChannel, channels, copyBean);
                break;
            case SHEIN:
                handleSheinMessage(dbChannel, channels, copyBean);
                break;
            case SHEIN_SEMI:
                handleSheinSemiMessage(dbChannel, channels, copyBean);
                break;
            case TEMU:
                handleTemuMessage(channelVO, dbChannel, channels, account, copyBean);
                break;
            case TARGET:
                // 不存在添加待处理/在售
                commonHandle(dbChannel, channels, copyBean);
                break;
            case RAKUTEN:
                commonHandle(dbChannel, channels, copyBean);
                break;
            default:
                break;
        }

        if (Objects.isNull(dbChannel)) {
            dbChannel = channels;
        }

        // 从表数据
        if (Objects.nonNull(dbChannel) && Objects.nonNull(dbChannel.getId())) {
            handleSlave(channelVO, dbChannel);
        }

        // 如果是停售  更新为在售
        if (Objects.nonNull(dbChannel) && Integer.valueOf(2).equals(channelVO.getMessageType())) {
            if (SaleStateEnum.OFF_SHELVE.getValue().equals(dbChannel.getSellStatus())) {
                dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
                dbChannel.setSkipInterceptor(true);
                this.updateById(dbChannel);
                try {
                    erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "sellStatus");
                } catch (Exception e) {}
            }
        }
    }

    private void settingCurrencyExchangeRate(Account account, ProductChannels channels) {

        String originalCurrency = StrUtil.isBlank(channels.getCurrencyCode()) ? account.getCurrency() : channels.getCurrencyCode();

        if (Objects.equals(originalCurrency, "USD")) {
            channels.setCurrencyExchangeRate(BigDecimal.ONE);
            return;
        }

        Date queryDate = new Date();
        BigDecimal currencyExchangeRate = finConfExchangeRateService.queryExchangeRate(account.getOrgId(), originalCurrency, "USD", queryDate);

        if (Objects.isNull(currencyExchangeRate)) {
//            currencyExchangeRate = BigDecimal.ONE;
            log.info("SKU币种汇率设置失败 - SKU映射:{}-{} 源币种:{} 目标币种:{} 日期:{}", channels.getId(), channels.getSellerSku(), originalCurrency, "USD", DateUtil.format(queryDate, DatePattern.NORM_DATE_PATTERN));
            return;
        }
        channels.setCurrencyExchangeRate(currencyExchangeRate);
    }

    @NotNull
    private void handleTemuMessage(AmazonProductChannelVO channelVO, ProductChannels dbChannel, ProductChannels channels, Account account, ProductChannels copyBean) {
        // 不存在添加待处理/在售
        if (Objects.isNull(dbChannel)) {

            // 查询是否存在相同SellerSku的数据
            Integer count = this.lambdaQuery()
                    .eq(ProductChannels::getSellerSku, channels.getSellerSku())
                    .eq(ProductChannels::getSaleChannel, SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian())
                    .ne(ProductChannels::getSellStatus, SellStatusEnum.ARCHIVE.getValue())
                    .eq(ProductChannels::getOrgId, channels.getOrgId())
                    .eq(ProductChannels::getDisabledAt, 0)
                    .eq(ProductChannels::getDisabledName, "")
                    .count();

            if (count > 0) {
                log.info("temu多渠道数据处理 - 当前sellerSku已存在:{} - {}", channels.getAccountId(), channels.getSellerSku());
                return;
            }
            //设置招商
            setInvestment(channels);
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setIsInterfaceData(true);
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            if (StrUtil.isBlank(channels.getSellStatus())) {
                channels.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
            }
            log.info("multiChannel TEMU 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            dbChannel = channels;
            recordInsertLog(channels);
        }else{
            if (Objects.equals(channelVO.getMessageType(), 2)) {
                log.info("数据来源：{} 取消价格更新", channelVO.getMessageType());
                return;
            }

            if (Objects.equals(account.getSaleChannel(), TemuShopTypeEnum.MAINLAND.getValue())) {
                if (StrUtil.isNotBlank(channels.getItemId())) {
                    dbChannel.setAsin1(channels.getItemId());
                }
            }

            if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
                dbChannel.setCurrencyCode(channels.getCurrencyCode());
            }

            if (Objects.nonNull(channels.getCatId())) {
                dbChannel.setCatId(channels.getCatId());
            }
            if (StrUtil.isNotBlank(channels.getCatName())) {
                dbChannel.setCatName(channels.getCatName());
            }
            if (StrUtil.isNotBlank(channels.getItemId())) {
                dbChannel.setItemId(channels.getItemId());
                dbChannel.setAsin(channels.getItemId());
            }
            if (StrUtil.isNotBlank(channels.getSkuId())) {
                dbChannel.setAsin(channels.getSkuId());
            }
            if (StrUtil.isNotBlank(channels.getListingName())) {
                dbChannel.setListingName(channels.getListingName());
            }
            if (StrUtil.isNotBlank(channels.getSkcId())) {
                dbChannel.setSkcId(channels.getSkcId());
                dbChannel.setAsin1(channels.getSkcId());
            }
            if (StrUtil.isBlank(dbChannel.getTitle()) && StrUtil.isNotBlank(channels.getTitle())) {
                dbChannel.setTitle(channels.getTitle());
            }
            if (StrUtil.isNotBlank(channels.getAsin1())) {
                dbChannel.setAsin1(channels.getAsin1());
            }
            if (StrUtil.isNotBlank(channels.getImageUrl())) {
                dbChannel.setImageUrl(channels.getImageUrl());
            }
            if (StrUtil.isNotBlank(channels.getSpuId())) {
                dbChannel.setSpuId(channels.getSpuId());
            }

            // 原始价格
            BigDecimal sourcePrice = dbChannel.getSellerSkuPrice() == null ? BigDecimal.ZERO : dbChannel.getSellerSkuPrice();

            if (Objects.nonNull(channels.getPrice())) {
                dbChannel.setSellerSkuPrice(channels.getPrice());
            }
            if (StrUtil.isNotBlank(channels.getSellStatus())) {
                dbChannel.setSellStatus(channels.getSellStatus());
            }
            // 比较数据
            if (channels.getPrice() != null && sourcePrice.compareTo(channels.getPrice()) != 0) {
                // 价格有变化,发布事件
                ProductChannelApprovalEvent event = new ProductChannelApprovalEvent(this, dbChannel);
                applicationEventPublisher.publishEvent(event);
            }
            //设置招商
            setInvestment(dbChannel);

            this.updateById(dbChannel);
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "spuId", "currencyExchangeRate", "sellerSkuPrice", "catName", "catId", "listingName", "sellStatus", "itemId", "skuId", "imageUrl", "skcId", "asin", "asin1", "title");
        }
        // 处理库存信息
        dbChannel.setInventory(channels.getInventory() == null ? 0 : channels.getInventory());
        // 更新库存
        productChannelsInventoryService.handleTemuInventory(dbChannel);

//                InventorySyncBean inventorySyncBean = new InventorySyncBean(account, Collections.singletonList(dbChannel));
//                SpringUtils.getBean(TemuInventorySynchronizer.class).sync(inventorySyncBean);

        try {
            productCategoryRelationService.handleTemuCategory(channels);
        } catch (Exception e) {
            log.error("Temu商品分类 - 处理失败 ：{}", JSON.toJSONString(channels), e);
        }
    }

    private void handleSheinSemiMessage(ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {
        boolean needUpdate = false;
        // 不存在添加待处理/在售
        if (Objects.isNull(dbChannel)) {
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            channels.setIsInterfaceData(true);
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            log.info("SHEIN_SEMI 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            return;
        }
        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }
        if (Objects.nonNull(channels.getSellerSkuPrice())) {
            dbChannel.setSellerSkuPrice(channels.getSellerSkuPrice());
            needUpdate = true;
        }

        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }

        if (needUpdate) {
            this.updateById(dbChannel);
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "title","sellerSkuPrice","currencyExchangeRate");
        }
    }

    private void handleSheinMessage(ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {
        boolean needUpdate = false;
        log.info("multiChannel ------------- SHEIN -------------");
        // 不存在添加待处理/在售
        if (Objects.isNull(dbChannel)) {
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            channels.setIsInterfaceData(true);
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            log.info("multiChannel SHEIN 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            log.info("multiChannel ------------- SHEIN OVER-------------");
            return;
        }
        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }
        if (Objects.nonNull(channels.getSellerSkuPrice())) {
            dbChannel.setSellerSkuPrice(channels.getSellerSkuPrice());
            needUpdate = true;
        }

        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }

        if (needUpdate) {
            this.updateById(dbChannel);
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "title","sellerSkuPrice","currencyExchangeRate");
        }
    }

    private void handleMicrosoftMessage(ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {
        boolean needUpdate = false;
        log.info("multiChannel ------------- MICROSOFT -------------");
        // 不存在添加待处理/在售
        if (Objects.isNull(dbChannel)) {
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            channels.setIsInterfaceData(true);
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            log.info("multiChannel MICROSOFT 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            log.info("multiChannel ------------- MICROSOFT OVER-------------");
            return;
        }

        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }

        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }
        if (Objects.nonNull(channels.getSellerSkuPrice())) {
            dbChannel.setSellerSkuPrice(channels.getSellerSkuPrice());
            needUpdate = true;
        }

        if (needUpdate) {
            this.updateById(dbChannel);
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "title","sellerSkuPrice","currencyExchangeRate");
        }

//                dbChannel.setTitle(channels.getTitle());
//                dbChannel.setAsin(channels.getAsin());
//                dbChannel.setUpc(channels.getUpc());
//                dbChannel.setIsInterfaceData(true);
//                dbChannel.setIsbn(channels.getIsbn());
//                dbChannel.setGtin(channels.getGtin());
//                dbChannel.setPrice(channels.getPrice());
//                dbChannel.setImageUrl(channels.getImageUrl());
//                // 如果是 Active，更新为在售
//                if (!"ENABLE".equalsIgnoreCase(channels.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
//                }
//                if ("ENABLE".equalsIgnoreCase(channels.getSellStatus()) && SaleStateEnum.OFF_SHELVE.getValue().equalsIgnoreCase(dbChannel.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
//                this.updateById(dbChannel);
//                try {erpOperateLogService.logRecord(copyBean, channels, "SKU映射", true, "sellStatus");} catch (Exception e) {}
    }

    private void handleTiktokMessage(ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {
        boolean needUpdate = false;
        log.info(" multiChannel ------------- TIKTOK -------------");
        // 不存在添加待处理/在售
        if (Objects.isNull(dbChannel)) {
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            channels.setIsInterfaceData(true);
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            log.info("multiChannel TIKTOK 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            log.info("multiChannel ------------- TIKTOK OVER-------------");
            return;
        }

        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }

        if (StrUtil.isBlank(dbChannel.getImageUrl()) && StrUtil.isNotBlank(channels.getImageUrl())) {

            needUpdate = true;
            dbChannel.setImageUrl(channels.getImageUrl());
        }

        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }

        if (StrUtil.isNotBlank(channels.getItemId())) {
            dbChannel.setItemId(channels.getItemId());
            needUpdate = true;
            dbChannel.setAsin1(channels.getItemId());
        }

        if (StrUtil.isNotBlank(channels.getSkuId())) {
            needUpdate = true;
            dbChannel.setSkuId(channels.getSkuId());
        }

        if (Objects.nonNull(channels.getPrice())) {
            needUpdate = true;
            dbChannel.setPrice(channels.getPrice());

        }
        if (Objects.nonNull(channels.getSellerSkuPrice())) {
            needUpdate = true;
            dbChannel.setSellerSkuPrice(channels.getSellerSkuPrice());
        }
        if (Objects.nonNull(channels.getAsin())) {
            needUpdate = true;
            dbChannel.setAsin(channels.getAsin());
        }

        if (needUpdate) {
            this.updateById(dbChannel);
            try {
                erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "title","imageUrl", "sellerSkuPrice", "price", "skuId", "itemId", "asin1","currencyExchangeRate");} catch (Exception e) {}
        }

//                updateItemAndSkuId(dbChannel, channels, copyBean, TIKTOK);

//                dbChannel.setTitle(channels.getTitle());
//                dbChannel.setAsin(channels.getAsin());
//                dbChannel.setUpc(channels.getUpc());
//                dbChannel.setIsInterfaceData(true);
//                dbChannel.setIsbn(channels.getIsbn());
//                dbChannel.setGtin(channels.getGtin());
//                dbChannel.setPrice(channels.getPrice());
//                dbChannel.setImageUrl(channels.getImageUrl());
//                // 如果是 Active，更新为在售
//                if (!"live".equalsIgnoreCase(channels.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
//                }
//                if ("live".equalsIgnoreCase(channels.getSellStatus()) && SaleStateEnum.OFF_SHELVE.getValue().equalsIgnoreCase(dbChannel.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
    }

    private void handleShopifyMessage(ProductChannels dbChannel, ProductChannels channels,  ProductChannels copyBean) {
        boolean needUpdate = false;
        log.info("multiChannel ------------- SHOPIFY -------------");
        // 不存在添加通过/在售
        if (Objects.isNull(dbChannel)) {
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            channels.setIsInterfaceData(true);
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            log.info("multiChannel SHOPIFY 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            log.info("multiChannel ------------- SHOPIFY OVER-------------");
            return;
        }

        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }

        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }
        if (Objects.nonNull(channels.getSellerSkuPrice())) {
            needUpdate = true;
            dbChannel.setSellerSkuPrice(channels.getSellerSkuPrice());
        }
        if (needUpdate) {
            this.updateById(dbChannel);
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "title", "sellerSkuPrice","currencyExchangeRate");
        }

//                dbChannel.setTitle(channels.getTitle());
//                dbChannel.setAsin(channels.getAsin());
//                dbChannel.setPrice(channels.getPrice());
//                dbChannel.setIsInterfaceData(true);
//                dbChannel.setImageUrl(channels.getImageUrl());
//                // 如果是 Active，更新为在售
//                if (!"Active".equalsIgnoreCase(channels.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
//                }
//                if ("Active".equalsIgnoreCase(channels.getSellStatus()) && SaleStateEnum.OFF_SHELVE.getValue().equalsIgnoreCase(dbChannel.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
//                this.updateById(dbChannel);
//                try {erpOperateLogService.logRecord(copyBean, channels, "SKU映射", true, "sellStatus");} catch (Exception e) {}
    }

    private void handleEbayMessage(ProductChannels copyBean, ProductChannels dbChannel, ProductChannels channels) {
        boolean needUpdate = false;
        log.info("multiChannel ------------- EBAY -------------");
        if (Objects.isNull(dbChannel)) {
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setIsInterfaceData(true);
            log.info("multiChannel EBAY 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            log.info("multiChannel ------------- EBAY OVER-------------");
            return;
        }
        LambdaUpdateChainWrapper<ProductChannels> chainWrapper = this.lambdaUpdate();
        chainWrapper.eq(ProductChannels::getId, dbChannel.getId());
        if (StrUtil.isNotBlank(channels.getItemId())) {
            chainWrapper.set(ProductChannels::getItemId, channels.getItemId());
            chainWrapper.set(ProductChannels::getAsin1, channels.getItemId());
            needUpdate = true;
        }
        if (StrUtil.isNotBlank(channels.getImageUrl()) && !Objects.equals(dbChannel.getImageUrl(), channels.getImageUrl())) {
            chainWrapper.set(ProductChannels::getImageUrl, channels.getImageUrl());
            needUpdate = true;
        }

        if (StrUtil.isNotBlank(channels.getTitle())) {
            chainWrapper.set(ProductChannels::getTitle, channels.getTitle());
            needUpdate = true;
        }
        if (Objects.nonNull(channels.getSellerSkuPrice())) {
            chainWrapper.set(ProductChannels::getSellerSkuPrice, channels.getSellerSkuPrice());
            needUpdate = true;
        }

        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
            chainWrapper.set(ProductChannels::getCurrencyCode, channels.getCurrencyCode());
        }
        if (Objects.nonNull(channels.getCurrencyExchangeRate())) {
            needUpdate = true;
            dbChannel.setCurrencyExchangeRate(channels.getCurrencyExchangeRate());
            chainWrapper.set(ProductChannels::getCurrencyExchangeRate, channels.getCurrencyExchangeRate());
        }


        if (needUpdate) {
            chainWrapper.update();
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "currencyCode","title", "sellerSkuPrice", "currencyExchangeRate");

        }


//                dbChannel.setUpc(channels.getUpc());
//                dbChannel.setIsbn(channels.getIsbn());
//                dbChannel.setAsin(channels.getAsin());
//                dbChannel.setIsInterfaceData(true);
//                dbChannel.setImageUrl(channels.getImageUrl());
        // 如果是 Active，更新为在售
//                if (!"Active".equalsIgnoreCase(channels.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
//                }
//                if ("Active".equalsIgnoreCase(channels.getSellStatus()) && SaleStateEnum.OFF_SHELVE.getValue().equalsIgnoreCase(dbChannel.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
//                this.updateById(dbChannel);
//                try {erpOperateLogService.logRecord(copyBean, channels, "SKU映射", true, "sellStatus");} catch (Exception e) {}
    }


    private void handleOverStockInventory(Account account,ProductChannels channels,List<AmazonProductChannelVO.WarehouseInventory> inventories) {

        if (CollectionUtil.isEmpty(inventories)) {
            return;
        }

        AbstractInventorySynchronizer synchronizer = inventorySynchronizerFactory.getSynchronizer(InventoryType.OVERSTOCK);
        InventorySyncBean commonBean = new InventorySyncBean();
        commonBean.setAccount(account);
        commonBean.setChannels(Arrays.asList(channels));
        List<ProductChannelsStockMessage> stockMessages = inventories.stream()
                .map(c -> {
                    ProductChannelsStockMessage stockMessage = new ProductChannelsStockMessage();
                    stockMessage.setSku(channels.getSellerSku());
                    stockMessage.setWarehouse(c.getWarehouseName());
                    stockMessage.setWarehouseName(c.getWarehouseName());
                    stockMessage.setAvailableUnits(String.valueOf(c.getQuantity()));
                    return stockMessage;
                }).collect(Collectors.toList());
        commonBean.setStockMessage(stockMessages);
        synchronizer.syncMultichannelTask(commonBean);
    }

    private void handleOverStockMessage(Account account, AmazonProductChannelVO channelVO, ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {

        boolean needUpdate = false;
        if (Objects.isNull(dbChannel)) {
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            channels.setIsInterfaceData(true);
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            log.info("multiChannel OVER_STOCK 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            handleOverStockInventory(account, channels, channelVO.getInventories());
            recordInsertLog(channels);
            return;
        }
        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }

        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }

        if (Objects.nonNull(channels.getSellerSkuPrice())) {
            needUpdate = true;
            dbChannel.setSellerSkuPrice(channels.getSellerSkuPrice());
        }

        if (needUpdate) {
            dbChannel.setIsInterfaceData(true);
            this.updateById(dbChannel);
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "title", "sellerSkuPrice", "currencyExchangeRate");
        }

        handleOverStockInventory(account, dbChannel, channelVO.getInventories());


        // 如果当前是On-Site更新为在售，反之为停售
//                dbChannel.setUpc(channels.getUpc());
//                dbChannel.setUpc(channels.getUpc());
//                dbChannel.setIsbn(channels.getIsbn());
//                dbChannel.setGtin(channels.getGtin());
//                dbChannel.setIsInterfaceData(true);
//                dbChannel.setImageUrl(channels.getImageUrl());
//                boolean updateStatus = "On-Site".equalsIgnoreCase(channels.getSellStatus());
//                if (!updateStatus) {
//                    dbChannel.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
//                }
//                if (SaleStateEnum.OFF_SHELVE.getValue().equalsIgnoreCase(dbChannel.getStatus()) && updateStatus) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
//                this.updateById(dbChannel);
//                try {erpOperateLogService.logRecord(copyBean, channels, "SKU映射", true, "sellStatus");} catch (Exception e) {}
    }

    private void handleWalmartMessage(ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {
        boolean needUpdate = false;
        log.info("multiChannel ------------- WALMART -------------");
        if (Objects.isNull(dbChannel)) {
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setIsInterfaceData(true);
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            log.info("multiChannel WALMART 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            log.info("multiChannel -------- WALMART OVER --------");
            return;
        }

        if (StrUtil.isNotBlank(channels.getPublishedStatus())) {
            if (Objects.equals("PUBLISHED", channels.getPublishedStatus())) {
                dbChannel.setSellStatus(SellStatusEnum.HIT_SHELVE.getValue());
            }else{
                dbChannel.setSellStatus(SellStatusEnum.OFF_SHELVE.getValue());
            }
        }

        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }

        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }

        if (StrUtil.isNotBlank(channels.getBrand()) && StrUtil.isBlank(dbChannel.getBrand())) {
            needUpdate = true;
            dbChannel.setBrand(channels.getBrand());
        }
        if (StrUtil.isNotBlank(channels.getUpc())) {
            needUpdate = true;
            dbChannel.setUpc(channels.getUpc());
        }
        if (StrUtil.isNotBlank(channels.getIsbn())) {
            needUpdate = true;
            dbChannel.setIsbn(channels.getIsbn());
        }
        if (StrUtil.isNotBlank(channels.getGtin())) {
            needUpdate = true;
            dbChannel.setGtin(channels.getGtin());
        }
        if (StrUtil.isNotBlank(channels.getItemId()) && StrUtil.isBlank(dbChannel.getItemId())) {
            dbChannel.setItemId(channels.getItemId());
            needUpdate = true;
            dbChannel.setAsin(channels.getItemId());
        }
        if (StrUtil.isNotBlank(channels.getAsin1())) {
            needUpdate = true;
            dbChannel.setAsin1(channels.getAsin1());
        }
        if (Objects.nonNull(channels.getIsPrimary())) {
            needUpdate = true;
            dbChannel.setIsPrimary(channels.getIsPrimary());
        }
        if (StrUtil.isNotBlank(channels.getImageUrl()) && StrUtil.isBlank(dbChannel.getImageUrl())) {
            needUpdate = true;
            dbChannel.setImageUrl(channels.getImageUrl());
        }
        if (Objects.nonNull(channels.getListingPrice()) && BigDecimal.ZERO.compareTo(channels.getListingPrice()) != 0) {
            needUpdate = true;
            dbChannel.setListingPrice(channels.getListingPrice());
        }
        if (Objects.nonNull(channels.getPrice()) && BigDecimal.ZERO.compareTo(channels.getPrice()) != 0) {
            needUpdate = true;
            dbChannel.setSellerSkuPrice(channels.getPrice());
        }

        dbChannel.setIsInterfaceData(true);
//                boolean flag = "Active".equalsIgnoreCase(channels.getSellStatus());
//                if (!flag) {
//                    dbChannel.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
//                }
//                if (SaleStateEnum.OFF_SHELVE.getValue().equalsIgnoreCase(dbChannel.getStatus()) && flag) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
        if (needUpdate) {
            this.updateById(dbChannel);
            try {
                erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "currencyCode","title","isPrimary","asin1","sellerSkuPrice", "listingPrice", "imageUrl", "itemId", "asin", "gtin", "isbn", "upc", "brand", "currencyExchangeRate");
            } catch (Exception e) {}
        }
    }

    private void handleWalmartDsvMessage(ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {
        boolean needUpdate = false;
        log.info("multiChannel ------------- WALMART_DSV -------------");
        if (Objects.isNull(dbChannel)) {
            // 新增待处理的SKU映射信息
            // TODO 缺少产品标识符类型字段数据，确实产品标识符
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            channels.setIsInterfaceData(true);
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            log.info("multiChannel WALMART_DSV 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            log.info("multiChannel -------- WALMART_DSV OVER --------");
            return;
        }
        if (StrUtil.isNotBlank(channels.getAsin1())) {
            needUpdate = true;
            dbChannel.setAsin1(channels.getAsin1());
        }
        if (StrUtil.isNotBlank(channels.getPublishedStatus())) {
            needUpdate = true;
            if (Objects.equals("PUBLISHED", channels.getPublishedStatus())) {
                dbChannel.setSellStatus(SellStatusEnum.HIT_SHELVE.getValue());
            }else{
                dbChannel.setSellStatus(SellStatusEnum.OFF_SHELVE.getValue());
            }
        }
        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }
        if (StrUtil.isNotBlank(channels.getAsin())) {
            needUpdate = true;
            dbChannel.setAsin(channels.getAsin());
        }
        if (StrUtil.isNotBlank(channels.getItemId())) {
            needUpdate = true;
            dbChannel.setItemId(channels.getItemId());
        }
        if (StrUtil.isNotBlank(channels.getImageUrl())) {
            needUpdate = true;
            dbChannel.setImageUrl(channels.getImageUrl());
        }
        if (Objects.nonNull(channels.getPrice()) && BigDecimal.ZERO.compareTo(channels.getPrice()) != 0) {
            needUpdate = true;
            dbChannel.setSellerSkuPrice(channels.getPrice());
        }
        if (StrUtil.isNotBlank(channels.getGtin())) {
            needUpdate = true;
            dbChannel.setGtin(channels.getGtin());
        }
        if (StrUtil.isNotBlank(channels.getUpc())) {
            needUpdate = true;
            dbChannel.setUpc(channels.getUpc());
        }
        if (Objects.nonNull(channels.getIsPrimary())) {
            needUpdate = true;
            dbChannel.setIsPrimary(channels.getIsPrimary());
        }
        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }

        if (Objects.nonNull(channels.getListingPrice()) && BigDecimal.ZERO.compareTo(channels.getListingPrice()) != 0) {
            needUpdate = true;
            dbChannel.setListingPrice(channels.getListingPrice());
        }
        dbChannel.setIsInterfaceData(true);
        if (needUpdate) {
            try {
                erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "currencyCode","title","isPrimary","asin1","gtin", "upc", "listingPrice", "sellerSkuPrice", "currencyExchangeRate");
            } catch (Exception e) {}
        }
        this.updateById(dbChannel);

//                if (!"PUBLISHED".equalsIgnoreCase(channels.getPublishedStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
//                }
//                if (SaleStateEnum.OFF_SHELVE.getValue().equalsIgnoreCase(channels.getSellStatus()) && "PUBLISHED".equalsIgnoreCase(channels.getPublishedStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                dbChannel.setIsInterfaceData(true);
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
//                this.updateById(dbChannel);
//                try {erpOperateLogService.logRecord(copyBean, channels, "SKU映射", true, "sellStatus");} catch (Exception e) {}
    }

    private void handleWayfairMessage(ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {
        boolean needUpdate = false;
        log.info("multiChannel ------------- WAYFAIR -------------");
        if (Objects.isNull(dbChannel)) {
            // 新增待处理的SKU映射信息
            // TODO 缺少产品标识符类型字段数据，确实产品标识符
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            channels.setIsInterfaceData(true);
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            log.info("multiChannel WAYFAIR 新增映射数据：{}", channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            log.info("multiChannel -------- WAYFAIR OVER --------");

            return;
        }

        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }

        if (StrUtil.isBlank(dbChannel.getAsin1()) && StrUtil.isNotBlank(channels.getAsin1())) {
            // 更新asin1信息
            needUpdate = true;
            dbChannel.setAsin1(channels.getAsin1());
        }
        if (Objects.nonNull(channels.getSellerSkuPrice())) {
            needUpdate = true;
            dbChannel.setSellerSkuPrice(channels.getSellerSkuPrice());
        }

        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }


        if (needUpdate) {
            this.updateById(dbChannel);
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "title","sellerSkuPrice", "asin1", "currencyExchangeRate");
        }


//                if (!"Active".equalsIgnoreCase(channels.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
//                }
//                if (SaleStateEnum.OFF_SHELVE.getValue().equalsIgnoreCase(channels.getSellStatus()) && "Active".equalsIgnoreCase(channels.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                dbChannel.setIsInterfaceData(true);
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
//                this.updateById(dbChannel);
//                try {erpOperateLogService.logRecord(copyBean, channels, "SKU映射", true, "sellStatus");} catch (Exception e) {}
    }

    private void hanldeVcMessage(ProductChannels dbChannel, ProductChannels channels, Account account,ProductChannels copyBean) {
        log.info(" multiChannel ------------- AMAZON_VC -------------");
        if (Objects.isNull(dbChannel)) {
            // 新增待处理的SKU映射信息
            // TODO 缺少产品标识符类型字段数据，确实产品标识符
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            channels.setIsInterfaceData(true);
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            log.info("multiChannel AMAZON_VC 新增映射数据：{}", channels.getSellerSku());

            handleChannelMessage(channels);
            handleListingPrice(channels, account);
            recordInsertLog(channels);
            log.info("multiChannel -------- AMAZON_VC OVER --------");
            return;
        }
//        if (StrUtil.isNotBlank(channels.getTitle())) {
//            dbChannel.setTitle(channels.getTitle());
//            needUpdate = true;
//        }
//
//        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
//            needUpdate = true;
//            dbChannel.setCurrencyCode(channels.getCurrencyCode());
//        }

        boolean needUpdate = commonUpdateSetting(dbChannel, channels);

        // 如果不是在售，更新销售状态
//                dbChannel.setAsin1(channels.getAsin1());
//                dbChannel.setBrand(channels.getBrand());
//                dbChannel.setUpc(channels.getUpc());
//                dbChannel.setIsbn(channels.getIsbn());
//                dbChannel.setIsInterfaceData(true);
//                dbChannel.setGtin(channels.getGtin());
//                dbChannel.setImageUrl(channels.getImageUrl());
//                if (!SaleStateEnum.HIT_SHELVE.getValue().equals(dbChannel.getSellStatus())) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
        if (needUpdate) {
            handleSkuSerialVersion(dbChannel.getOrgId(), Arrays.asList(channels), null, false);
            this.updateById(dbChannel);
            try {erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false,"title", "currencyExchangeRate");} catch (Exception e) {}
        }
    }

    private void handleAmazonMessage(AmazonProductChannelVO channelVO, ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {
        boolean needUpdate = false;
        log.info(" multiChannel ------------- AMAZON -------------");
        if (Objects.isNull(dbChannel)) {
            // 新增待处理的SKU映射信息
            // TODO 缺少产品标识符类型字段数据，确实产品标识符
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            channels.setIsInterfaceData(true);
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            log.info("multiChannel 新增映射数据：{}", channels.getSellerSku());
            handleChannelMessage(channels);
            recordInsertLog(channels);
            log.info("multiChannel -------- AMAZON OVER --------");
            return;
        }
        if (Objects.equals(channelVO.getMessageType(), 2)) {
            log.info("数据来源：{} 取消价格更新", channelVO.getMessageType());
            return;
        }

        BigDecimal oldPrice = dbChannel.getSellerSkuPrice();
        BigDecimal newPrice = channels.getSellerSkuPrice();

        if (StrUtil.isNotBlank(channels.getTitle())) {
            dbChannel.setTitle(channels.getTitle());
            needUpdate = true;
        }
        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }

        if (Objects.nonNull(newPrice) && newPrice.compareTo(BigDecimal.ZERO) != 0) {
            dbChannel.setSellerSkuPrice(newPrice);
            needUpdate = true;
        }
        if (needUpdate) {
            this.updateById(dbChannel);
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "currencyCode","title", "sellerSkuPrice", "currencyExchangeRate");
        }
        // 如果不是在售，更新销售状态
//                dbChannel.setAsin1(channels.getAsin1());
//                dbChannel.setBrand(channels.getBrand());
//                dbChannel.setUpc(channels.getUpc());
//                dbChannel.setIsbn(channels.getIsbn());
//                dbChannel.setFnsku(channels.getFnsku());
//                dbChannel.setGtin(channels.getGtin());
//                dbChannel.setImageUrl(channels.getImageUrl());
//                dbChannel.setDistributionMode(channels.getDistributionMode());
//                boolean isActive = "Active".equalsIgnoreCase(channels.getSellStatus());
//                if (!isActive) {
//                    dbChannel.setSellStatus(SaleStateEnum.OFF_SHELVE.getValue());
//                }
//                if (SaleStateEnum.OFF_SHELVE.getValue().equals(dbChannel.getSellStatus()) && isActive) {
//                    dbChannel.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//                }
//                dbChannel.setIsInterfaceData(true);
//                log.error("multiChannel 修改映射数据：{}", dbChannel.getSellerSku());
//                this.updateById(dbChannel);
//                // 记录修改日志
//                try {erpOperateLogService.logRecord(copyBean, channels, "SKU映射", true, "sellStatus");} catch (Exception e) {}
    }

    private void setInvestment(ProductChannels channels) {
        if (null == channels.getCatId()) return;
        TemuCategory one = temuCategoryService.lambdaQuery().select(TemuCategory::getInvestment).eq(TemuCategory::getCatId, channels.getCatId()).isNotNull(TemuCategory::getInvestment).last("limit 1").one();
        if (null == one) return;
        channels.setInvestment(one.getInvestment());
    }

    private void commonHandle(ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean) {
        if (Objects.isNull(dbChannel)) {
            channels.setSaleChannel(channels.getSaleChannel().toLowerCase());
            channels.setIsInterfaceData(true);
            channels.setApprovalStatus(ApprovalEnum.NEW.getValue());
            channels.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
            log.info("{} 新增映射数据：{}", channels.getSaleChannel(), channels.getSellerSku());
            this.save(channels);
            recordInsertLog(channels);
            return;
        }
        boolean needUpdate = commonUpdateSetting(dbChannel, channels);
        if (needUpdate) {
            this.updateById(dbChannel);
            erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "currencyCode","currencyExchangeRate","imageUrl","brand","itemId","title","sellerSkuPrice");
        }
    }

    private static boolean commonUpdateSetting(ProductChannels dbChannel, ProductChannels channels) {

        boolean needUpdate = false;

        if (StrUtil.isNotBlank(channels.getTitle())) {
            needUpdate = true;
            dbChannel.setTitle(channels.getTitle());
        }
        if (StrUtil.isNotBlank(channels.getBrand())) {
            needUpdate = true;
            dbChannel.setBrand(channels.getBrand());
        }
        if (StrUtil.isNotBlank(channels.getItemId())) {
            needUpdate = true;
            dbChannel.setItemId(channels.getItemId());
        }
        if (StrUtil.isNotBlank(channels.getImageUrl())) {
            needUpdate = true;
            dbChannel.setImageUrl(channels.getImageUrl());
        }
        if (StrUtil.isNotBlank(channels.getCurrencyCode())) {
            needUpdate = true;
            dbChannel.setCurrencyCode(channels.getCurrencyCode());
        }
        if (Objects.nonNull(channels.getSellerSkuPrice())) {
            dbChannel.setSellerSkuPrice(channels.getSellerSkuPrice());
            needUpdate = true;
        }
        return needUpdate;
    }

    private void handleSlave(AmazonProductChannelVO channelVO, ProductChannels channels) {
        log.info("多渠道消息 - 处理SKU映射从表数据 - {}", JSON.toJSONString(channels));
        // 处理从表数据
        RLock lock = redissonClient.getLock(RedisCons.SKU_MAP_LOCK + channels.getId());

        try {
            lock.lock(10, TimeUnit.SECONDS);
            productChannelsSlaveService.handleMessage(channelVO, channels);
        }finally {
            lock.unlock();
        }

    }

    private void updateItemAndSkuId(ProductChannels dbChannel, ProductChannels channels, ProductChannels copyBean,ProductChannelTypeEnum type) {
        boolean updateFlag = false;
        LambdaUpdateChainWrapper<ProductChannels> chainWrapper = this.lambdaUpdate();
        chainWrapper.eq(ProductChannels::getId, dbChannel.getId());
        if (StrUtil.isBlank(dbChannel.getAsin1()) && StrUtil.isNotBlank(channels.getItemId())) {
            dbChannel.setAsin1(channels.getItemId());
            updateFlag = true;
            chainWrapper.set(ProductChannels::getAsin1,  channels.getItemId());
        }
        // 更新ItemId信息
        if (StrUtil.isBlank(dbChannel.getItemId()) && StrUtil.isNotBlank(channels.getItemId())) {
            updateFlag = true;
            chainWrapper.set(ProductChannels::getItemId, channels.getItemId());
            dbChannel.setItemId(channels.getItemId());
        }
        if ((type == TIKTOK || type == TEMU)  && StrUtil.isNotBlank(channels.getSkuId())) {
            updateFlag = true;
            chainWrapper.set(ProductChannels::getSkuId, channels.getSkuId());
            dbChannel.setSkuId(channels.getSkuId());

        }
        if (type == TEMU  && StrUtil.isNotBlank(channels.getSkuId())) {
            updateFlag = true;
            chainWrapper.set(ProductChannels::getAsin, channels.getSkuId());
            dbChannel.setAsin(channels.getSkuId());
        }
        if (type == TEMU && StrUtil.isNotBlank(channels.getSkcId())) {
            updateFlag = true;
            chainWrapper.set(ProductChannels::getSkcId, channels.getSkcId());
            dbChannel.setSkcId(channels.getSkcId());
        }

//                productChannelsMapper.updateItemIdById(dbChannel.getId(), channels.getItemId());
        // 记录ItemId修改日志
        erpOperateLogService.logRecord(copyBean, dbChannel, "SKU映射", true, false, "itemId", "skuId");
         if (updateFlag) {
            chainWrapper.update();
        }
    }

    private void handleChannelMessage(ProductChannels channels) {
        List<String> checkSaleChannels = Arrays.asList(
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.SC.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DF.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DI.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_USPO.getEhengjian()
        );


        settingCountry(channels.getOrgId(), Lists.newArrayList(channels));


        List<Account> accounts = accountService.selectByAccountsCountry(channels.getOrgId(), channels.getAccountId(), checkSaleChannels);

        if (CollectionUtil.isEmpty(accounts)) {

            this.handleSkuSerialVersion(channels.getOrgId(), Collections.singletonList(channels), null, false);
            this.save(channels);
            return;
        }

        Integer orgId = channels.getOrgId();
        String asin = channels.getAsin();
        if (StrUtil.isBlank(asin)) {
            this.handleSkuSerialVersion(channels.getOrgId(), Collections.singletonList(channels), null, false);
            this.save(channels);
            return;
        }

        List<String> flags = accounts.stream().map(Account::getFlag).distinct().collect(Collectors.toList());

        List<ProductChannels> dbChannels = this.lambdaQuery()
                .eq(ProductChannels::getAsin, asin)
                .in(ProductChannels::getSaleChannel, checkSaleChannels)
                .eq(ProductChannels::getApprovalStatus, ApprovalEnum.APPROVED.getValue())
                .in(ProductChannels::getAccountId, flags)
                .list();

        if (CollectionUtil.isEmpty(dbChannels)) {
            this.handleSkuSerialVersion(channels.getOrgId(), Collections.singletonList(channels), null, false);
            this.save(channels);
            return;
        }


        Map<Boolean, List<ProductChannels>> opMap = dbChannels.stream()
                .collect(Collectors.groupingBy(c -> Objects.nonNull(c.getOperationUserId())));
        if (opMap.size() > 1) {
            this.handleSkuSerialVersion(channels.getOrgId(), Collections.singletonList(channels), null, false);
            this.save(channels);
            return;
        }
        if (opMap.containsKey(Boolean.TRUE)) {
            Set<Integer> set = dbChannels.stream().map(ProductChannels::getOperationUserId)
                    .collect(Collectors.toSet());
            if (set.size() > 1) {
                this.handleSkuSerialVersion(channels.getOrgId(), Collections.singletonList(channels), null, false);
                this.save(channels);
                return;
            }
        }


        // 设置关联信息
        productChannelRelateService.assignChannelRelates(dbChannels);

        // 检查是否存在不通的SKU数据
        Map<Boolean, List<ProductChannels>> map = dbChannels.stream()
                .collect(Collectors.groupingBy(c -> CollectionUtil.isEmpty(c.getProductRelates())));
        if (map.size() > 1) {
            this.handleSkuSerialVersion(channels.getOrgId(), Collections.singletonList(channels), null, false);
            this.save(channels);
            return;
        }

        Set<String> set = dbChannels.stream()
                .map(ProductChannels::getProductRelates)
                .map(c -> c.stream().map(r -> r.getErpsku() + r.getQty()).collect(Collectors.joining(",")))
                .collect(Collectors.toSet());

        if (set.size() > 1) {
            this.handleSkuSerialVersion(channels.getOrgId(), Collections.singletonList(channels), null, false);
            this.save(channels);
            return;
        }

        try {
            ProductChannels productChannels = dbChannels.get(0);

            channels.setType(productChannels.getType());
            channels.setErpSku(productChannels.getErpSku());
            channels.setQuantity(productChannels.getQuantity());
            channels.setOperationUserId(productChannels.getOperationUserId());
            channels.setOperationUserName(productChannels.getOperationUserName());
            if (ProductRelateTypeEnum.BUNDLE.getValue().equals(channels.getType())) {
                channels.setSkuType(1);
            }

            List<ProductChannelRelate> relates = productChannels.getProductRelates();

            if (StrUtil.isNotBlank(productChannels.getErpSku())) {
                String sku = productChannels.getErpSku().split(",")[0];
                Products products = productsService.lambdaQuery()
                        .eq(Products::getErpsku, sku)
                        .eq(Products::getOrgId, productChannels.getOrgId())
                        .last("limit 1")
                        .one();

                if (Objects.nonNull(products)) {
                    channels.setProductId(products.getId());
                    channels.setLineId(products.getLineId());
                }
            }
            this.handleSkuSerialVersion(channels.getOrgId(), Collections.singletonList(channels), null, false);
            this.save(channels);
            if (ProductRelateTypeEnum.BUNDLE.getValue().equals(channels.getType())) {
                for (ProductChannelRelate relate : relates) {
                    relate.setId(null);
                    relate.setProductChannelId(channels.getId());
                }
                channels.setProductRelates(relates);
                productChannelRelateService.saveBatch(relates);
            }
        } catch (Exception e) {
            log.error("VC数据处理异常:{}", e.getMessage(), e);
        }


    }


    private void recordInsertLog(ProductChannels channels) {
        String userName = "System";
        Integer userId = 0;
        try {
            AuthUserDetails userDetails = AuthContextHolder.getAuthUserDetails();
            userName = userDetails.getName();
            userId = userDetails.getId();
        } catch (Exception e) {
        }
        ErpOperateLog operateLog = new ErpOperateLog();
        operateLog.setLogType(1);
        operateLog.setOperateType(LogEnums.OperateTypeEnum.INSERT.getValue());
        operateLog.setOperateTable("dashboard.product_channels");
        operateLog.setOperateName("SKU映射");
        operateLog.setCreatedBy(userId);
        operateLog.setUpdatedBy(userId);
        operateLog.setOperateUserId(userId.longValue());
        operateLog.setOperateUserName(userName);
        operateLog.setOperateAt(LocalDateTime.now());
        operateLog.setCreatedAt(new Date());
        operateLog.setCreatedName(userName);
        operateLog.setUpdatedName(userName);
        operateLog.setBusinessId(channels.getId().longValue());
        SpringUtils.getBean(ErpOperateLogService.class).save(operateLog);
    }

    private static AccountsSellerResponse convertResponse(Integer contextId, ItemSearchResults searchResults,String sellerSku) {
        List<Item> items = searchResults.getItems();
        if (CollectionUtil.isEmpty(items)) {
            return null;
        }
        AccountsSellerResponse sellerResponse = new AccountsSellerResponse();
        Item item = null;
        for (Item it : items) {
            if (CollectionUtil.isEmpty(it.getIdentifiers())) {
                continue;
            }
            ItemIdentifiers identifiers = it.getIdentifiers();
            if (CollectionUtil.isEmpty(identifiers)) {
                continue;
            }
            ItemIdentifiersByMarketplace marketplace = identifiers.get(0);
            if (CollectionUtil.isEmpty(marketplace.getIdentifiers())) {
                continue;
            }
            List<ItemIdentifier> itemIdentifiers = marketplace.getIdentifiers();
            long count = itemIdentifiers.stream()
                    .filter(c -> "SKU".equalsIgnoreCase(c.getIdentifierType()) && Objects.equals(c.getIdentifier(), sellerSku))
                    .count();
            if (count > 0) {
                item = it;
                break;
            }
        }
        if (Objects.isNull(item)) {
            log.error("UPDATE_ASIN 未获取到对应SellerSku相应，SKU:{}  数据：{}", sellerSku, JSON.toJSONString(searchResults));
            return null;
        }
        // 设置ASIN
        sellerResponse.setAsin(item.getAsin());
        ItemIdentifiers identifiers = item.getIdentifiers();
        // 设置产品标识
        if (CollectionUtil.isNotEmpty(identifiers) && CollectionUtil.isNotEmpty(identifiers.get(0).getIdentifiers())) {
            List<ItemIdentifier> itemIdentifierInfos = identifiers.get(0).getIdentifiers();
            sellerResponse.setIdentifiers(BeanCopyUtils.copyBeanList(itemIdentifierInfos, AccountsSellerResponse.IdentifierItem.class));
        }
        // 图片信息
        ArrayList<ItemImagesByMarketplace> images = item.getImages();
        if (CollectionUtil.isNotEmpty(images) && CollectionUtil.isNotEmpty(images.get(0).getImages())) {
            List<ItemImage> itemImages = images.get(0).getImages();
            List<ItemImage> mainImages = itemImages.stream()
                    .filter(im -> im.getVariant().getValue().equalsIgnoreCase("MAIN"))
                    .filter(im-> !im.getLink().contains("SL75"))
                    .collect(Collectors.toList());
            List<ItemImage> sortImage = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(mainImages)) {
                sortImage = mainImages.stream()
                        .sorted(Comparator.comparing(p -> p.getHeight() + p.getWidth()))
                        .collect(Collectors.toList());
            } else {
                sortImage = itemImages.stream()
                        .sorted(Comparator.comparing(p -> p.getHeight() + p.getWidth()))
                        .collect(Collectors.toList());
            }
            LocalDateTime now = LocalDateTime.now();
            // 取小图和中图
            ItemImage small = sortImage.get(0);
            ItemImage middle = sortImage.get(sortImage.size() / 2);
            List<AccountsSellerResponse.ImageItem> imageItems = Arrays.asList(small)
                    .stream()
                    .map(im -> {
                        AccountsSellerResponse.ImageItem imageItem = new AccountsSellerResponse.ImageItem();
                        imageItem.setMain(true);
                        imageItem.setAttachUrl(im.getLink());
                        imageItem.setAttachId(String.valueOf(System.currentTimeMillis()));
                        imageItem.setAttachOpAt(now);
                        imageItem.setWidth(im.getWidth());
                        imageItem.setHeight(im.getHeight());
                        imageItem.setAttachBrief("logo.png");
                        imageItem.setAttachTitle("logo.png");
                        return imageItem;
                    }).collect(Collectors.toList());
            sellerResponse.setMeta(new AccountsSellerResponse.Image(imageItems, now, contextId));
        }
        // 产品类型
        ArrayList<ItemProductTypeByMarketplace> productTypes = item.getProductTypes();
        if (CollectionUtil.isNotEmpty(productTypes)) {
            sellerResponse.setProductType(productTypes.get(0).getProductType());
        }

        // ParentAsin
        ItemRelationships relationship = item.getRelationships();
        if (CollectionUtil.isNotEmpty(relationship) && CollectionUtil.isNotEmpty(relationship.get(0).getRelationships())) {
            List<ItemRelationship> relationships = item.getRelationships().get(0).getRelationships();
            ItemRelationship ship = relationships.get(0);
            if (CollectionUtil.isNotEmpty(ship.getParentAsins())) {
                sellerResponse.setParentAsin(ship.getParentAsins().get(0));
            }
        }

        // 品牌
        ArrayList<ItemSummaryByMarketplace> summaries = item.getSummaries();
        if (CollectionUtil.isNotEmpty(summaries)) {
            sellerResponse.setBrand(summaries.get(0).getBrand());
        }
        return sellerResponse;
    }


    private void handleChannelOrderMessage(ProductChannels productChannels) {
        if (Objects.isNull(productChannels)) {
            return;
        }
        log.info("SKU映射订单消息处理 - 店铺:{}  SellerSku:{} ", productChannels.getAccountId(), productChannels.getSellerSku());
        this.lambdaUpdate()
                .eq(ProductChannels::getId, productChannels.getId())
                .set(ProductChannels::getSellStatus, SaleStateEnum.HIT_SHELVE.getValue())
                .update();
        if (Objects.equals(productChannels.getApprovalStatus(), ApprovalEnum.APPROVED.getValue())) {
            log.info("SKU映射订单消息处理 - 审批已通过 - 发送消息 店铺:{}  SellerSku:{}", productChannels.getAccountId(),productChannels.getSellerSku());
            rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_NAME_DELIVER_DIRECT_DELAYED, "product_push_by_erp", productChannels.getId(),new CorrelationData(productChannels.getId().toString()));
        }
    }

    private AmazonAsinResponse getAmazonAsinInfo(String accessToken, String asin, String placeId) {
        String url = "https://sellingpartnerapi-na.amazon.com/catalog/2022-04-01/items/${ASIN}?marketplaceIds=${PLACEID}&includedData=attributes,dimensions,identifiers,images,productTypes,salesRanks,summaries,relationships";
        // 访问AMAZON获取ASIN信息
        url = url.replace("${ASIN}", asin);
        url = url.replace("${PLACEID}", StrUtil.isBlank(placeId) ? "ATVPDKIKX0DER" : placeId);
        HttpRequest get = HttpUtil.createGet(url);
        Map<String, String> header = new HashMap<>();
        header.put("x-amz-access-token", accessToken);
        header.put("x-amzn-RateLimit-Limit", "5");
        get.addHeaders(header);
        HttpResponse response = get.execute();
        if (!response.isOk()) {
            return null;
        }
        String body = response.body();
        if (StrUtil.isEmpty(body)) {
            return null;
        }
        return JSON.parseObject(body, AmazonAsinResponse.class);
    }


    private List<String> getAllErpSku(List<ProductChannels> channels) {
        List<String> allErpSku = new ArrayList<>();
        for (ProductChannels channel : channels) {
            if (StrUtil.isEmpty(channel.getErpSku()) && CollectionUtil.isEmpty(channel.getProductRelates())) {
                continue;
            }
            if (Objects.equals(channel.getType(), ProductRelateTypeEnum.SINGLE.getValue())) {
                allErpSku.add(channel.getErpSku());
                continue;
            }
            allErpSku.addAll(
                    channel.getProductRelates().stream().map(ProductChannelRelate::getErpsku).collect(Collectors.toList())
            );
        }
        return CollectionUtil.distinct(allErpSku);
    }

    public void saveOrUpdateProductChannelsWithMultipleVersionSku(Integer contextId, List<ProductChannels> channels, UserEntity userEntity,boolean isImport) {

        ProductChannelEditResult editResult = buildEditResult(contextId, channels);
        // 设置运营数据
        completeTheOperational(channels);
        // 设置基础数据并检查数据
        completeChannelAndCheck(editResult);
        // 处理图片
        handleImgUrl(channels);
        // 检查编辑数据
        checkSaveOrUpdateData(channels, isImport);

        List<ProductChannels> insert = channels.stream().filter(item -> item.getId() == null).collect(Collectors.toList());

        // 获取修改数据
        List<ProductChannels> update = channels.stream().filter(item -> item.getId() != null).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(insert)) {
            handleInsertChannels(contextId, insert);
        }

        if (CollectionUtil.isNotEmpty(update)) {
            handleUpdateChannels(contextId, update);
        }
        // 处理子表
        productChannelsSlaveService.handleSlaves(channels);
        // 处理成本价格
        handleCostPrice(contextId, channels);
        // 处理文件上传信息
        handlePicMeta(channels, userEntity);
    }

    ProductChannelEditResult buildEditResult(Integer contextId, List<ProductChannels> channels) {

        // 获取配置信息
        Map<String, Integer> configMap = labelConfigService.getConfigMapByModule(SysLabelConfigEnum.OPERATE_REQUIRED.getModule(), contextId);

        // 处理多版本库存映射
        // 获取所有的sku数据
        List<String> allErpSku = channels.stream().map(ProductChannels::getProductRelates)
                .flatMap(List::stream)
                .map(ProductChannelRelate::getErpsku)
                .collect(Collectors.toList());

        // 保存销售渠道信息
        List<String> flags = channels.stream().map(ProductChannels::getAccountId)
                .distinct().collect(Collectors.toList());

        List<Account> accounts = accountService.lambdaQuery()
                .in(Account::getFlag, flags).isNull(Account::getIsDelete)
                .eq(Account::getOrgId, contextId).select(Account::getId, Account::getFlag, Account::getType)
                .list();

        AssertUtil.isTrue(CollectionUtil.isNotEmpty(accounts), "店铺信息不存在:" + flags);

        Map<String, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getFlag, Function.identity(), (v1, v2) -> v1));


        List<SkuParents> skuParents = skuParentsService.selectByOrgIdAndSkus(contextId, allErpSku);
        List<Products> products = productsService.selectProductsByOrgIdAndErpSkus(contextId, allErpSku);


        Map<String, SkuParents> parentsMap = skuParents.stream().collect(Collectors.toMap(SkuParents::getParentSku, Function.identity(), (a, b) -> a));
        Map<String, Products> productMap = products.stream().collect(Collectors.toMap(Products::getErpsku, Function.identity(), (a, b) -> a));


        return ProductChannelEditResult.builder()
                .orgId(contextId).channels(channels).parentMap(parentsMap).productMap(productMap)
                .accountMap(accountMap).configMap(configMap)
                .build();

    }


    void completeChannelAndCheck(ProductChannelEditResult result) {
        Map<String, SkuParents> parentMap = result.getParentMap();
        Map<String, Products> productMap = result.getProductMap();
        Map<String, Account> accountMap = result.getAccountMap();

        // temu渠道sellerSku格式错误数据
        List<String> errorTemuSellerSkus = new ArrayList<>();
        // 是否VINE为空数据
        List<String> vineEmptySku = new ArrayList<>();
        // 检查VINE为空的渠道
        List<String> vineRequire = Arrays.asList(SaleChannelEnum.AMAZON.getValue(), SaleChannelEnum.VCUSPO.getValue(), SaleChannelEnum.VCDI.getValue(), SaleChannelEnum.VCDF.getValue());


        // 先查询是否开启多版本映射信息
        boolean enableMultipleVersion = systemGlobalConfigService.isEnableConfig(result.getOrgId(), SystemGlobalConfigEnum.SKU_VERSION_INIT_CONFIG);

        for (ProductChannels channel : result.getChannels()) {
            channel.setOrgId(result.getOrgId());

            String erpSku = channel.getProductRelates().stream().map(ProductChannelRelate::getErpsku).sorted().collect(Collectors.joining(","));

            channel.setErpSku(erpSku);
            // 检查多版本SKU异常
            checkMultipleVersionSkuAndThrow(channel, enableMultipleVersion, parentMap, productMap);

            Account account = accountMap.get(channel.getAccountId());
            AssertUtil.isTrue(Objects.nonNull(account), "店铺不存在：" + account.getFlag());
            channel.setSaleChannel(account.getType());


            if (SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian().equals(channel.getSaleChannel())) {
                if (!channel.getSellerSku().matches("^\\d{10,}$")) {
                    errorTemuSellerSkus.add(channel.getSellerSku());
                }
            }

            if (Objects.equals(result.getOrgId(), 1000049)) {
                if (vineRequire.contains(channel.getSaleChannel()) && StrUtil.isBlank(channel.getIsVine())) {
                    vineEmptySku.add(channel.getSellerSku());
                }
            }
            completeTheOtherData(channel, accountMap);
        }


        Map<String, Integer> configMap = result.getConfigMap();

        Integer vineConfig = configMap.get(SysLabelConfigEnum.VINE_REQUIRED.getLabelConfig());

        if (CollectionUtil.isNotEmpty(vineEmptySku) && Objects.equals(1, vineConfig)) {
            throw new CommonException("SC、VC-DF、VC-DI、VC-PO 渠道，SellerSku" + vineEmptySku + "的 是否Vine 必须填写");
        }
        if (CollectionUtil.isNotEmpty(errorTemuSellerSkus)) {
            throw new CommonException("Temu渠道,SellerSku:" + errorTemuSellerSkus + "必须为纯数字字符串，且字符长度符合大于等于10");
        }


        Integer inventoryConfig = configMap.get(SysLabelConfigEnum.NOT_INVENTORY_NORMAL.getLabelConfig());


        if (Objects.equals(result.getOrgId(), 1000049) && Objects.equals(1, inventoryConfig)) {
            List<String> noInventorySkus = checkInventory(result.getOrgId(), result.getChannels());
            if (CollectionUtil.isNotEmpty(noInventorySkus)) {
                throw new CommonException("存在无库存SKU：" + CollectionUtil.join(noInventorySkus, ",") + ", 不允许映射,如需取消限制,可在设置-基本配置-业务配置中调整");
            }
        }

    }

    private void checkMultipleVersionSkuAndThrow(ProductChannels channel, boolean enbaleMultipleVersion, Map<String, SkuParents> parentsMap, Map<String, Products> productMap) {
        ProductParentCheckResult checkResult = checkMultipleVersionSku(enbaleMultipleVersion, parentsMap, productMap, channel);

        if (CollectionUtil.isNotEmpty(checkResult.getNotFoundSku())) {
//            throw new CheckException("多版本开关为关闭状态，以下SKU不存在:" + checkResult.getNotFoundSku());
            MultipleVersionExceptionInfo info = new MultipleVersionExceptionInfo(enbaleMultipleVersion, checkResult.getNotFoundSku());
            throw new CheckException("多版本开关为关闭状态，以下SKU不存在:", info);
        }
        if (CollectionUtil.isNotEmpty(checkResult.getNotEnableSku())) {
//            throw new CheckException("多版本开关为关闭状态,以下SKU未启用:" + checkResult.getNotEnableSku());
            MultipleVersionExceptionInfo info = new MultipleVersionExceptionInfo(enbaleMultipleVersion, checkResult.getNotEnableSku());
            throw new CheckException("多版本开关为关闭状态,以下SKU未启用:", info);
        }

        if (CollectionUtil.isNotEmpty(checkResult.getNotFoundParentSku())) {
//            String errorMsg = String.format("“多版本开关为开启状态，以下父SKU不存在:\n %s", String.join(",", checkResult.getNotFoundParentSku()));
            throw new CheckException("多版本开关为开启状态，以下父SKU不存在:", new MultipleVersionExceptionInfo(enbaleMultipleVersion, checkResult.getNotFoundParentSku()));
        }
        if (CollectionUtil.isNotEmpty(checkResult.getNotParentSku())) {
//            String errorMsg = String.format("“多版本开关为开启状态，以下SKU非父级SKU:\n %s ", String.join(",", checkResult.getNotParentSku()));
            throw new CheckException("多版本开关为开启状态，以下SKU非父级SKU:", new MultipleVersionExceptionInfo(enbaleMultipleVersion, checkResult.getNotParentSku()));
        }
        if (CollectionUtil.isNotEmpty(checkResult.getNotSonSku())) {
//            String errorMsg = String.format("多版本开关为关闭状态，以下SKU非子级SKU:%s", String.join(",", checkResult.getNotSonSku()));
            throw new CheckException("多版本开关为关闭状态，以下SKU非子级SKU:", new MultipleVersionExceptionInfo(enbaleMultipleVersion, checkResult.getNotSonSku()));
        }
    }


    @Override
    @Transactional
    public void saveOrUpdateProductChannels(List<ProductChannels> channels, Integer contextId, UserEntity userEntity, boolean isImport,Map<String, Integer> configMap) {
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("参数不能为空");
        }

        // 默认所有配置全部为0
        if (Objects.isNull(configMap)) {
            configMap = new HashMap<>();
        }

        log.error("#SKU映射保存数据：{}", channels);

        String userName = UserUtils.getCurrentUserName("System");
        Integer userId = UserUtils.getCurrentUserId(-1);

        // 设置组织信息
        if (contextId != null) {
            channels.forEach(item -> item.setOrgId(contextId));
        }

        completeTheOperational(channels);


        // 保存销售渠道信息
        List<String> flags = channels.stream().map(ProductChannels::getAccountId)
                .distinct().collect(Collectors.toList());

        List<Account> accounts = accountService.lambdaQuery()
                .in(Account::getFlag, flags).isNull(Account::getIsDelete)
                .eq(Account::getOrgId, contextId).select(Account::getId, Account::getFlag, Account::getType)
                .list();

        AssertUtil.isTrue(CollectionUtil.isNotEmpty(accounts), "店铺信息不存在:" + flags);

        Map<String, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getFlag, Function.identity(), (v1, v2) -> v1));


        List<String> errorTemuSellerSkus = new ArrayList<>();

        List<String> vineRequire = Arrays.asList(SaleChannelEnum.AMAZON.getValue(), SaleChannelEnum.VCUSPO.getValue(), SaleChannelEnum.VCDI.getValue(), SaleChannelEnum.VCDF.getValue());

        List<String> vineEmptySku = new ArrayList<>();

        for (ProductChannels channel : channels) {
            if (StrUtil.isBlank(channel.getImageUrl())) {
                channel.setImageUrl("");
            }

            if (SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian().equals(channel.getSaleChannel())) {
                if (!channel.getSellerSku().matches("^\\d{10,}$")) {
                    errorTemuSellerSkus.add(channel.getSellerSku());
                }
            }

            if (Objects.equals(contextId, 1000049)) {
                if (vineRequire.contains(channel.getSaleChannel()) && StrUtil.isBlank(channel.getIsVine())) {
                    vineEmptySku.add(channel.getSellerSku());
                }
            }

            completeTheOtherData(channel, accountMap);
            log.info("SKU映射 -  产品信息 skuDataSetting ：{}", JSON.toJSONString(channel));
        }

        Integer vineConfig = configMap.get(SysLabelConfigEnum.VINE_REQUIRED.getLabelConfig());


        if (CollectionUtil.isNotEmpty(vineEmptySku) && Objects.equals(1, vineConfig)) {
            throw new CommonException("SC、VC-DF、VC-DI、VC-PO 渠道，SellerSku" + vineEmptySku + "的 是否Vine 必须填写");
        }
        if (CollectionUtil.isNotEmpty(errorTemuSellerSkus)) {
            throw new CommonException("Temu渠道,SellerSku:" + errorTemuSellerSkus + "必须为纯数字字符串，且字符长度符合大于等于10");
        }

//        if (CollectionUtil.isNotEmpty(errorNoAsinSku)) {
//            throw new CommonException("SellerSku" + errorNoAsinSku + "为Amazon渠道，必须填写Asin信息，请重新录入！");
//        }
        // 获取所有的ERPSKU信息


        // 本次数据是否是父SKU映射数据
        boolean multipleVersionSku = Objects.equals(channels.get(0).getIsParentSku(), 1);

        // 获取新增数据

        List<ProductChannels> insert = channels.stream()
                .filter(item -> item.getId() == null)
                .peek(c -> c.setApprovalStatus(ApprovalEnum.SUBMIT.getValue()))
                .collect(Collectors.toList());

        // 获取修改数据
        List<ProductChannels> update = channels.stream()
                .filter(item -> item.getId() != null)
                .collect(Collectors.toList());

        Integer inventoryConfig = configMap.get(SysLabelConfigEnum.NOT_INVENTORY_NORMAL.getLabelConfig());


        if (Objects.equals(contextId, 1000049) && Objects.equals(1, inventoryConfig)) {
            List<String> noInventorySkus = checkInventory(contextId, channels);

            if (CollectionUtil.isNotEmpty(noInventorySkus)) {
                throw new CommonException("存在无库存SKU：" + CollectionUtil.join(noInventorySkus, ",") + ", 不允许映射,如需取消限制,可在设置-基本配置-业务配置中调整");
            }
        }


        if (CollectionUtil.isNotEmpty(insert)) {
            handleInsertChannels(contextId, insert);
        }

        if (CollectionUtil.isNotEmpty(update)) {
            handleUpdateChannels(contextId, update);
        }
    }

    private void handleInsertChannels(Integer contextId, List<ProductChannels> insert) {
        productChannelApprovalNodeService.initFirstNode(contextId, insert, true);

        this.saveBatch(insert);
        // 数据推送多渠道
        pushToMultichannel(insert);
        // 手动记录新增日志
        erpOperateLogService.recordInsert(insert);
        // 插入erpSku信息
        List<ProductChannelRelate> channelRelates = insert.stream()
                .filter(item -> CollectionUtil.isNotEmpty(item.getProductRelates()))
                .map(item -> {
                    List<ProductChannelRelate> relates = item.getProductRelates();
                    relates.forEach(r -> r.setProductChannelId(item.getId()));
                    return relates;
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(channelRelates)) {
            productChannelRelateService.saveBatch(channelRelates);
        }
        try {
            for (ProductChannels c : insert) {
                if (ProductRelateTypeEnum.BUNDLE.getValue().equals(c.getType())) {
                    List<String> erpSkus = StrUtil.isBlank(c.getErpSku()) ? new ArrayList<>() : Arrays.asList(c.getErpSku().split(","));
                    log.info("SKU映射推送BOSS: 新增映射信息 调用 sendMnsForStockVirtualInErpsku 执行推送,参数 {}  {}  {}  {}", c.getOrgId(), c.getId(), erpSkus, c.getAccountId());
                    bossProductChannels.sendMnsForStockVirtualInErpsku(c.getOrgId(), c.getId(), erpSkus, c.getAccountId());
                } else {
                    ProductChannelEntity entity = convertBossProductChannels(c);
                    log.info("SKU映射推送BOSS: 新增映射信息 调用 sendMnsForStockVirtual 执行推送 : {}", JSON.toJSONString(entity));
                    bossProductChannels.sendMnsForStockVirtual(entity, 1);
                }
            }

        } catch (Exception e) {
            log.info("SKU映射推送BOSS异常 - ", e);
        }

        handleHistory(insert, null);
    }

    private void handleUpdateChannels(Integer contextId, List<ProductChannels> update) {
        log.info("开始执行SKU映射信息修改,条目：{}", update.size());

        List<Integer> channelIds = update.stream().map(ProductChannels::getId).collect(Collectors.toList());

        List<ProductChannels> dbChannels = this.listByIds(channelIds);
        if (CollectionUtil.isEmpty(dbChannels)) {
            throw new CommonException("未获取到需要修改的SKU映射信息！");
        }

        productChannelRelateService.assignChannelRelates(dbChannels);

        Map<Integer, ProductChannels> dbPMap = dbChannels.stream()
                .collect(Collectors.toMap(ProductChannels::getId, Function.identity(), (a, b) -> a));

        // 记录映射信息
        handleHistory(update, dbPMap);


        // 获取第一个项目信息
        for (ProductChannels productChannels : update) {
            productChannels.setCreatedAt(null);
            productChannels.setCreatedName(null);
            if (StrUtil.isBlank(productChannels.getApprovalStatus()) && !dbPMap.containsKey(productChannels.getId())) {
                productChannels.setApprovalStatus(ApprovalEnum.NEW.getValue());
                continue;
            }
            productChannels.setApprovalStatus(dbPMap.get(productChannels.getId()).getApprovalStatus());
        }

        // 检查是否需要提交
        checkChannelsCommit(update);

        // 数据推送多渠道
        pushToMultichannel(update);

        this.updateBatchById(update);

        try {
            erpOperateLogService.logRecord(dbChannels, update, "id", "SKU映射", false, false, "meta","instanceId");
        } catch (Exception e) {
            log.info("SKU映射更新日志记录异常 - ",e);
        }

        productChannelRelateService.deleteByChannelIds(channelIds);

        // 新增/修改/删除 erpSku信息
        List<ProductChannelRelate> relates = update.stream()
                .filter(item -> CollectionUtil.isNotEmpty(item.getProductRelates()))
                .peek(item -> item.getProductRelates().forEach(p -> p.setProductChannelId(item.getId())))
                .map(ProductChannels::getProductRelates)
                .flatMap(List::stream)
                .peek(item -> {
                    item.setId(null);
                    item.setUpdated(DateUtils.getNowDate());
                })
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(relates)) {
            productChannelRelateService.saveBatch(relates);
        }
    }

    private void pushToMultichannel(List<ProductChannels> channels) {

        for (ProductChannels productChannels : channels) {
            try {
                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.PRODUCT_CHANNEL_EDIT_QUEUE_ROUTING_KEY, productChannels);
            } catch (Exception e) {
                log.error("SKU映射推送多渠道 - 异常：{} {}", productChannels.getAccountId(), productChannels.getSellerSku(), e);
            }
        }



    }

    private void handleListingPrice(ProductChannels channels,Account account) {
        handleListingPrice(Lists.newArrayList(channels), Lists.newArrayList(account));
    }
    private void handleListingPrice(List<ProductChannels> insert,List<Account> accounts) {
        List<Account> vcdfAccounts = accounts.stream()
                .filter(c -> Objects.equals(c.getType(), SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DF.getEhengjian()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(vcdfAccounts)) {
            log.error("新增SKU映射同步Listing - 当前店铺不存在 {} 渠道 ", SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DF.getEhengjian());
            return;
        }

        List<String> flags = vcdfAccounts.stream().map(Account::getFlag).collect(Collectors.toList());

        List<ProductChannels> productChannels = insert.stream()
                .filter(c -> flags.contains(c.getAccountId()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(productChannels)) {
            log.error("新增SKU映射同步Listing - 当前SKU映射不存在 {} 渠道 ", SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DF.getEhengjian());
            return;
        }

        Map<String, Account> accountMap = vcdfAccounts.stream().collect(Collectors.toMap(Account::getFlag, Function.identity(), (a, b) -> a));

        for (ProductChannels channel : insert) {
            bizark.amz.listing.Item item = null;
            try {
                item = amazonApi.getListingItem(channel.getSellerSku(), accountMap.get(channel.getAccountId()));
            } catch (Exception e) {
                log.error("SKU映射获取Listing价格信息失败： {} - {}", channel.getSellerSku(), e.getMessage(), e);
                continue;
            }
            log.info("SKU映射获取Listing价格信息 - 响应：{}", JSON.toJSON(item));
            if (Objects.isNull(item)) {
                continue;
            }
            bizark.amz.listing.ItemAttributes attributes = item.getAttributes();

//            if (Objects.nonNull(attributes) && CollectionUtil.isNotEmpty(attributes.getList_price())) {
//                List<ItemAttributeComponents> listPrice = attributes.getList_price();
//                ItemAttributeComponents components = listPrice.get(0);
//                String price = components.getValue();
//                if (StrUtil.isNotBlank(price)) {
//                    channel.setListingPrice(new BigDecimal(price));
//                    // 更新订单信息
//                    if (StrUtil.isNotBlank(channel.getAsin())) {
//                        log.info("ListingPrice更新订单价格 - {}  -  {}  -  {} ", channel.getAsin(), channel.getAccountId(), channel.getListingPrice());
//                        saleOrdersMapper.updateDiscountPrice(channel.getAsin(), channel.getAccountId(), channel.getListingPrice());
//                    }
//
//                }
//
//            }
//            if (CollectionUtil.isNotEmpty(item.getProcurement())) {
//                List<ItemProcurement> procurement = item.getProcurement();
//                ItemProcurement itemProcurement = procurement.get(0);
//                Money costPrice = itemProcurement.getCostPrice();
//                if (Objects.nonNull(costPrice) && StrUtil.isNotBlank(costPrice.getAmount())) {
//                    channel.setSellerSkuPrice(new BigDecimal(costPrice.getAmount()));
//                }
//            }
        }

    }

    private void checkChannelsCommit(List<ProductChannels> update) {
        List<Integer> ids = update.stream().map(ProductChannels::getId)
                .collect(Collectors.toList());



        List<ProductChannels> channels = this.listByIds(ids);
        if (CollectionUtil.isEmpty(channels)) {
            return;
        }


        List<ProductChannelRelate> dbRealtes = productChannelRelateService.lambdaQuery()
                .in(ProductChannelRelate::getProductChannelId, ids)
                .list();


        Map<Integer, ProductChannels> erpSkuMap = channels.stream().collect(Collectors.toMap(ProductChannels::getId, Function.identity(), (a, b) -> a));
        Map<Integer, List<ProductChannelRelate>> relateMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(dbRealtes)) {
            relateMap = dbRealtes.stream()
                    .collect(Collectors.groupingBy(ProductChannelRelate::getProductChannelId));
        }

        for (ProductChannels productChannels : update) {
            // 如果当前状态是已退回，则直接进入待审批


            if (!erpSkuMap.containsKey(productChannels.getId())) {
                log.info("SKU映射 - 检查是否提交数据 - 未获取到DB对应数据：{} {}", productChannels.getAccountId(), productChannels.getSellerSku());
                continue;
            }
            ProductChannels dbChannel = erpSkuMap.get(productChannels.getId());

            log.info("SKU映射 - 检查是否提交审批 - DB数据：{}   修改数据：{}", JSON.toJSONString(dbChannel), JSON.toJSONString(productChannels));

            if (Objects.equals(dbChannel.getApprovalStatus(), ApprovalEnum.RETURN.getValue()) || Objects.equals(dbChannel.getApprovalStatus(), ApprovalEnum.NEW.getValue())) {
                productChannelApprovalNodeService.initFirstNode(productChannels, true);
                productChannels.setApprovalStatus(ApprovalEnum.SUBMIT.getValue());
                continue;
            }



            if (Objects.equals(productChannels.getType(), ProductRelateTypeEnum.SINGLE.getValue()) && !Objects.equals(productChannels.getErpSku(), dbChannel.getErpSku())) {
                productChannels.setApprovalStatus(ApprovalEnum.SUBMIT.getValue());
                productChannelApprovalNodeService.initFirstNode(productChannels, true);
                continue;
            }

            List<ProductChannelRelate> relates = productChannels.getProductRelates();

            if (CollectionUtil.isEmpty(relates) && !relateMap.containsKey(productChannels.getId())) {
                continue;
            }

            if (CollectionUtil.isEmpty(relates) && relateMap.containsKey(productChannels.getId())) {
                productChannels.setApprovalStatus(ApprovalEnum.SUBMIT.getValue());
                productChannelApprovalNodeService.initFirstNode(productChannels, true);
                continue;
            }
            if (CollectionUtil.isNotEmpty(relates) && !relateMap.containsKey(productChannels.getId())) {
                productChannels.setApprovalStatus(ApprovalEnum.SUBMIT.getValue());
                productChannelApprovalNodeService.initFirstNode(productChannels, true);
                continue;
            }
            relates.sort(Comparator.comparing(ProductChannelRelate::getErpsku));

            List<ProductChannelRelate> dbData = relateMap.get(productChannels.getId());
            dbData.sort(Comparator.comparing(c -> c.getErpsku() + c.getQty()));


            String udpateStr = relates.stream().map(c -> c.getErpsku() + c.getQty()).collect(Collectors.joining(","));
            String dbStr = dbData.stream().map(c -> c.getErpsku() + c.getQty()).collect(Collectors.joining(","));

            if (!Objects.equals(dbStr, udpateStr)) {
                productChannels.setApprovalStatus(ApprovalEnum.SUBMIT.getValue());
                productChannelApprovalNodeService.initFirstNode(productChannels, true);
            }
        }

    }

    private static void sortErpSku(ProductChannels item) {
        String erpSku = item.getErpSku();
        if (StrUtil.isBlank(erpSku)) {
            return;
        }
        String[] split = erpSku.split(",");
        Arrays.sort(split);
        item.setErpSku(
                Arrays.stream(split).collect(Collectors.joining(","))
        );

    }

    private void completeTheOperational(List<ProductChannels> channels) {
        List<Integer> userIds = channels.stream()
                .filter(c -> Objects.nonNull(c.getOperationUserId()) && StrUtil.isBlank(c.getOperationUserName()))
                .map(ProductChannels::getOperationUserId)
                .distinct()
                .collect(Collectors.toList());

        List<UserEntity> entities = userService.findByIds(userIds);
        if (CollectionUtil.isNotEmpty(entities)) {
            Map<Integer, String> userMap = entities.stream().collect(Collectors.toMap(UserEntity::getId, UserEntity::getName, (a, b) -> a));
            for (ProductChannels channel : channels) {
                if (Objects.nonNull(channel.getOperationUserId()) && StrUtil.isBlank(channel.getOperationUserName()) && userMap.containsKey(channel.getOperationUserId())) {
                    channel.setOperationUserName(userMap.get(channel.getOperationUserId()));
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChannelImportResultVO saveOrUpdateProductChannels(List<ProductChannels> channels, Integer confirm, Integer contextId, UserEntity userEntit, Map<String, Integer> configMap) {

        channelsDataAssembly(channels);

        List<String> checkSaleChannels = Arrays.asList(
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.SC.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DF.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DI.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_USPO.getEhengjian()
        );

        List<ProductChannels> needCheck = channels.stream().filter(c -> checkSaleChannels.contains(c.getSaleChannel()))
                .collect(Collectors.toList());


        if (CollectionUtil.isEmpty(needCheck) || (confirm != null && confirm < 0)) {
//            saveOrUpdateProductChannels(channels, contextId, userEntit, false, configMap);
            saveOrUpdateProductChannelsWithMultipleVersionSku(contextId, channels, userEntit, false);
            return new ChannelImportResultVO();
        }
        ChannelImportResultVO vo = null;

        if (Objects.equals(confirm, 0) && !(vo = checkImportAsins(contextId, needCheck)).isSuccess()) {
            vo.setConfirm(2);
            return vo;
        }
        if (Objects.equals(confirm, 2)) {
            vo = superadditionHistory(contextId, needCheck);
        }
        if (Objects.nonNull(vo) && CollectionUtil.isNotEmpty(vo.getDbSource())) {
            channels.addAll(vo.getDbSource());
        }
//        saveOrUpdateProductChannels(channels, contextId, userEntit, false, configMap);
        saveOrUpdateProductChannelsWithMultipleVersionSku(contextId, channels, userEntit, false);
        return new ChannelImportResultVO();
    }

    @NotNull
    private static void channelsDataAssembly(List<ProductChannels> channels) {

        for (ProductChannels channel : channels) {
            // 处理标签
            if (CollectionUtil.isNotEmpty(channel.getTagArr())) {
                channel.setTags(
                        channel.getTagArr().stream().map(String::valueOf).collect(Collectors.joining(","))
                );
            }
            List<ProductChannelRelate> relates = channel.getProductRelates();
            String erpSku = channel.getErpSku();
            Integer qty = 1;
            if (CollectionUtil.isNotEmpty(relates)) {
                erpSku = relates.stream().map(ProductChannelRelate::getErpsku).collect(Collectors.joining(","));
                qty = relates.stream().map(r -> Objects.isNull(r.getQty()) ? 1 : r.getQty()).reduce(Integer::sum).get();
            }
            channel.setErpSku(erpSku);
            channel.setQuantity(BigDecimal.valueOf(qty));
        }
    }


    public List<String> checkInventory(Integer orgId, List<ProductChannels> channels) {
        List<String> erpSkus = channels.stream()
                .map(ProductChannels::getErpSku)
                .filter(StrUtil::isNotBlank)
                .map(c -> Lists.newArrayList(c.split(",")))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(erpSkus)) {
            return new ArrayList<>();
        }

        // 获取所有ErpSku库存数据
        List<InventoryListEntity> availableInventories = inventoryListService.getInventoryList(orgId, erpSkus);

//        CompletableFuture<List<InventoryListEntity>> f1 = CompletableFuture.supplyAsync(() -> inventoryListService.getInventoryList(orgId, erpSkus), threadPoolTaskExecutor);
//        CompletableFuture<List<InventoryListEntity>> f2 = CompletableFuture.supplyAsync(() -> inventoryListService.findAllBySkuIn(orgId, erpSkus), threadPoolTaskExecutor);

//        try {
//            CompletableFuture.allOf(f1, f2).get(30, TimeUnit.SECONDS);
//        } catch (Exception e) {
//            throw new CommonException("获取ErpSku库存数据失败");
//        }
//        List<InventoryListEntity> availableInventories = f1.join();
//        List<InventoryListEntity> inventories = f2.join();


        Map<String, Integer> availableMap = new HashMap<>();
        Map<String, Integer> inventoryMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(availableInventories)) {
            availableMap.putAll(
                    availableInventories.stream().collect(Collectors.toMap(InventoryListEntity::getSku, InventoryListEntity::getInventoryAvailable, Integer::sum))
            );
        }
        if (CollectionUtil.isNotEmpty(availableInventories)) {
            inventoryMap.putAll(availableInventories.stream()
                    .collect(Collectors.groupingBy(InventoryListEntity::getSku, Collectors.summingInt(c -> 1))));
        }

        Set<String> set = new HashSet<>();

        for (ProductChannels channel : channels) {
            String erpSku = channel.getErpSku();
            if (StrUtil.isBlank(erpSku)) {
                continue;
            }
            for (String sku : erpSku.split(",")) {
                if (!availableMap.containsKey(sku)) {
                    set.add(sku);
                    continue;
                }
                Integer count = availableMap.get(sku);
                if (Objects.equals(count, 0) || !inventoryMap.containsKey(sku)) {
                    set.add(sku);
                }

            }

        }
        return new ArrayList<>(set);
    }

    private void handleHistory(List<ProductChannels> update, Map<Integer, ProductChannels> dbPMap) {

        try {
            if (Objects.isNull(dbPMap)) dbPMap = new HashMap<>();

            String userName = UserUtils.getCurrentUserName("");
            Integer userId = UserUtils.getCurrentUserId(0);

            List<ProductChannelsHistory> his = new ArrayList<>();
            for (ProductChannels up : update) {
                ProductChannels dbData = dbPMap.get(up.getId());
                if (Objects.isNull(up.getId()) || (Objects.isNull(dbData) && !Objects.equals(up.getErpSku(), dbData.getErpSku()))) {
                    ProductChannelsHistory history = BeanCopyUtils.copyBean(up, ProductChannelsHistory.class);
                    history.settingDefaultCreate();
                    history.settingDefaultUpdate();
                    history.setSourceId(history.getId());
                    history.setId(null);
                    his.add(history);
                    continue;
                }
            }
            if (CollectionUtil.isNotEmpty(his)) {
                threadPoolTaskExecutor.execute(() -> productChannelsHistoryService.saveBatch(his));
            }
        } catch (Exception e) {
            log.info("SKU映射历史数据表处理异常 - ", e);
        }


    }

    private void pushToBossForUpdate(List<ProductChannels> update, Map<Integer, ProductChannels> dbPMap,boolean isBefore) {
        //                checkDataForBoss(channelIds, update, dbPMap);
        for (ProductChannels c : update) {

            if (!dbPMap.containsKey(c.getId())) {
                log.error("SKU映射推送BOSS 失败：{}", c.getSellerSku());
                continue;
            }

            ProductChannels dbChannel = dbPMap.get(c.getId());

//                    erpOperateLogService.logRecord(dbChannel, c, "SKU映射", false, false, "meta");


            boolean stockVirtual = (
                    StrUtil.isBlank(c.getSellStatus())
                            || StrUtil.isBlank(c.getIsPush())
                            || Objects.isNull(c.getHandelingTime())
                            || Objects.isNull(c.getPushProportion())
            ) || (
                    !Objects.equals(dbChannel.getSellStatus(), c.getSellStatus())
                            || !Objects.equals(dbChannel.getIsPush(), c.getIsPush())
                            || !Objects.equals(dbChannel.getHandelingTime(), c.getHandelingTime())
                            || !Objects.equals(dbChannel.getPushProportion(), c.getPushProportion()
                    )
            );
            boolean maidian = !StrUtil.isNotBlank(c.getIsPush()) || !Objects.equals("N", c.getIsPush())
                    || !Objects.equals(dbChannel.getPushProportion(), c.getPushProportion());
            log.info("SKU映射推送BOSS：虚拟库存:{}   埋点:{}", stockVirtual, maidian);
            String erpSku = c.getErpSku();
            if (StrUtil.isNotBlank(erpSku)) {
                List<ProductChannels> productChannels = this.lambdaQuery()
                        .like(ProductChannels::getErpSku, Arrays.asList(erpSku.split(",")))
                        .list();
                maidian = CollectionUtil.isEmpty(productChannels);
                log.info("SKU映射推送BOSS: 存在ERPSKU，更新埋点：{}", maidian);
            }

            if (stockVirtual || maidian) {
                ProductChannelEntity entity = convertBossProductChannels(
                        isBefore ? dbChannel : c
                );
                log.info("SKU映射推送BOSS: 调用 sendMnsForStockVirtual 执行推送:{}", JSON.toJSONString(entity));
                bossProductChannels.sendMnsForStockVirtual(entity, 2);
            }


        }
    }

    private static void completeTheOtherData(ProductChannels channel, Map<String, Account> accountMap) {

        channel.settingDBDefaultValue();


        if (CollectionUtil.isNotEmpty(channel.getProductRelates())) {
            List<ProductChannelRelate> relates = channel.getProductRelates();
            String erpskus = relates.stream().map(ProductChannelRelate::getErpsku).collect(Collectors.joining(","));
            channel.setErpSku(erpskus);
        }
        channel.setSaleState(SaleStateEnum.HIT_SHELVE.getValue());
        if (channel.getId() == null) {
            channel.setApprovalStatus(ApprovalEnum.SUBMIT.getValue());
            if (accountMap.containsKey(channel.getAccountId()) && !Objects.equals(channel.getSaleChannel(), "temu")) {
                channel.setCurrencyCode(accountMap.get(channel.getAccountId()).getCurrency());
            }

        }
        sortErpSku(channel);
        channel.setCreatedAt(DateUtils.getNowDate());
        channel.setUpdatedAt(DateUtils.getNowDate());
        channel.setCreatedBy(UserUtils.getCurrentUserId(0));
        channel.setCreatedName(UserUtils.getCurrentUserName(""));
        channel.setUpdatedBy(UserUtils.getCurrentUserId(0));
        channel.setUpdatedName(UserUtils.getCurrentUserName(""));

        Account account = accountMap.get(channel.getAccountId());
        if (Objects.isNull(account)) {
            throw new CommonException("未获取到对应店铺信息：" + channel.getAccountId());
        }

        if (StrUtil.isNotBlank(channel.getFnsku()) && (
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.WALMART.getEhengjian().equalsIgnoreCase(account.getType()) ||
                        SyncMappingEnum.SkuGoodsMapSaleChannelEnum.WALMART_DSV.getEhengjian().equalsIgnoreCase(account.getType())
        )) {
            channel.setGtin(channel.getFnsku());
        }


        if (StrUtil.isBlank(channel.getSaleChannel())) {
//            // 亚马逊渠道
//            if (account.getAccountType().equalsIgnoreCase("amazon") && StrUtil.isBlank(channel.getAsin())) {
//                errorNoAsinSku.add(channel.getSellerSku());
//                return;
//            }
//            // 如果已经存在填写错误的信息，也直接返回，不做其他信息的后续处理
//            if (CollectionUtil.isNotEmpty(errorNoAsinSku)) {
//                return;
//            }
            channel.setSaleChannel(accountMap.get(channel.getAccountId()).getType());
        }
        if (ProductRelateTypeEnum.SINGLE.getValue().equals(channel.getType())) {
            channel.setSkuType(0);
            channel.setQuantity(BigDecimal.ONE);
            // 简单产品置空
            if (CollectionUtil.isNotEmpty(channel.getProductRelates())) {
                channel.setQuantity(BigDecimal.ONE);
                channel.setProductId(channel.getProductRelates().get(0).getProductId());
                channel.setErpSku(channel.getProductRelates().get(0).getErpsku());
                channel.setProductRelates(null);
            }

        }
        if (ProductRelateTypeEnum.BUNDLE.getValue().equals(channel.getType())) {
            channel.setSkuType(1);
            if (CollectionUtil.isNotEmpty(channel.getProductRelates())) {
                List<ProductChannelRelate> relates = channel.getProductRelates();
                BigDecimal quantity = relates.stream()
                        .map(r -> new BigDecimal(r.getQty() == null ? 1 : r.getQty()))
                        .reduce(BigDecimal::add)
                        .get();
                channel.setQuantity(quantity);
                String erpSKus = relates.stream()
                        .map(ProductChannelRelate::getErpsku)
                        .collect(Collectors.joining(","));
                channel.setErpSku(erpSKus);

            }
        }
    }

    private static ProductChannelEntity convertBossProductChannels(ProductChannels c) {
        ProductChannelEntity entity = BeanCopyUtils.copyBean(c, ProductChannelEntity.class);
        entity.setAccountFlag(c.getAccountId());
        List<ProductChannelRelate> relates = c.getProductRelates();
        if (CollectionUtil.isNotEmpty(relates)) {
            List<ProductChannelRelateEntity> relateEntityList = relates.stream()
                    .map(relate -> {
                        ProductChannelRelateEntity r = new ProductChannelRelateEntity();
                        r.setErpSku(relate.getErpsku());
                        r.setActive(relate.getActive());
                        r.setProductId(Objects.isNull(relate.getProductId()) ? 0 : relate.getProductId());
                        r.setProductChannelId(Objects.isNull(relate.getProductChannelId()) ? 0 : relate.getProductChannelId());
                        r.setQty(Objects.isNull(relate.getQty()) ? 0 : relate.getQty());
                        return r;
                    }).collect(Collectors.toList());
            entity.setProductChannelRelateList(relateEntityList);
        }
        return entity;
    }



    private void checkSaveOrUpdateData(List<ProductChannels> channels,boolean isImport) {

        List<ProductChannels> insert = channels.stream()
                .filter(c -> c.getId() == null)
                .collect(Collectors.toList());

        List<ProductChannels> update = channels.stream()
                .filter(c -> c.getId() != null)
                .collect(Collectors.toList());

        // 检查新增数据
        if (CollectionUtil.isNotEmpty(insert)) {
            List<String> sellerSkus = channels.stream()
                    .map(ProductChannels::getSellerSku)
                    .collect(Collectors.toList());
            List<ProductChannels> dbChannnels = this.lambdaQuery()
                    .in(ProductChannels::getSellerSku, sellerSkus)
                    .list();

            if (CollectionUtil.isNotEmpty(dbChannnels)) {
                Map<String, List<ProductChannels>> dbGroup = dbChannnels.stream()
                        .collect(Collectors.groupingBy(
                                c -> c.getSellerSku() + c.getAccountId() + c.getDisabledAt()
                        ));
                List<String> errors = new ArrayList<>();
                for (ProductChannels channel : channels) {
                    if (dbGroup.containsKey(channel.getSellerSku() + channel.getAccountId() + channel.getDisabledAt())) {
                        errors.add(channel.getAccountId() + "->" + channel.getSellerSku());
                    }
                }
                if (CollectionUtil.isNotEmpty(errors)) {
                    throw new CommonException("相同店铺下SellerSku不能重复，错误数据：" + errors.toString());
                }
            }
        }

        // 检查修改数据
        if (CollectionUtil.isNotEmpty(update)) {

            for (ProductChannels productChannels : update) {
                if (StrUtil.isBlank(productChannels.getImageUrl())) {
                    productChannels.setImageUrl("");

                }
            }

            List<String> sellerSkus = update.stream()
                    .map(ProductChannels::getSellerSku)
                    .collect(Collectors.toList());
            List<ProductChannels> dbChannnels = this.lambdaQuery()
                    .in(ProductChannels::getSellerSku, sellerSkus)
                    .list();
            List<ProductChannels> interfaceData = dbChannnels.stream()
                    .filter(c -> Objects.nonNull(c.getIsInterfaceData()) && c.getIsInterfaceData())
                    .collect(Collectors.toList());


            importDataHandle(channels);


            if (CollectionUtil.isNotEmpty(interfaceData)) {
                // 检查ASIN  父ASIN  品牌是否被修改
                Map<String, ProductChannels> dbMap = interfaceData.stream().collect(Collectors.toMap(k -> k.getAccountId() + k.getSellerSku(), Function.identity(), (a, b) -> a));
                List<String> asinCheckError = new ArrayList<>();
                List<String> parentAsinCheckError = new ArrayList<>();
                List<String> brandCheckError = new ArrayList<>();
                for (ProductChannels channel : channels) {

                    if (channel.isSkip()) {
                        continue;
                    }

                    String key = channel.getAccountId() + channel.getSellerSku();
                    if (!dbMap.containsKey(key)) {
                        continue;
                    }
                    if (StrUtil.isNotBlank(channel.getAsin())) {
                        channel.setAsin(channel.getAsin().trim());
                    }
                    if (StrUtil.isNotBlank(channel.getAsin1())) {
                        channel.setAsin1(channel.getAsin1().trim());
                    }
                    if (StrUtil.isNotBlank(channel.getBrand())) {
                        channel.setBrand(channel.getBrand().trim());
                    }
                    ProductChannels dbData = dbMap.get(key);



                    if ((StrUtil.isNotBlank(channel.getAsin()) || StrUtil.isNotBlank(dbData.getAsin())) && !String.valueOf(channel.getAsin()).equals(String.valueOf(dbData.getAsin()))) {
                        asinCheckError.add(channel.getSellerSku());
                    }
                    if ((StrUtil.isNotBlank(channel.getAsin1()) || StrUtil.isNotBlank(dbData.getAsin1())) && !String.valueOf(channel.getAsin1()).equals(String.valueOf(dbData.getAsin1()))) {
                        parentAsinCheckError.add(channel.getSellerSku());
                    }
                    if ((StrUtil.isNotBlank(channel.getBrand()) || StrUtil.isNotBlank(dbData.getBrand())) && !String.valueOf(channel.getBrand()).equals(String.valueOf(dbData.getBrand()))) {
                        brandCheckError.add(channel.getSellerSku());
                    }

                }
                // 只有导入检查数据是否被修改
                if (isImport) {
                    if (CollectionUtil.isNotEmpty(asinCheckError)) {
                        throw new CommonException("SellerSku" + asinCheckError + "为接口获取数据，ASIN不可修改");
                    }
                    if (CollectionUtil.isNotEmpty(parentAsinCheckError)) {
                        throw new CommonException("SellerSku" + parentAsinCheckError + "为接口获取数据，父ASIN不可修改");
                    }
                    if (CollectionUtil.isNotEmpty(brandCheckError)) {
                        throw new CommonException("SellerSku" + brandCheckError + "为接口获取数据，品牌不可修改");
                    }
                }
            }

            List<Integer> updateIds = update.stream()
                    .map(ProductChannels::getId)
                    .collect(Collectors.toList());

            dbChannnels = dbChannnels.stream()
                    .filter(c -> !updateIds.contains(c.getId()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(dbChannnels)) {
                return;
            }

            Map<String, List<ProductChannels>> group = dbChannnels.stream()
                    .collect(Collectors.groupingBy(
                            c -> c.getSellerSku() + c.getAccountId() + c.getDisabledAt()
                    ));

            List<String> errors = new ArrayList<>();
            for (ProductChannels productChannels : update) {
                if (group.containsKey(productChannels.getSellerSku() + productChannels.getAccountId() + productChannels.getDisabledAt())) {
                    errors.add(productChannels.getAccountId() + "->" + productChannels.getSellerSku());
                }
            }
            if (CollectionUtil.isNotEmpty(errors)) {
                throw new CommonException("相同店铺下SellerSku不能重复，错误数据：" + errors.toString());
            }
        }
    }

    private static void importDataHandle(List<ProductChannels> channels) {
        for (ProductChannels channel : channels) {
            // 如果是导入的数据，父ASIN是空的话，直接置为 NULL 防止空字符串覆盖数据
            if (channel.isImportData() && StrUtil.isBlank(channel.getAsin1())) {
                channel.setAsin1(null);
                channel.setSkip(true);

            }   // 如果是导入的数据，父ASIN是空的话，直接置为 NULL 防止空字符串覆盖数据
            if (channel.isImportData() && StrUtil.isBlank(channel.getBrand())) {
                channel.setBrand(null);
                channel.setSkip(true);
            }

            if (channel.isImportData() && StrUtil.isBlank(channel.getAsin())) {
                channel.setAsin(null);
                channel.setSkip(true);
            }

        }
    }

    @Override
    @Transactional
    public void synoSaveOrUpdateProductChannels(List<ProductChannels> channels, Integer contextId, UserEntity userEntity) {
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("参数不能为空");
        }
//        String userName = null;
//        Integer userId = null;
//        try {
//            AuthUserDetails principal = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
//            userName = principal.getName();
//            userId = principal.getId();
//        } catch (Exception e) {
//            log.error("获取当前登录用户信息失败:{}", e.getMessage());
//        }

        // 设置组织信息
        //channels.forEach(item -> item.setOrgId(contextId));
//        // 获取新增数据
//        Integer finalUserId = userId;
//        String finalUserName = userName;
        List<ProductChannels> insert = channels.stream()
                .peek(item -> {
                    if (CollectionUtil.isNotEmpty(item.getProductRelates())) {
                        List<ProductChannelRelate> relates = item.getProductRelates();
                        String erpskus = relates.stream().map(ProductChannelRelate::getErpsku)
                                .collect(Collectors.joining(","));
                        item.setErpSku(erpskus);
                    }
                    item.setSkipInterceptor(true);
                    //item.setApprovalStatus(ApprovalEnum.NEW.getValue());
//                    item.setCreatedAt(DateUtils.getNowDate());
//                    item.setCreatedBy(finalUserId == null ? 0 : finalUserId);
//                    item.setCreatedName(finalUserName == null ? " " : finalUserName);
                })
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(insert)) {
            //handleImgUrl(insert);
            this.saveBatch(insert);
//            // 新增记录审批日志
//            approvalLogRecord(insert);
//            // 处理文件上传信息
//            handlePicMeta(channels, userEntity);
            // 插入erpSku信息
            List<ProductChannelRelate> channelRelates = insert.stream()
                    .filter(item -> CollectionUtil.isNotEmpty(item.getProductRelates()))
                    .map(item -> {
                        List<ProductChannelRelate> relates = item.getProductRelates();
                        relates.forEach(r -> r.setProductChannelId(item.getId()));
                        return relates;
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(channelRelates)) {
                productChannelRelateService.saveBatch(channelRelates);
            }
        }

    }

    /**
     * 处理图片信息
     *
     * @param insert
     */
    private static void handleImgUrl(List<ProductChannels> insert) {
        for (ProductChannels productChannels : insert) {
            if (StrUtil.isBlank(productChannels.getMeta()) || StrUtil.isNotBlank(productChannels.getImageUrl())) {
                continue;
            }
            ContextAttachMeta metaData = JacksonUtils.jsonToBean(productChannels.getMeta(), ContextAttachMeta.class);

            if (metaData == null) {
                productChannels.setImageUrl("");
                continue;
            }

            List<Long> attachIds = new ArrayList<>();
            List<AttachItemRow> items = metaData.getItems();
            if (CollectionUtil.isNotEmpty(items)) {
                productChannels.setImageUrl(items.get(0).getAttachUrl());
            } else {
                productChannels.setImageUrl("");
            }

        }
    }


    public void handlePicMeta(List<ProductChannels> channels, UserEntity userEntity) {
        if (userEntity == null) {
            return;
        }
        for (ProductChannels channel : channels) {
            if (StrUtil.isBlank(channel.getMeta())) {
                continue;
            }
            documentMaterialService.updateAttchMent(channel.getId(), channel.getMeta(), userEntity);
        }
    }


    /**
     * 恒健ERP-SKU映射信息转换
     *
     * @param channels
     * @return
     */
    private List<ScGoodsSkuMap> productChannelsConvertToGoodsSkuMap(List<ProductChannels> channels) {
        List<String> accountFlag = channels.stream()
                .filter(item -> item.getAccountId() != null)
                .map(ProductChannels::getAccountId)
                .collect(Collectors.toList());

        List<Integer> channelIds = channels.stream()
                .map(ProductChannels::getId)
                .collect(Collectors.toList());

        List<ProductChannelRelate> relates = productChannelRelateService.lambdaQuery()
                .in(ProductChannelRelate::getProductChannelId, channelIds)
                .list();

        if (CollectionUtil.isNotEmpty(relates)) {
            Map<Integer, List<ProductChannelRelate>> group = relates.stream()
                    .collect(Collectors.groupingBy(ProductChannelRelate::getProductChannelId));
            for (ProductChannels channel : channels) {
                if (group.containsKey(channel.getId())) {
                    channel.setProductRelates(group.get(channel.getId()));
                }
            }
        }


//        List<Integer> userIds = channels.stream()
//                .filter(item -> item.getOperationUserId() != null)
//                .map(ProductChannels::getOperationUserId)
//                .collect(Collectors.toList());
//        Map<Integer, Long> userMap = new HashMap<>();
//        if (CollectionUtil.isNotEmpty(userIds)) {
//            UcUserPostDeptService ucPostDeptService = SpringUtils.getBean(UcUserPostDeptService.class);
//            List<UcUserPostDeptEntity> deptVOS = ucPostDeptService.findAllByUserId(userIds);
//            List<UserDeptVO> deptVOS = ucPostService.selectUserAndDeptByUserIds(userIds);
//            if (CollectionUtil.isNotEmpty(deptVOS)) {
//                userMap = deptVOS.stream().collect(Collectors.toMap(UserDeptVO::getUserId, UserDeptVO::getDeptId));
//            }
//
//        }
        Map<String, String> accountMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(accountFlag)) {
            List<Account> accounts = accountService.lambdaQuery()
                    .in(Account::getFlag, accountFlag)
                    .list();
            if (CollectionUtil.isNotEmpty(accounts)) {
                accountMap = accounts.stream().collect(Collectors.toMap(Account::getFlag, Account::getAccountInit));
            }
        }


//        Map<Integer, Long> finalUserMap = userMap;
        Map<String, String> finalAccountMap = accountMap;
        List<ScGoodsSkuMap> skuMaps = channels.stream()
                .map(item -> {
                    ScGoodsSkuMap skuMap = new ScGoodsSkuMap();
                    skuMap.setAsin(item.getAsin()).setParentAsin(item.getAsin1())
                            .setBrand(item.getBrand()).setSellerSku(item.getSellerSku()).setOrganizationId(item.getOrgId() == null ? null : item.getOrgId().longValue())
                            .setStatus(item.getApprovalStatus())
                            .setFnSku(item.getFnsku()).setBrand(item.getBrand()).setSaleStatus(SaleStateEnum.getSyncValue(item.getSellStatus()))
                            .setTitle(item.getTitle()).setVine(item.getIsVine()).setOrganizationId(1000L)
                            .setUpc(item.getUpc()).setUserId(item.getOperationUserId() == null ? null : item.getOperationUserId().longValue())
                            .setEnabledFlag(item.getStatus()).setImageUrl(item.getImageUrl()).setOperationId(item.getOperationUserId() == null ? null : item.getOperationUserId().longValue())
                            .setRemark(item.getRemark()).setCreatedAt(item.getCreatedAt()).setOperatorName(item.getOperationUserName())
                            .setCreatedBy(item.getCreatedBy()).setCreatedName(item.getCreatedName())
                            .setUpdatedAt(item.getUpdatedAt()).setUpdatedBy(item.getUpdatedBy());
                    if (ProductRelateTypeEnum.SINGLE.getValue().equals(item.getType())) {
                        skuMap.setSku(item.getErpSku());
                        skuMap.setErpSku(item.getErpSku());
                    }
                    if (ProductRelateTypeEnum.BUNDLE.getValue().equals(item.getType()) && CollectionUtil.isNotEmpty(item.getProductRelates())) {
                        skuMap.setSku(item.getProductRelates().get(0).getErpsku());
                        skuMap.setErpSku(item.getProductRelates().get(0).getErpsku());
                    }


                    if (StrUtil.isNotBlank(item.getSaleChannel()) && SyncMappingEnum.SkuGoodsMapSaleChannelEnum.map.containsKey(item.getSaleChannel())) {
                        skuMap.setSaleChannel(SyncMappingEnum.SkuGoodsMapSaleChannelEnum.map.get(item.getSaleChannel()));
                    } else {
                        skuMap.setSaleChannel(item.getSaleChannel());
                    }
                    if (item.getLineId() != null) {
                        ProductLineEntity line = productLineService.getById(item.getLineId());
                        if (line != null) {
                            skuMap.setBelongProject(line.getName());
                        }
                    }
//                    if (StrUtil.isNotBlank(item.getOperationUserName())) {
//                        skuMap.setOperatorName(SyncMappingEnum.SkuGoodsMapOperationUserEnum.map.get(item.getOperationUserName()));
//                    }
//                    if (finalUserMap.containsKey(item.getOperationUserId())) {
//                        skuMap.setDeptId(finalUserMap.get(item.getOperationUserId()));
//                    }
                    if (finalAccountMap.containsKey(item.getAccountId())) {
                        skuMap.setShopName(finalAccountMap.get(item.getAccountId()));
                    }
                    if (ProductRelateTypeEnum.BUNDLE.getValue().equals(item.getType()) && CollectionUtil.isNotEmpty(item.getProductRelates())) {
                        ProductChannelRelate relate = item.getProductRelates().get(0);
                        skuMap.setErpSku(relate.getErpsku());
                        skuMap.setErpSkuQuantity(relate.getQty());
                    }
                    if (ProductRelateTypeEnum.SINGLE.getValue().equals(item.getType())) {
                        skuMap.setErpSkuQuantity(1);
                    }
                    skuMap.setAsin(item.getAsin());
                    skuMap.setParentAsin(item.getAsin1());
                    return skuMap;
                }).collect(Collectors.toList());

        return skuMaps;
    }

    /**
     * 新增时记录审批日志
     *
     * @param insert
     */
    private void approvalLogRecord(List<ProductChannels> insert) {
        threadPoolTaskExecutor.execute(() -> {
            String userName = null;
            Integer userId = null;
            try {
                AuthUserDetails principal = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
                userName = principal.getName();
                userId = principal.getId();
            } catch (Exception e) {
                log.error("获取当前登录用户信息失败:{}", e.getMessage());
            }
            Integer finalUserId = userId;
            String finalUserName = userName;
            List<ProductChannelApproval> approvals = insert.stream()
                    .map(p -> {
                        ProductChannelApproval approval = new ProductChannelApproval();
                        approval.setProductChannelId(p.getId());
                        approval.setApprovalStatus(p.getApprovalStatus());
                        approval.setCreatedAt(DateUtils.getNowDate());
                        approval.setCreatedBy(finalUserId == null ? 0 : finalUserId);
                        approval.setCreatedName(finalUserName == null ? " " : finalUserName);
                        return approval;
                    }).collect(Collectors.toList());
            SpringUtils.getBean(ProductChannelApprovalService.class).saveBatch(approvals);
        });
    }

    @Override
    public void updateApproval(ApprovalDTO approvalDTO, Integer contextId) {
        if (CollectionUtil.isEmpty(approvalDTO.getIds())) {
            throw new CommonException("ID数据不能为空！");
        }
        List<ProductChannels> channels = this.lambdaQuery()
                .in(ProductChannels::getId, approvalDTO.getIds())
                .list();

        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到对应SKU映射信息！");
        }


        List<ProductChannels> errorChannels = channels.stream()
                .filter(c -> StrUtil.isBlank(c.getErpSku()))
                .collect(Collectors.toList());
        if (ApprovalEnum.APPROVED.getValue().equals(approvalDTO.getApprovalStatus()) && CollectionUtil.isNotEmpty(errorChannels)) {
            List<String> errorSkus = errorChannels.stream().map(ProductChannels::getSellerSku).collect(Collectors.toList());
            throw new CommonException("SellerSku" + errorSkus + "未绑定产品信息！");
        }


//        List<ProductChannels> copyBeans = BeanCopyUtils.copyBeanList(channels, ProductChannels.class);

        String userName = null;
        Integer userId = null;
        try {
            AuthUserDetails principal = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
            userName = principal.getName();
            userId = principal.getId();
        } catch (Exception e) {
            log.error("获取当前登录用户信息失败:{}", e.getMessage());
        }

        // 恒健组织切换审批模式
        if (Objects.equals(contextId, 1000049)) {
            SpringUtils.getBean(ProductChannelApprovalNodeService.class)
                    .modifyApproval(channels, ApprovalEnum.valueOf(approvalDTO.getApprovalStatus()), approvalDTO.getApprovalAdvice());
            return;
        }

        Date now = DateUtils.getNowDate();

        channels.forEach(c -> c.setApprovalStatus(approvalDTO.getApprovalStatus()));

        productChannelsMapper.updateApprovalStatus(approvalDTO.getIds(), approvalDTO.getApprovalStatus(), userId, userName);
        Integer finalUserId = userId;
        String finalUserName = userName;

        saveApprovalLog(channels,approvalDTO);

        try {
            List<ProductChannelRelate> relates = productChannelRelateService.lambdaQuery()
                    .in(ProductChannelRelate::getProductChannelId, approvalDTO.getIds())
                    .list();
            if (CollectionUtil.isNotEmpty(relates)) {
                Map<Integer, List<ProductChannelRelate>> map = relates.stream().collect(Collectors.groupingBy(ProductChannelRelate::getProductChannelId));
                for (ProductChannels channel : channels) {
                    if (map.containsKey(channel.getId())) {
                        channel.setProductRelates(map.get(channel.getId()));
                    }
                }
            }
            Map<Integer, ProductChannels> channelsMap = channels.stream().collect(Collectors.toMap(ProductChannels::getId, Function.identity(), (a, b) -> a));
//            pushToBossForUpdate(channels, channelsMap, false);
        } catch (Exception e) {
            log.error("SKU映射推送BOSS - 审批推送异常 ：{} ", e.getMessage(), e);
        }

//        try {
//            erpOperateLogService.logRecord(copyBeans, channels, "id", "SKU映射", true, false, "approvalStatus");
//        } catch (Exception e) {
//            log.error("审批日志记录失败：{}", e.getMessage());
//        }

//        LambdaUpdateChainWrapper<ProductChannels> chainWrapper = this.lambdaUpdate()
//                .in(ProductChannels::getId, approvalDTO.getIds())
//                .set(ProductChannels::getApprovalStatus, approvalDTO.getApprovalStatus())
//                .set(ProductChannels::getUpdatedAt, now);
//        if (userId != null) {
//            chainWrapper.set(ProductChannels::getUpdatedBy, userId);
//        }
//        if (userName != null) {
//            chainWrapper.set(ProductChannels::getUpdatedName, userName);
//        }
//        chainWrapper.update();
        // 审批日志

        threadPoolTaskExecutor.execute(() -> {
            List<ProductChannelApproval> approvals = channels.stream()
                    .map(c -> {
                        ProductChannelApproval approval = new ProductChannelApproval();
                        approval.setApprovalAdvise(approvalDTO.getApprovalAdvice());
                        approval.setApprovalStatus(approvalDTO.getApprovalStatus());
                        approval.setProductChannelId(c.getId());
                        approval.setCreatedAt(now);
                        approval.setCreatedBy(finalUserId);
                        approval.setCreatedName(finalUserName);
                        approval.setUpdatedBy(finalUserId);
                        approval.setUpdatedName(finalUserName);
                        approval.setUpdatedAt(now);
                        return approval;
                    }).collect(Collectors.toList());
            SpringUtils.getBean(ProductChannelApprovalService.class).saveBatch(approvals);
        });
        // 审批通过发送MQ信息
        if (ApprovalEnum.APPROVED.getValue().equals(approvalDTO.getApprovalStatus())) {
            for (ProductChannels channel : channels) {
                log.error("审批通过，数据推送MQ:{} - {}", channel.getId(), channel.getSellerSku());
                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_NAME_DELIVER_DIRECT_DELAYED, "product_push_by_erp", channel.getId(),new CorrelationData(channel.getId().toString()));
            }
        }
//
//        // 数据同步恒健ERP
//        if (ApprovalEnum.RETURN.getValue().equals(approvalDTO.getApprovalStatus()) || ApprovalEnum.APPROVED.getValue().equals(approvalDTO.getApprovalStatus())) {
//            // TODO 数据同步恒健ERP
//            threadPoolTaskExecutor.execute(()->{
//                List<ProductChannels> sync = this.lambdaQuery()
//                        .in(ProductChannels::getId, approvalDTO.getIds())
//                        .list();
//                if (CollectionUtil.isNotEmpty(sync)) {
//                    syncScGoodsSkuMap(contextId, sync);
//                }
//            });
//        }

    }

    private void saveApprovalLog(List<ProductChannels> channels, ApprovalDTO approvalDTO) {
        String userName = "System";
        Integer userId = 0;
        try {
            AuthUserDetails principal = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
            userName = principal.getName();
            userId = principal.getId();
        } catch (Exception e) {
            log.error("获取当前登录用户信息失败:{}", e.getMessage());
        }
        String finalUserName = userName;
        Integer finalUserId = userId;
        // 手动记录日志
        LocalDateTime localDateTime = LocalDateTime.now();
        Class<ProductChannels> type = ProductChannels.class;
        String tableName = type.isAnnotationPresent(TableName.class) ?
                type.getAnnotation(TableName.class).value()
                : StrUtil.toUnderlineCase(type.getSimpleName());
        Date date = new Date();
        List<ErpOperateLog> logs = channels.stream()
                .map(c -> {
                    ErpOperateLog operateLog = new ErpOperateLog();
                    operateLog.setOperateOldValue(ApprovalEnum.getLabel(c.getApprovalStatus()));
                    operateLog.setOperateNewValue(ApprovalEnum.getLabel(approvalDTO.getApprovalStatus()));
                    operateLog.setOperateName("SKU映射");
                    operateLog.setOperateTable(tableName);
                    operateLog.setOperateType(1);
                    operateLog.setOperateTarget("approvalStatus");
                    operateLog.setOperateUserId(finalUserId.longValue());
                    operateLog.setOperateUserName(finalUserName);
                    operateLog.setBusinessId(c.getId().longValue());
                    operateLog.setFieldDesc("审批状态");
                    operateLog.setUpdatedName(finalUserName);
                    operateLog.setUpdatedAt(date);
                    operateLog.setUpdatedBy(finalUserId);
                    operateLog.setLogType(1);
                    operateLog.setOperateAt(localDateTime);
                    operateLog.setCreatedAt(date);
                    operateLog.setCreatedName(finalUserName);
                    operateLog.setCreatedBy(finalUserId);
                    return operateLog;
                }).collect(Collectors.toList());

        if (StrUtil.isNotBlank(approvalDTO.getApprovalAdvice())) {
            List<ErpOperateLog> adviceLogs = approvalDTO.getIds()
                    .stream()
                    .map(id -> {
                        ErpOperateLog operateLog = new ErpOperateLog();
                        operateLog.setOperateTable("dashboard.product_channels");
                        operateLog.setLogType(1);
                        operateLog.setOperateNewValue(approvalDTO.getApprovalAdvice());
                        operateLog.setBusinessId(id.longValue());
                        operateLog.setOperateAt(localDateTime);
                        operateLog.setOperateUserId(finalUserId.longValue());
                        operateLog.setOperateUserName(finalUserName);
                        operateLog.setFieldDesc("审批意见");
                        operateLog.setOperateName("SKU映射");
                        operateLog.setOperateTarget("approvalAdvice");
                        operateLog.setOperateType(2);
                        operateLog.setCreatedBy(finalUserId);
                        operateLog.setCreatedName(finalUserName);
                        operateLog.setUpdatedAt(date);
                        operateLog.setUpdatedBy(finalUserId);
                        operateLog.setCreatedAt(date);
                        operateLog.setUpdatedName(finalUserName);
                        return operateLog;
                    }).collect(Collectors.toList());
            logs.addAll(adviceLogs);
        }
        SpringUtils.getBean(ErpOperateLogService.class).saveBatch(logs);
    }

    @Override
    @Transactional
    public void commitApproval(Integer[] ids, Integer contextId) {
        List<ProductChannels> channels = this.listByIds(Arrays.asList(ids));
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到需要提交的数据信息！");
        }
        for (ProductChannels channel : channels) {
            if (Objects.isNull(channel.getOperationUserId())) {
                throw new CommonException("当前提交数据无运营信息，无法提交审批！");
            }
        }
        long count = channels.stream()
                .filter(item -> (!item.getApprovalStatus().equals(ApprovalEnum.NEW.getValue())) && (!item.getApprovalStatus().equals(ApprovalEnum.RETURN.getValue())))
                .count();

        if (count > 0) {
            throw new CommonException("只能提交待处理和退回状态的审批");
        }

        List<String> errorSellerSkus = channels.stream()
                .filter(c -> StrUtil.isBlank(c.getErpSku()))
                .map(ProductChannels::getSellerSku)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(errorSellerSkus)) {
            throw new CommonException("SellerSku" + errorSellerSkus + "未绑定产品信息！");
        }


        Map<String, Integer> configMap = labelConfigService.getConfigMapByModule(SysLabelConfigEnum.OPERATE_REQUIRED.getModule(), contextId);


        Integer inventoryConfig = configMap.get(SysLabelConfigEnum.NOT_INVENTORY_NORMAL.getLabelConfig());

        if (Objects.equals(contextId, 1000049) && Objects.equals(1, inventoryConfig)) {
            List<String> noInventorySkus = checkInventory(contextId, channels);

            if (CollectionUtil.isNotEmpty(noInventorySkus)) {
                throw new CommonException("存在无库存SKU：" + CollectionUtil.join(noInventorySkus, ",") + ", 不允许映射,如需取消限制,可在设置-基本配置-业务配置中调整");
            }
        }

        // 恒健组织更换审批模式
        if (Objects.equals(contextId, 1000049)) {
            productChannelApprovalNodeService.modifyApproval(channels, ApprovalEnum.SUBMIT, null);
            return;
        }

        Integer userId = null;
        String userName = "";
        try {
            AuthUserDetails authUser = (AuthUserDetails)SecurityUtils.getSubject().getPrincipal();
            userId = authUser.getId();
            userName = authUser.getName();
        } catch (Exception e) {

        }

        List<ProductChannels> copyBeans = BeanCopyUtils.copyBeanList(channels, ProductChannels.class);

        this.lambdaUpdate()
                .in(ProductChannels::getId, ids)
                .set(ProductChannels::getApprovalStatus, ApprovalEnum.SUBMIT.getValue())
                .set(ProductChannels::getUpdatedBy, userId)
                .set(ProductChannels::getUpdatedAt, new Date())
                .set(ProductChannels::getUpdatedName, userName)
                .update();

        for (ProductChannels channel : channels) {
            channel.setApprovalStatus(ApprovalEnum.SUBMIT.getValue());
            try {
                SpringUtils.getBean(ProductChannelApprovalService.class).recordApprovalLog(channel);
            } catch (Exception e) {
            }
        }

        try {
            erpOperateLogService.logRecord(copyBeans, channels, "id", "SKU映射", true, false, "approvalStatus");
        } catch (Exception e) {
            log.error("提交审批日志记录失败：{}", e.getMessage());
        }
//        ApprovalDTO approvalDTO = new ApprovalDTO();
//        approvalDTO.setIds(Arrays.asList(ids));

//        if (CollectionUtil.isNotEmpty(channels)) {
//            threadPoolTaskExecutor.execute(()->{
//                syncScGoodsSkuMap(contextId, channels);
//            });
//        }


    }


    public void updateScGoodsSkuMapAsin(ScGoodsSkuMap skuMap){
        try {
            log.info("同步恒健ERP父ASIN信息：{}", JSON.toJSONString(skuMap));
            HttpRequest post = HttpUtil.createPost(crmApiUrl + "/api/public/skuGoodsMap/updateAsinInfo");
            post.body(JSON.toJSONString(skuMap));
            skuMap.setOrganizationId(1000L);
            HttpResponse httpResponse = post.execute();
            if (httpResponse.isOk()) {
                log.info("SKU映射同步更新恒健ERP信息成功：{}", httpResponse.body());
                return;
            }
            log.info("SKU映射同步更新恒健ERP信息失败：{}", httpResponse.body());
        } catch (Exception e) {
            log.error("SKU映射同步更新恒健ERP信息失败：{}", e.getMessage());
        }

    }


//    public void syncScGoodsSkuMap(Integer contextId, List<ProductChannels> list) {
//
//        log.error("准备同步恒健ERP-SKU映射信息");
//        List<ScGoodsSkuMap> skuMaps = productChannelsConvertToGoodsSkuMap(list);
//        log.error("数据转换完毕，数据条目：{}，数据：{}", skuMaps.size(), JSON.toJSONString(skuMaps));
//        HttpResponse response = null;
//        try {
//            HttpRequest post = HttpUtil.createPost(crmApiUrl + "/prod-api/api/public/skuGoodsMap/syncSaveOrUpdate?" + "contextId=" + contextId);
//            post.body(JSON.toJSONString(skuMaps));
//            response = post.execute();
//        } catch (Exception e) {
//            List<Integer> syncIds = list.stream().map(ProductChannels::getId).collect(Collectors.toList());
//            log.error("恒健ERP-SKU映射信息同步失败,数据ID：{}", syncIds);
//            e.printStackTrace();
//            return;
//        }
//        if (Objects.isNull(response) || !response.isOk()) {
//            List<Integer> syncIds = list.stream().map(ProductChannels::getId).collect(Collectors.toList());
//            log.error("恒健ERP-SKU映射信息同步失败,数据ID：{}", syncIds);
//            throw new CommonException("SKU映射同步失败:" + response.body());
//        }
//        log.error("恒健ERP-SKU映射信息同步成功");
//
//
//    }

    @Override
    public List<ProductChannelsVO> selectByAsinList(List<String> asinList,Integer contextId, Integer type,String saleChannel) {
        List<ProductChannels> list = lambdaQuery().eq(!StringUtils.isEmpty(saleChannel), ProductChannels::getSaleChannel, saleChannel).in(ProductChannels::getAsin, asinList).eq(null!=contextId,ProductChannels::getOrgId, contextId).list();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<ProductChannelsVO> productChannelsVOS = BeanCopyUtils.copyBeanList(list, ProductChannelsVO.class);
        buildProductChannelQueryDetails(contextId, type, list, productChannelsVOS);
        return productChannelsVOS;
    }

    @Override
    public List<ProductChannelsVO> selectByTkSpuList(List<String> spuList,Integer contextId, Integer type,String saleChannel) {
        List<ProductChannels> list = lambdaQuery()
                .eq(!StringUtils.isEmpty(saleChannel), ProductChannels::getSaleChannel, saleChannel)
                .in(ProductChannels::getItemId, spuList)
                .eq(null!=contextId,ProductChannels::getOrgId, contextId).list();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<ProductChannelsVO> productChannelsVOS = BeanCopyUtils.copyBeanList(list, ProductChannelsVO.class);
        buildProductChannelQueryDetails(contextId, type, list, productChannelsVOS);
        return productChannelsVOS;
    }

    private void buildProductChannelQueryDetails(Integer contextId, Integer type, List<ProductChannels> list, List<ProductChannelsVO> productChannelsVOS) {
        List<String> erpskuList = list.stream()
                .filter(e->!StringUtils.isEmpty(e.getErpSku()))
                .flatMap(str -> Arrays.stream(str.getErpSku().split(","))) // 按逗号分割每个字符串并扁平化
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());
        List<Products> productsList = productsService.lambdaQuery().eq(null != type, Products::getProductType, type)
                .in(Products::getErpsku, erpskuList)
                .eq(null!= contextId,Products::getOrgId, contextId).list();
        if (CollectionUtils.isEmpty(productsList)) {
            return;
        }
        List<ProductsVO> productVOS = BeanCopyUtils.copyBeanList(productsList, ProductsVO.class);
        Map<String, List<ProductsVO>> listMap = productVOS.stream().collect(Collectors.groupingBy(e -> e.getErpsku()));
        buildProductList(productChannelsVOS, listMap);

        List<Long> secondIdList = productVOS.stream().map(e -> e.getCategorySecondId()).distinct().collect(Collectors.toList());
        List<ScGoodsCategory> scGoodsCategories = scGoodsCategoryService.listByIds(secondIdList);
        if (CollectionUtils.isEmpty(scGoodsCategories)) {
            return;
        }
        List<ScGoodsCategoryVO> scGoodsCategoryVOS = new ArrayList<>();
        List<ScGoodsCategory> categoryList = scGoodsCategoryService.list();
        scGoodsCategories.stream().forEach(e->{
            String name = scGoodsCategoryService.buildCategoryLevelName(e.getCategoryId(), categoryList);
            ScGoodsCategoryVO goodsCategoryVO = BeanCopyUtils.copyBean(e, ScGoodsCategoryVO.class);
            goodsCategoryVO.setCategoryName(name);
            scGoodsCategoryVOS.add(goodsCategoryVO);
        });
        Map<Integer, List<ScGoodsCategoryVO>> goodsMap = scGoodsCategoryVOS.stream().collect(Collectors.groupingBy(e -> e.getCategoryId()));
        productVOS.stream().forEach(value->{
            if (null != value.getCategorySecondId()) {
                value.setScGoodsCategoryVOList(goodsMap.get(Integer.valueOf(String.valueOf(value.getCategorySecondId()))));
            }

        });
        //Map<String, List<ProductsVO>> valueMap = productVOS.stream().collect(Collectors.groupingBy(e -> e.getErpsku()));
        buildProductList(productChannelsVOS, listMap);
    }

    private static void buildProductList(List<ProductChannelsVO> productChannelsVOS, Map<String, List<ProductsVO>> listMap) {
        for (ProductChannelsVO channelsVO : productChannelsVOS) {
            if (StringUtils.isEmpty(channelsVO.getErpSku())) continue;
            List<String> splitList = Arrays.stream(channelsVO.getErpSku().split(",")).collect(Collectors.toList());
            List<ProductsVO> addList = new ArrayList<>();
            for (String sku : splitList) {
                List<ProductsVO> productsVOS = listMap.get(sku);
                if(CollectionUtils.isEmpty(productsVOS)) continue;
                addList.addAll(productsVOS);
            }
            List<ProductsVO> list = addList.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(
                            () -> new TreeSet<>(Comparator.comparing(e -> e.getErpsku()))), ArrayList::new));
            channelsVO.setProductsList(list);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChannelImportResultVO importProductChannels(MultipartFile file, Integer confirm, Integer contextId, UserEntity userEntity) throws IOException, CheckException {

        if (Objects.equals(confirm, 500)) {
            throw new CommonException("请调整ASIN对应不同SKU的数据后重新导入");
        }
        // 解析文件
        List<ProductChannels> imports = readFileSettingRowIndex(file, contextId);
        if (CollectionUtil.isEmpty(imports)) {
            throw new CommonException("未获取Excel数据");
        }
        log.error("#SKU映射导入数据：{}", imports);
        Map<String, Integer> configMap = labelConfigService.getConfigMapByModule(SysLabelConfigEnum.OPERATE_REQUIRED.getModule(), contextId);

        // 检查必填项
        checkRequire(contextId, imports, configMap);
        // 检查店铺
        checkAccount(imports, contextId);
        // 设置对应数据ID
        settingDbId(imports, contextId);
        // 设置字典和其他数据
        settingOtherData(imports, contextId);
        // 设置正确产品类型
        imports.forEach(c -> c.setType(ProductRelateTypeEnum.DESC_MAP.get(c.getTypeDict())));
        // 检查产品类型数量是否正确,简单产品sku数量不能大于一
        checkSingleAndBundle(imports);
        // 检查产品信息
        checkProducts(imports, contextId);
        // 设置运营信息
        checkAndSettingOperationUser(contextId, imports);
        ChannelImportResultVO vo = null;

        if (confirm < 0) {
//            saveOrUpdateProductChannels(imports, contextId, userEntity,configMap);
            saveOrUpdateProductChannelsWithMultipleVersionSku(contextId, imports, userEntity, true);
            return new ChannelImportResultVO();
        }


        // 检查在表格内存在对应不同的SKU
        if (Objects.equals(confirm, 0) && !(vo = checkAsinChannels(contextId, imports)).isSuccess()) {
            vo.setConfirm(500);
            return vo;
        }
        // 第一次确认检查 销售状态为在售审批状态为已通过的相同ASIN的数据
        Integer sameAsinApplied = configMap.get(SysLabelConfigEnum.ASIN_DATA_APPLICATION.getLabelConfig());
        if (!Objects.equals(sameAsinApplied, 1)) {
            // 如果没有开启配置，直接置为-1保存数据
            confirm = -1;
            vo = new ChannelImportResultVO(confirm);
        }


        if ((Objects.equals(confirm,0) || Objects.equals(confirm,1)) && !(vo = checkImportAsins(contextId, imports)).isSuccess()) {
            return vo;
        }

        // 存在如下相同ASIN的SellerSKU对应到不同的SKU及数量 或者 运营，是否以当前SKU及数量、运营 进行更新历史数据？
        if (Objects.equals(confirm, 2)) {
            // 添加历史数据更新信息
            vo = superadditionHistory(contextId, imports);
        }

        // 通过中间环节后直接保存
        if (confirm < 0 || (Objects.nonNull(vo) && Objects.nonNull(vo.getConfirm()) && vo.getConfirm() < 0)) {
//            saveOrUpdateProductChannels(imports, contextId, userEntity, true, configMap);
            saveOrUpdateProductChannelsWithMultipleVersionSku(contextId, imports, userEntity, true);

        }
        return new ChannelImportResultVO();
    }


    private void checkDuplicate(List<ProductChannels> imports) {
        List<ProductChannels> duplicateChannels = imports.stream()
                .collect(Collectors.groupingBy(c -> c.getAccountId() + c.getSellerSku()))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(duplicateChannels)) {
            String errorMsg = duplicateChannels.stream()
                    .map(c -> String.format("第 %s 行，存在相同店铺和SellerSku数据行", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

    }

    private ChannelImportResultVO superadditionHistory(Integer contextId, List<ProductChannels> imports) {
        ChannelImportResultVO result = checkImportAsins(contextId, imports);
        result.setConfirm(-1); // 设置结束标识
        if (CollectionUtil.isEmpty(result.getWarn())) {
            return result;
        }

        List<SkuAsinCheckVO> warn = result.getWarn();
        List<ProductChannels> productChannels = result.getDbSource();
        if (CollectionUtil.isEmpty(productChannels)) {
            return result;
        }
        Map<String, ProductChannels> channelsMap = imports.stream()
                .collect(Collectors.toMap(c -> c.getAsin() + c.getCountry(), Function.identity(), (a, b) -> a));
        if (CollectionUtil.isEmpty(channelsMap)) {
            return result;
        }
        // 添加到本次导入操作中
        for (ProductChannels channel : productChannels) {
            ProductChannels c = channelsMap.get(channel.getAsin() + channel.getCountry());
            channel.setErpSku(c.getErpSku());
            channel.setType(c.getType());
            channel.setQuantity(c.getQuantity());
            List<ProductChannelRelate> relates = BeanCopyUtils.copyBeanList(c.getProductRelates(), ProductChannelRelate.class);
            for (ProductChannelRelate relate : relates) {
                relate.setProductChannelId(channel.getId());
                relate.setId(null);
            }
            channel.setProductRelates(relates);
//            channel.setOperationUserId(c.getOperationUserId());
//            channel.setOperationUserName(c.getOperationUserName());
            if (Objects.equals(channel.getApprovalStatus(), ApprovalEnum.APPROVED.getValue())) {
                channel.setApprovalStatus(ApprovalEnum.SUBMIT.getValue());
            }
            // 如果是组合产品，添加明细列表
        }
        imports.addAll(productChannels);
        return result;
    }

    private ChannelImportResultVO checkImportAsins(Integer contextId, List<ProductChannels> imports){
        Map<String, Integer> configMap = labelConfigService.getConfigMapByModule(SysLabelConfigEnum.OPERATE_REQUIRED.getModule(), contextId);
        Integer config = configMap.get(SysLabelConfigEnum.ASIN_DATA_APPLICATION.getLabelConfig());
        if (Objects.equals(config, 1)) {
            return checkImportAsins(
                    ApprovalEnum.exclude(ApprovalEnum.SUBMIT),
                    contextId,
                    imports,
                    "存在如下相同ASIN的SellerSKU对应到不同的SKU及数量 或者 运营，是否以当前填写的SKU及数量 进行更新历史数据？"
            );
        }
        return new ChannelImportResultVO(-1);
    }

    private ChannelImportResultVO checkImportAsins(List<String> approvalStatus, Integer contextId, List<ProductChannels> imports, String message) {
        List<String> checkSaleChannels = Arrays.asList(
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.SC.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DF.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DI.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_USPO.getEhengjian()
        );

        List<ProductChannels> needCheck = imports.stream().filter(c -> checkSaleChannels.contains(c.getSaleChannel())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(needCheck)) {
            return new ChannelImportResultVO(-1);
        }

        // 获取当前ASIN对应SKU映射信息
        List<String> asins = needCheck.stream()
                .map(ProductChannels::getAsin)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(asins)) {
            return new ChannelImportResultVO(-1);
        }

        ProductChannels query = new ProductChannels();
//        query.setApprovalStatus(ApprovalEnum.APPROVED.getValue());
        query.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
        query.setOrgId(contextId);
        query.setAsinsQuery(asins);
        List<ProductChannels> asinChannels = productChannelsMapper.selectProductChannels(query);

        if (CollectionUtil.isEmpty(asinChannels)) {
            return new ChannelImportResultVO(-1);
        }
        asinChannels = asinChannels.stream()
                .filter(c -> approvalStatus.contains(c.getApprovalStatus()))
                .filter(c -> checkSaleChannels.contains(c.getSaleChannel()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(asinChannels)) {
            return new ChannelImportResultVO(-1);
        }

        List<ProductChannels> tmpList = Lists.newArrayList();
        List<ProductChannels> noReltes = imports.stream()
                .filter(c -> CollectionUtil.isEmpty(c.getProductRelates()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noReltes)) {
            tmpList.addAll(noReltes);
        }
        tmpList.addAll(asinChannels);
        // 设置关联信息
        productChannelRelateService.assignChannelRelates(tmpList);

        // 设置国家数据
        ArrayList<ProductChannels> allChannels = new ArrayList<>();
        allChannels.addAll(tmpList);
        allChannels.addAll(imports);
        settingCountry(contextId,allChannels);

        List<ProductChannels> insertData = imports.stream().filter(c -> Objects.isNull(c.getId())).collect(Collectors.toList());

        // 处理新增数据
        if (CollectionUtil.isNotEmpty(insertData)) {
            Map<String, List<ProductChannels>> countryGroup = insertData.stream().collect(Collectors.groupingBy(ProductChannels::getCountry));
            Map<String, List<ProductChannels>> asinCountryGroup = asinChannels.stream().collect(Collectors.groupingBy(ProductChannels::getCountry));
            for (Map.Entry<String, List<ProductChannels>> entry : countryGroup.entrySet()) {
                if (asinCountryGroup.containsKey(entry.getKey())) {
                    handleInsertChannels(asinCountryGroup.get(entry.getKey()), entry.getValue());
                }
            }
        }
        List<ProductChannels> updateData = imports.stream().filter(c -> Objects.nonNull(c.getId())).collect(Collectors.toList());


        List<Integer> updateIds = updateData.stream().map(ProductChannels::getId).collect(Collectors.toList());

        // 排除当前修改的数据
        asinChannels = asinChannels.stream().filter(c -> !updateIds.contains(c.getId())).collect(Collectors.toList());


        Map<String, List<ProductChannels>> asinGroup = needCheck.stream().collect(Collectors.groupingBy(ProductChannels::getAsin));
        Map<String, List<ProductChannels>> asinCountryGroup = asinChannels.stream().collect(Collectors.groupingBy(c -> c.getAsin() + c.getCountry()));

        ChannelImportResultVO result = new ChannelImportResultVO();

        for (Map.Entry<String, List<ProductChannels>> entry : asinGroup.entrySet()) {
            String asin = entry.getKey();
            List<ProductChannels> channels = entry.getValue();

            Map<String, List<ProductChannels>> countryGroup = channels.stream().collect(Collectors.groupingBy(ProductChannels::getCountry));

            for (Map.Entry<String, List<ProductChannels>> e : countryGroup.entrySet()) {

                if (!asinCountryGroup.containsKey(asin + e.getKey())) {
                    continue;
                }

                ChannelImportResultVO resultVO = checkSameAsin(e.getValue(), asinCountryGroup.get(asin + e.getKey()), channels);
                result.appendWarn(resultVO);
            }



        }
        // 数据去重
        result.setSuccess(CollectionUtil.isEmpty(result.getWarn()));
        if (!result.isSuccess()) {
            result.setMessage(message);

        }
        result.setConfirm(result.isSuccess() ? -1 : 2);
        return result;
    }

    private ChannelImportResultVO  checkSameAsin(List<ProductChannels> sameCountryChannels, List<ProductChannels> dbSameCountryChannels, List<ProductChannels> channels) {
        ChannelImportResultVO result = new ChannelImportResultVO();
        for (ProductChannels channel : sameCountryChannels) {
            for (ProductChannels db : dbSameCountryChannels) {
                checkTheSameAs(channel, db, result);
            }
        }
        return result;
    }

    private void settingCountry(Integer orgId,List<ProductChannels> list) {
        List<String> accountId = list.stream().map(ProductChannels::getAccountId).distinct().collect(Collectors.toList());
        List<Account> accounts = accountService.lambdaQuery()
                .eq(Account::getOrgId, orgId)
                .in(Account::getFlag, accountId)
                .list();

        if (CollectionUtil.isEmpty(accounts)) {
            throw new CommonException("未获取到店铺信息");
        }

        Map<String, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getFlag, Function.identity(), (v1, v2) -> v1));

        List<String> notFound = accountId.stream().filter(c -> !accountMap.containsKey(c)).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(notFound)) {
            throw new CommonException("店铺" + notFound + "不存在");
        }
        for (ProductChannels channels : list) {
            if (accountMap.containsKey(channels.getAccountId())) {
                channels.setCountry(accountMap.get(channels.getAccountId()).getCountryCode());
            }
        }
    }


    private void handleInsertChannels(List<ProductChannels> asinChannels, List<ProductChannels> insertData) {

        if (CollectionUtil.isEmpty(asinChannels)) {
            return;
        }

        // 获取SKU为空的数据
        List<ProductChannels> emptySkuChannels = insertData.stream()
                .filter(c -> StrUtil.isBlank(c.getErpSku()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(emptySkuChannels)) {
            return;
        }
        //
        Map<String, ProductChannels> channelsMap = emptySkuChannels.stream()
                .filter(c -> CollectionUtil.isNotEmpty(c.getProductRelates()))
                .collect(Collectors.toMap(c -> c.getAsin() + c.getSellerSku() + c.getAccountId(), Function.identity(), (a, b) -> a));

        if (CollectionUtil.isEmpty(channelsMap)) {
            return;
        }

        for (ProductChannels channel : asinChannels) {
            String key = channel.getAsin() + channel.getSellerSku() + channel.getAccountId();
            if (!channelsMap.containsKey(key)) {
                continue;
            }
            ProductChannels db = channelsMap.get(key);
            channel.setErpSku(db.getErpSku());
            channel.setQuantity(db.getQuantity());
            channel.setProductRelates(db.getProductRelates());
            channel.setType(db.getType());
        }



    }

    private static void checkTheSameAs(ProductChannels channels, ProductChannels dbChannels, ChannelImportResultVO result) {
        List<ProductChannelRelate> importRelates = CollectionUtil.isEmpty(channels.getProductRelates()) ? new ArrayList<>() : channels.getProductRelates();
        List<ProductChannelRelate> dbRelates = CollectionUtil.isEmpty(dbChannels.getProductRelates()) ? new ArrayList<>() : dbChannels.getProductRelates();
        List<SkuAsinCheckVO> warn = result.getWarn();
        Map<Integer, SkuAsinCheckVO> warnMap = warn.stream().collect(Collectors.toMap(SkuAsinCheckVO::getChannelId, Function.identity(), (a, b) -> a));
        boolean importEmptyAndDbHas = CollectionUtil.isEmpty(importRelates) && CollectionUtil.isNotEmpty(dbRelates);
        boolean importHasAndDbEmpty = CollectionUtil.isNotEmpty(importRelates) && CollectionUtil.isEmpty(dbRelates);
        if ((importEmptyAndDbHas || importHasAndDbEmpty) && !warnMap.containsKey(dbChannels.getId())) {
            result.getWarn().add(SkuAsinCheckVO.convert(dbChannels));
            result.getDbSource().add(dbChannels);
            return;
        }
        List<String> importMark = importRelates.stream().map(c -> c.getErpsku() + c.getQty()).collect(Collectors.toList());
        List<String> dbMark = dbRelates.stream().map(c -> c.getErpsku() + c.getQty()).collect(Collectors.toList());

        Collection<String> diff = CollectionUtil.disjunction(importMark, dbMark);
        if (CollectionUtil.isNotEmpty(diff) && !warnMap.containsKey(dbChannels.getId())) {
            result.getWarn().add(SkuAsinCheckVO.convert(dbChannels));
            result.getDbSource().add(dbChannels);
        }
    }




    private ChannelImportResultVO checkAsinChannels(Integer orgId,List<ProductChannels> channels) {
        settingCountry(orgId, channels);

        ChannelImportResultVO vo = new ChannelImportResultVO(-1);
        Map<String, List<ProductChannels>> asinSkuMap = channels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getAsin()))
                .collect(Collectors.groupingBy(c -> c.getAsin() + c.getCountry()));
        if (CollectionUtil.isEmpty(asinSkuMap)) {
            return vo;
        }

        List<ProductChannels> productChannels = new ArrayList<>();
        for (Map.Entry<String, List<ProductChannels>> entry : asinSkuMap.entrySet()) {
            List<ProductChannels> value = entry.getValue();
            Map<String, List<ProductChannels>> map = value.stream().collect(Collectors.groupingBy(r -> r.getErpSku() + r.getQuantityStr()));
            if (map.size() > 1) {
                productChannels.addAll(value);
            }

        }

        if (CollectionUtil.isEmpty(productChannels)) {
            return vo;
        }
        List<SkuAsinCheckVO> vos = productChannels.stream()
                .map(c -> {
                    SkuAsinCheckVO checkVO = new SkuAsinCheckVO();
                    checkVO.setSellerSku(c.getSellerSku());
                    checkVO.setAccountFlag(c.getAccountId());
                    checkVO.setChannelId(c.getId());
                    checkVO.setAsin(c.getAsin());
                    checkVO.setCountry(c.getCountry());
                    checkVO.setAccountTitle(c.getAccountTitle());
                    String erpSku = c.getErpSku();
                    String quantityStr = c.getQuantityStr();
                    String[] splitSku = erpSku.split(",");
                    String[] splitQty = quantityStr.split(",");
                    String erpSkuShow = "";
                    for (int i = 0; i < splitSku.length; i++) {
                        erpSkuShow += splitSku[i] + "*" + splitQty[i];
                        if (i != splitSku.length - 1) {
                            erpSkuShow += ",";
                        }
                    }
                    checkVO.setErpSku(erpSkuShow);
                    checkVO.setOperateUserName(c.getOperationUserName());
                    return checkVO;
                }).collect(Collectors.toList());
        // 设置当前处理标记
        vo.setWarn(vos);
        vo.setSuccess(false);
        vo.setMessage("以下ASIN在表格内存在相同国家对应到不同的SKU，请确认是否正确？");
        return vo;
    }

    private void settingOtherData(List<ProductChannels> imports, Integer contextId) {
        String sellStateDict = "sku_sale_status";
        String approvalDict = "approval_status";
        String typeDict = "sku_mapping_type";
        Map<String, List<SysDictData>> ditMap = sysDictTypeService.selectDictDataByTypes(Arrays.asList(sellStateDict, approvalDict, typeDict));
        Map<String,String> sellMap = ditMap.get(sellStateDict).stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (a, b) -> a));
        Map<String,String> approvalMap = ditMap.get(approvalDict).stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (a, b) -> a));
        Map<String,String> typeMap = ditMap.get(typeDict).stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (a, b) -> a));

        for (ProductChannels channels : imports) {

            channels.setSellStatus(sellMap.get(channels.getSellStatusDict()));
            if (StrUtil.isNotBlank(channels.getApprovalStatusDict())) {
                channels.setApprovalStatus(approvalMap.get(channels.getApprovalStatusDict()));
            }
            channels.setType(typeMap.get(channels.getTypeDict()));
            if (StrUtil.isNotBlank(channels.getIsSeedDict())) {
                channels.setIsSeed(Objects.equals("是", channels.getTypeDict()) ? "Y" : "N");
            }
            if (StrUtil.isNotBlank(channels.getIsVineDict())) {
                channels.setIsVine(Objects.equals("是", channels.getIsVineDict()) ? "Y" : "N");
            }
            if (StrUtil.isNotBlank(channels.getIsPushDict())) {
                channels.setIsPush(Objects.equals("是", channels.getIsPushDict()) ? "Y" : "N");
            }
        }


    }

    private static void checkAndSettingOperationUser(Integer contextId, List<ProductChannels> imports) {
        List<String> userNames = imports.stream()
                .map(ProductChannels::getOperationUserName)
                .map(StrUtil::trim)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(userNames)) {
            return;
        }

        List<UserEntity> userEntities = SpringUtils.getBean(UserService.class)
                .queryByNames(userNames, contextId);

        if (CollectionUtil.isEmpty(userEntities)) {
            throw new CommonException("运营信息均填写有误，请核对后重新录入");
        }
        userEntities = userEntities.stream()
                .filter(c -> c.getStatus().equals(1))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(userEntities)) {
            throw new CommonException("运营信息均填写有误，请核对后重新录入");
        }


        Map<String, Integer> map = userEntities.stream()
                .collect(Collectors.toMap(UserEntity::getName, UserEntity::getId));

        List<ProductChannels> errorOp = imports.stream()
                .filter(c -> StrUtil.isNotBlank(c.getOperationUserName()) && !map.containsKey(c.getOperationUserName()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(errorOp)) {

            String errorMsg = errorOp.stream()
                    .map(c -> String.format("第 %s 行，运营填写有误，请重新录入", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);

        }

        for (ProductChannels channel : imports) {
            if (StrUtil.isNotBlank(channel.getOperationUserName())) {
                channel.setOperationUserId(map.get(channel.getOperationUserName()));
            }
        }
    }

    private void checkProducts(List<ProductChannels> imports, Integer contextId) throws CheckException {
        // 查询是否开启多版本SKU控制
        boolean enableMultipleVersionSku = systemGlobalConfigService.isEnableConfig(contextId, SystemGlobalConfigEnum.SKU_VERSION_INIT_CONFIG);

        List<String> importSkus = imports.stream()
                .filter(c -> StrUtil.isNotBlank(c.getErpSku()))
                .map(ProductChannels::getErpSku)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(importSkus)) {
            return;
        }

        List<String> erpSkus = importSkus.stream()
                .map(c -> c.split(","))
                .flatMap(Stream::of)
                .collect(Collectors.toList());


        Map<String, SkuParents> parentsMap = new HashMap<>();
        Map<String, Products> productMap = new HashMap<>();


        List<SkuParents> skuParents = skuParentsService.selectByOrgIdAndSkus(contextId, erpSkus);
        if (CollectionUtil.isNotEmpty(skuParents)) {
            parentsMap.putAll(
                    skuParents.stream().collect(Collectors.toMap(SkuParents::getParentSku, Function.identity(), (a, b) -> a))
            );
        }


        List<Products> products = productsService.lambdaQuery().eq(Products::getOrgId, contextId).in(Products::getErpsku, erpSkus).list();


        if (CollectionUtil.isNotEmpty(products)) {
            productMap.putAll(
                    products.stream()
                            .collect(Collectors.toMap(Products::getErpsku, Function.identity(), (a, b) -> a))
            );

        }


        StringBuilder notfoundbuilder = new StringBuilder();
        StringBuilder notEnableBuilder = new StringBuilder();

        List<String> notParentSkus = new ArrayList<>();
        List<String> notSonSkus = new ArrayList<>();
        List<String> notFoundParentSkus = new ArrayList<>();

        for (ProductChannels channels : imports) {
            if (StrUtil.isBlank(channels.getErpSku())) {
                continue;
            }
            String erpSku = channels.getErpSku().trim();
            String[] erpSkuSplit = erpSku.split(",");

            channels.setIsParentSku(enableMultipleVersionSku ? 1 : 0);

            ProductParentCheckResult checkResult = checkMultipleVersionSku(enableMultipleVersionSku, parentsMap, productMap, channels);

            boolean skip = false;


            if (CollectionUtil.isNotEmpty(checkResult.getNotSonSku())) {
                notSonSkus.addAll(checkResult.getNotSonSku());
                skip = true;
            }

            if (CollectionUtil.isNotEmpty(checkResult.getNotParentSku())) {
//                notEnableBuilder.append(String.format("第 %s 行，SKU列中 %s 对应父SKU不存在 \n", channels.getRowIndex(), checkResult.getNotParentSku()));
                notParentSkus.addAll(checkResult.getNotParentSku());
                skip = true;
            }

            if (CollectionUtil.isNotEmpty(checkResult.getNotFoundParentSku())) {
                notFoundParentSkus.addAll(checkResult.getNotFoundParentSku());
//                notEnableBuilder.append(String.format("第 %s 行，SKU列中 %s 对应父SKU不存在 \n", channels.getRowIndex(), checkResult.getNotParentSku()));
                skip = true;
            }


            if (CollectionUtil.isNotEmpty(checkResult.getNotFoundSku())) {
                notfoundbuilder.append(String.format("第 %s 行，SKU列中 %s 填写错误 \n", channels.getRowIndex(), checkResult.getNotFoundSku()));
                skip = true;
            }
            if (CollectionUtil.isNotEmpty(checkResult.getNotEnableSku())) {
                notEnableBuilder.append(String.format("第 %s 行，SKU列中 %s 对应产品未启用 \n", channels.getRowIndex(), checkResult.getNotEnableSku()));
                skip = true;
            }


            if (skip) {
                continue;
            }


            String quantityStr = channels.getQuantityStr();
            String[] qs = null;
            if (StrUtil.isNotBlank(quantityStr)) {
                qs = quantityStr.split(",");
            }
            // 赋值主信息同步恒健ERP使用
            List<ProductChannelRelate> channelRelates = new ArrayList<>();

            Integer userId = UserUtils.getCurrentUserId();
            String userName = UserUtils.getCurrentUserName();

            try {
                for (int i = 0; i < erpSkuSplit.length; i++) {
                    ProductChannelRelate relate = new ProductChannelRelate();
                    relate.setErpsku(erpSkuSplit[i]);
                    relate.setCreatedBy(userId);
                    relate.setCreatedName(userName);
                    relate.setCreatedAt(DateUtils.getNowDate());
                    relate.setProductChannelId(channels.getId());
                    if (qs != null && qs.length > i) {
                        relate.setQty(Integer.parseInt(qs[i]));
                    }

                    if (Objects.equals(channels.getIsParentSku(), 1)) {
                        SkuParents parents = parentsMap.get(relate.getErpsku());
                        relate.setProductId(parents.getId());
                    }

                    if (productMap.containsKey(relate.getErpsku())) {
                        relate.setProductId(productMap.get(relate.getErpsku()).getId());
                    }
                    channelRelates.add(relate);
                }
            } catch (Exception e) {
                throw new CommonException("请核对数量输入格式是否正确！");
            }
            channels.setProductRelates(channelRelates);
        }
        notfoundbuilder.append(notEnableBuilder);

        if (CollectionUtil.isNotEmpty(notFoundParentSkus)) {
            String errorMsg = String.format("“多版本开关为开启状态，以下父SKU不存在:\n %s", String.join(",", notFoundParentSkus));
            throw new CheckException("多版本开关为开启状态，以下父SKU不存在", new MultipleVersionExceptionInfo(enableMultipleVersionSku, notFoundParentSkus));
        }
        if (CollectionUtil.isNotEmpty(notParentSkus)) {
            String errorMsg = String.format("“多版本开关为开启状态，以下SKU非父级SKU:\n %s ", String.join(",", notParentSkus));
            throw new CheckException("多版本开关为开启状态，以下SKU非父级SKU:", new MultipleVersionExceptionInfo(enableMultipleVersionSku, notParentSkus));
        }

        if (CollectionUtil.isNotEmpty(notSonSkus)) {
            String errorMsg = String.format("“多版本开关为关闭状态，以下SKU非子级SKU:\n %s ", String.join(",", notSonSkus));
            throw new CheckException("多版本开关为关闭状态，以下SKU非子级SKU:", new MultipleVersionExceptionInfo(enableMultipleVersionSku, notSonSkus));
        }

        if (notfoundbuilder.length() > 0) {
            throw new CommonException(notfoundbuilder.toString());
        }
    }


    private ProductParentCheckResult checkMultipleVersionSku(boolean enableMultipleVersionSku, Map<String, SkuParents> parentsMap, Map<String, Products> productMap, ProductChannels channels) {

        log.info("checkMultipleVersionSku - {}", JSON.toJSONString(parentsMap));


        ProductParentCheckResult result = new ProductParentCheckResult();

        channels.setIsParentSku(enableMultipleVersionSku ? 1 : 0);

        for (String sku : channels.getErpSku().split(",")) {
            Products p = productMap.get(sku);

            SkuParents parents = parentsMap.get(sku);

            if (enableMultipleVersionSku) {
                // 如果开启了多版本SKU映射，那么所有产品的SKU必须是父SKU
                if (Objects.nonNull(p) && Objects.equals(p.getProductType(), ProductTypeEnum.PRODUCT.getCode())) {
                    // 如果查询到SKU并且是产品
                    result.addNotParentSku(sku);
                    continue;
                }
                if (Objects.isNull(parents)) {
                    // 如果没查到，也不是父SKU
                    result.addNotFoundParentSku(sku);
                    continue;
                }
                channels.setProductId(parents.getId());
                continue;
            }

            if (Objects.nonNull(parents)) {
                result.addNotSonSku(sku);
                continue;
            }

            if (Objects.isNull(p)) {
                result.addNotFoundSku(sku);
                continue;
            }
            if (Objects.equals(p.getEnable(), 0)) {
                result.addNotEnableSku(sku);
                continue;
            }
            channels.setProductId(p.getId());
            // 设置项目ID
            if (Objects.equals(channels.getLineId(), 0)) {
                channels.setLineId(p.getLineId());
            }

        }
        return result;
    }

    private static void checkSingleAndBundle(List<ProductChannels> imports) {
        Map<String, List<ProductChannels>> typeMap = imports.stream()
                .filter(c -> StrUtil.isNotBlank(c.getType()))
                .collect(Collectors.groupingBy(ProductChannels::getType));

        if (typeMap.containsKey(ProductRelateTypeEnum.BUNDLE.getValue())) {
            List<ProductChannels> errorNumber = new ArrayList<>();
            List<ProductChannels> bundleChannels = typeMap.get(ProductRelateTypeEnum.BUNDLE.getValue());
            // 组合产品如果只有一个ErpSku时，数量必须大于1
            List<ProductChannels> errorBundle = new ArrayList<>();
            for (ProductChannels channel : bundleChannels) {
                String erpSku = channel.getErpSku();
                if (erpSku.split(",").length == 1) {
                    String quantityStr = channel.getQuantityStr();
                    if (quantityStr.contains(",")) {
                        String[] split = quantityStr.split(",");
                        List<String> notNumber = Arrays.stream(split)
                                .filter(c -> !NumberUtil.isNumber(c))
                                .collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(notNumber)) {
                            errorNumber.add(channel);
                        }
                        continue;
                    }
                    if (!NumberUtil.isNumber(quantityStr)) {
                        errorNumber.add(channel);
                        continue;
                    }
                    BigDecimal quantity = new BigDecimal(quantityStr);
                    if (BigDecimal.valueOf(2).compareTo(quantity) > 0) {
                        errorBundle.add(channel);
                    }
                }

            }

            if (CollectionUtil.isNotEmpty(errorNumber)) {
                String errrorMsg = errorNumber.stream()
                        .map(c -> String.format("第 %s 行，请检查数量输入格式", c.getRowIndex()))
                        .collect(Collectors.joining("\n"));
                throw new CommonException(errrorMsg);
            }

            if (CollectionUtil.isNotEmpty(errorBundle)) {
                String errrorMsg = errorBundle.stream()
                        .map(c -> String.format("第 %s 行，组合产品如果只有一个SKU则数量必须大于1", c.getRowIndex()))
                        .collect(Collectors.joining("\n"));
                throw new CommonException(errrorMsg);
            }
        }


        if (typeMap.containsKey(ProductRelateTypeEnum.SINGLE.getValue())) {
            List<ProductChannels> singleChannels = typeMap.get(ProductRelateTypeEnum.SINGLE.getValue());
            List<ProductChannels> errorTypes = singleChannels.stream()
                    .filter(c -> StrUtil.isBlank(c.getErpSku()) || c.getErpSku().contains(","))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(errorTypes)) {
                String errrorMsg = errorTypes.stream()
                        .map(c -> String.format("第 %s 行，简单产品的SKU不能为空且最多输入一个", c.getRowIndex()))
                        .collect(Collectors.joining("\n"));
                throw new CommonException(errrorMsg);

            }

            List<ProductChannels> errorQtys = singleChannels.stream()
                    .filter(c -> StrUtil.isBlank(c.getQuantityStr()) || c.getQuantityStr().contains(",") || !c.getQuantityStr().equals("1"))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(errorQtys)) {
                String errorMsg = errorQtys.stream()
                        .map(c -> String.format("第 %s 行,数量的输入格式，简单产品SKU数量只能为1，且只能存在一个SKU信息", c.getRowIndex()))
                        .collect(Collectors.joining("\n"));
                throw new CommonException(errorMsg);

            }

        }
    }

    @NotNull
    private List<ProductChannels> readFileSettingRowIndex(MultipartFile file,Integer contextId) throws IOException {
        List<ProductChannels> imports = new ArrayList<>();

        // 获取字典
        List<SysDictData> dictData = sysDictTypeService.selectDictDataByType("product_channel_tag");
        Map<String, String> dictMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(dictData)) {
            Map<String, String> map = dictData.stream()
                    .collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (v1, v2) -> v1));
            dictMap.putAll(map);
        }


        EasyExcel.read(file.getInputStream(), ProductChannelsImportVO.class, new AnalysisEventListener<ProductChannelsImportVO>() {
            @Override
            public void invoke(ProductChannelsImportVO channels, AnalysisContext analysisContext) {
                // 只读取第一个Sheet页
                ReadSheetHolder readSheetHolder = analysisContext.readSheetHolder();
                ReadSheet readSheet = readSheetHolder.getReadSheet();
                Integer sheetNo = readSheet.getSheetNo();
                if (sheetNo != 0) {
                    return;
                }


                if (StrUtil.isNotBlank(channels.getIsPushDict())) {
                    channels.setIsPush("是".equals(channels.getIsPushDict()) ? "Y" : "N");
                }
                channels.setOrgId(contextId);
                channels.setRowIndex(readSheetHolder.getRowIndex() + 1);
                ProductChannels importData = BeanCopyUtils.copyBean(channels, ProductChannels.class);
                importData.setImportData(Boolean.TRUE);

                if (StrUtil.isBlank(importData.getListingName())) {
                    importData.setListingName(null);
                }
                if (StrUtil.isBlank(importData.getFnsku())) {
                    importData.setFnsku(null);
                }
                if (StrUtil.isBlank(importData.getIsSeed())) {
                    importData.setIsSeed(null);
                }
                if (StrUtil.isBlank(importData.getIsVine())) {
                    importData.setIsVine(null);
                }
                if (StrUtil.isBlank(importData.getIsPush())) {
                    importData.setIsPush(null);
                }
                // 处理标签
                if (StrUtil.isNotBlank(channels.getTagLabels())) {
                    String tags = Arrays.stream(channels.getTagLabels().split(","))
                            .map(dictMap::get)
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.joining(","));
                    importData.setTags(tags);
                }

                imports.add(importData);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                System.out.println(123);
            }
        }).doReadAll();
        return imports;
    }

    private void settingDbId(List<ProductChannels> imports,Integer contextId) {
        List<String> sellerSkus = imports.stream()
                .map(ProductChannels::getSellerSku)
                .collect(Collectors.toList());
        List<ProductChannels> dbChannels = this.lambdaQuery()
                .in(ProductChannels::getSellerSku, sellerSkus)
                .eq(ProductChannels::getOrgId, contextId)
                .list();
        if (CollectionUtil.isEmpty(dbChannels)) {
            return;
        }
        Map<String, ProductChannels> map = dbChannels.stream()
                .collect(Collectors.toMap(c -> c.getSellerSku() + c.getAccountId(), Function.identity(), (a, b) -> a));


        List<ProductChannels> errorApprovalStatus = new ArrayList<>();
        for (ProductChannels channels : imports) {
            if (map.containsKey(channels.getSellerSku() + channels.getAccountId())) {
                ProductChannels dbChannel = map.get(channels.getSellerSku() + channels.getAccountId());
                if (Objects.equals(dbChannel.getApprovalStatus(), ApprovalEnum.SUBMIT.getValue())) {
                    errorApprovalStatus.add(channels);
                }
                channels.setId(dbChannel.getId());

                if (StrUtil.isBlank(channels.getAsin())) {
                    channels.setAsin(dbChannel.getAsin());
                }

                if (StrUtil.isBlank(channels.getAsin1())) {
                    channels.setAsin1(dbChannel.getAsin1());
                }

            }
        }
        if (CollectionUtil.isNotEmpty(errorApprovalStatus)) {
            String errorMsg = errorApprovalStatus.stream()
                    .map(c -> String.format("第 %s 行，SellerSku当前状态为待审批,不可修改", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }


    }

    private List<String> checkAccount(List<ProductChannels> imports,Integer contextId) {
        List<String> titles = imports.stream()
                .map(ProductChannels::getAccountId)
                .distinct()
                .collect(Collectors.toList());

        List<Account> accounts = accountService.lambdaQuery()
                .in(Account::getTitle, titles)
                .isNull(Account::getIsDelete)
                .eq(Account::getOrgId, contextId)
                .list();

        if (CollectionUtil.isEmpty(accounts)) {
            throw new CommonException("店铺名称全部录入有误，请重新核对后录入");
        }

        Map<String, List<Account>> titleMap = accounts.stream()
                .collect(Collectors.groupingBy(Account::getTitle));

        List<ProductChannels> errorTitles = imports.stream()
                .filter(c -> !titleMap.containsKey(c.getAccountId()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(errorTitles)) {
            String errorMsg = errorTitles.stream()
                    .map(c -> String.format("第 %s 行,店铺名称 %s 录入有误", c.getRowIndex(), c.getAccountId()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

        List<ProductChannels> repeatErrors = imports.stream().collect(Collectors.groupingBy(c -> c.getAccountId() + c.getSellerSku()))
                .entrySet().stream()
                .filter(c -> c.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .flatMap(List::stream)
                .collect(Collectors.toList());


        if (CollectionUtil.isNotEmpty(repeatErrors)) {
            String errorMsg = repeatErrors.stream()
                    .map(c -> String.format("第 %s 行,店铺 %s 下存在相同的 SellerSku", c.getRowIndex(), c.getAccountId()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

        // 设置店铺值
        for (ProductChannels channel : imports) {
            Account account = titleMap.get(channel.getAccountId()).get(0);
            channel.setAccountId(account.getFlag());
            channel.setSaleChannel(account.getType());
            channel.settingDBDefaultValue();
            channel.setAccountTitle(account.getTitle());

            if (StrUtil.isNotBlank(channel.getFnsku()) && (
                    SyncMappingEnum.SkuGoodsMapSaleChannelEnum.WALMART.getEhengjian().equalsIgnoreCase(account.getType()) ||
                            SyncMappingEnum.SkuGoodsMapSaleChannelEnum.WALMART_DSV.getEhengjian().equalsIgnoreCase(account.getType())
            )) {
                channel.setGtin(channel.getFnsku());
            }

        }
        return titles;
    }

    private void checkRequire(Integer contextId, List<ProductChannels> imports, Map<String, Integer> configMap) {
        // 检查SellerSku
        List<ProductChannels> errorSellerSku = imports.stream()
                .filter(c -> StrUtil.isBlank(c.getSellerSku()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(errorSellerSku)) {
            String errorMsg = errorSellerSku.stream()
                    .map(c -> String.format("第 %s 行，SellerSku必须填写", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

        // 检查店铺
        List<ProductChannels> errorAccount = imports.stream()
                .filter(c -> StrUtil.isBlank(c.getAccountId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(errorAccount)) {
            String errorMsg = errorAccount.stream()
                    .map(c -> String.format("第 %s 行，店铺必须填写", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

        // 检查销售状态
        List<ProductChannels> sellStatusError = imports.stream()
                .filter(c -> StrUtil.isBlank(c.getSellStatusDict()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(sellStatusError)) {
            String errorMsg = sellStatusError.stream()
                    .map(c -> String.format("第 %s 行，销售状态必须填写", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }


        Integer vineStatus = configMap.get(SysLabelConfigEnum.VINE_REQUIRED.getLabelConfig());


        if (Objects.equals(contextId, 1000049) && Objects.equals(vineStatus, 1)) {

            List<String> vineRequire = Arrays.asList(
                    SaleChannelEnum.AMAZON.getValue(), SaleChannelEnum.VCUSPO.getValue(),
                    SaleChannelEnum.VCDI.getValue(), SaleChannelEnum.VCDF.getValue()
            );
            List<ProductChannels> vineError = imports.stream()
                    .filter(c -> StrUtil.isBlank(c.getIsVineDict()) && vineRequire.contains(c.getSaleChannel()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(vineError)) {
                String errorMsg = vineError.stream()
                        .map(c -> String.format("第 %s 行，渠道 SC、VC-DF、VC-DI、VC-PO 是否Vine必须填写", c.getRowIndex()))
                        .collect(Collectors.joining("\n"));
                throw new CommonException(errorMsg);
            }
        }


        List<ProductChannels> errorErpsku = imports.stream()
                .filter(c -> StrUtil.isNotBlank(c.getTypeDict()) && (StrUtil.isBlank(c.getErpSku()) || StrUtil.isBlank(c.getQuantityStr())))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(errorErpsku)) {
            String errorMsg = errorErpsku.stream()
                    .map(c -> String.format("第 %s 行，产品类型选择后，SKU和数量必须填写", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

        // 检查产品类型*
        List<ProductChannels> typeError = imports.stream()
                .filter(c -> (StrUtil.isNotBlank(c.getErpSku()) || StrUtil.isNotBlank(c.getQuantityStr())) && StrUtil.isBlank(c.getTypeDict()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(typeError)) {
            String errorMsg = typeError.stream()
                    .map(c -> String.format("第 %s 行，SKU和数量填写后，产品类型必须选择", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

        List<ProductChannels> errorSkuAndQty = imports.stream()
                .filter(c -> (StrUtil.isNotBlank(c.getErpSku()) && StrUtil.isBlank(c.getQuantityStr())) || (StrUtil.isNotBlank(c.getQuantityStr()) && StrUtil.isBlank(c.getErpSku())))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(errorSkuAndQty)) {
            String errorMsg = errorSkuAndQty.stream()
                    .map(c -> String.format("第 %s 行，请检查SKU和数量信息", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

        List<ProductChannels> lengthCompareError = imports.stream()
                .filter(c -> StrUtil.isNotBlank(c.getErpSku()) && StrUtil.isNotBlank(c.getQuantityStr()))
                .filter(c -> c.getErpSku().split(",").length != c.getQuantityStr().split(",").length)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(lengthCompareError)) {
            String errorMsg = lengthCompareError.stream()
                    .map(c -> String.format("第 %s 行，SKU的输入个数和数量的输入个数不匹配", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

        // 检查运营
        // 获取配置
        Integer operateConfig = configMap.get(SysLabelConfigEnum.OPERATE_REQUIRED.getLabelConfig());
        if (Objects.equals(1, operateConfig)) {
            List<ProductChannels> opError = imports.stream()
                    .filter(c -> StrUtil.isBlank(c.getOperationUserName()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(opError)) {
                String errorMsg = opError.stream()
                        .map(c -> String.format("第 %s 行，运营必须填写", c.getRowIndex()))
                        .collect(Collectors.joining("\n"));
                throw new CommonException(errorMsg);
            }
        }

    }

    @Override
    public void syncData(Integer[] ids, Integer contextId) {
        List<ProductChannels> channels = this.listByIds(Arrays.asList(ids));
        if (CollectionUtil.isEmpty(channels)) {
            log.error("未获取到SKU映射信息");
            return;
        }
//        syncScGoodsSkuMap(contextId, channels);

    }

    @Override
    public void downLoadProductChannelsExcel(HttpServletResponse response, Integer contextId) throws IOException, IllegalAccessException {
        ProductChannels channels = new ProductChannels();
        channels.setSellerSku("SGLASDM153");
        channels.setSellStatus("在售");
        channels.setAccountId("office-US");
        channels.setApprovalStatusDict("待处理");
        channels.setSaleChannelDict("SC");
        channels.setTypeDict("组合产品");
        channels.setErpSku("D-THO-5524JPT-WT,D-THO-5524JPT-BK");
        channels.setQuantityStr("1,1");
        channels.setFnsku("itemlabel");
        channels.setAsin("SGASDA4156465");
        channels.setAsin1("GASDAS1532");
        channels.setBrand("brand");
        channels.setSellStatusDict("在售");
        channels.setIsVineDict("是");
        channels.setIsVineDict("是");
        channels.setUpc("upc");
        channels.setOperationUserName("coonie");
        channels.setDeptName("deptName");
        channels.setHandelingTime(3);
        channels.setIsPushDict("是");
        List<Map<String, String>> exportData = ExcelUtils.getExportData(Arrays.asList(channels));
        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");
        Map<String, List<SysDictData>> map = SpringUtils.getBean(ISysDictTypeService.class)
                .selectDictDataByTypes(Arrays.asList("approval_status", "account_sale_channel", "sku_sale_status", "sku_mapping_type"));
        if (true) {
//        if (map.containsKey("approval_status")) {
//            List<String> labels = map.get("approval_status").stream().map(SysDictData::getDictLabel).collect(Collectors.toList());
            List<String> labels = Arrays.asList("待处理", "退回");
            createDropDownList(sheet, ArrayUtil.toArray(labels, String.class), 1, 1000, 3, 3);
        }
        // 设置店铺名称下拉框
//        List<Account> accounts = accountService.lambdaQuery()
//                .select(Account::getTitle)
//                .eq(Account::getOrgId, contextId)
//                .list();
//        if (CollectionUtil.isNotEmpty(accounts)) {
//            List<String> titles = accounts.stream()
//                    .map(Account::getTitle)
//                    .distinct()
//                    .limit(10)
//                    .collect(Collectors.toList());
//            createDropDownList(sheet, ArrayUtil.toArray(titles, String.class), 1, 1000, 1, 1);
//        }
        if (map.containsKey("account_sale_channel")) {
            List<String> labels = map.get("account_sale_channel").stream().map(SysDictData::getDictLabel).collect(Collectors.toList());
            createDropDownList(sheet, ArrayUtil.toArray(labels, String.class), 1, 1000, 4, 4);
        }
        if (map.containsKey("sku_mapping_type")) {
            List<String> labels = map.get("sku_mapping_type").stream().map(SysDictData::getDictLabel).collect(Collectors.toList());
            createDropDownList(sheet, ArrayUtil.toArray(labels, String.class), 1, 1000, 5, 5);
        }
        if (map.containsKey("sku_sale_status")) {
            List<String> labels = map.get("sku_sale_status").stream().map(SysDictData::getDictLabel).collect(Collectors.toList());
            createDropDownList(sheet, ArrayUtil.toArray(labels, String.class), 1, 1000, 2, 2);
        }
//        ExcelWriter excelWriter = new ExcelWriter(workbook,"Sheet1");
        ExcelWriter excelWriter = new ExcelWriter(sheet);
        excelWriter.setDefaultRowHeight(15);
        excelWriter.setColumnWidth(-1, 25);
        excelWriter.write(exportData);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=skumap.xlsx");
        ServletOutputStream out = response.getOutputStream();
        excelWriter.flush(out);

        excelWriter.close();
    }

    @Override
    public ProductChannels selectProductChannelsById(Integer id, Integer contextId) {
        ProductChannels channels = this.getById(id);
        if (channels == null) {
            throw new CommonException("未查询到数据");
        }
        ConvertUtils.convert(channels);

        if (StrUtil.isNotBlank(channels.getTags())) {
            channels.setTagArr(
                    Stream.of(channels.getTags().split(","))
                            .map(Integer::parseInt)
                            .collect(Collectors.toList())
            );
        }

        if (CollectionUtil.isNotEmpty(channels.getApprovals())) {
            ConvertUtils.convert(channels.getApprovals());
        }

        if (StrUtil.isNotBlank(channels.getAccountId())) {
            Account account = accountService.lambdaQuery()
                    .eq(Account::getFlag, channels.getAccountId())
                    .eq(Account::getOrgId, channels.getOrgId())
                    .select(Account::getId, Account::getTitle, Account::getFlag)
                    .one();
            if (ObjectUtil.isNotNull(account)) {
                channels.setAccountTitle(account.getTitle());
            }
        }

        productChannelRelateService.assignChannelRelates(Collections.singletonList(channels));

        buildCategoryNameWithMultipleVersionSku(Arrays.asList(channels), null);

        // 设置项目数据
        productLineAssembly(contextId, channels);

        Integer operationUserId = channels.getOperationUserId();
        if (operationUserId != null) {
            List<UserDeptVO> deptVOS = null;
            try {
                deptVOS = ucPostService.selectUserAndDeptByUserIds(Arrays.asList(operationUserId));
            } catch (Exception e) {
                log.error("查询运营信息失败：{}", e.getMessage());
                e.printStackTrace();
            }
            if (CollectionUtil.isNotEmpty(deptVOS)) {
                channels.setDeptName(deptVOS.get(0).getDeptName());
                channels.setOperationUserName(deptVOS.get(0).getUserName());
            }
        }


        // 简单产品返回处理
//        if (ProductRelateTypeEnum.SINGLE.getValue().equals(channels.getType()) && CollectionUtil.isNotEmpty(products)) {
//            Map<String, Products> pMap = products.stream()
//                    .collect(Collectors.toMap(Products::getErpsku, Function.identity(), (a, b) -> a));
//            ProductChannelRelate relate = new ProductChannelRelate();
//            relate.setErpsku(channels.getErpSku());
//            relate.setQty(channels.getQuantity() == null ? 1 : channels.getQuantity().intValue());
//            relate.setLineName(channels.getLineName());
//            relate.setCategoryName(channels.getCategoryName());
//            if (pMap.containsKey(channels.getErpSku())) {
//                Products p = pMap.get(channels.getErpSku());
//                relate.setProductId(p.getId());
//            }
//            channels.setProductRelates(Arrays.asList(relate));
//        }
        return channels;
    }

    private void productLineAssembly(Integer orgId, ProductChannels channels) {
        if (StrUtil.isBlank(channels.getErpSku())) {
            return;
        }

        if (Objects.equals(channels.getIsParentSku(), 1)) {
            Map<String, List<SkuChildren>> childrenMap = childrenMapping(orgId, Arrays.asList(channels));
            childrenAssembly(channels, childrenMap);
        }


        List<String> erpSkus = Arrays.asList(channels.getErpSku().split(","));


        Map<String, List<String>> childrenSkuMap = new HashMap<>();

        if (Objects.equals(channels.getIsParentSku(), 1)) {
//            Map<String, List<SkuChildren>> childrenMap = childrenMapping(orgId, Arrays.asList(channels));
//            childrenAssembly(channels, childrenMap);
            List<ProductLineInfo> lineInfos = skuChildrenService.selectLineInfoByParentSkus(orgId, Arrays.asList(channels.getErpSku().split(",")));
            // 获取一个存在的项目信息
            if (CollectionUtil.isEmpty(lineInfos) || CollectionUtil.isEmpty(channels.getProductRelates())) {
                return;
            }

            if (Objects.nonNull(channels.getLineId())) {
                String lineName = lineInfos.stream().filter(c -> Objects.equals(c.getLineId(), channels.getLineId())).map(ProductLineInfo::getLineName).findFirst().orElse(null);
                channels.setLineName(lineName);
            }

            Map<String, ProductLineInfo> skuLineMap = lineInfos.stream().filter(c -> StrUtil.isNotBlank(c.getLineName()))
                    .collect(Collectors.toMap(ProductLineInfo::getParentSku, Function.identity(), (v1, v2) -> v1));


            channels.getProductRelates()
                    .stream().filter(c -> skuLineMap.containsKey(c.getErpsku()))
                    .forEach(c -> {
                        ProductLineInfo info = skuLineMap.get(c.getErpsku());
                        c.setLineId(info.getLineId());
                        c.setLineName(info.getLineName());
                    });
            return;
        }

        Map<String, Products> lineMap = new HashMap<>();

        List<Products> products = productsService.selectProductsByErpSkus(erpSkus);

        if (CollectionUtil.isNotEmpty(products)) {
            lineMap = products.stream()
                    .filter(p -> StrUtil.isNotBlank(p.getLineName()))
                    .collect(Collectors.toMap(Products::getErpsku, p -> p, (a, b) -> a));
        }

        Products p = erpSkus.stream().filter(lineMap::containsKey).map(lineMap::get).findAny().orElse(null);

        if (Objects.nonNull(p)) {
            channels.setLineName(p.getLineName());
        }
        if (CollectionUtil.isNotEmpty(channels.getProductRelates())) {
            List<ProductChannelRelate> relates = channels.getProductRelates();
            for (ProductChannelRelate relate : relates) {
                if (lineMap.containsKey(relate.getErpsku())) {
                    relate.setLineName(lineMap.get(relate.getErpsku()).getLineName());
                }
            }
//            buildCategoryName(null, relates, products, Arrays.asList(channels));
        }
//        buildCategoryName(products, channels);
    }


    @Deprecated
    private void buildCategoryName(List<Products> products, ProductChannels channels) {

        Map<String, Products> pMap = products.stream()
                .collect(Collectors.toMap(Products::getErpsku, Function.identity(), (a, b) -> a));

        productsService.settingProductCategory(products);


        if (ProductRelateTypeEnum.SINGLE.getValue().equals(channels.getType()) && CollectionUtil.isNotEmpty(products)) {
            ProductChannelRelate relate = new ProductChannelRelate();
            relate.setErpsku(channels.getErpSku());
            relate.setQty(channels.getQuantity() == null ? 1 : channels.getQuantity().intValue());
            relate.setLineName(channels.getLineName());
            relate.setCategoryName(channels.getCategoryName());
            if (pMap.containsKey(channels.getErpSku())) {
                Products p = pMap.get(channels.getErpSku());
                relate.setProductId(p.getId());
                relate.setLineName(p.getLineName());
                relate.setLineId(p.getLineId());
            }
            channels.setProductRelates(Collections.singletonList(relate));
        }


        //设置分类
        if (StrUtil.isNotBlank(channels.getErpSku())) {
            Arrays.stream(channels.getErpSku().split(","))
                    .forEach(c->{
                        Products p = pMap.get(c);
                        if (Objects.nonNull(p) && StrUtil.isNotBlank(p.getCategoryName())) {
                            channels.setCategoryName(p.getCategoryName());
                        }
                    });
        }


        // 设置明细分类
        if (CollectionUtil.isEmpty(channels.getProductRelates())) {
            return;
        }


        for (ProductChannelRelate relate : channels.getProductRelates()) {
            Products p = pMap.get(relate.getErpsku());
            if (Objects.nonNull(p) && StrUtil.isNotBlank(p.getCategoryName())) {
                relate.setCategoryName(p.getCategoryName());
            }

        }
    }




    private void settingSkuCategory(Integer contextId, ProductChannels channels) {

        productChannelRelateService.assignChannelRelates(Arrays.asList(channels));

        // 补充分类名称信息
        buildCategoryNameWithMultipleVersionSku(Arrays.asList(channels), null);
    }

    @Override
    @Transactional
    public void deleteProductChannels(Integer[] ids) {
        List<ProductChannels> channels = listByIds(Arrays.asList(ids));

        if (CollectionUtil.isNotEmpty(channels)) {

            this.removeByIds(Arrays.asList(ids));

            productChannelRelateService.lambdaUpdate()
                    .in(ProductChannelRelate::getProductChannelId, ids)
                    .remove();

//            if (CollectionUtil.isNotEmpty(channels)) {
//                threadPoolTaskExecutor.execute(() -> {
//                    List<String> sellerSkus = channels.stream()
//                            .map(ProductChannels::getSellerSku)
//                            .collect(Collectors.toList());
//                    HttpResponse response = HttpUtil.createPost(crmApiUrl + "/prod-api/api/public/skuGoodsMap/deleteScGoodsSkuMap")
//                            .body(JSON.toJSONString(sellerSkus))
//                            .execute();
//                    if (!response.isOk()) {
//                        log.error("恒健ERP-SKU映射信息删除失败:{}", response.body());
//                        throw new CommonException("恒健ERP-SKU映射信息删除失败:" + response.body());
//                    }
//                    log.error("恒健ERP-SKU映射信息删除成功");
//                });
//            }
        }


    }

    @Override
    @Transactional
    public void saveProductChannels(String message, String type) {
        saveProductChannelsQueueData(message);

    }

    @Override
    @Transactional
    public void allotOperation(ApprovalDTO dto) {
        if (CollectionUtil.isEmpty(dto.getIds())) {
            throw new CommonException("请传入数据ID");
        }
        if (dto.getOperationUserId() == null || dto.getOperationUserName() == null) {
            throw new CommonException("运营ID和运营名称不能为空！");
        }

        List<ProductChannels> productChannels = this.listByIds(dto.getIds());
        if (CollectionUtil.isEmpty(productChannels)) {
            throw new CommonException("未获取到对应SKU映射信息数据！");
        }

        log.info("批量分配负责人参数 - {}", JSON.toJSONString(dto));

        List<ProductChannels> copyBeans = BeanCopyUtils.copyBeanList(productChannels, ProductChannels.class);

        this.lambdaUpdate()
                .set(ProductChannels::getOperationUserId, dto.getOperationUserId())
                .set(ProductChannels::getOperationUserName, dto.getOperationUserName())
                .in(ProductChannels::getId, dto.getIds())
                .update();
        for (ProductChannels channel : productChannels) {
            channel.setOperationUserId(Integer.parseInt(dto.getOperationUserId()));
            channel.setOperationUserName(dto.getOperationUserName());
        }

        // 从表更新
        productChannelsSlaveService.handleSlaves(productChannels);


        try {
            Map<Integer, ProductChannels> copyMap = copyBeans.stream().collect(Collectors.toMap(ProductChannels::getId, Function.identity(), (v1, v2) -> v1));

            for (ProductChannels channel : productChannels) {
                ProductChannels copyBean = copyMap.get(channel.getId());
                if (Objects.nonNull(copyBean)) {
                    erpOperateLogService.logRecord(copyBean, channel, "SKU映射", true, false, "operationUserId", "operationUserName");
                }
            }

        } catch (Exception e) {
            log.error("SKU映射运营分配日志记录失败：{}", e.getMessage());
        }

        // 从表更新
        productChannelsSlaveService.handleSlaves(productChannels);

//        syncProductChannels(productChannels, 1000);


    }

    @Override
    public void updateSaleState(ApprovalDTO approvalDTO, Integer contextId) {
        if (approvalDTO.getSaleState() == null) {
            throw new CommonException("销售状态不能为空");
        }
        if (CollectionUtil.isEmpty(approvalDTO.getIds())) {
            throw new CommonException("请传入数据ID");
        }
        List<Integer> ids = approvalDTO.getIds();

        List<ProductChannels> dbChannels = this.lambdaQuery()
                .in(ProductChannels::getId, approvalDTO.getIds())
                .list();
        List<ProductChannels> copyBeans = BeanCopyUtils.copyBeanList(dbChannels, ProductChannels.class);

        dbChannels.forEach(e -> e.setSellStatus(approvalDTO.getSaleState()));

        this.lambdaUpdate()
                .set(ProductChannels::getSellStatus, approvalDTO.getSaleState())
                .in(ProductChannels::getId, ids)
                .update();
        try {
            erpOperateLogService.logRecord(copyBeans, dbChannels, "id", "SKU映射", true, false, "sellStatus");
        } catch (Exception e) {
            log.error("日志记录失败：{}", e.getMessage());
        }

        // 同步恒健ERP
//        List<ProductChannels> channels = listByIds(ids);
//        threadPoolTaskExecutor.execute(()->{
//            syncProductChannels(channels, contextId);
//        });


    }

    @Override
    public void syncProductChannels(List<ProductChannels> channels, Integer contextId) {
//        threadPoolTaskExecutor.execute(()->{
//            syncScGoodsSkuMap(contextId, channels);
//        });
    }

    @Override
    @Transactional
    public void updateAsinInfo(List<Integer> ids) {
        List<ProductChannels> channels = ids == null ? list() : listByIds(ids);
        if (CollectionUtil.isEmpty(channels)) {
            return;
        }
        List<ProductChannels> productChannels = channels.stream()
                .filter(item -> StrUtil.isNotBlank(item.getAccountId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(productChannels)) {
            return;
        }
        updateProductChannelsInfoWithTransactional(productChannels, true, 0, null);

        try {
            if (CollectionUtil.isNotEmpty(ids)) {
                pushToMultichannel(listByIds(ids));
            }
        } catch (Exception e) {
            log.info("数据推送多渠道异常 - {} - ", e);
        }

    }

    @Transactional
    public void updateProductChannelsInfoWithTransactional(List<ProductChannels> productChannels, boolean recordLog,long time, TimeUnit timeUnit) {
        // 获取店铺信息
        List<String> flags = productChannels.stream()
                .filter(e -> StrUtil.isNotBlank(e.getAccountId()))
                .map(ProductChannels::getAccountId)
                .distinct().collect(Collectors.toList());
        List<Account> accounts = accountService.lambdaQuery()
                .in(Account::getFlag, flags)
                .list();
        if (CollectionUtil.isEmpty(accounts)) {
            return;

        }
        Date nowDate = DateUtils.getNowDate();
        AmazonApi amazonApi = SpringUtils.getBean(AmazonApi.class);
        Map<String, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getFlag, Function.identity(), (a, b) -> a));

        Map<String, List<ProductChannels>> map = productChannels.stream()
                .collect(Collectors.groupingBy(ProductChannels::getAccountId));


        Map<String, String> notifyMap = new HashMap<>();

        for (Map.Entry<String, List<ProductChannels>> entry : map.entrySet()) {
            if (!accountMap.containsKey(entry.getKey())) {

                continue;
            }

            Account account = accountMap.get(entry.getKey());

            List<ProductChannels> channelsList = entry.getValue();

            List<List<ProductChannels>> splitChannels = CollectionUtil.split(channelsList, 20);
            for (List<ProductChannels> channels : splitChannels) {
                List<String> currentSellerSkus = channels.stream().map(ProductChannels::getSellerSku).collect(Collectors.toList());
                ItemSearchResults searchResults = null;
                try {
//                    if (time > 0 && timeUnit != null) {
//                        timeUnit.sleep(time);
//                    }
                    searchResults = amazonApi.searchCatalogItems(currentSellerSkus, accountMap.get(entry.getKey()));
                } catch (Exception e) {
                    log.error("UPDATE_ASIN 获取SellerSku信息失败：{} , 原因：{}", currentSellerSkus, e.getMessage());
                }
                if (Objects.isNull(searchResults) || CollectionUtil.isEmpty(searchResults.getItems())) {
                    log.error("UPDATE_ASIN 接口获取数据为空：{}", currentSellerSkus);
                    continue;
                }
                try {
                    handleCatelogMeta(accountMap, channels, searchResults, recordLog,notifyMap);
                } catch (Exception e) {
                    log.error("UPDATE_ASIN ASIN信息更新异常，SKU:{}  数据：{}  异常信息：{}", currentSellerSkus, JSON.toJSONString(searchResults), e.getMessage());
                }

                // 更新排名信息
                try {
                    if (!Objects.equals(account.getCountryCode(), "DE")) {
                        handleRank(account,searchResults);
                    }
                } catch (Exception e) {
                    log.error("UPDATE_ASIN ASIN信息更新异常：{}", e.getMessage());
                }
            }


        }
    }

    private void handleRank(Account account,ItemSearchResults searchResults) {
        List<Item> items = searchResults.getItems();
        if (CollectionUtil.isEmpty(items)) {
            return;
        }

        items = items.stream()
                .filter(c -> CollectionUtil.isNotEmpty(c.getSalesRanks()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(items)) {
            log.info("无需更新Rank信息，SalesRank 为空");
            return;
        }

        items = items.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(Item::getAsin))), ArrayList::new)
                );

        List<String> asins = items.stream()
                .map(Item::getAsin)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(asins)) {
            return;
        }

        List<MarListingInfo> marListingInfos = marListingInfoService.lambdaQuery()
                .in(MarListingInfo::getAsin, asins)
                .eq(MarListingInfo::getCountryCode, account.getCountryCode())
                .list();

        if (CollectionUtil.isEmpty(marListingInfos)) {
            log.error("未获取到Listing信息：{}", asins);
            return;
        }

        Map<String, Item> map = items.stream()
                .collect(Collectors.toMap(Item::getAsin, Function.identity(), (a, b) -> a));

        List<MarListingInfo> updateData = new ArrayList<>();
        for (MarListingInfo info : marListingInfos) {
            boolean updateFlag = false;
            if (!map.containsKey(info.getAsin())) {
                continue;
            }
            ItemSalesRanksByMarketplace saleRank = map.get(info.getAsin()).getSalesRanks().get(0);

            List<ItemDisplayGroupSalesRank> displayGroupRanks = saleRank.getDisplayGroupRanks();
            List<ItemClassificationSalesRank> classificationRanks = saleRank.getClassificationRanks();

            if (CollectionUtil.isNotEmpty(displayGroupRanks)) {
                updateFlag = true;
                ItemDisplayGroupSalesRank salesRankType1 = displayGroupRanks.get(0);
                info.setMainCategory(salesRankType1.getTitle());
                info.setMainCategoryRank(salesRankType1.getRank() == null ? "0" : String.valueOf(salesRankType1.getRank()));
            }

            if (CollectionUtil.isNotEmpty(classificationRanks)) {
                updateFlag = true;
                ItemClassificationSalesRank salesRankType2 = classificationRanks.get(0);
                info.setSubCategory(salesRankType2.getTitle());
                info.setSubCategoryRank(salesRankType2.getRank() == null ? "0" : String.valueOf(salesRankType2.getRank()));
            }
            if (updateFlag) {
                updateData.add(info);
            }
        }
        if (CollectionUtil.isNotEmpty(updateData)) {
            log.info("更新排名信息：{}", JSON.toJSONString(updateData));
            marListingInfoService.updateBatchById(updateData);
        }

//        Date date = new Date();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//
//        List<MarListingInfoHis> listingInfoHis = marListingInfoHisMapper.selectByAsinsAndDate(asins, sdf.format(date) + " 00:00:00",sdf.format(date) + " 23:59:59");
//        if (CollectionUtil.isEmpty(listingInfoHis)) {
//            return;
//        }
//        List<MarListingInfoHis> hisUpdate = new ArrayList<>();
//        for (MarListingInfoHis his : listingInfoHis) {
//            boolean updateFlag = false;
//            if (!map.containsKey(his.getAsin())) {
//                continue;
//            }
//            ItemSalesRanksByMarketplace saleRank = map.get(his.getAsin()).getSalesRanks().get(0);
//
//            List<ItemDisplayGroupSalesRank> displayGroupRanks = saleRank.getDisplayGroupRanks();
//            List<ItemClassificationSalesRank> classificationRanks = saleRank.getClassificationRanks();
//
//            if (CollectionUtil.isNotEmpty(displayGroupRanks)) {
//                updateFlag = true;
//                ItemDisplayGroupSalesRank salesRankType1 = displayGroupRanks.get(0);
//                his.setMainCategory(salesRankType1.getTitle());
//                his.setMainCategoryRank(salesRankType1.getRank() == null ? "0" : String.valueOf(salesRankType1.getRank()));
//            }
//
//            if (CollectionUtil.isNotEmpty(classificationRanks)) {
//                updateFlag = true;
//                ItemClassificationSalesRank salesRankType2 = classificationRanks.get(0);
//                his.setSubCategory(salesRankType2.getTitle());
//                his.setSubCategoryRank(salesRankType2.getRank() == null ? "0" : String.valueOf(salesRankType2.getRank()));
//            }
//            if (updateFlag) {
//                hisUpdate.add(his);
//            }
//
//        }
//
//        if (CollectionUtil.isNotEmpty(hisUpdate)) {
//            for (MarListingInfoHis his : hisUpdate) {
//                log.info("更新Listing历史信息：{}", his.getAsin());
//                marListingInfoHisMapper.updateById(his);
//            }
//        }

    }

    private void handleCatelogMeta(Map<String, Account> accountMap, List<ProductChannels> channels, ItemSearchResults searchResults, boolean recordLog, Map<String, String> notifyMap) {
        Date nowDate = DateUtils.getNowDate();
        String userName = "";
        Integer userId = 0;
        try {
            AuthUserDetails userDetails = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
            userName = userDetails.getName();
            userId = userDetails.getId();
        } catch (Exception e) {
            log.error("获取当前登录用户失败！");
        }
        List<ProductChannelsHistory> histories = new ArrayList<>();
        for (ProductChannels channel : channels) {
            ProductChannels copyBean = BeanCopyUtils.copyBean(channel, ProductChannels.class);

            LambdaUpdateChainWrapper<ProductChannels> updateChainWrapper = this.lambdaUpdate()
                    .eq(ProductChannels::getId, channel.getId())
                    .set(ProductChannels::getIsInterfaceData, true)
                    .set(ProductChannels::getUpdatedName, userName)
                    .set(ProductChannels::getUpdatedAt, nowDate);
            AccountsSellerResponse response = convertResponse(channel.getOrgId(), searchResults, channel.getSellerSku());

            if (response == null) {
                continue;
            }
            if (StrUtil.isNotBlank(response.getAsin())) {
                channel.setAsin(response.getAsin());
            }
            channel.setSource(true);
            updateChainWrapper.set(ProductChannels::getAsin, response.getAsin());
            updateChainWrapper.set(ProductChannels::getAsin1, response.getParentAsin() == null ? StrUtil.EMPTY : response.getParentAsin());
            log.info("父ASIN获取 - SKU:{} ASIN:{} ParentAsin:{}", channel.getSellerSku(), channel.getAsin(), response.getParentAsin());
            if (StrUtil.isNotBlank(response.getParentAsin())) {
                String sourceParentAsin = channel.getAsin1();
                channel.setSource(true);
                channel.setApiParentAsin(response.getParentAsin());
                if (!String.valueOf(sourceParentAsin).equalsIgnoreCase(String.valueOf(response.getParentAsin()))) {
                    // 父ASIN发生改变记录历史信息
                    ProductChannelsHistory history = BeanCopyUtils.copyBean(channel, ProductChannelsHistory.class);
                    history.setSourceId(channel.getId());
                    history.setId(null);
                    histories.add(history);
                    threadPoolTaskExecutor.execute(() -> {
                        ScGoodsSkuMap map = new ScGoodsSkuMap();
                        map.setShopName(accountMap.get(channel.getAccountId()).getAccountInit());
                        map.setSellerSku(channel.getSellerSku());
                        map.setAsin(channel.getAsin());
                        map.setParentAsin(response.getParentAsin());
                        map.setSaleChannel(
                                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.hengjianErpChannel(channel.getSaleChannel())
                        );
                        this.updateScGoodsSkuMapAsin(map);
                    });
                }
                channel.setAsin1(response.getParentAsin());
            }


            try {
                if (Objects.equals(NOTIFY_OPERATE, "Y") && StrUtil.isNotBlank(copyBean.getAsin1()) && StrUtil.isBlank(response.getParentAsin())) {
//                    // TODO 父ASIN变化为空，通知运营
//                    if (StrUtil.isBlank(channel.getRemark())) {
//                        notifyToUser(copyBean);
//
//                    } else {
//                        if (!redisTemplate.hasKey(channel.getRemark())) {
//                            notifyToUser(copyBean);
//                            redisTemplate.opsForValue().set(channel.getRemark(), 1, 2, TimeUnit.HOURS);
//                        }
//
//                    }
                }
            } catch (Exception e) {
                log.info("更新ASIN信息- 运营通知失败:{}", channel.getSellerSku(), e);
            }


            if (StrUtil.isNotBlank(response.getBrand())) {
                channel.setBrand(response.getBrand());
            }
            updateChainWrapper.set(ProductChannels::getBrand, response.getBrand());

            List<AccountsSellerResponse.IdentifierItem> identifiers = response.getIdentifiers();
            if (CollectionUtil.isNotEmpty(identifiers)) {
                for (AccountsSellerResponse.IdentifierItem identifier : identifiers) {
                    if (identifier.getIdentifierType().equalsIgnoreCase("EAN")) {
                        channel.setEan(identifier.getIdentifier());
                        updateChainWrapper.set(ProductChannels::getEan, identifier.getIdentifier());
                    }
                    if (identifier.getIdentifierType().equalsIgnoreCase("UPC")) {
                        channel.setUpc(identifier.getIdentifier());
                        updateChainWrapper.set(ProductChannels::getUpc, identifier.getIdentifier());
                    }
                    if (identifier.getIdentifierType().equalsIgnoreCase("GTIN")) {
                        channel.setGtin(identifier.getIdentifier());
                        updateChainWrapper.set(ProductChannels::getGtin, identifier.getIdentifier());
                    }
                }
            } else {
                updateChainWrapper.set(ProductChannels::getGtin, null);
                updateChainWrapper.set(ProductChannels::getUpc, null);
                updateChainWrapper.set(ProductChannels::getEan, null);
            }
            AccountsSellerResponse.Image meta = response.getMeta();
            if (Objects.nonNull(meta)) {
                channel.setMeta(JSON.toJSONString(meta));
                List<AccountsSellerResponse.ImageItem> items = meta.getItems();
                if (CollectionUtil.isNotEmpty(items)) {
                    AccountsSellerResponse.ImageItem imageItem = items.stream()
                            .filter(AccountsSellerResponse.ImageItem::getMain)
                            .findAny().orElse(null);
                    channel.setImageUrl(Objects.nonNull(imageItem) ? imageItem.getAttachUrl() : null);
                    updateChainWrapper.set(ProductChannels::getImageUrl, channel.getImageUrl());
                    updateChainWrapper.set(ProductChannels::getMeta, channel.getMeta());
                }
            } else {
                updateChainWrapper.set(ProductChannels::getImageUrl, null);
                updateChainWrapper.set(ProductChannels::getMeta, null);
            }
            // 执行更新
            updateChainWrapper.update();

            if (recordLog) {
                try {
                    erpOperateLogService.logRecord(copyBean,
                            channel,
                            "SKU映射",
                            false,
                            true,
                            "meta"
                    );
                } catch (Exception e) {
                    log.error("SKU同步任务日志记录失败：{}", e.getMessage());
                }
            }
        }
        if (CollectionUtil.isNotEmpty(histories)) {
            productChannelsHistoryService.saveBatch(histories);
        }
    }

    private void notifyToUser(ProductChannels copyBean) {

        if (!Objects.equals(copyBean.getOrgId(), 1000049)) {
            return;
        }

        // 如果原来存在父ASIN，则通知运营
        String message = "ASIN: " + copyBean.getAsin() + ",SKU:" + copyBean.getErpSku() + " ,其父ASIN从 " + copyBean.getAsin1() + " 变成无，请确认是否异常";
        // 获取对应运营信息
        Integer operationUserId = copyBean.getOperationUserId();
        if (Objects.isNull(operationUserId)) {
            log.error("UPDATE_ASIN 当前SKU映射信息运营为空：{}", copyBean.getSellerSku());
            return;
        }
        List<UserEntity> users = userService.findByIds(Collections.singletonList(operationUserId));
        if (CollectionUtil.isEmpty(users)) {
            log.error("UPDATE_ASIN 未获取到运营信息：{} {}", copyBean.getSellerSku(), operationUserId);
            return;
        }
        UserEntity user = users.get(0);
        String phone = user.getPhone();
        if (StrUtil.isBlank(phone) || !Objects.equals(user.getStatus(),1)) {
            log.error("UPDATE_ASIN 当前运营尚未配置手机号：{} {}", copyBean.getSellerSku(), user.getName());
            return;
        }
        QywxUtil.wxMessageSend(message , phone);
        log.error("UPDATE_ASIN 父ASIN变化为空，已通知运营 :{}-{}  {}  {} ", user.getName(), phone, copyBean.getSellerSku(), copyBean.getAsin());
    }

    public void updateProductChannelsInfoNoTransactional(List<ProductChannels> productChannels, boolean recordLog,long time,TimeUnit timeUnit) {
        updateProductChannelsInfoWithTransactional(productChannels, recordLog, time, timeUnit);
    }

    @Override
    public void updateReturnAsinInfo(String message) {
        ProductChannelReturnVO vo = JSON.parseObject(message, ProductChannelReturnVO.class);
        String asin = vo.getAsin();
        List<ProductChannels> channels = this.lambdaQuery()
                .eq(ProductChannels::getAsin, asin)
                .list();
        if (CollectionUtil.isEmpty(channels)) {
            log.error("未获取到asin对应的sku映射信息：{}", asin);
            return;
        }
        for (ProductChannels channel : channels) {
            channel.setAsin1(vo.getParentAsins());
            channel.setAccountId(vo.getChannelFlag());
            channel.setUpc(vo.getUpc());
            channel.setImageUrl(vo.getMainImageLink());
            channel.setBrand(vo.getBrand());
        }
        this.updateBatchById(channels);
    }


    @Override
    public String exportProductChannelAsyncDiscard(String json) {
        ProductChannelExportDTO exportDTO = JSON.parseObject(json, ProductChannelExportDTO.class);

        ProductChannels query = exportDTO.getChannels();
        if (Objects.isNull(query)) {
            query = new ProductChannels();
        }
        List<Integer> ids = exportDTO.getIds();

        if (CollectionUtil.isNotEmpty(ids)) {
            query.setQueryIds(ids);
        }
        query.setOrgId(exportDTO.getContextId());
        query.settingQueryParam();
        approvalNodeQuerySetting(query);
        // 流式查询导出
        String fileName = filePath + "skumap-" + System.currentTimeMillis() + ".xlsx";
        ProductChannelsResultHandler handler = null;
        if (StrUtil.isNotBlank(PRODUCT_CHANNEL_EXPORT_BATCH)) {
            handler = new ProductChannelsResultHandler(exportDTO.getContextId(),Integer.parseInt(PRODUCT_CHANNEL_EXPORT_BATCH), fileName);
        } else {
            handler = new ProductChannelsResultHandler(exportDTO.getContextId(), fileName);
        }
        productChannelsMapper.streamQueryProductChannels(handler, query);
        File file = handler.getFile();
        String uploadFile = AliyunOssClientUtil.uploadFile(file.getName(), FileUtil.getInputStream(file), "erp/productChannels/export");
        try {
            file.delete();
        } catch (Exception e) {
            log.info("SKU映射导出文件删除失败 - {}", fileName);
        }
        return uploadFile;
    }

    public String exportProductChannelAsync(String json) {
        String uuid = UUID.randomUUID().toString();
        log.info("SKU映射异步导出 - {} - 参数 - {}", uuid, json);
        ProductChannelExportDTO exportDTO = JSON.parseObject(json, ProductChannelExportDTO.class);
        long start = System.currentTimeMillis();
        exportDTO.setUuid(uuid);
        return exportProductChannelAsync(exportDTO);
    }

    @Override
    public String exportProductChannelAsync(ProductChannelExportDTO exportDTO) {

        String uuid = StrUtil.isBlank(exportDTO.getUuid()) ? UUID.randomUUID().toString() : exportDTO.getUuid();

        long start = System.currentTimeMillis();

        ProductChannelsExportQuery query = exportDTO.getChannels();
        if (Objects.isNull(query)) {
            query = new ProductChannelsExportQuery();
        }
        List<Integer> ids = exportDTO.getIds();

        if (CollectionUtil.isNotEmpty(ids)) {
            query.setQueryIds(ids);
        }
        query.setOrgId(exportDTO.getContextId());
        query.settingQueryParam();
        approvalNodeQuerySetting(query);


        int page = 1;
        int batch = 4000;
        if (StrUtil.isNotBlank(PRODUCT_CHANNEL_EXPORT_BATCH)) {
            batch = Integer.parseInt(PRODUCT_CHANNEL_EXPORT_BATCH);
        }
        List<ProductChannelsExportVO> exportData = null;
        query.setLimitEnd(batch);
        query.modifyPageStart(page++);

        Map<String, String> dictMap = new HashMap<>();


        List<SysDictData> dictData = sysDictTypeService.selectDictDataByType("product_channel_tag");
        if (CollectionUtil.isNotEmpty(dictData)) {
            Map<String, String> map = dictData.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
            dictMap.putAll(map);
        }


        String fileName = filePath + "skumap-" + System.currentTimeMillis() + ".xlsx";

        File tempFile = new File(fileName);
        if (!tempFile.getParentFile().exists()) {
            tempFile.getParentFile().mkdirs();
        }


        WriteSheet writeSheet = EasyExcel.writerSheet(0,"sheet1").build();
        com.alibaba.excel.ExcelWriter excelWriter = EasyExcel.write(fileName, ProductChannelsExportVO.class).build();

        int total = 0;
        while (CollectionUtil.isNotEmpty(exportData = productChannelsMapper.productChannelsExportQuery(query))) {

            long currentStart = System.currentTimeMillis();


            // 切换页码
            query.modifyPageStart(page++);
            handleChannels(query.getOrgId(),dictMap, exportData);

            total += exportData.size();

            excelWriter.write(exportData, writeSheet);

            log.info("SKU映射导出处理耗时 {} - 条目:{} - 耗时:{}", uuid, exportData.size(), System.currentTimeMillis() - currentStart);

            exportData.clear();
        }
        excelWriter.finish();
        File file = new File(fileName);
        long uploadStart = System.currentTimeMillis();
        String uploadFile = AliyunOssClientUtil.uploadFile(file.getName(), FileUtil.getInputStream(file), "erp/productChannels/export");
        log.info("SKU映射异步导出 - {} - 文件上传耗时 - {} - {}", uuid, System.currentTimeMillis() - uploadStart, uploadFile);
        try {
            file.delete();
        } catch (Exception e) {
            log.info("SKU映射导出文件删除失败 - {}", fileName);
        }
        log.info("SKU映射异步导出 - {} - 执行耗时 - {}", uuid, System.currentTimeMillis() - start);
        return uploadFile;
    }

    public void handleChannels(Integer orgId, Map<String, String> dictMap,List<ProductChannelsExportVO> channels) {
        try {
            ConvertUtils.dictConvert(channels);

            List<String> accountFlag = channels.stream().map(ProductChannelsExportVO::getAccountFlag).distinct().collect(Collectors.toList());

            Map<String, String> companyMapping = accountService.companyMapping(orgId, accountFlag);

            for (ProductChannelsExportVO vo : channels) {
                if (SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian().equalsIgnoreCase(vo.getSaleChannel()) && Objects.nonNull(vo.getCostPrice()) && Objects.nonNull(vo.getSellerSkuPrice())) {
                    if (vo.getSellerSkuPrice().compareTo(vo.getCostPrice()) < 0) {
                    }
                    vo.setPriceWarning(
                            vo.getSellerSkuPrice().compareTo(vo.getCostPrice()) < 0 ? "低于成本价" : "高于等于成本价"
                    );

                }

                if (StrUtil.isNotBlank(vo.getTags())) {
                    String tagsDict = Stream.of(vo.getTags().split(","))
                            .map(dictMap::get)
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.joining(","));
                    if (StrUtil.isNotBlank(tagsDict)) {
                        vo.setTagsDict(tagsDict);
                    }
                }

                if (StrUtil.isNotBlank(vo.getAsin())) {
                    vo.setAsin(vo.getAsin().trim());
                }

                if (!Objects.equals(vo.getSaleChannel(), SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian())) {
                    vo.setItemId(null);
                }
                if (companyMapping.containsKey(vo.getAccountFlag())) {
                    vo.setCompany(companyMapping.get(vo.getAccountFlag()));
                }

                handleUrl(vo);

                vo.setPrimary(
                        Objects.nonNull(vo.getIsPrimary()) && Objects.equals(1, vo.getIsPrimary()) ? "是" : "否"
                );

                vo.setIsSeedDict(
                        StrUtil.isBlank(vo.getIsSeed()) ? null : vo.getIsSeed().equalsIgnoreCase("Y") ? "是" : "否"
                );
                vo.setIsVineDict(
                        StrUtil.isBlank(vo.getIsVine()) ? null : vo.getIsVine().equalsIgnoreCase("Y") ? "是" : "否"
                );
                vo.setIsPushDict(
                        StrUtil.isBlank(vo.getIsPush()) ? null : vo.getIsPush().equalsIgnoreCase("Y") ? "是" : "否"
                );
            }
            // 处理ERPSKU数量信息
            handleErpSkuData(channels);
            // 处理型号信息
            handleModelNumber(channels);
        } catch (Exception e) {
            log.info("SKU映射导出处理异常 - ", e);
        }
    }

    /**
     * 处理ERPSKU信息
     * @param vos
     */
    private void handleErpSkuData(List<ProductChannelsExportVO> vos) {
        for (ProductChannelsExportVO vo : vos) {
            if (StrUtil.isNotBlank(vo.getErpSku()) && Objects.equals(ProductRelateTypeEnum.SINGLE.getValue(), vo.getType())) {
                vo.setQuantityStr("1");
            }
        }
        List<ProductChannelsExportVO> notSingles = vos.stream().filter(c -> !Objects.equals(c.getType(), ProductRelateTypeEnum.SINGLE.getValue()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(notSingles)) {
            return;
        }


        List<Integer> channelIds = notSingles.stream().map(ProductChannelsExportVO::getId).collect(Collectors.toList());
        List<ProductChannelRelate> relates = productChannelRelateService.lambdaQuery()
                .in(ProductChannelRelate::getProductChannelId, channelIds)
                .list();
        if (CollectionUtil.isEmpty(relates)) {
            return;
        }

        Map<Integer, List<ProductChannelRelate>> map = relates.stream().collect(Collectors.groupingBy(ProductChannelRelate::getProductChannelId));

        for (ProductChannelsExportVO vo : notSingles) {
            if (!map.containsKey(vo.getId())) {
                continue;
            }
            List<ProductChannelRelate> channelRelates = map.get(vo.getId());
            vo.setErpSku(
                    channelRelates.stream().map(ProductChannelRelate::getErpsku).collect(Collectors.joining(","))
            );
            vo.setQuantityStr(
                    channelRelates.stream().map(c -> String.valueOf(c.getQty())).collect(Collectors.joining(","))
            );
        }
    }

    /**
     * 处理型号信息
     * @param vos
     */
    private void handleModelNumber(List<ProductChannelsExportVO> vos) {
        if (CollectionUtil.isEmpty(vos)) {
            return;
        }
        List<String> erpSkus = new ArrayList<>();
        for (ProductChannelsExportVO vo : vos) {
            if (StrUtil.isBlank(vo.getErpSku())) {
                continue;
            }
            String[] split = vo.getErpSku().split(",");
            for (String s : split) {
                erpSkus.add(s);
            }
        }
        if (CollectionUtil.isEmpty(erpSkus)) {
            return;
        }

        Integer orgId = vos.get(0).getOrgId();
        List<Products> products = productsService.lambdaQuery()
                .in(Products::getErpsku, erpSkus)
                .eq(Products::getOrgId, orgId)
                .list();
        if (CollectionUtil.isEmpty(products)) {
            return;
        }
        Map<String, Products> productsMap = products.stream().collect(Collectors.toMap(Products::getErpsku, Function.identity(), (a, b) -> a));

        for (ProductChannelsExportVO vo : vos) {
            if (StrUtil.isBlank(vo.getErpSku())) {
                continue;
            }
            String erpSku = vo.getErpSku();
            String[] split = erpSku.split(",");
            for (String s : split) {
                if (!productsMap.containsKey(s)) {
                    continue;
                }
                Products p = productsMap.get(s);
                vo.setErpSkuName(
                        StrUtil.isBlank(vo.getErpSkuName()) ? p.getName() : vo.getErpSkuName() + "," + p.getName()
                );
                if (StrUtil.isBlank(p.getProductMidModel())) {
                    continue;
                }
                if (StrUtil.isBlank(vo.getModel())) {
                    vo.setModel(p.getProductMidModel());
                    continue;
                }
                vo.setModel(
                        StrUtil.isBlank(vo.getModel()) ?
                                p.getProductMidModel() :
                                vo.getModel() + "," + p.getProductMidModel()
                );
            }

        }

    }



    private void handleUrl(ProductChannelsExportVO vo) {
        String saleChannel = vo.getSaleChannel();
        SyncMappingEnum.SkuGoodsMapSaleChannelEnum value = SyncMappingEnum.SkuGoodsMapSaleChannelEnum.ehengjianValueOf(saleChannel);
        if (Objects.isNull(value)) {
            return;
        }

        switch (value) {
            case SC:
                if (StrUtil.isNotBlank(vo.getAsin())) {
                    String url = String.format("https://www.amazon.com/dp/%s", vo.getAsin());
                    WriteCellData<String> hyperlink = settingHyperLink(url);
                    vo.setUrl(hyperlink);
                }
                break;
            case VC_DF:
                if (StrUtil.isNotBlank(vo.getAsin())) {
                    WriteCellData<String> hyperlink = settingHyperLink(String.format("https://www.amazon.com/dp/%s", vo.getAsin()));
                    vo.setUrl(hyperlink);

                }
                break;
            case WALMART:
                if (StrUtil.isNotBlank(vo.getAsin())) {

                    WriteCellData<String> hyperlink = settingHyperLink(String.format("https://www.walmart.com/ip/%s", vo.getAsin()));
                    vo.setUrl(hyperlink);

//                    vo.setUrl(String.format("https://www.walmart.com/ip/%s", vo.getAsin()));
                }
                break;
            case TEMU:
                if (StrUtil.isNotBlank(vo.getItemId())) {
                    WriteCellData<String> hyperlink = settingHyperLink(String.format("https://www.temu.com/goods.html?goods_id=%s", vo.getItemId()));
                    vo.setUrl(hyperlink);
//                    vo.setUrl(String.format("https://www.temu.com/goods.html?goods_id=%s", vo.getItemId()));
                }
                break;
            case TIKTOK:
                if (StrUtil.isNotBlank(vo.getAsin1())) {
                    WriteCellData<String> hyperlink = settingHyperLink(String.format("https://shop.tiktok.com/view/product/%s", vo.getAsin1()));
                    vo.setUrl(hyperlink);

//                    vo.setUrl(String.format("https://shop.tiktok.com/view/product/%s", vo.getAsin1()));
                }
                break;
        }
    }



    @NotNull
    private static WriteCellData<String> settingHyperLink(String address) {
        WriteCellData<String> hyperlink = new WriteCellData<>(address);

        // 超链接
        HyperlinkData hyperlinkData = new HyperlinkData();
        hyperlinkData.setAddress(address);
        hyperlinkData.setHyperlinkType(HyperlinkData.HyperlinkType.URL);
        hyperlink.setHyperlinkData(hyperlinkData);

        // 字体颜色
        RichTextStringData richTextStringData = new RichTextStringData();
        WriteFont writeFont = new WriteFont();
        writeFont.setColor(IndexedColors.BLUE.getIndex());
        richTextStringData.applyFont(0, address.length() - 1, writeFont);
        richTextStringData.setTextString(address);


        hyperlink.setRichTextStringDataValue(richTextStringData);


        return hyperlink;
    }

    @Override
    public void exportProductChannel(ProductChannelsExportQuery query, List<Integer> ids, Integer contextId, HttpServletResponse response) throws IOException {
        if (CollectionUtil.isNotEmpty(ids)) {
            query.setQueryIds(ids);
        }
        query.setOrgId(contextId);
        query.settingQueryParam();
        approvalNodeQuerySetting(query);
        // 流式查询导出
        String fileName = filePath + "skumap-" + System.currentTimeMillis() + ".xlsx";
        ProductChannelsResultHandler handler = null;
        if (StrUtil.isNotBlank(PRODUCT_CHANNEL_EXPORT_BATCH)) {
            handler = new ProductChannelsResultHandler(contextId,Integer.parseInt(PRODUCT_CHANNEL_EXPORT_BATCH), fileName);
        } else {
            handler = new ProductChannelsResultHandler(contextId, fileName);
        }
        productChannelsMapper.streamQueryProductChannels(handler, query);
        byte[] buffer = handler.load();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("skumap.xlsx", "UTF-8"));
        response.setCharacterEncoding("UTF-8");
        IoUtil.write(response.getOutputStream(), false, buffer);

        try {
            handler.getFile().delete();
        } catch (Exception e) {
            log.info("SKU映射导出文件删除失败 - {}", fileName);
        }
        return;
    }

    /**
     * 设置店铺名称
     * @param channels
     */
    private void settingAccountTitle(List<ProductChannels> channels) {
        List<String> flags = channels.stream()
                .map(ProductChannels::getAccountId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(flags)) {
            List<Account> accounts = accountService.lambdaQuery()
                    .in(Account::getFlag, flags)
                    .list();
            if (CollectionUtil.isNotEmpty(accounts)) {
                Map<String, Account> accountMap = accounts.stream()
                        .filter(a -> StrUtil.isNotBlank(a.getFlag()))
                        .collect(Collectors.toMap(Account::getFlag, Function.identity(), (a, b) -> a));
                for (ProductChannels channel : channels) {
                    if (accountMap.containsKey(channel.getAccountId())) {
                        String accountId = channel.getAccountId();
                        channel.setAccountId(accountMap.get(accountId).getTitle());
                        channel.setAccountInit(accountMap.get(accountId).getAccountInit());
                    }
                }
            }

        }
    }


    public void saveProductChannelsQueueData(String message) {
        List<AmazonProductChannelVO> vos = JSON.parseArray(message, AmazonProductChannelVO.class);
        if (CollectionUtil.isEmpty(vos)) {
            log.error("direct.e.erp.run.amazon.sku.info.queue receive data is empty ...");
            return ;
        }

        vos = vos.stream()
                .filter(v -> StrUtil.isNotBlank(v.getAccountId()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(vos)) {
            log.error("获取到的数据不存在店铺标记信息！");
            return;
        }


        List<String> flags = vos.stream()
                .map(AmazonProductChannelVO::getAccountId)
                .distinct()
                .collect(Collectors.toList());

        List<Account> accounts = accountService.lambdaQuery()
                .in(Account::getFlag, flags)
                .list();
        if (CollectionUtil.isEmpty(accounts)) {
            log.error("未获取到店铺信息！");
            return;
        }


        Map<String, Integer> accountOrgMap = accounts.stream()
                .collect(Collectors.toMap(Account::getFlag, Account::getOrgId, (a, b) -> a));


        List<String> sellerSkus = vos.stream()
                .map(AmazonProductChannelVO::getSellerSku)
                .collect(Collectors.toList());

        List<ProductChannels> dbChannels = this.lambdaQuery()
                .select(ProductChannels::getId, ProductChannels::getSellerSku)
                .in(ProductChannels::getSellerSku, sellerSkus)
                .list();
        Map<String, Integer> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(dbChannels)) {
            map = dbChannels.stream().collect(Collectors.toMap(p -> p.getSellerSku() + p.getAccountId(), ProductChannels::getId, (a, b) -> a));
        }

        Map<String, Integer> finalMap = map;
        List<ProductChannels> channelsData = new ArrayList<>();
        for (AmazonProductChannelVO v : vos) {
            ProductChannels channels = new ProductChannels();
            // 不对数据做修改，只做新增
            if (finalMap.containsKey(v.getSellerSku() + v.getAccountId())) {
                log.error("SellerSku:{} <-> AccountID :{} 已存在，跳过不做修改", v.getSellerSku(), v.getAccountId());
                continue;
            }
            if (accountOrgMap.containsKey(v.getAccountId())) {
                channels.setOrgId(accountOrgMap.get(v.getAccountId()));
            }
            channels.setAccountId(v.getAccountId());
            channels.setPrice(v.getPrice());
            channels.setUpc(v.getUpc());
            channels.setSellerSku(v.getSellerSku());
            channels.setSaleChannel(v.getSaleChannel());
            channels.setOpenDate(v.getOpenDate());
            channels.setSourceType(v.getSaleChannel());
            channels.setAsin(v.getAsin());
            channelsData.add(channels);
        }
        if (CollectionUtil.isNotEmpty(channelsData)) {

            Map<Integer, List<ProductChannels>> orgMap = channelsData.stream().filter(c -> Objects.nonNull(c.getOrgId())).collect(Collectors.groupingBy(ProductChannels::getOrgId));


            for (Map.Entry<Integer, List<ProductChannels>> entry : orgMap.entrySet()) {
                Map<String, Integer> configMap = labelConfigService.getConfigMapByModule(SysLabelConfigEnum.OPERATE_REQUIRED.getModule(), entry.getKey());
                saveOrUpdateProductChannels(channelsData, null, null, false, configMap);
            }



        }


//        List<ProductChannels> insert = channelsList.stream()
//                .filter(c -> c.getId() == null)
//                .collect(Collectors.toList());
//
//        List<ProductChannels> update = channelsList.stream()
//                .filter(c -> c.getId() != null)
//                .collect(Collectors.toList());
//        if (CollectionUtil.isNotEmpty(insert)) {
//            saveOrUpdateProductChannels(insert, userName);
//            approvalLogRecord(insert);
//        }
//        if (CollectionUtil.isNotEmpty(update)) {
//            this.updateBatchById(update);
//            approvalLogRecord(update);
//        }
    }



    /**
     * @param
     * @description: 获取所有映射ASIN数据
     * @author: Moore
     * @date: 2023/9/21 10:18
     * @return: java.util.List<java.lang.String>
     **/
    @Override
    public List<ListingVo> selectAsinListingAll(Integer contextId) {
        if (contextId == null) {
            return Collections.emptyList();
        }
        ListingVo listingVoList = new ListingVo();
        listingVoList.setOrgId(contextId);
        return productChannelsMapper.selectProductChannelsList(listingVoList);
    }

    /**
     * @param
     * @description: 获取所有映射ASIN数据
     * @author: Moore
     * @date: 2023/9/21 10:18
     * @return: java.util.List<java.lang.String>
     **/
    @Override
    public List<ListingVo>  selectReviewAsinList() {
        ProductChannels productChannels = new ProductChannels();
        productChannels.setOrgId(1000049);
        List<ListingVo> channels =  productChannelsMapper.selectReviewAsinList(productChannels);
        return channels.stream().filter(item -> !com.bizark.op.common.util.StringUtils.isEmpty(item.getAsin())).collect(Collectors.toList());
    }


    @Override
    public List<ProductInventoryVO> inventoryInfo(Integer[] ids,boolean checkPush) {
        List<ProductChannels> channels = this.lambdaQuery()
                .in(ProductChannels::getId, ids)
                .select(ProductChannels::getId, ProductChannels::getOrgId, ProductChannels::getItemId, ProductChannels::getAccountId, ProductChannels::getSellerSku, ProductChannels::getErpSku)
                .list();

        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到SKU映射信息！");
        }
        return inventoryInfo(channels,checkPush);
    }
    @Override
    public List<ProductInventoryVO> inventoryInfo(List<ProductChannels> channels,boolean checkPush) {

        List<String> flags = channels.stream()
                .map(ProductChannels::getAccountId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(flags)) {
            throw new CommonException("选择的SKU映射信息未配置店铺！");
        }

        List<Account> accounts = accountService.lambdaQuery()
                .in(Account::getFlag, flags)
                .select(Account::getId, Account::getFlag, Account::getAccountInit, Account::getType,Account::getConnectStr)
                .list();

        if (CollectionUtil.isEmpty(accounts)) {
            throw new CommonException("未获取到店铺信息！");
        }
        // 检查店铺是否开启库存推送
        List<String> closePushFlag = accountService.checkAccountAutoPush(accounts);
        if (CollectionUtil.isNotEmpty(closePushFlag) && checkPush) {
            accounts = accounts.stream()
                    .filter(c -> !closePushFlag.contains(c.getFlag()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(accounts) && checkPush) {
            throw new CommonException("当前选择的SKU映射对应店铺均未开启库存推送！");

        }
        Map<String, Account> accountMap = accounts.stream()
                .collect(Collectors.toMap(Account::getFlag, Function.identity(), (a, b) -> a));

        channels = channels.stream()
                .filter(c -> accountMap.containsKey(c.getAccountId()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到SellerSku对应店铺信息");
        }

        if (CollectionUtil.isEmpty(channels) && checkPush) {
            throw new CommonException("当前选择的SKU映射或其店铺未开启库存推送！");
        }

        // 获取erpSku库存信息
        Map<String, Integer> erpSkuInventoryMap = getErpSkuInventoryMap(channels);

        // TODO 返回假数据
        boolean returnFalseData = true;
        List<String> wareHouse = Arrays.asList("aimking-US",
                "Amazon-COLAMY-US",
                "Amazon-Comoch-US", "Amazon-NJTX-US", "Amazon-Reidsville-US", "ASINKING-US", "AZ-H-Fengchang-US", "AZ-H-HZAS-US", "AZ-H-JYCX-US", "AZ-H-WUMI-US", "CAP3", "COPE_CA_US", "COPE_GA_US",
                "COPE_SPARE_US", "CTG_US_IN", "Eastwest_NJ_US", "EFCA", "EFCA-RT", "EFGA", "EFGA-RT", "EFGA-SP", "EFNJ", "EFSP", "EFTX", "EP_GA_US", "EW_CA_US", "FL01", "FY_CA_US", "HF_CA_US", "HJ04", "HOU02", "LCNJ",
                "LWCA", "LW_CA_US", "MBH-GC1", "Midwest_GA_US", "NewStlye-US", "NW_NJ_US", "office-US", "PA01", "PrimeStreet-US", "SAV", "Seray-US", "SGT_CA_US", "SGT_GA_US", "SGT_NJ_US", "SNPL", "SNPL_NewStlye-US", "SNPL_office-US", "SUOK", "SUOK_NewStlye-US", "SUOK_office-US", "SUOL", "SUOL2", "SUOL_NewStlye-US", "SUOL_office-US",
                "T1-Amazon-PPT-US", "T1-StatesvilleDirect-Amazon-US-US", "T1-YIStore-Amazon-US", "TX-WCCC-US", "WINMAX-US", "XYTX", "YM_CA_US", "YM_NJ_US", "YUJU-Rimiking-US", "YUJU-YingTai-US", "ZY-ZhenMin-US");
        if (returnFalseData) {
            List<ProductInventoryVO> vos = channels.stream()
                    .map(c -> {
                        int count = 0;
                        ProductInventoryVO inventoryVO = new ProductInventoryVO();
                        if (accountMap.containsKey(c.getAccountId())) {
                            Account account = accountMap.get(c.getAccountId());
                            inventoryVO.setAccountId(account.getId()).setAccountFlag(account.getFlag()).setAccountInit(account.getAccountInit());
                        }
                        int index = RandomUtil.randomInt(0, wareHouse.size());
                        int index1 = RandomUtil.randomInt(0, wareHouse.size());
                        inventoryVO.setSellerSku(c.getSellerSku()).setItemId(c.getItemId());
                        ProductInventoryVO.SellerSKuInventoryInfo info1 = new ProductInventoryVO.SellerSKuInventoryInfo();
                        info1.setWareHouse(wareHouse.get(index));
                        info1.setWareHouseId(String.valueOf(System.currentTimeMillis()));
                        info1.setQuantity(String.valueOf(++count));
                        ProductInventoryVO.SellerSKuInventoryInfo info2 = new ProductInventoryVO.SellerSKuInventoryInfo();
                        info2.setWareHouse(wareHouse.get(index1));
                        info2.setWareHouseId(String.valueOf(System.currentTimeMillis()));
                        info2.setQuantity(String.valueOf(++count));
                        inventoryVO.setInventoryInfos(Arrays.asList(info1, info2));
                        inventoryVO.setItemId(c.getItemId());
                        inventoryVO.setId(c.getId());
                        return inventoryVO;
                    }).collect(Collectors.toList());
            buildErpSkuInventory(channels, vos);
            return vos;
        }

        List<ProductChannels> finallyChannels = new ArrayList<>();
        for (ProductChannels channel : channels) {
            // TODO 获取库存信息
            if (!accountMap.containsKey(channel.getAccountId())) {
                log.error("SellerSku:{} , 未获取到店铺信息！", channel.getSellerSku());
                continue;
            }
            Account acc = accountMap.get(channel.getAccountId());
            channel.setAccountType(acc.getType());
            channel.setAccount(acc);
            finallyChannels.add(channel);
        }
        if (CollectionUtil.isEmpty(finallyChannels)) {
            return null;
        }
        return getProductInventory(finallyChannels, accountMap);
    }

    @NotNull
    private List<ProductInventoryVO> getProductInventory(List<ProductChannels> channels, Map<String, Account> accountMap) {
        // 结果集
        List<ProductInventoryVO> inventoryInfos = new ArrayList<>();

        Map<String, List<ProductChannels>> accountGroup = channels.stream()
                .collect(Collectors.groupingBy(ProductChannels::getAccountId));
        // 分组查询
        for (Map.Entry<String, List<ProductChannels>> entry : accountGroup.entrySet()) {
            Account account = accountMap.get(entry.getKey());
            List<ProductChannels> productChannels = entry.getValue();
            SaleChannelMappingEnum type = SaleChannelMappingEnum.nameOfEnum(account.getType());
            switch (type) {
                case SHOPIFY:
                    // TODO 检查是否存在 库存ID 为空的数据
                    ShopifyInventoryLevelResponse shopifyResponse = selectInventoryFromShopify(productChannels, account, inventoryInfos);
                    if (Objects.nonNull(shopifyResponse)) {
                        List<ProductInventoryVO> inventoryVOS = shopifyResponse.convert(productChannels, account);
                        // 查询对应仓库关系信息
                        buildInventoryWarehouse(inventoryVOS);
                        inventoryInfos.addAll(inventoryVOS);
                    }
                    break;
                case WALMART_DSV:
                    for (ProductChannels channel : productChannels) {
                        WalmartInventorySelectResponse response = inventorySelectApi.selectWalmartDSVInventory(account, channel.getGtin());
                        if (Objects.isNull(response)) {
                            log.error("request select walmart inventory error ..");
                            continue;
                        }
                        ProductInventoryVO inventoryVO = new ProductInventoryVO();
                        inventoryVO.setAccountId(account.getId());
                        inventoryVO.setAccountFlag(account.getFlag());
                        inventoryVO.setId(channel.getId());
                        inventoryVO.setAccountInit(account.getAccountInit());
                        inventoryVO.setSellerSku(channel.getSellerSku());
                        ProductInventoryVO.SellerSKuInventoryInfo info = new ProductInventoryVO.SellerSKuInventoryInfo();
                        info.setQuantity(String.valueOf(response.getQuantity().getAmount()));
                        inventoryVO.setInventoryInfos(Collections.emptyList());
                        inventoryInfos.add(inventoryVO);
                    }
                    break;
                case WALMART:
                    for (ProductChannels channel : productChannels) {
                        WalmartInventorySelectResponse response = inventorySelectApi.selectWalmartInventory(account, channel.getSellerSku());
                        if (Objects.isNull(response)) {
                            log.error("request select walmart inventory error ..");
                            break;
                        }
                        ProductInventoryVO inventoryVO = new ProductInventoryVO();
                        inventoryVO.setAccountId(account.getId());
                        inventoryVO.setId(channel.getId());
                        inventoryVO.setAccountFlag(account.getFlag());
                        inventoryVO.setAccountInit(account.getAccountInit());
                        inventoryVO.setSellerSku(channel.getSellerSku());
                        ProductInventoryVO.SellerSKuInventoryInfo info = new ProductInventoryVO.SellerSKuInventoryInfo();
                        info.setQuantity(String.valueOf(response.getQuantity().getAmount()));
                        inventoryVO.setInventoryInfos(Collections.singletonList(info));
                        inventoryInfos.add(inventoryVO);
                    }
                    break;
                case TIKTOK:
                    List<String> tiktokItemIds = productChannels.stream().map(ProductChannels::getItemId).collect(Collectors.toList());
                    TiktokInventorySelectResponse tiktokResponse = inventorySelectApi.selectTiktokInventory(account, tiktokItemIds);
                    if (Objects.nonNull(tiktokResponse)) {
                        List<ProductInventoryVO> productInventoryVOS = tiktokResponseConvert(tiktokResponse, account, productChannels);
                        // 查询对应仓库关系信息
                        buildInventoryWarehouse(productInventoryVOS);
                        inventoryInfos.addAll(productInventoryVOS);
                    }
                    break;
                case MICROSOFT:
                    for (ProductChannels channel : productChannels) {
                        MicroSoftInventorySelectResponse selectResponse = inventorySelectApi.selectMicroSoftInventory(accountMap.get(channel.getAccountId()), channel.getSellerSku());
                        ProductInventoryVO inventoryVO = microSoftInventoryConvert(account, channel, selectResponse);
                        if (inventoryVO != null){
                            inventoryInfos.add(inventoryVO);
                        }
                    }
                    break;
                case OVERSTOCK:
                    selectInventoryFromOverstock(account, productChannels, inventoryInfos);
                    break;
                case AMAZON:
                    // TODO 暂定
                    break;
                case EBAY:
                    selectInventoryFromEbay(productChannels, account, inventoryInfos);
                default:
                    break;
            }

            if (CollectionUtil.isNotEmpty(inventoryInfos)) {
                buildErpSkuInventory(channels, inventoryInfos);
            }

        }
        return inventoryInfos;
    }

    /**
     * 查询Ebay库存信息
     * @param productChannels
     * @param account
     * @param inventoryInfos
     */
    private void selectInventoryFromEbay(List<ProductChannels> productChannels, Account account, List<ProductInventoryVO> inventoryInfos) {
        List<String> sellerSkus = productChannels.stream()
                .map(ProductChannels::getSellerSku)
                .collect(Collectors.toList());

        EbayInventorySelectResponse ebayInventory = inventorySelectApi.selectEbayInventory(account, sellerSkus);
        if (Objects.isNull(ebayInventory)) {
            return;
        }
        List<EbayInventorySelectResponse.InventoryResponse> responses = ebayInventory.getResponses();
        if (Objects.isNull(responses)) {
            return;
        }

        Map<String, ProductChannels> channelsMap = productChannels.stream().collect(Collectors.toMap(ProductChannels::getSellerSku, Function.identity(), (a, b) -> a));

        for (EbayInventorySelectResponse.InventoryResponse res : responses) {
            EbayInventorySelectResponse.InventoryItem inventoryItem = res.getInventoryItem();
            if (Objects.isNull(inventoryItem)) {
                continue;
            }
            String sku = inventoryItem.getSku();
            ProductInventoryVO inventoryVO = new ProductInventoryVO();
            inventoryVO.setSellerSku(sku);
            if (channelsMap.containsKey(sku)) {
                inventoryVO.setId(channelsMap.get(sku).getId());
            }
            inventoryVO.setAccountInit(account.getFlag());
            inventoryVO.setAccountId(account.getId());
            EbayInventorySelectResponse.Availability availability = inventoryItem.getAvailability();
            if (Objects.isNull(availability)) {
                continue;
            }
            List<EbayInventorySelectResponse.PickupAtLocationAvailability> pickupAtLocationAvailability = availability.getPickupAtLocationAvailability();
            if (CollectionUtil.isEmpty(pickupAtLocationAvailability)) {
                continue;
            }
            EbayInventorySelectResponse.PickupAtLocationAvailability inStock = pickupAtLocationAvailability.stream()
                    .filter(p -> Objects.equals("IN_STOCK", p.getAvailabilityType()))
                    .findAny().orElse(null);
            if (Objects.isNull(inStock)) {
                continue;
            }
            ProductInventoryVO.SellerSKuInventoryInfo sKuInventoryInfo = new ProductInventoryVO.SellerSKuInventoryInfo();
            sKuInventoryInfo.setQuantity(String.valueOf(inStock.getQuantity()));
            sKuInventoryInfo.setWareHouseId(inStock.getMerchantLocationKey());
            inventoryVO.setInventoryInfos(Collections.singletonList(sKuInventoryInfo));
            inventoryInfos.add(inventoryVO);
        }
    }

    private void selectInventoryFromOverstock(Account account, List<ProductChannels> productChannels, List<ProductInventoryVO> inventoryInfos) {
        OverstockInventorySelectResponse selectResponse = inventorySelectApi.selectOverStockInventory(account);
        for (ProductChannels channel : productChannels) {
            OverstockInventorySelectResponse.CurrentInventory inventory = selectResponse.findCurrentInventory(channel.getSellerSku());
            if (Objects.isNull(inventory)) {
                continue;
            }
            List<OverstockInventorySelectResponse.CurrentWarehouseQuantity> warehouseQuantity = inventory.getCurrentWarehouseQuantity();
            ProductInventoryVO inventoryVO = new ProductInventoryVO();
            inventoryVO.setAccountId(account.getId());
            inventoryVO.setId(channel.getId());
            inventoryVO.setItemId(channel.getItemId());
            inventoryVO.setAccountFlag(account.getFlag());
            inventoryVO.setAccountInit(account.getAccountInit());
            inventoryVO.setSellerSku(channel.getSellerSku());
            List<ProductInventoryVO.SellerSKuInventoryInfo> sellerSKuInventoryInfos = new ArrayList<>();
            for (OverstockInventorySelectResponse.CurrentWarehouseQuantity quantity : warehouseQuantity) {
                // 仓库数量封装对象
                ProductInventoryVO.SellerSKuInventoryInfo sellerSKuInventoryInfo = new ProductInventoryVO.SellerSKuInventoryInfo();
                List<OverstockInventorySelectResponse.WarehouseName> warehouseName = quantity.getWarehouseName();
                OverstockInventorySelectResponse.WarehouseName warehouse = warehouseName.get(0);
                List<OverstockInventorySelectResponse.CurrentInventoryQuantityDetail> quantityDetails = quantity.getCurrentInventoryQuantityDetails();
                for (OverstockInventorySelectResponse.CurrentInventoryQuantityDetail quantityDetail : quantityDetails) {
                    sellerSKuInventoryInfo.setWareHouseId(warehouse.getCode()[0]);
                    sellerSKuInventoryInfo.setQuantity(quantityDetail.getSellable()[0]);
                }
                sellerSKuInventoryInfos.add(sellerSKuInventoryInfo);
            }
            inventoryInfos.add(inventoryVO);
        }
        buildInventoryWarehouse(inventoryInfos);
    }

    private void selectInventoryFromMicroSoft(Map<String, Account> accountMap, List<ProductChannels> productChannels, Account account, List<ProductInventoryVO> inventoryInfos) {
        for (ProductChannels channel : productChannels) {
            if (!accountMap.containsKey(channel.getAccountId())) {
                log.error("MicroSoft inventory select fail , account not found : {}", channel.getSellerSku());
                continue;
            }
            MicroSoftInventorySelectResponse selectResponse = inventorySelectApi.selectMicroSoftInventory(accountMap.get(channel.getAccountId()), channel.getSellerSku());
            ProductInventoryVO inventoryVO = microSoftInventoryConvert(account, channel, selectResponse);
            if (inventoryVO != null){
                inventoryInfos.add(inventoryVO);
            }
        }
    }

    private static ProductInventoryVO microSoftInventoryConvert(Account account, ProductChannels channel, MicroSoftInventorySelectResponse selectResponse) {
        if (Objects.isNull(selectResponse)) {
            log.error("MicroSoft inventory select fail , response is null : {}", channel.getSellerSku());
            return null;
        }
        MicroSoftInventorySelectResponse.ResponseData result = selectResponse.getResult();
        if (Objects.isNull(result)) {
            log.error("MicroSoft inventory select fail , result is null : {}", channel.getSellerSku());
            return null;
        }
        List<MicroSoftInventorySelectResponse.ResponseContent> content = result.getContent();
        if (CollectionUtil.isEmpty(content)) {
            log.error("MicroSoft inventory select fail , content is null : {}", channel.getSellerSku());
            return null;
        }
        MicroSoftInventorySelectResponse.ResponseContent skuContent = content.stream()
                .filter(c -> c.getSku().equals(channel.getSellerSku()))
                .findAny().orElse(null);
        if (Objects.isNull(skuContent)) {
            log.error("MicroSoft inventory select fail , skuContent is null : {}", channel.getSellerSku());
            return null;
        }
        Integer quantity = Objects.nonNull(skuContent.getInventoryStatus())
                ? skuContent.getInventoryStatus().getAvailableInventory()
                : skuContent.getInventory();
        ProductInventoryVO inventoryVO = new ProductInventoryVO();
        inventoryVO.setSellerSku(channel.getSellerSku())
                .setAccountInit(account.getAccountInit())
                .setAccountFlag(account.getFlag())
                .setAccountId(account.getId())
                .setId(channel.getId());

        ProductInventoryVO.SellerSKuInventoryInfo sellerSKuInventoryInfo = new ProductInventoryVO.SellerSKuInventoryInfo();
        sellerSKuInventoryInfo.setQuantity(String.valueOf(quantity));
        return inventoryVO;
    }

    private void selectInventoryFromTiktok(List<ProductChannels> productChannels, Account account, List<ProductInventoryVO> inventoryInfos) {
        List<String> tiktokItemIds = productChannels.stream().map(ProductChannels::getItemId).collect(Collectors.toList());
        TiktokInventorySelectResponse selectResponse = inventorySelectApi.selectTiktokInventory(account, tiktokItemIds);

        List<ProductInventoryVO> productInventoryVOS = tiktokResponseConvert(selectResponse, account, productChannels);
        // 查询对应仓库关系信息
        buildInventoryWarehouse(productInventoryVOS);
        inventoryInfos.addAll(productInventoryVOS);
    }

    private void selectInventoryFromWalmart(List<ProductChannels> productChannels, Account account, List<ProductInventoryVO> inventoryInfos) {
        for (ProductChannels channel : productChannels) {
            WalmartInventorySelectResponse response = inventorySelectApi.selectWalmartInventory(account, channel.getSellerSku());
            if (Objects.isNull(response)) {
                log.error("request select walmart inventory error ..");
                break;
            }
            ProductInventoryVO inventoryVO = new ProductInventoryVO();
            inventoryVO.setAccountId(account.getId());
            inventoryVO.setId(channel.getId());
            inventoryVO.setAccountFlag(account.getFlag());
            inventoryVO.setAccountInit(account.getAccountInit());
            inventoryVO.setSellerSku(channel.getSellerSku());
            ProductInventoryVO.SellerSKuInventoryInfo info = new ProductInventoryVO.SellerSKuInventoryInfo();
            info.setQuantity(String.valueOf(response.getQuantity().getAmount()));
            inventoryVO.setInventoryInfos(Collections.singletonList(info));
            inventoryInfos.add(inventoryVO);
        }
    }

    private void selectInventoryFromWalmartDSV(List<ProductChannels> productChannels, Account account, List<ProductInventoryVO> inventoryInfos) {
        for (ProductChannels channel : productChannels) {
            WalmartInventorySelectResponse response = inventorySelectApi.selectWalmartDSVInventory(account, channel.getGtin());
            if (Objects.isNull(response)) {
                log.error("request select walmart inventory error ..");
                break;
            }
            ProductInventoryVO inventoryVO = new ProductInventoryVO();
            inventoryVO.setAccountId(account.getId());
            inventoryVO.setAccountFlag(account.getFlag());
            inventoryVO.setId(channel.getId());
            inventoryVO.setAccountInit(account.getAccountInit());
            inventoryVO.setSellerSku(channel.getSellerSku());
            ProductInventoryVO.SellerSKuInventoryInfo info = new ProductInventoryVO.SellerSKuInventoryInfo();
            info.setQuantity(String.valueOf(response.getQuantity().getAmount()));
            inventoryVO.setInventoryInfos(Collections.emptyList());
            inventoryInfos.add(inventoryVO);
        }
    }


    private ShopifyInventoryLevelResponse selectInventoryFromShopify(List<ProductChannels> productChannels, Account account, List<ProductInventoryVO> inventoryInfos) {
        buildInventoryItemId(productChannels, account);
        List<String> inventoryIds = productChannels.stream().map(ProductChannels::getInventoryItemId).collect(Collectors.toList());
        ShopifyInventoryLevelResponse shopifyResponse = inventorySelectApi.selectShopifyInventory(account, inventoryIds);
        if (Objects.isNull(shopifyResponse)) {
            return null;
        }
        return shopifyResponse;
        // 查询ERP对应仓库
//        List<ShopifyInventoryLevelResponse.InventoryLevelInfo> inventoryLevels = shopifyResponse.getInventoryLevels();
//        List<Long> locationIds = inventoryLevels.stream()
//                .map(ShopifyInventoryLevelResponse.InventoryLevelInfo::getLocationId)
//                .collect(Collectors.toList());
//        List<StockAccountAddressEntity> addressEntities = stockAccountAddressService.findByAccountFlag(account.getFlag());
//        if (CollectionUtil.isNotEmpty(addressEntities)) {
//            Map<String, StockAccountAddressEntity> entityMap = addressEntities.stream().collect(Collectors.toMap(StockAccountAddressEntity::getAddressCode, Function.identity(), (a, b) -> a));
//
//        }

    }

    public void buildInventoryItemId(List<ProductChannels> productChannels, Account account) {
        List<ProductChannels> inventoryItemEmpty = productChannels.stream()
                .filter(c -> StrUtil.isBlank(c.getInventoryItemId()) && StrUtil.isNotBlank(c.getItemId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(inventoryItemEmpty)) {
            List<String> itemIds = inventoryItemEmpty.stream()
                    .map(ProductChannels::getItemId)
                    .distinct()
                    .collect(Collectors.toList());
            // 查询库存ID
            ShopifyProductResponse productResponse = productSelectApi.selectShopifyProduct(account, itemIds);
            if (Objects.isNull(productResponse)) {
                log.error("shopify库存数据查询为空!");
                return;
            }
            // 设置库存ID,更新数据库
            for (ProductChannels channel : productChannels) {
                Long itemId = productResponse.findInventoryItemId(channel.getSellerSku());
                if (Objects.nonNull(itemId)) {
                    channel.setInventoryItemId(String.valueOf(itemId));
                    productChannelsMapper.updateInventoryItemIdById(channel.getId(), channel.getInventoryItemId());
                }
            }
        }
        return;
    }


    /**
     * Upload content to the given URL.
     *
     * @param source the content to upload
     * @param url    the URL to upload content
     */
    private void uploadFeedContent(byte[] source, String url, String format) {
        OkHttpClient client = new OkHttpClient();

        String contentType = String.format(format, StandardCharsets.UTF_8);
        try {
            log.info("uploadFeedContent url: {}", url);
            Request request = new Request.Builder()
                    .url(url)
                    .put(RequestBody.create(MediaType.parse(contentType), source))
                    .build();

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error(
                        String.format("Call to upload document failed with response code: %d and message: %s",
                                response.code(), response.message()));
            }
        } catch (IOException e) {
            log.error("upload feed content fail", e);
        }

    }


    private void buildErpSkuInventory(List<ProductChannels> channels, List<ProductInventoryVO> inventoryInfos) {
        List<String> erpSkus = channels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getErpSku()))
                .map(ProductChannels::getErpSku)
                .map(c -> c.split(","))
                .flatMap(Arrays::stream)
                .collect(Collectors.toList());
        List<InventoryListEntity> inventories = inventoryListService.getInventoryList(channels.get(0).getOrgId(), erpSkus);

        if (CollectionUtil.isEmpty(inventories)) {
            return;
        }
        Map<String, Map<String, Integer>> inventoryMap = new HashMap<>();
        for (InventoryListEntity inventory : inventories) {
            if (inventoryMap.containsKey(inventory.getSku())) {
                Map<String, Integer> qtyMap = inventoryMap.get(inventory.getSku());
                if (!qtyMap.containsKey(inventory.getOrgWarehouseCode())) {
                    qtyMap.put(inventory.getOrgWarehouseCode(), inventory.getInventoryAvailable());
                }
            } else {
                Map<String, Integer> map = new HashMap<>();
                map.put(inventory.getOrgWarehouseCode(), inventory.getInventoryAvailable());
                inventoryMap.put(inventory.getSku(), map);
            }
        }


        Map<String, String> sellerSkuMap = channels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getErpSku()))
                .collect(Collectors.toMap(ProductChannels::getSellerSku, ProductChannels::getErpSku));
        if (CollectionUtil.isEmpty(sellerSkuMap)) {
            return;
        }
        for (ProductInventoryVO inventoryInfo : inventoryInfos) {
            String sellerSku = inventoryInfo.getSellerSku();
            if (!sellerSkuMap.containsKey(sellerSku)) {
                continue;
            }
            String[] erpSku = sellerSkuMap.get(sellerSku).split(",");
            List<ProductInventoryVO.SellerSKuInventoryInfo> infos = inventoryInfo.getInventoryInfos();
            if (CollectionUtil.isEmpty(infos)) {
                continue;
            }
            for (ProductInventoryVO.SellerSKuInventoryInfo sellerSKuInventoryInfo : infos) {
                String wareHouse = sellerSKuInventoryInfo.getWareHouse();
                if (StrUtil.isBlank(wareHouse)) {
                    continue;
                }
                List<ProductInventoryVO.SkuInventoryInfo> skuInfos = new ArrayList<>();
                for (String sku : erpSku) {
                    ProductInventoryVO.SkuInventoryInfo info = new ProductInventoryVO.SkuInventoryInfo();
                    info.setSku(sku);
                    if (!inventoryMap.containsKey(sku)) {
                        info.setQuantity(BigDecimal.ZERO);
                        continue;
                    }
                    Map<String, Integer> qtyMap = inventoryMap.get(sku);
                    if (!qtyMap.containsKey(wareHouse)) {
                        info.setQuantity(BigDecimal.ZERO);
                        continue;
                    }
                    info.setQuantity(
                            qtyMap.containsKey(wareHouse)
                                    ? BigDecimal.valueOf(qtyMap.get(wareHouse))
                                    : BigDecimal.ZERO
                    );
                    skuInfos.add(info);
                }
                sellerSKuInventoryInfo.setSkuInventoryInfo(skuInfos);
            }
        }

    }

    private Map<String, Integer> getErpSkuInventoryMap(List<ProductChannels> channels) {

        ProductChannels productChannels = channels.get(0);

        List<String> erpSKus = channels.stream()
                .map(ProductChannels::getErpSku)
                .filter(StrUtil::isNotBlank)
                .map(s -> s.split(","))
                .flatMap(Arrays::stream)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(erpSKus)) {
            return new HashMap<>();
        }
        List<Products> products = productsService.lambdaQuery()
                .in(Products::getErpsku, erpSKus)
                .eq(Products::getOrgId, productChannels.getOrgId())
                .select(Products::getErpsku, Products::getInventory, Products::getId)
                .list();
        if (CollectionUtil.isEmpty(products)) {
            return new HashMap<>();
        }
        return products.stream()
                .peek(p -> p.setInventory(Objects.isNull(p.getInventory()) ? 0 : p.getInventory()))
                .collect(Collectors.toMap(Products::getErpsku, Products::getInventory, (a, b) -> a));
    }


    /**
     * Tiktok 库存查询后数据转换
     *
     * @param tiktokResponse
     * @param account
     * @param productChannels
     * @return
     */
    public List<ProductInventoryVO> tiktokResponseConvert(TiktokInventorySelectResponse tiktokResponse, Account account, List<ProductChannels> productChannels) {
        if (tiktokResponse == null || tiktokResponse.getData() == null) {
            return new ArrayList<>();
        }


        if (CollectionUtil.isEmpty(tiktokResponse.getData().getInventory())) {
            return new ArrayList<>();
        }
        List<ProductInventoryVO> inventoryVOS = new ArrayList<>();

        Map<String, ProductChannels> channelsMap = productChannels.stream().collect(Collectors.toMap(ProductChannels::getSellerSku, Function.identity(), (a, b) -> a));

        List<TiktokInventorySelectResponse.InventoryInfo > productStocks = tiktokResponse.getData().getInventory();

        for (TiktokInventorySelectResponse.InventoryInfo productStock : productStocks) {
            List<TiktokInventorySelectResponse.SkuInfo> skus = productStock.getSkus();
            for (TiktokInventorySelectResponse.SkuInfo sku : skus) {
                List<TiktokInventorySelectResponse.WarehouseInventory > warehouseStockInfos = sku.getWarehouseInventory();
                if (CollectionUtil.isEmpty(warehouseStockInfos)) {
                    continue;
                }
                for (TiktokInventorySelectResponse.WarehouseInventory warehouseStockInfo : warehouseStockInfos) {
                    ProductInventoryVO inventoryVO = new ProductInventoryVO();
                    inventoryVO.setSellerSku(sku.getSellerSku());
                    if (channelsMap.containsKey(sku.getSellerSku())) {
                        inventoryVO.setId(channelsMap.get(sku.getSellerSku()).getId());
                    }
                    inventoryVO.setAccountId(account.getId());
                    inventoryVO.setAccountInit(account.getAccountInit());
                    inventoryVO.setAccountFlag(account.getFlag());
                    inventoryVO.setItemId(sku.getId());
                    ProductInventoryVO.SellerSKuInventoryInfo sellerSKuInventoryInfo = new ProductInventoryVO.SellerSKuInventoryInfo();
                    sellerSKuInventoryInfo.setWareHouseId(warehouseStockInfo.getWarehouseId());
                    sellerSKuInventoryInfo.setQuantity(String.valueOf(warehouseStockInfo.getAvailableQuantity()));
                    inventoryVO.setInventoryInfos(Lists.newArrayList(sellerSKuInventoryInfo));
                    inventoryVOS.add(inventoryVO);
                }
            }

        }

        return inventoryVOS;
//
//        List<TiktokInventorySelectResponse.InventoryInfo> inventory = tiktokResponse.getData().getInventory();
//        if (inventory == null || inventory.isEmpty()) {
//            return new ArrayList<>();
//        }
//        Map<String, ProductChannels> channelsMap = productChannels.stream()
//                .collect(Collectors.toMap(ProductChannels::getSellerSku, Function.identity(), (a, b) -> a));
//        for (TiktokInventorySelectResponse.InventoryInfo info : inventory) {
//            List<TiktokInventorySelectResponse.SkuInfo> skus = info.getSkus();
//            if (skus == null || skus.isEmpty()) {
//                continue;
//            }
//            for (TiktokInventorySelectResponse.SkuInfo skuInfo : skus) {
//                if (!channelsMap.containsKey(skuInfo.getSellerSku())) {
//                    continue;
//                }
//                ProductInventoryVO inventoryVO = new ProductInventoryVO();
//                inventoryVO.setAccountInit(account.getAccountInit()).setSellerSku(skuInfo.getSellerSku())
//                        .setAccountId(account.getId()).setAccountFlag(account.getFlag())
//                        .setItemId(info.getProductId());
//                List<TiktokInventorySelectResponse.WarehouseInventory> warehouseInventory = skuInfo.getWarehouseInventory();
//                if (warehouseInventory == null || warehouseInventory.isEmpty()) {
//                    continue;
//                }
//                List<ProductInventoryVO.SellerSKuInventoryInfo> sellerSKuInventoryInfos = warehouseInventory.stream()
//                        .map(v -> {
//                            ProductInventoryVO.SellerSKuInventoryInfo sellerSKuInventoryInfo = new ProductInventoryVO.SellerSKuInventoryInfo();
//                            sellerSKuInventoryInfo.setWareHouseId(v.getWarehouseId());
//                            sellerSKuInventoryInfo.setQuantity(v.getAvailableQuantity());
//                            return sellerSKuInventoryInfo;
//                        }).collect(Collectors.toList());
//                inventoryVO.setInventoryInfos(sellerSKuInventoryInfos);
//                inventoryVOS.add(inventoryVO);
//            }
//
//        }
//        // 查询对应仓库关系信息
//        buildInventoryWarehouse(inventoryVOS);
//
//        return inventoryVOS;
    }

    /**
     * 补充对应店铺仓库信息
     *
     * @param inventoryVOS
     */
    private void buildInventoryWarehouse(List<ProductInventoryVO> inventoryVOS) {
        if (CollectionUtil.isEmpty(inventoryVOS)) {
            return;
        }
        List<Integer> accountIds = inventoryVOS.stream().map(ProductInventoryVO::getAccountId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, StockAccountAddressEntity> addressMap = getAddressMap(accountIds);
        for (ProductInventoryVO inventoryVO : inventoryVOS) {
            List<ProductInventoryVO.SellerSKuInventoryInfo> infos = inventoryVO.getInventoryInfos();
            if (CollectionUtil.isEmpty(infos)) {
                continue;
            }
            for (ProductInventoryVO.SellerSKuInventoryInfo info : infos) {
                String wareHouseId = info.getWareHouseId();
                if (StrUtil.isBlank(wareHouseId) || !addressMap.containsKey(wareHouseId)) {
                    continue;
                }
                StockAccountAddressEntity address = addressMap.get(wareHouseId);
                if (CollectionUtil.isNotEmpty(address.getItems())) {
                    info.setWareHouse(address.getItems().get(0).getOrgWarehouseCode());
                }
            }
        }
    }

    public Map<String, StockAccountAddressEntity> getAddressMap(List<Integer> accountIds) {
        List<StockAccountAddressEntity> addresses = stockAccountAddressService.findByAccountIdIn(accountIds);
        if (CollectionUtil.isEmpty(addresses)) {
            return new HashMap<>();
        }
        return addresses.stream()
                .collect(Collectors.toMap(StockAccountAddressEntity::getAddressCode, Function.identity(), (a, b) -> a));
    }


    @Override
    @Transactional
    public void syncInventory(Integer[] ids) {

        List<ProductChannels> channels = this.listByIds(Arrays.asList(ids));
        if (CollectionUtil.isEmpty(channels)) {
            log.error("同步失败，未获取到SKU映射数据信息：{}", Arrays.toString(ids));
            throw new CommonException("同步失败，未获取到SKU映射数据信息");
        }
        // 过滤不支持自动同步的渠道
        channels = filterSaleChannel(channels);


        List<String> flags = channels.stream()
                .map(ProductChannels::getAccountId)
                .collect(Collectors.toList());

        List<Account> accounts = accountService.lambdaQuery()
                .in(Account::getFlag, flags)
                .list();
        if (CollectionUtil.isEmpty(accounts)) {
            log.error("同步失败，未获取到店铺信息：{}", flags);
            throw new CommonException("同步失败，未获取到店铺信息！");
        }

//        channels = check(channels, accounts);

        List<String> skipSaleChannels = Arrays.asList(
                SaleChannelMappingEnum.WAYFAIR.getName(),
                SaleChannelMappingEnum.AMAZON.getName(),
                SaleChannelMappingEnum.VCDI.getName(),
                SaleChannelMappingEnum.AMAZONVENDOR.getName(),
                SaleChannelMappingEnum.EBAY.getName()
        );

        List<ProductChannels> channelsList = channels.stream()
                .filter(c -> !skipSaleChannels.contains(c.getSaleChannel()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(channelsList)) {
            throw new CommonException("Wayfair、SC、VC-DI、VC-USPO、Ebay 渠道不支持同步库存");
        }


        Map<String, Account> accountMap = accounts.stream()
                .collect(Collectors.toMap(Account::getFlag, Function.identity(), (a, b) -> a));


        List<ProductChannels> vcdfChannels = channels.stream()
                .filter(c -> SaleChannelMappingEnum.AMAZONVENDORDS.getName().equals(c.getSaleChannel()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(vcdfChannels)) {
            // 发送MQ
            for (ProductChannels channel : vcdfChannels) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("sku", channel.getSellerSku());
                jsonObject.put("storeName", channel.getAccountId());
                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.SKU_INVENTORY_REQ_ROUTING_KEY, jsonObject);
                // 记录标记
                redisTemplate.opsForValue().set(RedisCons.INVENTORY_SYNC_PRE + channel.getSellerSku() + ":" + channel.getAccountId(), channel.getSellerSku(), 15, TimeUnit.MINUTES);
            }

        }


        List<ProductChannels> syncInventoryChannels = channels.stream()
                .filter(c -> !SaleChannelMappingEnum.AMAZONVENDORDS.getName().equals(c.getSaleChannel()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(syncInventoryChannels)) {
            return;
        }

        // 执行非VC-DF渠道库存同步
        inventorySyncExecute(syncInventoryChannels, accountMap);

    }

    @NotNull
    private List<ProductChannels> filterSaleChannel(List<ProductChannels> channels) {
        List<String> support = Arrays.asList(
                SaleChannelMappingEnum.WALMART.getName(),
                SaleChannelMappingEnum.WALMART_DSV.getName(),
                SaleChannelMappingEnum.SHOPIFY.getName(),
                SaleChannelMappingEnum.MICROSOFT.getName(),
                SaleChannelMappingEnum.TIKTOK.getName(),
                SaleChannelMappingEnum.SHEIN.getName(),
                SaleChannelMappingEnum.AMAZONVENDORDS.getName()
        );
        channels = channels.stream()
                .filter(c -> support.contains(c.getSaleChannel()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("仅支持 VC-DF、Walmart、Walmart_dsv、Shein、Shopify、Microsoft、Tiktok 渠道手动同步库存");
        }

        if (channels.size() == 1) {
            ProductChannels productChannels = channels.get(0);
            String redisKey = RedisCons.INVENTORY_SYNC_PRE + productChannels.getSellerSku() + ":" + productChannels.getAccountId();
            if (
                    Objects.equals(SaleChannelMappingEnum.AMAZONVENDORDS.getName(), productChannels.getSaleChannel())
                    && redisTemplate.hasKey(redisKey)
            ) {
                throw new CommonException("SellerSku库存正在同步中");
            }

        }

        Iterator<ProductChannels> iterator = channels.iterator();

        while (iterator.hasNext()) {

            ProductChannels next = iterator.next();
            if (!Objects.equals(SaleChannelMappingEnum.AMAZONVENDORDS.getName(), next.getSaleChannel())) {
                continue;
            }
            String redisKey = RedisCons.INVENTORY_SYNC_PRE + next.getSellerSku() + ":" + next.getAccountId();

            if (redisTemplate.hasKey(redisKey)) {
                log.info("当前SellerSku：{} - {}", next.getAccountId(), next.getSellerSku());
                iterator.remove();
            }

        }

        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("当前选择的SellerSKU库存在同步中");
        }



        return channels;
    }

    private void inventorySyncExecute(List<ProductChannels> syncInventoryChannels, Map<String, Account> accountMap) {
        // 手动同步库存信息
        Map<String, List<ProductChannels>> accountGroup = syncInventoryChannels.stream()
                .collect(Collectors.groupingBy(ProductChannels::getAccountId));

        // 同步库存信息
        List<SyncInventoryResult.Error> errors = new ArrayList<>();
        List<String> errorSkus = new ArrayList<>();
        for (Map.Entry<String, List<ProductChannels>> entry : accountGroup.entrySet()) {
            List<String> sellerSKus = entry.getValue().stream().map(ProductChannels::getSellerSku).collect(Collectors.toList());
            if (!accountMap.containsKey(entry.getKey())) {
                log.error("未获取到对应店铺，无法同步库存：{}", entry.getKey());
                errorSkus.addAll(sellerSKus);
                continue;
            }
            Account account = accountMap.get(entry.getKey());
            InventoryType typeEnum = InventoryType.typeOf(account.getType());
            if (Objects.isNull(typeEnum)) {
                log.error("未知店铺类型，无法同步库存：{}", entry.getKey());
                errorSkus.addAll(sellerSKus);
                continue;
            }
            AbstractInventorySynchronizer synchronizer = inventorySynchronizerFactory.getSynchronizer(typeEnum);
            if (Objects.isNull(synchronizer)) {
                log.error("未获取到对应同步器类型：{}",account.getType());
                errorSkus.addAll(sellerSKus);
                continue;
            }
            SyncInventoryResult result = synchronizer.sync(new InventorySyncBean(account, entry.getValue()));
            errors.addAll(result.getErrors());
        }

        if (CollectionUtil.isNotEmpty(errors)) {
            List<String> errorSellerSku = errors.stream().map(SyncInventoryResult.Error::getSellerSKu).collect(Collectors.toList());
            errorSkus.addAll(errorSellerSku);
        }

        if (CollectionUtil.isNotEmpty(errorSkus)) {
            throw new CommonException("同步失败的SellerSku" + errorSkus);
        }
    }

    @Override
    public void updateInventoryWarehouse(List<StockAccountAddressEntity> addresses) {
        if (CollectionUtil.isEmpty(addresses)) {
            return;
        }
        List<String> flags = addresses.stream()
                .map(StockAccountAddressEntity::getAccountFlag)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(flags)) {
            return;
        }
        List<ProductChannelsInventory> channelsInventories = productChannelsInventoryService.lambdaQuery()
                .in(ProductChannelsInventory::getAccountFlag, flags)
                .list();
        if (CollectionUtil.isEmpty(channelsInventories)) {
            return;
        }
        Map<String, List<ProductChannelsInventory>> flagMap = channelsInventories.stream()
                .collect(Collectors.groupingBy(ProductChannelsInventory::getAccountFlag));

        Map<String, List<StockAccountAddressEntity>> flagGroup = addresses.stream()
                .collect(Collectors.groupingBy(StockAccountAddressEntity::getAccountFlag));

        List<ProductChannelsInventory> update = new ArrayList<>();
        for (Map.Entry<String, List<ProductChannelsInventory>> entry : flagMap.entrySet()) {
            if (!flagGroup.containsKey(entry.getKey())) {
                continue;
            }
            List<ProductChannelsInventory> value = entry.getValue();
            List<StockAccountAddressEntity> entities = flagGroup.get(entry.getKey());
            Map<String, String> map = entities.stream()
                    .filter(c -> CollectionUtil.isNotEmpty(c.getItems()))
                    .collect(Collectors.toMap(StockAccountAddressEntity::getAddressCode, c -> c.getItems().get(0).getOrgWarehouseCode()));
            if (CollectionUtil.isEmpty(map)) {
                continue;
            }
            for (ProductChannelsInventory inventory : value) {
                if (StrUtil.isBlank(inventory.getWarehouseId()) && map.containsKey(inventory.getWarehouseId())) {
                    inventory.setOrgWarehouse(map.get(inventory.getWarehouseId()));
                    update.add(inventory);
                }
            }

        }
        if (CollectionUtil.isNotEmpty(update)) {
            productChannelsInventoryService.updateBatchById(update);
        }



    }

    @Override
    public List<ProductInventory> selectInVentory(Integer[] ids, Integer contextId) {

        List<ProductChannels> channels = listByIds(Arrays.asList(ids));
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到SKU映射信息！");
        }

        // 检查运营
        Integer userId = UserUtils.getCurrentUserId();

//        boolean authCheck = false;
//
//        List<UcPermissionEntity> permissions = ucPermissionService.findAllPermissionsByUserIdAndContextIdWithNoVerify(userId, contextId);
//        if (CollectionUtil.isNotEmpty(permissions)) {
//            long count = permissions.stream()
//                    .filter(p -> p.getLabelId().equalsIgnoreCase("sku.map.approval"))
//                    .count();
//            authCheck = count > 0;
//        }
//
//        if (!authCheck) {
//            channels = channels.stream()
//                    .filter(c -> Objects.nonNull(c.getOperationUserId()) && c.getOperationUserId().equals(userId))
//                    .collect(Collectors.toList());
//
//            if (CollectionUtil.isEmpty(channels)) {
//                throw new CommonException("操作失败,只可修改当前用户的SellerSku库存信息");
//            }
//        }

        List<String> flags = channels.stream()
                .map(ProductChannels::getAccountId)
                .collect(Collectors.toList());
        List<Account> accounts = accountService.lambdaQuery()
                .in(Account::getFlag, flags)
                .list();

        if (CollectionUtil.isEmpty(accounts)) {
            throw new CommonException("未获取到店铺信息！");
        }


        channels = check(contextId,channels, accounts);

        Map<String, Account> accountMap = accounts.stream()
                .collect(Collectors.toMap(Account::getFlag, Function.identity(), (a, b) -> a));

        // 获取库存信息

//        List<ProductChannelsInventory> channelsInventories  = productChannelsInventoryMapper.selectByChannelIds(channelIds);

        // 检查仓库配置 VC-DF、walmart-DSV、tiktok、wayfair、shopify
        List<String> filterChannels = Arrays.asList(
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DF.getEhengjian().toLowerCase(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.WALMART_DSV.getEhengjian().toLowerCase(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TIKTOK.getEhengjian().toLowerCase(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.SHOPIFY.getEhengjian().toLowerCase(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.WAYFAIR.getEhengjian().toLowerCase(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.SHEIN.getEhengjian().toLowerCase(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TARGET.getEhengjian().toLowerCase()
        );


        Map<Boolean, List<ProductChannels>> booleanListMap = channels.stream()
                .collect(Collectors.groupingBy(c -> filterChannels.contains(c.getSaleChannel().toLowerCase())));
        List<ProductInventory> productInventories = new ArrayList<>();

        // 获取所有库存信息
        List<Integer> channelIds = channels.stream().map(ProductChannels::getId).collect(Collectors.toList());

        List<ProductChannelsInventory> channelsInventories = productChannelsInventoryMapper.selectByChannelIds(channelIds);

        if (booleanListMap.containsKey(Boolean.TRUE)) {
            List<ProductInventory> inventories = handleWarehouseInventory(contextId, booleanListMap.get(Boolean.TRUE), channelsInventories, accountMap);
            productInventories.addAll(inventories);
        }

        if (booleanListMap.containsKey(Boolean.FALSE)) {
            List<ProductChannels> noWarehouseChannels = booleanListMap.get(Boolean.FALSE);
            List<String> noWarehouseFlags = noWarehouseChannels.stream().map(ProductChannels::getAccountId).distinct().collect(Collectors.toList());
            // 生成空库存信息
            List<ProductInventory> emptyInventory = getEmptyInventories(noWarehouseChannels, accountMap);

            Map<String, ProductChannelsInventory> stockMap = channelsInventories.stream()
                    .filter(c -> noWarehouseFlags.contains(c.getAccountFlag()))
                    .collect(Collectors.toMap(c -> c.getAccountFlag() + c.getSellerSku(), Function.identity(), (a, b) -> a));

            for (ProductInventory inventory : emptyInventory) {
                String key = inventory.getAccountFlag() + inventory.getSellerSku();
                if (stockMap.containsKey(key)) {
                    ProductChannelsInventory channelsInventory = stockMap.get(key);
                    inventory.setSellerSkuQuantity(
                            StrUtil.isNotBlank(channelsInventory.getQuantity()) ?
                                    new BigDecimal(channelsInventory.getQuantity()) :
                                    null
                    );
                }
            }
            productInventories.addAll(emptyInventory);
        }

        //获取erpSku库存
        buildSkuQuantity(filterChannels,contextId, channels, productInventories);
        for (ProductInventory inventory : productInventories) {
            inventory.setSku(inventory.getErpsku());
            inventory.setMark(inventory.getAccountInit() + inventory.getSellerSku() + inventory.getErpsku());
        }
        // 处理多映射情况
        List<ProductInventory> finallyData = new ArrayList<>();

        Map<String, ProductInventory> filterMap = new HashMap<>();

        for (ProductInventory inventory : productInventories) {
            if (StrUtil.isBlank(inventory.getWareHouse())) {
                finallyData.add(inventory);
                continue;
            }
            String key = inventory.getChannelId() + inventory.getSellerSku() + inventory.getWareHouse();
            if (!filterMap.containsKey(key)) {
                filterMap.put(key, inventory);
                continue;
            }
            ProductInventory pin = filterMap.get(key);

            if (Objects.isNull(inventory.getSellerSkuQuantity())) {
                continue;
            }

            if (Objects.isNull(pin.getSellerSkuQuantity())) {
                pin.setSellerSkuQuantity(inventory.getSellerSkuQuantity());
                continue;
            }
            pin.setSellerSkuQuantity(
                  pin.getSellerSkuQuantity().add(inventory.getSellerSkuQuantity())
            );

        }
        if (CollectionUtil.isNotEmpty(filterMap)) {
            finallyData.addAll(filterMap.values());
        }
        finallyData.sort(Comparator.comparing(ProductInventory::getSellerSku));
        return finallyData;
    }

    private List<ProductInventory> handleWarehouseInventory(Integer contextId, List<ProductChannels> productChannels, List<ProductChannelsInventory> channelsInventories, Map<String, Account> accountMap) {

        List<ProductChannelsInventory> result = new ArrayList<>();
        Map<String, List<ProductChannelsInventory>> map = channelsInventories.stream()
                .collect(Collectors.groupingBy(c -> c.getChannelId() + "&&" + c.getWarehouseId()));
        for (Map.Entry<String, List<ProductChannelsInventory>> entry : map.entrySet()) {
            List<ProductChannelsInventory> value = entry.getValue();
            ProductChannelsInventory inventory = value.get(0);
            if (value.size() > 1) {
                String warehouseStr = value.stream().map(ProductChannelsInventory::getOrgWarehouse).collect(Collectors.joining(","));
                inventory.setOrgWarehouse(warehouseStr);
            }
            result.add(inventory);
        }

        // 获取当前店铺所有的库存信息
        List<String> sellerSkus = productChannels.stream().map(ProductChannels::getSellerSku).distinct().collect(Collectors.toList());
        List<String> accountFlags = productChannels.stream().map(ProductChannels::getAccountId).collect(Collectors.toList());
        // 获取仓库配置
        List<AddressEntityVO> addressVos = productChannelsInventoryMapper.selectStockAccountAddressByAccountFlagsAndOrgId(accountFlags, contextId);
        if (CollectionUtil.isEmpty(addressVos)) {
            throw new CommonException("SellerSku" + sellerSkus + "未配置店铺仓库");
        }


        List<Integer> itemIds = addressVos.stream()
                .map(AddressEntityVO::getItemId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<Integer, AddressEntityVO> itemMap = addressVos.stream()
                .collect(Collectors.toMap(AddressEntityVO::getItemId, Function.identity(), (a, b) -> a));


        if (CollectionUtil.isNotEmpty(itemIds)) {
            List<AddressEntityVO> entityVOS = productChannelsInventoryMapper.selectReplaceWarehouseByStockAccountItemIds(itemIds);
            if (CollectionUtil.isNotEmpty(entityVOS)) {
                Map<Integer, List<AddressEntityVO>> replaceWarehouseMap = entityVOS.stream()
                        .collect(Collectors.groupingBy(AddressEntityVO::getItemId));

                for (Map.Entry<Integer, List<AddressEntityVO>> entry : replaceWarehouseMap.entrySet()) {
                    Integer itemId = entry.getKey();
                    if (!itemMap.containsKey(itemId)) {
                        continue;
                    }
                    AddressEntityVO vo = itemMap.get(itemId);
                    List<AddressEntityVO> value = entry.getValue();
                    for (AddressEntityVO entityVO : value) {
                        entityVO.setAddressCode(vo.getAddressCode());
                    }
                    addressVos.addAll(value);
                }
            }
        }



        // 生成所有的空库存数据
        List<ProductInventory> inventories = genInventoryData(accountMap,productChannels, addressVos);



        Map<String, List<ProductChannelsInventory>> stockMap = result.stream().collect(Collectors.groupingBy(c -> c.getAccountFlag() + c.getSellerSku() + c.getWarehouseId()));
        // 设置对应的库存数据
        for (ProductInventory inventory : inventories) {
            String key = inventory.getAccountFlag() + inventory.getSellerSku() + inventory.getWareHouseId();
            if (stockMap.containsKey(key)) {
                List<ProductChannelsInventory> productChannelsInventories = stockMap.get(key);
                BigDecimal qty = productChannelsInventories.stream()
                        .filter(c -> StrUtil.isNotBlank(c.getQuantity()))
                        .map(c -> new BigDecimal(c.getQuantity()))
                        .reduce(BigDecimal::add)
                        .get();
                inventory.setSellerSkuQuantity(qty);
            }
        }
        return inventories;

//        List<Integer> hasStock = channelsInventories.stream().map(ProductChannelsInventory::getChannelId).collect(Collectors.toList());
//
//        // 获取未获取到库存的SellerSku，返回空库存数据
//        List<ProductChannels> noStockChannels = productChannels.stream().filter(c -> !hasStock.contains(c.getId())).collect(Collectors.toList());
//        if (CollectionUtil.isNotEmpty(noStockChannels)) {
//            // 生成空库存数据
//            List<ProductInventory> inventories = genInventoryData(accountMap,noStockChannels, addressVos);
//            allInventory.addAll(inventories);
//        }
//        List<String> warehouseIds = channelsInventories.stream().map(ProductChannelsInventory::getWarehouseId).collect(Collectors.toList());
//        // 获取没有库存的仓库，返回空仓库库存数据
//        List<AddressEntityVO> noStockAddress = addressVos.stream()
//                .filter(c -> !warehouseIds.contains(c.getAddressCode()))
//                .collect(Collectors.toList());
//
//        // 生成所有的库存数据
//        // 分组仓库ID,处理ID多映射实体仓库 和 实体仓库映射多ID
//        if (CollectionUtil.isNotEmpty(noStockAddress)) {
//            addresCodeMap = noStockAddress.stream().collect(Collectors.groupingBy(AddressEntityVO::getAddressCode));
//            orgWarehouseMap = noStockAddress.stream().collect(Collectors.groupingBy(AddressEntityVO::getOrgWarehouseCode));
//        }
//

    }

    private static List<ProductInventory> genInventoryData(Map<String, Account> accountMap, List<ProductChannels> channels,  List<AddressEntityVO> addresses) {

        // 分组仓库ID,处理ID多映射实体仓库
        Map<String, List<AddressEntityVO>> addresCodeMap = addresses.stream().collect(Collectors.groupingBy(AddressEntityVO::getAddressCode));

        List<ProductInventory> allInventory = new ArrayList<>();

        List<ProductInventory> inventories = getEmptyInventories(channels, accountMap);

        for (ProductInventory inventory : inventories) {
            for (Map.Entry<String, List<AddressEntityVO>> entry : addresCodeMap.entrySet()) {
                List<AddressEntityVO> address = entry.getValue();
                String accountFlag = inventory.getAccountFlag();
                address = address.stream()
                        .filter(c -> accountFlag.equals(c.getAccountFlag()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(address)) {
                    continue;
                }

                String orgWarehouseCode = address.stream().map(AddressEntityVO::getOrgWarehouseCode).collect(Collectors.joining(","));
                ProductInventory copyBean = BeanCopyUtils.copyBean(inventory, ProductInventory.class);
                copyBean.setWareHouseId(entry.getKey());
                copyBean.setWareHouse(orgWarehouseCode);
                allInventory.add(copyBean);
            }
        }
        return allInventory;

        // 去重数据
//        return allInventory.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
//                () -> new TreeSet<>(Comparator.comparing(c -> c.getWareHouse() + c.getWareHouseId() + c.getSellerSku() + c.getAccountFlag())))
//                , ArrayList::new));
    }

    private List<ProductInventory> buildInventories(List<ProductChannels> channels, Map<String, Account> accountMap, List<AddressEntityVO> addresses) {
        for (ProductChannels channel : channels) {
            Account account = accountMap.get(channel.getAccountId());



        }

        return null;
    }

    @NotNull
    private static List<ProductInventory> getEmptyInventories(List<ProductChannels> channels, Map<String, Account> accountMap) {
        List<ProductInventory> inventoryList = channels.stream()
                .map(c -> {
                    Account account = accountMap.get(c.getAccountId());
                    ProductInventory inventory = new ProductInventory();
                    inventory.setChannelId(c.getId().longValue());
                    inventory.setAccountFlag(c.getAccountId());
                    inventory.setHandelingTime(c.getHandelingTime());
                    inventory.setSellerSku(c.getSellerSku());
                    inventory.setErpsku(c.getErpSku());
                    inventory.setSaleChannel(c.getSaleChannel());
                    inventory.setAccountId(account.getId());
                    inventory.setAccountInit(account.getAccountInit());
                    inventory.setAccountTitle(account.getTitle());
                    return inventory;
                }).collect(Collectors.toList());
        return inventoryList;
    }

    private void buildSkuQuantity(List<String> hasWarehouseChannels, Integer contextId, List<ProductChannels> channels, List<ProductInventory> productInventories) {
        Map<String, Map<String, Integer>> qtyMap = getInventoryQtyMap(contextId, channels);

        Map<Long, List<ProductInventory>> channelGroup = productInventories.stream()
                .collect(Collectors.groupingBy(ProductInventory::getChannelId));

        Map<Integer, ProductChannels> channelsMap = channels.stream().collect(Collectors.toMap(ProductChannels::getId, Function.identity(), (v1, v2) -> v1));

        for (Map.Entry<Long, List<ProductInventory>> entry : channelGroup.entrySet()) {
            for (ProductInventory inventory : entry.getValue()) {
                if (StrUtil.isBlank(inventory.getErpsku())) {
                    continue;
                }

                ProductChannels productChannels = channelsMap.get(inventory.getChannelId().intValue());
                if (Objects.isNull(productChannels)) {
                    continue;
                }

                List<String> finallySkus = Arrays.asList(inventory.getErpsku().split(","));
                // 如果是父SKU,需要使用子SKU替换
                if (Objects.equals(productChannels.getIsParentSku(),1)) {
                    List<SkuChildren> children = productChannels.getChildren();
                    if (CollectionUtil.isEmpty(children)) {
                        continue;
                    }

                    List<String> childSkus = children.stream().map(SkuChildren::getSku).collect(Collectors.toList());
                    finallySkus = childSkus;
                }

                for (String sku : finallySkus) {
                    if (!qtyMap.containsKey(sku)) {
                        inventory.appendSkuQuantity("-");
                        continue;
                    }

                    Map<String, Integer> map = qtyMap.get(sku);

                    if (!hasWarehouseChannels.contains(inventory.getSaleChannel())) {
                        Integer qty = map.entrySet().stream()
                                .map(Map.Entry::getValue)
                                .reduce(Integer::sum)
                                .orElse(0);
                        Integer total = qty;
                        inventory.appendSkuQuantity(total);
                        continue;
                    }

                    boolean hasQty = false;
                    Integer totalQty = 0;
                    String wareHouse = inventory.getWareHouse();
                    String[] splitWarehouse = wareHouse.split(",");


                    for (String s : splitWarehouse) {
                        if (map.containsKey(s)) {
                            hasQty = true;
                            totalQty = totalQty + map.get(s);
                        }
                    }

                    if (!hasQty) {
                        inventory.appendSkuQuantity("-");
                        continue;
                    }
                    inventory.appendSkuQuantity(totalQty);
                }
            }
        }
    }

    private static List<ProductChannelsInventory> buildInventories(List<ProductChannels> channels, List<StockAccountAddressEntity> addressEntities, Map<String, Account> accountMap) {
        List<ProductChannelsInventory> allInventories = new ArrayList<>();
        for (ProductChannels channel : channels) {
            if (CollectionUtil.isNotEmpty(addressEntities)) {
                List<ProductChannelsInventory> inventories = addressEntities.stream()
                        .filter(c -> CollectionUtil.isNotEmpty(c.getItems()))
                        .map(c -> buildInventory(channel, c, accountMap))
                        .collect(Collectors.toList());
                allInventories.addAll(inventories);
            } else {
                allInventories.add(buildInventory(channel, null, accountMap));
            }
        }
        return allInventories;
    }

    private static List<ProductChannelsInventory> buildInventories(ProductChannels channel, List<String> filterChannels, Map<String, List<StockAccountAddressEntity>> addressMap, Map<String, Account> accountMap) {
        List<ProductChannelsInventory> allInventory = new ArrayList<>();
        if (filterChannels.contains(channel.getSaleChannel())) {
            // 需要仓库
            List<StockAccountAddressEntity> entities = addressMap.get(channel.getSaleChannel());
            if (CollectionUtil.isNotEmpty(entities)) {
                List<ProductChannelsInventory> inventories = entities.stream()
                        .map(c ->buildInventory(channel, c, accountMap))
                        .collect(Collectors.toList());
                allInventory.addAll(inventories);
            }
        } else {
            ProductChannelsInventory inventory = buildInventory(channel, null, accountMap);
            allInventory.add(inventory);
        }
        return allInventory;
    }

    @NotNull
    private static ProductChannelsInventory buildInventory(ProductChannels channel, StockAccountAddressEntity address, Map<String, Account> accountMap) {
        ProductChannelsInventory inventory = new ProductChannelsInventory();
        inventory.setChannelId(channel.getId());
        inventory.setAccountFlag(channel.getAccountId());
        inventory.setSellerSku(channel.getSellerSku());
        if (Objects.nonNull(address)) {
            inventory.setOrgWarehouse(address.getItems().get(0).getOrgWarehouseCode());
            inventory.setWarehouseId(address.getAddressCode());
        }
        if (accountMap.containsKey(channel.getAccountId())) {
            inventory.setAccountId(accountMap.get(channel.getAccountId()).getId());
        }
        return inventory;
    }

    public List<ProductChannels> check(Integer orgId, List<ProductChannels> channels, List<Account> accounts) {

//        channels = channels.stream()
//                .filter(c -> Objects.equals(c.getIsPush(), "Y"))
//                .collect(Collectors.toList());
//        if (CollectionUtil.isEmpty(channels)) {
//            throw new CommonException("SellerSku未开启库存推送");
//        }

        channels = channels.stream()
                .filter(c -> Objects.equals(ApprovalEnum.APPROVED.getValue(), c.getApprovalStatus()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("SellerSku审批未通过");
        }
        if (CollectionUtil.isEmpty(accounts)) {
            return channels;

        }
        List<String> closePush = accountService.checkAccountAutoPush(accounts);
        channels = channels.stream()
                .filter(c -> !closePush.contains(c.getAccountId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("SellerSku对应店铺未开启库存推送");
        }

        boolean enableConfig = systemGlobalConfigService.isEnableConfig(orgId, SystemGlobalConfigEnum.SKU_VERSION_INIT_CONFIG);


        Map<String, String> titleMap = accounts.stream().collect(Collectors.toMap(Account::getFlag, Account::getTitle, (v1, v2) -> v1));

        Map<Boolean, List<ProductChannels>> map = channels.stream().collect(Collectors.groupingBy(c -> Objects.equals(c.getIsParentSku(), 1)));

        List<ProductChannels> errorChannels = map.get(!enableConfig);

        if (CollectionUtil.isNotEmpty(errorChannels)) {

            List<String> errorSkus = errorChannels.stream()
                    .map(ProductChannels::getErpSku)
                    .map(c -> c.split(","))
                    .flatMap(Stream::of)
                    .collect(Collectors.toList());

            // 需要确认不是辅料
            Map<String, Products> productsMap = new HashMap<>();
            if (enableConfig) {
                List<Products> products = productsService.lambdaQuery()
                        .eq(Products::getOrgId, orgId)
                        .in(Products::getErpsku, errorSkus)
                        .eq(Products::getProductType, ProductTypeEnum.PRODUCT.getCode())
                        .select(Products::getErpsku, Products::getProductType)
                        .list();

                if (CollectionUtil.isEmpty(products)) {
                    return channels;
                }

                productsMap.putAll(
                        products.stream().collect(Collectors.toMap(Products::getErpsku, Function.identity(), (v1, v2) -> v1))
                );
            }



            MultipleVersionExceptionInfo info = new MultipleVersionExceptionInfo();
            List<Map<Object, Object>> errors = new ArrayList<>();
            for (ProductChannels channel : errorChannels) {

                String erpSku = channel.getErpSku();
                if (StrUtil.isBlank(erpSku)) {
                    continue;
                }

                if (!enableConfig || Stream.of(erpSku.split(",")).filter(productsMap::containsKey).count() > 0L) {
                    errors.add(
                            MapUtil.builder().put("accountFlag", titleMap.get(channel.getAccountId()))
                                    .put("sellerSku", channel.getSellerSku())
                                    .build()
                    );
                }

            }

            info.setEnableConfig(enableConfig);
            info.setData(errors);

            if (CollectionUtil.isNotEmpty(errors)) {
                throw new CheckException(String.format("以下配对信息中存在%s级SKU，但多版本开关已%s", enableConfig ? "子" : "父", enableConfig ? "开启" : "关闭"), info);
            }


        }


        return channels;
    }

    private List<ProductInventory> buildInventoryData(List<ProductChannels> channels, List<ProductChannelsInventory> channelsInventories, Map<String, Account> accountMap, Map<String, Map<String, Integer>> qtyMap) {
        Map<String,ProductChannels> channelsMap = channels.stream()
                .collect(Collectors.toMap(ProductChannels::getSellerSku, Function.identity(), (a, b) -> a));
        List<ProductInventory> inventories = new ArrayList<>();
        for (ProductChannelsInventory channelsInventory : channelsInventories) {
            if (!accountMap.containsKey(channelsInventory.getAccountFlag()) || !channelsMap.containsKey(channelsInventory.getSellerSku())) {
                continue;
            }
            ProductChannels productChannels = channelsMap.get(channelsInventory.getSellerSku());
            String erpSku = productChannels.getErpSku();
            ProductInventory inventory = buildInventory(channelsInventory, accountMap);
            if (StrUtil.isNotBlank(erpSku)) {
                String[] erpSkus = erpSku.split(",");
                for (String sku : erpSkus) {
                    ProductInventory copyBean = BeanCopyUtils.copyBean(inventory, ProductInventory.class);
                    copyBean.setSku(sku);
                    Map<String, Integer> map = qtyMap.get(sku);
                    copyBean.setSkuQuantity(
                            CollectionUtil.isNotEmpty(map) && map.containsKey(inventory.getWareHouse()) ?
                                    map.get(inventory.getWareHouse()).toString() : "0"
                    );
                    inventories.add(copyBean);
                }
            } else {
                inventories.add(inventory);
            }
        }
        return inventories;
    }

    private Map<String, Map<String, Integer>> getInventoryQtyMap(Integer contextId, List<ProductChannels> channels) {

        boolean enableConfig = systemGlobalConfigService.isEnableConfig(contextId, SystemGlobalConfigEnum.SKU_VERSION_INIT_CONFIG);

        Map<Boolean, List<ProductChannels>> map = channels.stream().collect(Collectors.groupingBy(c -> Objects.equals(c.getIsParentSku(), 1)));


        List<ProductChannels> errorChannels = map.get(!enableConfig);

        if (CollectionUtil.isNotEmpty(errorChannels)) {

            List<String> errorSkus = errorChannels.stream()
                    .map(ProductChannels::getErpSku)
                    .map(c -> c.split(","))
                    .flatMap(Stream::of)
                    .collect(Collectors.toList());

            if (!enableConfig) {
                throw new CheckException(
                        String.format("多版本开关为%s状态，以下SKU非%s级SKU", enableConfig ? "开启" : "关闭", enableConfig ? "父" : "子"),
                        new MultipleVersionExceptionInfo(enableConfig, errorSkus)
                );
            }
            // 需要确认不是辅料
            List<Products> products = productsService.lambdaQuery()
                    .eq(Products::getOrgId, contextId)
                    .in(Products::getErpsku, errorSkus)
                    .eq(Products::getProductType, ProductTypeEnum.PRODUCT.getCode())
                    .select(Products::getErpsku, Products::getProductType)
                    .list();

            if (CollectionUtil.isNotEmpty(products)) {
                errorSkus = products.stream().map(Products::getErpsku).collect(Collectors.toList());
                throw new CheckException(
                        String.format("多版本开关为%s状态，以下SKU非%s级SKU", enableConfig ? "开启" : "关闭", enableConfig ? "父" : "子"),
                        new MultipleVersionExceptionInfo(enableConfig, errorSkus)
                );
            }

        }


        long count =  channels.stream().filter(c -> Objects.equals(c.getIsParentSku(), 1)).count();


        // 如果存在父SKU数据,需要查询父SKU下所有子SKU数据
        Map<String, Map<String, Integer>> qtyMap = new HashMap<>();
        List<String> erpSkus = channels.stream()
                .map(ProductChannels::getErpSku)
                .map(c -> c.split(","))
                .flatMap(Stream::of)
                .collect(Collectors.toList());

        if (count > 0) {
            // 把当前所有的sku作为父查询
            Map<String, List<SkuChildren>> childrenMap = skuChildrenService.selectSkuByParentSku(contextId, erpSkus);
            if (CollectionUtil.isNotEmpty(childrenMap)) {
                // 添加到SKU数据列表
                erpSkus.addAll(
                        childrenMap.values().stream().flatMap(List::stream).map(SkuChildren::getSku).distinct().collect(Collectors.toList())
                );
                // 给每个SKU映射设置子数据
                childrenAssembly(channels, childrenMap);
            }
        }
        List<ErpSkuStockVO> vos = productChannelsMapper.selectErpSkuInventory(contextId, erpSkus);

        if (CollectionUtil.isEmpty(vos)) {
            return qtyMap;
        }

        Map<String, List<ErpSkuStockVO>> skuGroup = vos.stream()
                .collect(Collectors.groupingBy(ErpSkuStockVO::getSku));

        for (String sku : erpSkus) {
            if (!skuGroup.containsKey(sku)) {
                continue;
            }
            List<ErpSkuStockVO> stockVS = skuGroup.get(sku);
            if (!qtyMap.containsKey(sku)) {
                Map<String, Integer> inventoryMap = stockVS.stream().collect(Collectors.toMap(ErpSkuStockVO::getOrgWarehouseCode, ErpSkuStockVO::getInventoryAvailable));
                qtyMap.put(sku, inventoryMap);
                continue;
            }
        }

//        for (ProductChannels channel : channels) {
//            String erpSku = channel.getErpSku();
//            String[] split = erpSku.split(",");
//            for (String sku : split) {
//                if (!skuGroup.containsKey(sku)) {
//                    continue;
//                }
//                List<ErpSkuStockVO> stockVS = skuGroup.get(sku);
//                if (!qtyMap.containsKey(sku)) {
//                    Map<String, Integer> inventoryMap = stockVS.stream().collect(Collectors.toMap(ErpSkuStockVO::getOrgWarehouseCode, ErpSkuStockVO::getInventoryAvailable));
//                    qtyMap.put(sku, inventoryMap);
//                    continue;
//                }
//            }
//        }
        return qtyMap;
    }

    @NotNull
    private static ProductInventory buildInventory(ProductChannelsInventory channelsInventory, Map<String, Account> accountMap) {
        ProductInventory inventory = new ProductInventory();
        Account account = accountMap.get(channelsInventory.getAccountFlag());
        inventory.setAccountFlag(account.getFlag());
        if (Objects.nonNull(channelsInventory.getQuantity())) {
            inventory.setSellerSkuQuantity(new BigDecimal(channelsInventory.getQuantity()));
        }
        inventory.setAccountFlag(channelsInventory.getAccountFlag());
        inventory.setSellerSku(channelsInventory.getSellerSku());
        inventory.setAccountInit(account.getAccountInit());
        inventory.setWareHouseId(channelsInventory.getWarehouseId());
        inventory.setWareHouse(channelsInventory.getOrgWarehouse());
        inventory.setAccountId(account.getId());
        inventory.setUnit(channelsInventory.getUnit());
        return inventory;
    }

    private static List<ProductChannels> filterApprovalAndPushStatus(Integer[] ids, List<ProductChannels> channels) {
        channels = channels.stream()
                .filter(c -> ApprovalEnum.APPROVED.getValue().equals(c.getApprovalStatus()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("只有审批通过的SKU映射信息可以修改库存信息！");
        }

        channels = channels.stream()
                .filter(c -> Objects.equals("Y", c.getIsPush()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException(ids.length == 1 ? "该SellerSku未开启库存推送" : "当前选择的所有SellerSku均未开启库存推送！");
        }
        return channels;
    }


    @Override
    public void updateRestricted(String accountId, String sellerSku, String restrictedReason,boolean eq) {
        this.lambdaUpdate()
                .eq(ProductChannels::getAccountId, accountId)
                .eq(eq, ProductChannels::getSellerSku, sellerSku)
                .ne(!eq, ProductChannels::getSellerSku, sellerSku)
                .set(ProductChannels::getIsRestricted, StrUtil.isNotBlank(restrictedReason))
                .set(ProductChannels::getRestrictedReason, restrictedReason)
                .update();
    }

    @Override
    public void updateRestricted(String accountId, List<String> sellerSkus, String restrictedReason, boolean in) {
        this.lambdaUpdate()
                .eq(ProductChannels::getAccountId, accountId)
                .in(in, ProductChannels::getSellerSku, sellerSkus)
                .notIn(!in, ProductChannels::getSellerSku, sellerSkus)
                .set(ProductChannels::getIsRestricted, StrUtil.isNotBlank(restrictedReason))
                .set(ProductChannels::getRestrictedReason, restrictedReason)
                .update();

    }

    @Override
    public void updateRestricted(Integer id, String restrictedReason) {
        this.lambdaUpdate()
                .eq(ProductChannels::getId, id)
                .set(ProductChannels::getIsRestricted, StrUtil.isNotBlank(restrictedReason))
                .set(ProductChannels::getRestrictedReason, restrictedReason)
                .update();
    }

    @Override
    public void syncRestricte(Integer... ids) {
        List<ProductChannels> channels = this.listByIds(Arrays.asList(ids));
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到SKU映射信息");
        }

        for (ProductChannels channel : channels) {
            JSONObject object = new JSONObject();
            object.put("sku", channel.getSellerSku());
            object.put("storeName", channel.getAccountId());
            rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.SKU_RESTRICTED_REQ_ROUTING_KEY, object);

        }

    }

    @Override
    public ProductChannels asinCountryCheck(AsinCountryCheckDTO dto) {

        String asin = dto.getAsin();
        Integer contextId = dto.getContextId();

        List<String> checkSaleChannels = Arrays.asList(
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.SC.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DF.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_DI.getEhengjian(),
                SyncMappingEnum.SkuGoodsMapSaleChannelEnum.VC_USPO.getEhengjian()
        );

        List<Account> accounts = accountService.selectByAccountsCountry(contextId, dto.getAccountFlag(),checkSaleChannels);

        if (CollectionUtil.isEmpty(accounts)) {
            throw new CommonException("未获取到店铺信息：" + dto.getAccountFlag());
        }

//        ProductChannels query = new ProductChannels();
//        query.setAsin(asin);
////        query.setApprovalStatus(ApprovalEnum.APPROVED.getValue());
//        query.setSellStatus(SaleStateEnum.HIT_SHELVE.getValue());
//        query.setOrgId(contextId);
//        List<ProductChannels> productChannels = productChannelsMapper.selectProductChannels(query);
//        if (CollectionUtil.isEmpty(productChannels)) {
//            return null;
//        }
//        productChannels = productChannels.stream()
//                .filter(c -> checkSaleChannels.contains(c.getSaleChannel()) && !Objects.equals(c.getApprovalStatus(), ApprovalEnum.SUBMIT.getValue()))
//                .collect(Collectors.toList());
        List<ProductChannels> productChannels = this.lambdaQuery()
                .eq(ProductChannels::getAsin, asin)
                .eq(ProductChannels::getOrgId, contextId)
                .eq(ProductChannels::getSellStatus, SaleStateEnum.HIT_SHELVE.getValue())
                .ne(ProductChannels::getApprovalStatus, ApprovalEnum.SUBMIT.getValue())
                .in(ProductChannels::getAccountId, accounts.stream().map(Account::getFlag).collect(Collectors.toList()))
                .list();


        if (CollectionUtil.isEmpty(productChannels)) {
            return null;
        }


        // 检查当前获取的数据是否相同
        if (!checkSameChannelRelates(productChannels)) {
            return null;
        }
        ProductChannels channels = productChannels.stream()
                .filter(c -> Objects.nonNull(c.getOperationUserId()))
                .findAny().orElse(productChannels.get(0));

        // 获取项目信息
        List<Integer> lineIds = new ArrayList<>();
        List<Integer> productIds = new ArrayList<>();
        List<ProductChannelRelate> relates = channels.getProductRelates();
        if (CollectionUtil.isNotEmpty(relates)) {
            List<Integer> pIds = relates.stream()
                    .map(ProductChannelRelate::getProductId)
                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            productIds.addAll(pIds);
        }
        if (Objects.nonNull(channels.getLineId())) {
            lineIds.add(channels.getLineId());
        }
        if (Objects.nonNull(channels.getProductId())) {
            productIds.add(channels.getProductId());
        }
        // 查询产品信息
        List<Products> products = productsService.lambdaQuery()
                .eq(Products::getOrgId, contextId)
                .in(Products::getId, productIds)
                .select(Products::getId, Products::getLineId, Products::getCategorySecondId, Products::getCategoryFirstId)
                .list();

        if (CollectionUtil.isEmpty(products)) {
            return channels;
        }
        // 构建分类信息
        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> productsService.buildProductsCategoryName(products), threadPoolTaskExecutor)
                .exceptionally(e -> {
                    log.error("设置产品分类信息异常:{}", e.getMessage());
                    return null;
                });

        // 获取项目信息
        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> productsService.buildLineName(channels.getOrgId(), products), threadPoolTaskExecutor)
                .exceptionally(e -> {
                    log.error("设置产品项目信息异常:{}", e.getMessage());
                    return null;
                });
        try {
            CompletableFuture.allOf(f1, f2).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            if (e instanceof TimeoutException) {
                log.error("产品分类项目信息查询超时:{}", e.getMessage());
            }
        }

        Map<Integer, Products> productsMap = products.stream()
                .collect(Collectors.toMap(Products::getId, Function.identity(), (a, b) -> a));

        if (CollectionUtil.isNotEmpty(relates)) {
            for (ProductChannelRelate relate : relates) {
                if (Objects.isNull(relate.getProductId()) || !productsMap.containsKey(relate.getProductId())) {
                    continue;
                }
                Products p = productsMap.get(relate.getProductId());
                relate.setCategoryName(p.getCategoryName());
                relate.setLineName(p.getLineName());
            }
        }

        if (productsMap.containsKey(channels.getProductId())) {
            Products p = productsMap.get(channels.getProductId());
            channels.setCategoryName(p.getCategoryName());
            channels.setLineName(p.getLineName());
        }
        return channels;
    }

    private boolean checkSameChannelRelates(List<ProductChannels> productChannels) {

        productChannelRelateService.assignChannelRelates(productChannels);

        Map<Boolean, List<ProductChannels>> map = productChannels.stream()
                .collect(Collectors.groupingBy(c -> StrUtil.isBlank(c.getSkuAntQuantity())));

        if (map.containsKey(Boolean.TRUE)) {
            return false;
        }
        int count = map.get(Boolean.FALSE).stream()
                .collect(Collectors.groupingBy(ProductChannels::getSkuAntQuantity))
                .size();
        if (count > 1) {
            return false;
        }
        return true;

        // 检查类型
//        ProductChannels firstChannel = productChannels.get(0);
//        String type = firstChannel.getType();
//        long count = productChannels.stream().filter(c -> !Objects.equals(type, c.getType())).count();
//        if (count > 0) {
//            return false;
//        }
//         检查运营
//        Integer operationUserId = firstChannel.getOperationUserId();
//        count = productChannels.stream().filter(c -> !Objects.equals(operationUserId, c.getOperationUserId())).count();
//        if (count > 0) {
//            return false;
//        }
        // 检查SKU信息
//        if (Objects.equals(type, ProductRelateTypeEnum.SINGLE.getValue())) {
//            count = productChannels.stream()
//                    .filter(c -> !Objects.equals(c.getErpSku() + c.getQuantity(), firstChannel.getErpSku() + firstChannel.getQuantity()))
//                    .count();
//            if (count > 0) {
//                return false;
//            }
//            ProductChannelRelate relate = new ProductChannelRelate();
//            relate.setErpsku(firstChannel.getErpSku());
//            relate.setQty(1);
//            relate.setActive("Y");
//             获取产品信息
//            Products products = productsService.lambdaQuery().eq(Products::getErpsku, firstChannel.getErpSku())
//                    .eq(Products::getOrgId, firstChannel.getOrgId())
//                    .one();
//            if (Objects.nonNull(products)) {
//                relate.setProductId(products.getId());
//            }
//            for (ProductChannels channel : productChannels) {
//                ProductChannelRelate bean = BeanCopyUtils.copyBean(relate, ProductChannelRelate.class);
//                bean.setProductChannelId(channel.getId());
//                channel.setProductRelates(Collections.singletonList(bean));
//            }
//            return true;
//        }
//
//        List<Integer> channelIds = productChannels.stream()
//                .map(ProductChannels::getId)
//                .collect(Collectors.toList());
//        List<ProductChannelRelate> relates = productChannelRelateService.lambdaQuery()
//                .in(ProductChannelRelate::getProductChannelId, channelIds)
//                .list();
//        if (CollectionUtil.isEmpty(relates)) {
//            return false;
//        }
//        Map<Integer, List<ProductChannelRelate>> relateMap = relates.stream()
//                .collect(Collectors.groupingBy(ProductChannelRelate::getProductChannelId));
//
//        count = productChannels.stream()
//                .filter(c -> !relateMap.containsKey(c.getId()))
//                .count();
//
//        if (count > 0) {
//            return false;
//        }

        // 检查是否所有关联都相同
//        Set<String> set = relateMap.entrySet()
//                .stream()
//                .map(entry -> entry.getValue().stream().map(c -> c.getErpsku() + c.getQty()).collect(Collectors.joining("&&")).trim())
//                .collect(Collectors.toSet());
//        if (set.size() == 1) {
//            return false;
//        }
//        // 设置关联信息
//        for (ProductChannels channel : productChannels) {
//            channel.setProductRelates(relateMap.get(channel.getId()));
//        }
//        return true;
    }

    private List<SkuAsinCheckVO> getBundelChannelSkuAsinInfo(List<ProductChannels> bundelChannels) {
        List<Integer> channelIds = bundelChannels.stream().map(ProductChannels::getId).collect(Collectors.toList());
        List<ProductChannelRelate> channelRelates = productChannelRelateService.selectByChannelIds(channelIds);
        if (CollectionUtil.isEmpty(channelRelates)) {
            return new ArrayList<>();
        }
        Map<Integer, List<ProductChannelRelate>> relateMap = channelRelates.stream()
                .collect(Collectors.groupingBy(ProductChannelRelate::getProductChannelId));

        return bundelChannels.stream()
                .filter(c -> relateMap.containsKey(c.getId()))
                .map(c -> {
                    SkuAsinCheckVO vo = new SkuAsinCheckVO();
                    vo.setAccountFlag(c.getAccountId());
                    vo.setAccountTitle(c.getAccountTitle());
                    vo.setSellerSku(c.getSellerSku());
                    vo.setAsin(c.getAsin());
                    List<ProductChannelRelate> relates = relateMap.get(c.getId());

                    List<SkuAsinCheckVO.SkuDetail> details = relates.stream()
                            .map(r -> {
                                SkuAsinCheckVO.SkuDetail detail = new SkuAsinCheckVO.SkuDetail();
                                detail.setSku(r.getErpsku());
                                detail.setQuantity(r.getQty());
                                return detail;
                            }).collect(Collectors.toList());
                    vo.setDetails(details);
                    return vo;
                }).collect(Collectors.toList());
    }

    @NotNull
    private static List<SkuAsinCheckVO> getSingleChannelSkuAsinInfo(List<ProductChannels> singleChannels) {

        List<SkuAsinCheckVO> singleChecks = singleChannels.stream()
                .map(c -> {
                    SkuAsinCheckVO vo = new SkuAsinCheckVO();
                    vo.setAccountFlag(c.getAccountId());
                    vo.setAccountTitle(c.getAccountTitle());
                    vo.setSellerSku(c.getSellerSku());
                    vo.setAsin(c.getAsin());

                    SkuAsinCheckVO.SkuDetail detail = new SkuAsinCheckVO.SkuDetail();
                    detail.setSku(c.getErpSku());
                    detail.setQuantity(1);
                    vo.setDetails(Collections.singletonList(detail));
                    return vo;
                }).collect(Collectors.toList());
        return singleChecks;
    }



    @Override
    public  Map<String, List<String>> queryProductChannelsByAsin(Long orgId, List<String> asinList) {
        Map<String, List<String>> resultErrorMap = asinList.stream().collect(Collectors.toMap(e -> e, e -> Arrays.asList(e), (a, b) -> a));
        List<ProductChannels> list = lambdaQuery()
                .select(ProductChannels::getErpSku, ProductChannels::getAsin)
                .eq(ProductChannels::getOrgId, orgId)
                .in(ProductChannels::getAsin, asinList)
                .groupBy(ProductChannels::getErpSku, ProductChannels::getAsin)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return resultErrorMap;
        }

        List<String> erpSkuList = list.stream().map(e -> e.getErpSku()).distinct().collect(Collectors.toList());
        List<Products> productsList = productsService.lambdaQuery()
                .select(Products::getErpsku,Products::getCategorySecondId)
                .eq(Products::getOrgId, orgId)
                .in(Products::getErpsku, erpSkuList).groupBy(Products::getErpsku, Products::getCategorySecondId).list();
        if (CollectionUtils.isEmpty(productsList)) {
            return resultErrorMap;
        }


        List<Integer> secondIdList = productsList.stream().filter(e->null != e.getCategorySecondId()).map(e -> e.getCategorySecondId().intValue()).distinct().collect(Collectors.toList());
        List<ScGoodsCategory> allGoodsList = scGoodsCategoryService.list();
        Map<Integer, ScGoodsCategory> categoryMap = allGoodsList.stream().collect(Collectors.toMap(ScGoodsCategory::getCategoryId, Function.identity(), (a, b) -> a));
        //全部当前分类
        List<ScGoodsCategory> goodsCategories = allGoodsList.stream().filter(e -> secondIdList.contains(e.getCategoryId())).collect(Collectors.toList());
        Map<Integer, Integer> goodsMap = new HashMap<>();
        List<Integer> secondValueList = new ArrayList<>();
        for (ScGoodsCategory scGoodsCategory : goodsCategories) {
            ScGoodsCategory levelCategory = scGoodsCategoryService.getLevelCategory(scGoodsCategory, categoryMap, 2);
            if (null != levelCategory && null != levelCategory.getCategoryId()) {
                secondValueList.add(levelCategory.getCategoryId());
                goodsMap.put(scGoodsCategory.getCategoryId(), levelCategory.getCategoryId());
            }
        }

        if (CollectionUtils.isEmpty(secondValueList)) {
            return resultErrorMap;
        }
        List<Products> products = productsService.lambdaQuery().select(Products::getErpsku,Products::getCategorySecondId)
                .eq(Products::getOrgId, orgId)
                .in(Products::getCategorySecondId, secondValueList).groupBy(Products::getErpsku,Products::getCategorySecondId).list();
        if (CollectionUtils.isEmpty(products)) {
            return resultErrorMap;
        }

        List<String> erpSkuQueryList = products.stream().map(e -> e.getErpsku()).collect(Collectors.toList());

        List<ProductChannels> resultAsinList = lambdaQuery().select(ProductChannels::getAsin,ProductChannels::getErpSku)
                .eq(ProductChannels::getOrgId, orgId)
                .in(ProductChannels::getErpSku, erpSkuQueryList).groupBy(ProductChannels::getAsin,ProductChannels::getErpSku).list();

        if (CollectionUtils.isEmpty(resultAsinList)) {
            return resultErrorMap;
        }

        Map<String, List<ProductChannels>> asinGroupMap = list.stream().collect(Collectors.groupingBy(e -> e.getAsin()));
        Map<String, List<Products>> erpSkuMap = productsList.stream().collect(Collectors.groupingBy(e -> e.getErpsku()));
        Map<Long, List<Products>> categoryV2Map = products.stream().collect(Collectors.groupingBy(e -> e.getCategorySecondId()));
        Map<String, List<ProductChannels>> channelResultMap = resultAsinList.stream().collect(Collectors.groupingBy(e -> e.getErpSku()));

        return  buildAsinQueryVO(asinList, asinGroupMap, erpSkuMap, goodsMap,categoryV2Map,channelResultMap);
    }

    @Override
    public void repairOrgId() {
        List<ProductChannelsInventory> inventories = productChannelsInventoryService.lambdaQuery()
                .isNull(ProductChannelsInventory::getOrgId)
                .list();
        if (CollectionUtil.isEmpty(inventories)) {
            return;
        }
        log.info("repair count {}", inventories.size());
        List<Integer> accountIds = inventories.stream()
                .filter(c -> Objects.nonNull(c.getAccountId()))
                .map(ProductChannelsInventory::getAccountId)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(accountIds)) {
            log.info("repair error , empty accountIds");
            return;
        }

        List<Account> accounts = accountService.listByIds(accountIds);
        if (CollectionUtil.isEmpty(accounts)) {
            log.info("repair error , empty accountIds");
            return;
        }
        Map<Integer, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getId, Function.identity(), (a, b) -> a));
        for (ProductChannelsInventory inventory : inventories) {
            if (accountMap.containsKey(inventory.getAccountId())) {
                inventory.setOrgId(accountMap.get(inventory.getAccountId()).getOrgId());
            }
        }
        productChannelsInventoryService.updateBatchById(inventories);
    }

    private Map<String, List<String>> buildAsinQueryVO(List<String> asinList, Map<String, List<ProductChannels>> asinGroupMap, Map<String, List<Products>> erpSkuMap, Map<Integer, Integer> goodsMap, Map<Long, List<Products>> categoryV2Map, Map<String, List<ProductChannels>> channelResultMap) {
        Map<String, List<String>> resultMap = new HashMap<>();
        for (String asin : asinList) {
            List<ProductChannels> list = asinGroupMap.get(asin);
            if (CollectionUtils.isEmpty(list)) {
                resultMap.put(asin, Arrays.asList(asin).stream().collect(Collectors.toList()));
                continue;
            }
            List<String> erpSkuList = list.stream().filter(e->!StringUtils.isEmpty(e.getErpSku())).map(e -> e.getErpSku()).distinct().collect(Collectors.toList());

            List<Integer> secondList = new ArrayList<>();
            for (String erpSku : erpSkuList) {
                List<Products> products = erpSkuMap.get(erpSku);
                if (CollectionUtils.isEmpty(products)) continue;
                products = products.stream().filter(e->null != e.getCategorySecondId()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(products)) continue;
                secondList.addAll(products.stream().map(e->e.getCategorySecondId().intValue()).distinct().collect(Collectors.toList()));
            }
            secondList = secondList.stream().filter(e->null != e).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(secondList)) {
                resultMap.put(asin, Arrays.asList(asin).stream().collect(Collectors.toList()));
                continue;
            }

            List<Integer> sendIdListV2 = new ArrayList<>();
            for (Integer secondId : secondList) {
                Integer resultSecondId = goodsMap.get(secondId);
                if (null != resultSecondId) {
                    sendIdListV2.add(resultSecondId);
                }
            }
            sendIdListV2 = sendIdListV2.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sendIdListV2)) {
                resultMap.put(asin, Arrays.asList(asin).stream().collect(Collectors.toList()));
                continue;
            }

            List<String> erpskuList = new ArrayList<>();
            for (Integer sendId : sendIdListV2) {
                List<Products> productsList = categoryV2Map.get(sendId.longValue());
                if (CollectionUtils.isEmpty(productsList)) continue;
                erpskuList.addAll(productsList.stream().map(e->e.getErpsku()).distinct().collect(Collectors.toList()));
            }
            erpskuList = erpskuList.stream().filter(e->!StringUtils.isEmpty(e)).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(erpskuList)) {
                resultMap.put(asin, Arrays.asList(asin).stream().collect(Collectors.toList()));
                continue;
            }
            List<String> asinResultList = new ArrayList<>();
            for (String erpSku : erpskuList) {
                List<ProductChannels> channels = channelResultMap.get(erpSku);
                if (CollectionUtils.isEmpty(channels)) continue;
                asinResultList.addAll(channels.stream().map(e->e.getAsin()).collect(Collectors.toList()));
            }
            asinResultList = asinResultList.stream().filter(e->!StringUtils.isEmpty(e)).distinct().collect(Collectors.toList());;
            if (CollectionUtils.isEmpty(asinResultList)) {
                resultMap.put(asin, Arrays.asList(asin).stream().collect(Collectors.toList()));
                continue;
            }
            resultMap.put(asin, asinResultList);
        }
        return resultMap;
    }


    @Override
    public ChannelImportResultVO checkAsinSameAsSku(Integer contextId, Integer[] ids) {
        List<ProductChannels> channels = this.listByIds(Arrays.asList(ids));
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到SKU映射信息");
        }
        channels = channels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getAsin()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(channels)) {
            ChannelImportResultVO vo = new ChannelImportResultVO();
            vo.setConfirm(-1);
            return vo;
        }

        Map<String, Integer> configMap = labelConfigService.getConfigMapByModule(SysLabelConfigEnum.OPERATE_REQUIRED.getModule(), contextId);
        Integer config = configMap.get(SysLabelConfigEnum.ASIN_DATA_APPLICATION.getLabelConfig());
        if (Objects.equals(config, 1)) {
            return checkImportAsins(ApprovalEnum.ALL_STATUS, contextId, channels, "存在如下相同ASIN的SellerSKU对应到不同的SKU及数量");
        }
        ChannelImportResultVO vo = new ChannelImportResultVO();
        vo.setConfirm(-1);
        return vo;

    }

    @Override
    public List<ProductChannels> getProductChannelList(Long orgId, Collection<String> erpskus, Collection<String> accountIds, Collection<String> asins) {
        return productChannelsMapper.selectProductChannelList(orgId, erpskus, accountIds, asins);
    }



    @Override
    public List<ErpSkuStockVO> selectSkuInventory(Long id) {
        ProductChannels channels = this.getById(id);
        if (Objects.isNull(channels)) {
            throw new CommonException("未获取到SKU映射信息");
        }
        String erpSku = channels.getErpSku();
        if (StrUtil.isBlank(erpSku)) {
            return new ArrayList<>();
        }

        return productChannelsMapper.selectErpSkuInventory(channels.getOrgId(), Arrays.asList(erpSku.split(",")));
    }

    @Override
    public void updateInventoryPushConf(ChannelsInventoryConfReq req) {
        log.info("SKU映射修改配置 - {}", JSON.toJSONString(req));
        List<ProductChannels> channels = this.listByIds(req.getId());
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到SKU映射信息");
        }

        List<ProductChannels> noApproved = channels.stream()
                .filter(c -> !Objects.equals(ApprovalEnum.APPROVED.getValue(), c.getApprovalStatus()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(noApproved)) {
            throw new CommonException("仅支持修改审批状态已通过的数据");
        }
        String isPush = req.getIsPush();
        if (Objects.equals("Y", isPush) && Objects.isNull(req.getPushProportion())) {
            throw new CommonException("自动推送库存为是时，推送比例不能为空");
        }

        productChannelsMapper.updateInventoryPushConf(req);

        List<ProductChannels> copyBeanList = BeanCopyUtils.copyBeanList(channels, ProductChannels.class);


        channels.forEach(c->{
            c.setIsPush(req.getIsPush());
            if (Objects.nonNull(req.getHandelingTime())) {
                c.setHandelingTime(req.getHandelingTime());
            }
            if (Objects.nonNull(req.getPushProportion())) {
                c.setHandelingTime(req.getHandelingTime());
            }
            if (Objects.nonNull(req.getPushProportion())) {
                c.setPushProportion(req.getPushProportion());
            }
        });
        // 记录修改日志
        Integer currentUserId = UserUtils.getCurrentUserId();
        String currentUserName = UserUtils.getCurrentUserName();
        threadPoolTaskExecutor.execute(()->{
            Map<Integer, ProductChannels> copyMap = copyBeanList.stream().collect(Collectors.toMap(ProductChannels::getId, Function.identity(), (v1, v2) -> v1));
            for (ProductChannels channel : channels) {
                try {
                    ProductChannels copyBean = copyMap.get(channel.getId());
                    erpOperateLogService.logRecord(currentUserId, currentUserName, copyBean, channel, "SKU映射", true, false, "isPush", "pushProportion", "handelingTime");
                } catch (Exception e) {
                }
            }
        });




        try {
            productChannelRelateService.assignChannelRelates(channels);
            pushToMultichannel(channels);
//            pushToBossForUpdate(channels, channelsMap, false);
        } catch (Exception e) {
            log.error("SKU映射推送BOSS - 异常：{} ", e.getMessage(), e);
        }
    }


    @Override
    public void repairProduct() {

        List<ProductChannels> channels = productChannelsMapper.selectRapairProductChannels();
        log.info("repairProduct 条目：{}", channels.size());
        if (CollectionUtil.isEmpty(channels)) {
            log.info("repairProduct - channels is emtpy");
            return;
        }

        List<ProductChannels> singles = channels.stream()
                .filter(c -> ProductRelateTypeEnum.SINGLE.getValue().equals(c.getType()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(singles)) {
            log.info("repairProduct - singles size : {}", singles.size());
            handleSingles(singles);
            singles.forEach(c -> c.setSkipInterceptor(true));
            this.updateBatchById(singles);
        }



        List<ProductChannels> bundle = channels.stream()
                .filter(c -> ProductRelateTypeEnum.BUNDLE.getValue().equals(c.getType()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(bundle)) {
            log.info("repairProduct - bundle is emtpy");
            return;
        }


        List<Integer> ids = bundle.stream()
                .map(ProductChannels::getId)
                .collect(Collectors.toList());

        List<ProductChannelRelate> relates = productChannelRelateService.lambdaQuery()
                .in(ProductChannelRelate::getProductChannelId, ids)
                .list();


        if (CollectionUtil.isEmpty(relates)) {
            log.info("repairProduct - relates is emtpy");
            return;
        }


        List<Integer> productIds = relates.stream()
                .map(ProductChannelRelate::getProductId)
                .distinct().collect(Collectors.toList());


        List<Products> products = productsService.lambdaQuery()
                .select(Products::getId, Products::getLineId)
                .in(Products::getId, productIds)
                .list();

        if (CollectionUtil.isEmpty(products)) {
            log.info("repairProduct - bundle products is emtpy");
            return;
        }

        Map<Integer, Integer> productMap = products.stream().collect(Collectors.toMap(Products::getId, Products::getLineId));

        Map<Integer, List<ProductChannelRelate>> relateMap = relates.stream().collect(Collectors.groupingBy(ProductChannelRelate::getProductChannelId));

        for (ProductChannels productChannels : bundle) {
            if (!relateMap.containsKey(productChannels.getId())) {
                continue;
            }
            List<ProductChannelRelate> channelRelates = relateMap.get(productChannels.getId());
            for (ProductChannelRelate relate : channelRelates) {
                Integer lineId = productMap.get(relate.getProductId());
                if (Objects.isNull(lineId)) {
                    continue;
                }
                if (Objects.isNull(productChannels.getLineId()) || Objects.equals(productChannels.getLineId(),0)) {
                    productChannels.setLineId(lineId);
                }
                if (Objects.isNull(productChannels.getProductId()) || Objects.equals(productChannels.getProductId(), 0)) {
                    productChannels.setProductId(relate.getProductId());
                }
                break;
            }
        }
        bundle.forEach(c -> c.setSkipInterceptor(true));
        updateBatchById(bundle);


    }

    private void handleSingles(List<ProductChannels> singles) {
        Map<Integer, List<ProductChannels>> map = singles.stream().collect(Collectors.groupingBy(ProductChannels::getOrgId));

        for (Map.Entry<Integer, List<ProductChannels>> entry : map.entrySet()) {

            List<ProductChannels> productChannels = entry.getValue();

            List<String> erpSkus = productChannels.stream()
                    .map(ProductChannels::getErpSku)
                    .distinct().collect(Collectors.toList());

            List<Products> products = productsService.lambdaQuery()
                    .in(Products::getErpsku, erpSkus)
                    .eq(Products::getOrgId, entry.getKey())
                    .select(Products::getLineId, Products::getErpsku, Products::getId)
                    .list();

            if (CollectionUtil.isNotEmpty(products)) {
                Map<String, Products> productsMap = products.stream().collect(Collectors.toMap(Products::getErpsku, Function.identity(), (a, b) -> a));
                for (ProductChannels channel : productChannels) {
                    Products p = productsMap.get(channel.getErpSku());
                    if (Objects.isNull(p)) {
                        continue;
                    }

                    if (Objects.isNull(channel.getLineId()) || Objects.equals(channel.getLineId(), 0)) {
                        channel.setLineId(p.getLineId());
                    }

                    if (Objects.isNull(channel.getProductId()) || Objects.equals(channel.getProductId(), 0)) {
                        channel.setProductId(p.getId());
                    }

                }


            }



        }
    }




    @Override
    public void updateListingPrice(Integer orgId, String accountFlag) {
        Account account = accountService.lambdaQuery()
                .eq(Account::getFlag, accountFlag)
                .eq(Account::getOrgId, Objects.isNull(orgId) ? 1000049 : orgId)
                .one();
        if (Objects.isNull(account)) {
            log.error("更新Listing价格 - 未获取到店铺信息 ：{} - {}", orgId, accountFlag);
            return;
        }
        updateListingPrice(account);
    }

    @Override
    public void updateListingPrice(Account account) {
        List<ProductChannels> channels = this.lambdaQuery()
                .eq(ProductChannels::getAccountId, account.getFlag())
                .eq(ProductChannels::getOrgId, account.getOrgId())
                .eq(ProductChannels::getDisabledName, "")
                .list();
        if (CollectionUtil.isEmpty(channels)) {
            return;
        }
        for (ProductChannels channel : channels) {
            ProductChannels copyBean = BeanCopyUtils.copyBean(channel, ProductChannels.class);
            String sellerSku = channel.getSellerSku();
            bizark.amz.listing.Item item = null;
            try {
                item = amazonApi.getListingItem(sellerSku, account);
            } catch (Exception e) {
                log.error("更新Listing价格 - 请求异常：{} {}", sellerSku, e.getMessage(), e);
                continue;
            }
            if (Objects.isNull(item)) {
                log.error("更新Listing价格 - 未获取到价格信息：{}", sellerSku);
                continue;
            }
            // 更新价格
            boolean isRecordLog = false;
            BigDecimal listingPrice = channel.getListingPrice();
            bizark.amz.listing.ItemAttributes attributes = item.getAttributes();
//            if (Objects.nonNull(attributes) && CollectionUtil.isNotEmpty(attributes.getList_price())) {
//                List<ItemAttributeComponents> listPrice = attributes.getList_price();
//                ItemAttributeComponents components = listPrice.get(0);
//                String price = components.getValue();
//                if (StrUtil.isNotBlank(price)) {
//                    channel.setListingPrice(new BigDecimal(price));
//                    channel.setPreListingPrice(listingPrice);
//
//                }
//            } else {
//                channel.setListingPrice(null);
//                channel.setPreListingPrice(listingPrice);
//            }

            // 记录变更日志
            if ((Objects.isNull(listingPrice) && Objects.nonNull(channel.getListingPrice())) || Objects.nonNull(listingPrice) && Objects.isNull(channel.getListingPrice())) {
                isRecordLog = true;
            } else {
                isRecordLog = listingPrice.compareTo(channel.getListingPrice()) != 0;
            }

            BigDecimal sellerSkuPrice = channel.getSellerSkuPrice();
//            if (CollectionUtil.isNotEmpty(item.getProcurement())) {
//                List<ItemProcurement> procurement = item.getProcurement();
//                ItemProcurement itemProcurement = procurement.get(0);
//                Money costPrice = itemProcurement.getCostPrice();
//                if (Objects.nonNull(costPrice) && StrUtil.isNotBlank(costPrice.getAmount())) {
//                    channel.setSellerSkuPrice(new BigDecimal(costPrice.getAmount()));
//                } else {
//                    channel.setSellerSkuPrice(null);
//                }
//            }

            if (!isRecordLog) {
                if ((Objects.isNull(sellerSkuPrice) && Objects.nonNull(channel.getSellerSkuPrice())) || Objects.nonNull(sellerSkuPrice) && Objects.isNull(channel.getSellerSkuPrice())) {
                    isRecordLog = true;
                } else {
                    isRecordLog = sellerSkuPrice.compareTo(channel.getSellerSkuPrice()) != 0;
                }
            }
            channel.setSkipInterceptor(true);
            this.updateById(channel);
            try {
                if (isRecordLog) {
                    productChannelsMapper.recordPriceLog(channel);
                }
                erpOperateLogService.logRecord(copyBean, channel, "SKU映射", true, false, "sellerSkuPrice", "listingPrice", "preListingPrice");
            } catch (Exception e) {
            }
        }
    }


    @Override
    public ProductChannels selectTemuChannel(String asin, Integer accountId) {
        Account account = accountService.getById(accountId);
        if (Objects.isNull(account)) {
            throw new CommonException("未获取到店铺信息");
        }
        return this.lambdaQuery()
                .eq(ProductChannels::getSaleChannel, SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian())
                .eq(ProductChannels::getOrgId, account.getOrgId())
                .eq(ProductChannels::getAsin, asin)
                .eq(ProductChannels::getAccountId, account.getFlag())
                .eq(ProductChannels::getDisabledAt, 0)
                .eq(ProductChannels::getDisabledName, "")
                .last("limit 1")
                .one();
    }

    @Override
    public void streamByOrgId(Integer orgId, ResultHandler<ProductChannels> handler) {
        productChannelsMapper.streamQueryByOrgIdAndSaleChannel(handler, orgId, SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian());
    }

    @Override
    public void initChannelsPush(Integer orgId) {
        productChannelsMapper.streamQueryByOrgIdAndSaleChannel(new ResultHandler<ProductChannels>() {
            @Override
            public void handleResult(ResultContext<? extends ProductChannels> resultContext) {
                // 数据推送多渠道
                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.PRODUCT_CHANNEL_EDIT_QUEUE_ROUTING_KEY, resultContext.getResultObject());
            }
        }, orgId, null);
    }

    @Override
    public void syncWalmartSaleQty(Integer orgId) {
        List<Account> accounts = accountService.lambdaQuery()
                .eq(Account::getOrgId, orgId)
                .eq(Account::getType, SyncMappingEnum.SkuGoodsMapSaleChannelEnum.WALMART.getEhengjian())
                .list();

        if (CollectionUtil.isEmpty(accounts)) {
            log.info("同步Walmart可售库存 - 未获取到店铺信息:{}");
            return;
        }

        for (Account account : accounts) {


        }


    }

    @Override
    public void syncWalmartWfsInventory(Integer accountId) {
        List<Account> accounts = Objects.isNull(accountId) ?
                accountService.lambdaQuery()
                        .eq(Account::getOrgId, 1000049)
                        .eq(Account::getType, SyncMappingEnum.SkuGoodsMapSaleChannelEnum.WALMART.getEhengjian())
                        .list()
                : accountService.listByIds(Arrays.asList(accountId));
        if (CollectionUtil.isNotEmpty(accounts)) {
            for (Account account : accounts) {
                log.info("Walmart平台仓库存同步 - {}", account.getFlag());
                // 获取当前店铺下所有数据
                try {
                    doSyncWfsInventory(account);
                } catch (Exception e) {
                    log.info("Walmart平台仓库存同步异常:{}", account.getFlag(), e);
                }
            }
        }

    }

    @Override
    public List<ProductChannels> selectByIdCursor(Integer cursor, int limit) {
        return this.lambdaQuery()
                .gt(ProductChannels::getId, cursor)
                .eq(ProductChannels::getDisabledAt, 0)
                .eq(ProductChannels::getDisabledName, "")
                .last("limit " + limit)
                .orderByAsc(ProductChannels::getId)
                .list();
    }

    @Override
    public List<ProductChannels> selectByIdCursor(Integer orgId, String saleChannel,Integer cursor, int limit,String sellStatus) {
        LambdaQueryChainWrapper<ProductChannels> chainWrapper = this.lambdaQuery()
                .gt(ProductChannels::getId, cursor)
                .eq(ProductChannels::getOrgId, orgId)
                .eq(ProductChannels::getDisabledAt, 0)
                .eq(ProductChannels::getDisabledName, "")
                .last("limit " + limit)
                .orderByAsc(ProductChannels::getId);

        if (StrUtil.isNotBlank(sellStatus)) {
            chainWrapper.eq(ProductChannels::getSellStatus, sellStatus);
        }
        if (StrUtil.isNotBlank(saleChannel)) {
            chainWrapper.eq(ProductChannels::getSaleChannel, saleChannel);
        }
        return chainWrapper.list();
    }

    @Override
    public ProductChannels selectSkuInventoryById(Integer id) {
        ProductChannels channels = this.selectProductChannelById(id);
        if (Objects.isNull(channels)) {
            throw new CommonException("未获取到SKU映射信息");
        }

        String erpSku = channels.getErpSku();
        if (StrUtil.isBlank(erpSku)) {
            throw new CommonException("当前SellerSku无对应SKU信息");
        }

        Map<String, List<SkuChildren>> childrenMap = new HashMap<>();


        if (Objects.equals(channels.getIsParentSku(), 1)) {
            // 把当前所有SKU作为父查询
            childrenMap = skuChildrenService.selectSkuByParentSku(channels.getOrgId(), Arrays.asList(erpSku.split(",")));
        }

        skuInventorySettingWithMultipleVersionSku(channels.getOrgId(), childrenMap, Lists.newArrayList(channels), true);
        return channels;
    }

    @Override
    public ProductChannels selectProductChannelById(Integer id) {
        return productChannelsMapper.selectProductChannelById(id);
    }


    @Override
    @Transactional
    public void allocateTags(ProductChannelAllocateTagDTO dto) {
        List<ProductChannels> channels = this.listByIds(dto.getIds());
        if (CollectionUtil.isEmpty(channels)) {
            throw new CommonException("未获取到SKU映射信息");
        }

        if (Objects.equals(dto.getType(), 1) && CollectionUtil.isEmpty(dto.getTagArr())) {
            throw new CommonException("添加标签时 tagArr 不能为空");
        }


        modifyProductChannelsTags(dto, channels);


//        if (Objects.equals(dto.getType(), 1)) {
//            // 添加标签
//            return;
//        }
//        //
//
//
//        LambdaUpdateChainWrapper<ProductChannels> chainWrapper = this.lambdaUpdate()
//                .in(ProductChannels::getId, dto.getIds());
//
//        if (CollectionUtil.isEmpty(dto.getTagArr())) {
//            chainWrapper.set(ProductChannels::getTags, null);
//        }else{
//            String tags = dto.getTagArr().stream()
//                    .map(String::valueOf)
//                    .distinct()
//                    .collect(Collectors.joining(","));
//            chainWrapper.set(ProductChannels::getTags, tags);
//        }
//
//        chainWrapper.update();
//
//        // 记录日志
//        Integer userId = UserUtils.getCurrentUserId();
//        String userName = UserUtils.getCurrentUserName();
//        threadPoolTaskExecutor.execute(()->{
//            // 获取字典
//            List<SysDictData> dictData = sysDictTypeService.selectDictDataByType("product_channel_tag");
//            Map<String, String> dictMap = new HashMap<>();
//            if (CollectionUtil.isNotEmpty(dictData)) {
//                Map<String, String> map = dictData.stream()
//                        .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (v1, v2) -> v1));
//                dictMap.putAll(map);
//            }
//
//            String newValue = getTagLabel(dto.getTagArr(), dictMap);
//
//
//            List<ErpOperateLog> logs = channels.stream()
//                    .map(chanel -> {
//                        ErpOperateLog operateLog = new ErpOperateLog();
//                        operateLog.setLogType(1);
//                        operateLog.setOperateType(LogEnums.OperateTypeEnum.UPDATE.getValue());
//                        operateLog.setOperateTable("dashboard.product_channels");
//                        operateLog.setOperateName("SKU映射");
//                        operateLog.setCreatedBy(userId);
//                        operateLog.setFieldDesc("标签");
//                        operateLog.setOperateTarget("tags");
//                        operateLog.setUpdatedBy(userId);
//                        operateLog.setOperateUserId(userId.longValue());
//                        operateLog.setOperateUserName(userName);
//                        operateLog.setOperateAt(LocalDateTime.now());
//                        operateLog.setCreatedAt(new Date());
//                        operateLog.setCreatedName(userName);
//                        operateLog.setUpdatedName(userName);
//                        operateLog.setBusinessId(chanel.getId().longValue());
//
//                        if (StrUtil.isNotBlank(chanel.getTags())) {
//                            List<Integer> tagArr = Arrays.stream(chanel.getTags().split(","))
//                                    .map(Integer::parseInt)
//                                    .collect(Collectors.toList());
//                            String oldValue = getTagLabel(tagArr, dictMap);
//                            operateLog.setOperateOldValue(oldValue);
//                        } else {
//                            operateLog.setOperateOldValue(null);
//                        }
//                        operateLog.setOperateNewValue(newValue);
//                        return operateLog;
//                    })
//                    .filter(c -> StrUtil.isNotBlank(c.getOperateNewValue()) || StrUtil.isNotBlank(c.getOperateOldValue()))
//                    .collect(Collectors.toList());
//
//            if (CollectionUtil.isNotEmpty(logs)) {
//                erpOperateLogService.saveBatch(logs);
//            }
//        });




    }

    private void modifyProductChannelsTags(ProductChannelAllocateTagDTO dto, List<ProductChannels> channels) {
        List<ProductChannels> copyBeanList = BeanCopyUtils.copyBeanList(channels, ProductChannels.class);
        Map<Integer, ProductChannels> copyBeanMap = copyBeanList.stream().collect(Collectors.toMap(ProductChannels::getId, Function.identity(), (v1, v2) -> v1));

        for (ProductChannels channel : channels) {
            String tags = channel.getTags();
            List<Integer> sourceTags = StrUtil.isEmpty(tags)
                    ? new ArrayList<>()
                    : Arrays.stream(tags.split(",")).map(Integer::parseInt).collect(Collectors.toList());

            List<Integer> tagArr = dto.getTagArr();

            Integer type = dto.getType();
            if (Objects.equals(type, 1)) {
                sourceTags.addAll(tagArr);
                CollectionUtil.distinct(sourceTags);
                CollectionUtil.sort(sourceTags, Comparator.comparing(Function.identity()));
                channel.setTags(sourceTags.stream().map(String::valueOf).distinct().collect(Collectors.joining(",")));
                continue;
            }

            // 移除
            if (StrUtil.isBlank(channel.getTags())) {
                continue;
            }

            sourceTags.removeAll(tagArr);
            CollectionUtil.sort(sourceTags, Comparator.comparing(Function.identity()));
            channel.setTags(sourceTags.stream().map(String::valueOf).distinct().collect(Collectors.joining(",")));
        }

        this.updateBatchById(channels);

        Integer userId = UserUtils.getCurrentUserId(0);
        String userName = UserUtils.getCurrentUserName("System");

        // 记录日志
        threadPoolTaskExecutor.execute(() -> {
            List<SysDictData> dictData = sysDictTypeService.selectDictDataByType("product_channel_tag");
            Map<String, String> dictMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(dictData)) {
                Map<String, String> map = dictData.stream()
                        .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (v1, v2) -> v1));
                dictMap.putAll(map);
            }
            List<ErpOperateLog> logs = channels.stream()
                    .map(chanel -> buildTagsLogBean(dictMap, copyBeanMap.get(chanel.getId()), chanel, userId, userName))
                    .filter(c -> !Objects.equals(c.getOperateNewValue(), c.getOperateOldValue()))
                    .collect(Collectors.toList());
            erpOperateLogService.saveBatch(logs);

        });


    }


    private ErpOperateLog buildTagsLogBean(Map<String, String> dictMap,ProductChannels before,ProductChannels after,Integer userId,String userName){

        ErpOperateLog operateLog = new ErpOperateLog();
        operateLog.setLogType(1);
        operateLog.setOperateType(LogEnums.OperateTypeEnum.UPDATE.getValue());
        operateLog.setOperateTable("dashboard.product_channels");
        operateLog.setOperateName("SKU映射");
        operateLog.setCreatedBy(userId);
        operateLog.setUpdatedBy(userId);
        operateLog.setFieldDesc("标签");
        operateLog.setOperateTarget("tags");
        operateLog.setOperateUserId(userId != null ? userId.longValue() : 0);
        operateLog.setOperateUserName(userName);
        operateLog.setOperateAt(LocalDateTime.now());
        operateLog.setCreatedAt(new Date());
        operateLog.setCreatedName(userName);
        operateLog.setUpdatedName(userName);
        operateLog.setBusinessId(after.getId().longValue());

        if (StrUtil.isNotBlank(before.getTags())) {
            List<Integer> tagArr = Arrays.stream(before.getTags().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            String oldValue = getTagLabel(tagArr, dictMap);
            operateLog.setOperateOldValue(oldValue);
        } else {
            operateLog.setOperateOldValue(StrUtil.EMPTY);
        }


        if (StrUtil.isNotBlank(after.getTags())) {
            List<Integer> tagArr = Arrays.stream(after.getTags().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            String newValue = getTagLabel(tagArr, dictMap);
            operateLog.setOperateNewValue(newValue);
        } else {
            operateLog.setOperateNewValue(StrUtil.EMPTY);
        }
        return operateLog;
    }


    private static String getTagLabel(List<Integer> tagArr, Map<String, String> dictMap) {
        if (CollectionUtil.isEmpty(tagArr)) {
            return null;
        }

        return tagArr.stream()
                .map(tag -> dictMap.containsKey(String.valueOf(tag)) ? dictMap.get(String.valueOf(tag)) : String.valueOf(tag))
                .collect(Collectors.joining(","));
    }

    @Override
    public void updateApprovalStatus(ProductChannels channel, ApprovalEnum approvalEnum, String opinion) {
        ProductChannels copyBean = BeanCopyUtils.copyBean(channel, ProductChannels.class);

        channel.setApprovalStatus(approvalEnum.getValue());

        LambdaUpdateChainWrapper<ProductChannels> chainWrapper = this.lambdaUpdate()
                .set(ProductChannels::getApprovalStatus, approvalEnum.getValue())
                .eq(ProductChannels::getId, channel.getId());

        if (approvalEnum != ApprovalEnum.APPROVED && approvalEnum != ApprovalEnum.SUBMIT) {
            chainWrapper.set(ProductChannels::getInstanceId, null);
        }
        chainWrapper.update();

        // 记录审批日志
        approvalLogRecord(Arrays.asList(channel));

        if (approvalEnum == ApprovalEnum.APPROVED) {
            // 审批通过发送事件消息
            try {
                ProductChannelApprovalEvent event = new ProductChannelApprovalEvent(this, channel);
                applicationEventPublisher.publishEvent(event);
            } catch (Exception e) {
                log.error("SKU映射审批事件处理异常 - {} - {}", JSON.toJSONString(channel), approvalEnum);
            }
            // 审批通过发送MQ信息
            rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_NAME_DELIVER_DIRECT_DELAYED, "product_push_by_erp", channel.getId(), new CorrelationData(channel.getId().toString()));
        }
//        erpOperateLogService.logRecord(copyBean, channel, "SKU映射", true, false, "approvalStatus");
        Integer currentUserId = UserUtils.getCurrentUserId(0);
        String currentUserName = UserUtils.getCurrentUserName("System");

        ErpOperateLog operateLog = new ErpOperateLog();
        operateLog.setOperateNewValue(approvalEnum.getLabel());

        if (StrUtil.isNotBlank(opinion)) {
            operateLog.setOperateNewValue(
                    operateLog.getOperateNewValue() + " 备注:" + opinion
            );
        }

        operateLog.setOperateOldValue(ApprovalEnum.getLabel(copyBean.getApprovalStatus()));
        operateLog.setOperateName("SKU映射");
        operateLog.setBusinessId(channel.getId().longValue());
        operateLog.setOperateTable("dashboard.product_channels");
        operateLog.setOperateAt(LocalDateTime.now());
        operateLog.setLogType(-100);
        operateLog.setOperateUserId(currentUserId.longValue());
        operateLog.setOperateUserName(currentUserName);
        operateLog.setFieldDesc("审批状态");
        operateLog.setOperateTarget("approvalStatus");
        operateLog.setOperateType(1);
        operateLog.setCreatedAt(new Date());
        operateLog.setCreatedName(currentUserName);
        operateLog.setCreatedBy(currentUserId);
        erpOperateLogService.save(operateLog);
    }

    @Override
    public void allocationInstanceId(Integer id, String instanceId) {
        this.lambdaUpdate()
                .eq(ProductChannels::getId, id)
                .set(ProductChannels::getInstanceId, instanceId)
                .update();
    }

    @Override
    public void initApprovalNodeByCondition(Integer orgId, String saleChannel, String sellerStatus) {
        int cursor = 0;
        List<ProductChannels> channels;

        while (CollectionUtil.isNotEmpty(channels = selectByIdCursor(orgId, saleChannel, cursor, 2000, sellerStatus))) {
            cursor = channels.get(channels.size() - 1).getId();
            initChannelApproval(channels);
//            syncSkuInventory(channels);
        }
    }

    @Override
    public void repairSlave() {
        productChannelsMapper.repairSlave();
    }


    @Override
    public void initApprovalNodeByChannelId(Integer channelId) {
        ProductChannels channels = this.getById(channelId);
        if (Objects.isNull(channels)) {
            throw new CommonException("未获取到SKU映射信息");
        }
        initChannelApproval(Lists.newArrayList(channels));
    }



    private void initChannelApproval(List<ProductChannels> channels) {
        channels = channels.stream()
                .filter(c -> Objects.equals(c.getApprovalStatus(), ApprovalEnum.SUBMIT.getValue()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(channels)) {
            return;
        }
        // 设置运营以及部门数据
        Map<Integer, List<ProductChannels>> orgMap = channels.stream().collect(Collectors.groupingBy(ProductChannels::getOrgId));
        for (Map.Entry<Integer, List<ProductChannels>> entry : orgMap.entrySet()) {
            productChannelApprovalNodeService.initFirstNode(entry.getKey(), entry.getValue(), false);
            this.updateBatchById(entry.getValue());
        }
    }


    public List<ProductChannels> selectByOrgIdAndAccountIdAndSellerSkus(Integer orgId, String accountId, List<String> sellerSkus) {
        return this.lambdaQuery()
                .eq(ProductChannels::getOrgId, orgId)
                .eq(ProductChannels::getAccountId, accountId)
                .in(ProductChannels::getSellerSku, sellerSkus)
                .eq(ProductChannels::getDisabledAt, 0)
                .eq(ProductChannels::getDisabledName, StrUtil.EMPTY)
                .list();
    }



    private void doSyncWfsInventory(Account account) {
        int offset = 0;
        int limit = 200;
        WalmartWfsInventoryResponse response;

        String redisKey = "WFS_" + account.getOrgId() + account.getFlag();

        String startDate = stringRedisTemplate.opsForValue().get(redisKey);
        Date now = new Date();

        DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT.withZone(ZoneId.systemDefault());

        if (StrUtil.isBlank(startDate)) {
            DateTime dateTime = DateUtil.offsetDay(now, -3);
            startDate = formatter.format(dateTime.toInstant());
        }


        while (
                Objects.nonNull((response = walmartApi.getWfsInventoryWithRetry(account, startDate, formatter.format(now.toInstant()), offset, limit, 1)))
                        && Objects.nonNull(response.getPayload())
                        && CollectionUtil.isNotEmpty(response.getPayload().getInventory())
        ) {
            offset += limit;
            List<WalmartWfsInventoryResponse.Payload.Inventory> inventories = response.getPayload().getInventory();

            List<String> sellerSkus = inventories.stream()
                    .map(WalmartWfsInventoryResponse.Payload.Inventory::getSku)
                    .collect(Collectors.toList());


            List<ProductChannels> channels = selectByOrgIdAndAccountIdAndSellerSkus(account.getOrgId(), account.getFlag(), sellerSkus);

            if (CollectionUtil.isEmpty(channels)) {
                continue;
            }

            Map<String, WalmartWfsInventoryResponse.Payload.Inventory> inventoryMap = inventories.stream().collect(Collectors.toMap(WalmartWfsInventoryResponse.Payload.Inventory::getSku, Function.identity(), (v1, v2) -> v2));
            Map<ProductChannels, ProductChannels> logMap = new HashMap<>();

            updateProductChannels(channels, inventoryMap, logMap);

            if (CollectionUtil.isNotEmpty(logMap)) {
                recordWalmartWfsLog(logMap);
            }

        }

        // 存储昨日标记
        stringRedisTemplate.opsForValue().set(redisKey, formatter.format(DateUtil.offsetDay(now, -1).toInstant()), 1, TimeUnit.DAYS);
    }

    private void updateProductChannels(List<ProductChannels> channels, Map<String, WalmartWfsInventoryResponse.Payload.Inventory> inventoryMap, Map<ProductChannels, ProductChannels> logMap) {
        for (ProductChannels channel : channels) {
            if (!inventoryMap.containsKey(channel.getSellerSku())) {
                continue;
            }
            WalmartWfsInventoryResponse.Payload.Inventory inventory = inventoryMap.get(channel.getSellerSku());

            if (CollectionUtil.isEmpty(inventory.getShipNodes())) {
                continue;
            }

            List<WalmartWfsInventoryResponse.Payload.Inventory.ShipNode> shipNodes = inventory.getShipNodes();
            Integer availToSellQty = shipNodes.stream()
                    .map(WalmartWfsInventoryResponse.Payload.Inventory.ShipNode::getAvailToSellQty)
                    .filter(Objects::nonNull)
                    .reduce(Integer::sum)
                    .get();
            ProductChannels source = BeanCopyUtils.copyBean(channel, ProductChannels.class);

            log.info("平台仓库存获取 - {} - {} - {}", channel.getSellerSku(), channel.getAvailToSellQty(), availToSellQty);
            if (!Objects.equals(channel.getAvailToSellQty(), availToSellQty)) {
                channel.setAvailToSellQty(availToSellQty);
//                    updates.add(channel);
                logMap.put(source, channel);

                // 直接更新
                this.lambdaUpdate().eq(ProductChannels::getId, channel.getId())
                        .set(ProductChannels::getAvailToSellQty, availToSellQty)
                        .update();
            }
        }
    }

    private void recordWalmartWfsLog(Map<ProductChannels, ProductChannels> logMap) {
        for (Map.Entry<ProductChannels, ProductChannels> entry : logMap.entrySet()) {

            ProductChannels source = entry.getKey();
            ProductChannels after = entry.getValue();

            if (Objects.equals(source.getAvailToSellQty(), after.getAvailToSellQty())) {
                continue;
            }

            ErpOperateLog opLog = new ErpOperateLog();
            opLog.setOperateTable("dashboard.product_channels");
            opLog.setOperateAt(LocalDateTime.now());
            opLog.setOperateName("SKU映射");
            opLog.setLogType(-100);
            opLog.setBusinessId(entry.getKey().getId().longValue());
            opLog.setOperateUserId(0L);
            opLog.setOperateUserName("System");
            opLog.setFieldDesc("平台仓库存");
            opLog.setOperateTarget("availToSellQty");
            opLog.setOperateType(1);
            opLog.setOperateNewValue(entry.getValue().getAvailToSellQty() == null ? "" : String.valueOf(entry.getValue().getAvailToSellQty()));
            opLog.setOperateOldValue(entry.getKey().getAvailToSellQty() == null ? "" : String.valueOf(entry.getKey().getAvailToSellQty()));
            opLog.setCreatedAt(new Date());
            opLog.setCreatedName("System");
            opLog.setCreatedBy(0);
            erpOperateLogService.save(opLog);
        }
    }


    /**
     * Description: walmart 商品id查询。
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/23
     */
    @Override
    public List<WalmartAsinVo> selectWalmartItemId() {
        ProductChannels productChannels = new ProductChannels();
        productChannels.setOrgId(1000049);
        List<WalmartAsinVo> channels =  productChannelsMapper.selectWalmartProductChannelsList(productChannels);
        //销售状态为在售
        return channels.stream().filter(item -> !StringUtils.isEmpty(item.getAsin())&&"Hit Shelve".equals(item.getSellStatus())).collect(Collectors.toList());
    }

    @Override
    public List<String> selectBrandsByOrgId(Integer contextId) {
        return productChannelsMapper.selectBrandsByOrgId(contextId);
    }

    @Override
    public void createExportTask(ProductChannelsExportQuery channels, List<Integer> ids, Integer contextId, UserEntity authUserEntity) {
        if (CollectionUtil.isNotEmpty(ids)) {
            channels.setQueryIds(ids);
        }
        channels.setOrgId(contextId);
        approvalNodeQuerySetting(channels);
        ProductChannelExportDTO dto = new ProductChannelExportDTO();
        dto.setChannels(channels);
        if (ids != null) {
            dto.setIds(Lists.newArrayList(ids));
        }
        dto.setContextId(contextId);
        TaskCenterRequest request = new TaskCenterRequest();
        request.setTaskCode("erp.productChannel.export");
        request.setOrgId(contextId);
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(dto));
        request.setArgs(list);
        taskCenterService.startTask(request, authUserEntity);
    }

    @Override
    public void initMyDepotSellerSku(String sellerSku) {
        LambdaQueryChainWrapper<ProductChannels> chainWrapper = this.lambdaQuery()
                .eq(ProductChannels::getSaleChannel, SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian())
                .eq(ProductChannels::getOrgId, 1000049)
                .eq(ProductChannels::getApprovalStatus, ApprovalEnum.APPROVED.getValue());
        if (StrUtil.isNotBlank(sellerSku)) {
            chainWrapper.eq(ProductChannels::getSellerSku, sellerSku);
        }
        List<ProductChannels> productChannels = chainWrapper.list();

        if (CollectionUtil.isEmpty(productChannels)) {
            return;
        }
        for (ProductChannels channel : productChannels) {
            ProductChannelApprovalEvent event = new ProductChannelApprovalEvent(this, channel);
            applicationEventPublisher.publishEvent(event);
        }

    }

    @Override
    public ProductChannels selectByAccountIdAndSellerSku(String accountId, String sellerSku) {
        return productChannelsMapper.selectByAccountIdAndSellerSku(accountId,sellerSku);
    }


    @Override
    public List<ErpSkuStockVO> selectInventoryByErpSku(Integer contextId, List<String> erpSkus) {

        // 直接构建响应数据
        List<InventoryListEntity> entities = inventoryListService.selectAvailableInventoryBySkus(contextId, erpSkus);

        Map<String, List<InventoryListEntity>> skuMap = entities.stream().collect(Collectors.groupingBy(InventoryListEntity::getSku));

        return erpSkus.stream()
                .map(c -> {
                    ErpSkuStockVO stockVO = new ErpSkuStockVO();
                    stockVO.setSku(c);
                    if (skuMap.containsKey(c)) {
                        Integer available = skuMap.get(c).stream()
                                .map(InventoryListEntity::getInventoryAvailable)
                                .reduce(Integer::sum)
                                .orElse(0);
                        stockVO.setInventoryAvailable(available);
                    } else {
                        stockVO.setInventoryAvailable(0);
                    }
                    return stockVO;
                }).collect(Collectors.toList());
    }


    @Override
    public void initCostPrice() {

        List<ProductChannels> channels = null;

        Integer idCursor = 0;

        while (CollectionUtil.isNotEmpty((channels = selectByIdCursor(idCursor, 1000)))) {

            idCursor = CollectionUtil.getLast(channels).getId();

            List<String> accountFlags = channels.stream().map(ProductChannels::getAccountId).distinct().collect(Collectors.toList());

            List<Account> accounts = accountService.lambdaQuery()
                    .in(Account::getFlag, accountFlags)
                    .list();

            Map<String,String> accountMap = accounts.stream().collect(Collectors.toMap(c -> c.getOrgId() + c.getFlag(), Account::getType, (v1,v2) -> v1));

            channels = channels.stream()
                    .filter(c -> "temu".equalsIgnoreCase(accountMap.getOrDefault(c.getOrgId() + c.getAccountId(), "")))
                    .collect(Collectors.toList());


            Map<Integer, List<ProductChannels>> orgMap = channels.stream().collect(Collectors.groupingBy(ProductChannels::getOrgId));


            for (Map.Entry<Integer, List<ProductChannels>> entry : orgMap.entrySet()) {

                Integer orgId = entry.getKey();

                List<ProductChannels> productChannels = entry.getValue();

                handleCostPrice(orgId, productChannels);
            }


        }




    }

    private void handleCostPrice(Integer orgId, List<ProductChannels> productChannels) {


        List<String> erpSkus = productChannels.stream().map(ProductChannels::getErpSku)
                .filter(StrUtil::isNotBlank)
                .map(c -> Arrays.asList(c.split(",")))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(erpSkus)) {
            return;
        }


        List<MarTemuPromotionPriceReport> priceReports = priceReportMapper.selectErpSkuLastPriceByErSkuAndContextId(erpSkus, orgId);

        if (CollectionUtil.isEmpty(priceReports)) {
            return;
        }

        Map<String, BigDecimal> priceMap = priceReports.stream().collect(Collectors.toMap(MarTemuPromotionPriceReport::getErpSku, MarTemuPromotionPriceReport::getPrice));

        productChannelRelateService.assignChannelRelates(productChannels);

        handleCostPrice(productChannels, priceMap);
    }


    private void handleCostPrice(List<ProductChannels> productChannels, Map<String, BigDecimal> priceMap) {


        for (ProductChannels channel : productChannels) {
            // 更新成本价
            List<ProductChannelRelate> productRelates = channel.getProductRelates();
            if (CollectionUtil.isEmpty(productRelates)) {
                continue;
            }
            List<String> errorSkus = productRelates.stream()
                    .map(ProductChannelRelate::getErpsku)
                    .filter(c -> !priceMap.containsKey(c))
                    .collect(Collectors.toList());


            if (CollectionUtil.isNotEmpty(errorSkus)) {
                log.info("SellerSku成本价计算失败 - 存在未获取到成本价的SKU - {}", errorSkus);
                continue;
            }

            BigDecimal costPrice = productRelates.stream()
                    .map(c -> priceMap.get(c.getErpsku()).multiply(new BigDecimal(c.getQty())))
                    .reduce(BigDecimal::add)
                    .orElse(null);

            ProductChannels copyBean = new ProductChannels();
            BeanUtils.copyProperties(channel, copyBean);
            channel.setCostPrice(costPrice);

            this.lambdaUpdate()
                    .eq(ProductChannels::getId, channel.getId())
                    .set(ProductChannels::getCostPrice, costPrice)
                    .update();

            erpOperateLogService.logRecord(copyBean, channel, "SKU映射", true, false, "costPrice");

        }
    }


    @Override
    public void referenceCostPrice(Integer orgId,String erpSku, BigDecimal costPrice) {

        List<ProductChannels> productChannels = this.lambdaQuery()
                .like(ProductChannels::getErpSku, erpSku)
                .list();


//        List<ProductChannelRelate> channelRelates = productChannelRelateService.lambdaQuery()
//                .eq(ProductChannelRelate::getErpsku, erpSku)
//                .list();
//        if (CollectionUtil.isEmpty(channelRelates)) {
//            return;
//        }
//
//        List<Integer> productChannelId = channelRelates.stream().map(ProductChannelRelate::getProductChannelId).collect(Collectors.toList());
//
//        List<ProductChannels> productChannels = this.lambdaQuery()
//                .in(ProductChannels::getId, productChannelId)
//                .eq(ProductChannels::getOrgId, orgId)
//                .list();
//

        if (CollectionUtil.isEmpty(productChannels)) {
            return;
        }


        List<String> erpSkus = productChannels.stream().map(ProductChannels::getErpSku)
                .filter(StrUtil::isNotBlank)
                .map(c -> Arrays.asList(c.split(",")))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(erpSkus)) {
            return;
        }


        MarTemuPromotionPriceReportMapper priceReportMapper = SpringUtils.getBean(MarTemuPromotionPriceReportMapper.class);


        List<MarTemuPromotionPriceReport> priceReports = priceReportMapper.selectErpSkuLastPriceByErSkuAndContextId(erpSkus, orgId);


        Map<String, BigDecimal> priceMap = priceReports.stream().collect(Collectors.toMap(MarTemuPromotionPriceReport::getErpSku, MarTemuPromotionPriceReport::getPrice));


        productChannelRelateService.assignChannelRelates(productChannels);

        // 覆盖当前价格
        priceMap.put(erpSku, costPrice);

        handleCostPrice(productChannels,priceMap);

    }

    @Override
    public List<ProductChannels> selectTiktokProductList(TiktokProductQueryDTO dto) {
        List<ProductChannels> channels = productChannelsMapper.selectTiktokProductList(dto);
        return channels;
    }

    @Override
    public Integer resetInventoryStatus() {
        return productChannelsMapper.resetInventoryStatus();
    }


    public void initSkuVersionSerial(Integer orgId) {
        List<String> filterSkuSeriaChannel = Arrays.asList("HJ-2-VC-US", "HJ-3-VC-US");

        List<ProductChannels> dbChannels = productChannelsMapper.selectByOrgIdAndAccountIdsSkuAsinNotEmpty(orgId, filterSkuSeriaChannel);

        List<ProductChannels> waitProcessing = dbChannels.stream().filter(c -> !c.getErpSku().contains(","))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(waitProcessing)) {
            return;
        }

        Map<String, List<ProductChannels>> asinSkuMap = waitProcessing.stream().collect(Collectors.groupingBy(c -> c.getAsin() + c.getErpSku()));

        Map<String, Integer> serialMap = new HashMap<>();

        // 拷贝数据，隔离引用
//        List<ProductChannels> copyChannels =  BeanCopyUtils.copyBeanList(waitProcessing, ProductChannels.class);

        Set<Integer> skipIds = new HashSet<>();


        for (ProductChannels channels : waitProcessing) {

            if (skipIds.contains(channels.getId())) {
                continue;
            }

            List<ProductChannels> currentChannels = asinSkuMap.get(channels.getAsin() + channels.getErpSku())
                    .stream().filter(c -> !Objects.equals(c.getId(), channels.getId()))
                    .collect(Collectors.toList());


            Integer currentSerialNumber = serialMap.getOrDefault(channels.getErpSku(), 0);

            Integer nextSerialNumber = currentSerialNumber % 4 + 1;

            channels.setSkuVersionSerial(nextSerialNumber);
            channels.setSkuVersion(channels.getErpSku() + "V" + channels.getSkuVersionSerial());

            // 记录当前SKU版本信息
            serialMap.put(channels.getErpSku(), Objects.equals(nextSerialNumber, 4) ? 0 : nextSerialNumber);

            if (CollectionUtil.isEmpty(currentChannels)) {
                productChannelsMapper.updateSkuVersionSerial(orgId, channels.getErpSku(), nextSerialNumber);
                this.updateById(channels);
                // 递增当前SKU数据
                skipIds.add(channels.getId());
                continue;
            }

            currentChannels.forEach(c-> {
                c.setSkuVersionSerial(nextSerialNumber);
                c.setSkuVersion(c.getErpSku() + "V" + c.getSkuVersionSerial());
            });

            channels.setSkuVersionSerial(nextSerialNumber);
            channels.setSkuVersion(channels.getErpSku() + "V" + channels.getSkuVersionSerial());
            currentChannels.add(channels);

            productChannelsMapper.updateSkuVersionSerial(orgId, channels.getErpSku(), nextSerialNumber);

            this.updateBatchById(currentChannels);

            skipIds.addAll(
                    currentChannels.stream().map(ProductChannels::getId).collect(Collectors.toList())
            );

        }

    }


    private void handleSkuSerialVersion(Integer contextId, List<ProductChannels> channels,List<ProductChannels> dbChannels,boolean updateDb) {

        if (!Objects.equals(contextId, 1000049)) {
            return;
        }

        List<String> filterSkuSeriaChannel = Arrays.asList("HJ-2-VC-US", "HJ-3-VC-US");

        List<ProductChannels> skuSerialChannels = channels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getAsin()) && filterSkuSeriaChannel.contains(c.getAccountId()))
                .filter(c -> StrUtil.isNotBlank(c.getErpSku()) && !c.getErpSku().contains(","))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(skuSerialChannels)) {
            return;
        }

        List<String> skus = skuSerialChannels.stream()
                .map(ProductChannels::getErpSku)
                .distinct()
                .collect(Collectors.toList());

        if (Objects.isNull(dbChannels)) {
            dbChannels = productChannelsMapper.selectByOrgIdAndAccountIdsAndErpSkuLikeAndAsinNotEmpty(contextId, filterSkuSeriaChannel, skus);
        }

        if (CollectionUtil.isEmpty(dbChannels)) {
            channels.forEach(channel -> {
                channel.setSkuVersionSerial(1);
                channel.setSkuVersion(channel.getErpSku() + "V" + channel.getSkuVersionSerial());
            });
            return;
        }

        Map<Integer, String> idSkuMap = dbChannels.stream().collect(Collectors.toMap(ProductChannels::getId, c -> c.getErpSku() + c.getAsin(), (v1, v2) -> v1));

        // 拷贝数据库现有数据

        Map<String, List<ProductChannels>> dbSkuMap = dbChannels.stream().collect(Collectors.groupingBy(ProductChannels::getErpSku));


        Map<String, List<ProductChannels>> skuMap = skuSerialChannels.stream().collect(Collectors.groupingBy(ProductChannels::getErpSku));


        for (Map.Entry<String, List<ProductChannels>> entry : skuMap.entrySet()) {

            String currentSku = entry.getKey();

            RLock lock = redissonClient.getLock("skuVersion" + contextId + currentSku);

            lock.lock(10, TimeUnit.SECONDS);
            try {
                doHandleSkuVersion(contextId, entry, dbSkuMap, idSkuMap, updateDb);
            } finally {
                lock.unlock();
            }
        }
    }

    private void doHandleSkuVersion(Integer contextId, Map.Entry<String, List<ProductChannels>> entry, Map<String, List<ProductChannels>> dbSkuMap, Map<Integer, String> idSkuMap, boolean updateDb) {
        String currentSku = entry.getKey();

        List<ProductChannels> current = dbSkuMap.get(currentSku);

        if (CollectionUtil.isEmpty(current)) {
            // 直接设置版本信息
            entry.getValue().forEach(channel -> {
                channel.setSkuVersionSerial(1);
                channel.setSkuVersion(channel.getErpSku() + "V" + channel.getSkuVersionSerial());
            });
            return;
        }

        List<ProductChannels> copyDbChannels = CollectionUtil.newArrayList(current);


        Map<String, List<ProductChannels>> asinMap = current.stream().collect(Collectors.groupingBy(ProductChannels::getAsin));

        Map<Boolean, List<ProductChannels>> booleanMap = entry.getValue().stream().collect(Collectors.groupingBy(channel -> asinMap.containsKey(channel.getAsin())));


        if (booleanMap.containsKey(Boolean.TRUE)) {

            for (ProductChannels channel : booleanMap.get(Boolean.TRUE)) {

                List<ProductChannels> productChannels = asinMap.get(channel.getAsin());

                if (Objects.nonNull(channel.getId()) && idSkuMap.containsKey(channel.getId())) {
                    String soucrSku = idSkuMap.get(channel.getId());
                    if (Objects.equals(channel.getErpSku() + channel.getAsin(), soucrSku)) {
                        // 如果是更新，并且ASIN和sku无变化
                        continue;
                    }
                }


                ProductChannels dbFirst = productChannels.stream().filter(c -> StrUtil.isNotBlank(c.getSkuVersion()))
                        .findAny().orElse(null);
                if (Objects.isNull(dbFirst)) {
                    throw new CommonException(String.format("SKU映射版本异常 - %s - %s - %s", channel.getSellerSku(), channel.getAsin(), channel.getErpSku()));
                }
                String versionNumber = dbFirst.getSkuVersion().substring(dbFirst.getSkuVersion().length() - 1, dbFirst.getSkuVersion().length());


                channel.setSkuVersionSerial(dbFirst.getSkuVersionSerial());
                channel.setSkuVersion(channel.getErpSku() + "V" + versionNumber);


                // 添加数据等待后续处理
                copyDbChannels.add(channel);
            }
        }

        List<ProductChannels> waitProcessing = booleanMap.get(Boolean.FALSE);


        if (CollectionUtil.isEmpty(waitProcessing)) {
            return;
        }

        // 计算版本号
        int nextSerialNumber = copyDbChannels.get(0).getSkuVersionSerial() % 4 + 1;

        // 新的序列号
        copyDbChannels.forEach(channel -> channel.setSkuVersionSerial(nextSerialNumber));

        // 需要更新原db数据
//            List<Integer> currentIds = current.stream().map(ProductChannels::getId).collect(Collectors.toList());
//            this.lambdaUpdate()
//                    .in(ProductChannels::getId, currentIds)
//                    .set(ProductChannels::getSkuVersionSerial, nextSerialNumber)
//                    .update();

        productChannelsMapper.updateSkuVersionSerial(contextId, currentSku, nextSerialNumber);

        // 赋值编号和序列号
        waitProcessing.forEach(channel -> {
            channel.setSkuVersionSerial(nextSerialNumber);
            channel.setSkuVersion(channel.getErpSku() + "V" + channel.getSkuVersionSerial());
        });

        if (updateDb) {
            this.updateBatchById(waitProcessing);
        }
        return;
    }


    @Override
    public void initMultipleSkuVersion() {
        List<Integer> orgIds = productChannelsMapper.distinctOrgId();

        if (CollectionUtil.isEmpty(orgIds)) {
            return;
        }

        for (Integer orgId : orgIds) {
            Integer cursor = 0;
            List<ProductChannels> channels = null;

            while (CollectionUtil.isNotEmpty(channels = selectByIdCursor(orgId, null, cursor, 1000, null))) {
                initMultipleSkuVersionWithBundleChannels(orgId, channels);
            }
        }
    }

    @Override
    public void sellerSkuProjectUpdate(SellerSkuUpdateLineDTO dto) {
        //组织
        Integer orgId = dto.getOrgId();
        //项目ID
        Long lineId = dto.getLineId();
        //sku
        String sku = dto.getSku();

        if (null == lineId || null == orgId || StringUtils.isEmpty(sku)) {
            log.info("SELLER_SKU_PROJECT_UPDATE: 入参数据异常, 参数为:{}", dto);
            return;
        }
        //未开启sku多版本启用状态
        if (!systemGlobalConfigService.isEnableConfig(orgId, SystemGlobalConfigEnum.SKU_VERSION_INIT_CONFIG)) {
            log.info("SELLER_SKU_PROJECT_UPDATE: 当前组织:{} 未开启sku多版本", orgId);
            List<ProductChannels> list = lambdaQuery()
                    .select(ProductChannels::getId)
                    .eq(ProductChannels::getOrgId, orgId)
                    .like(ProductChannels::getErpSku, sku).list();

            if (CollectionUtil.isEmpty(list)) {
                log.info("SELLER_SKU_PROJECT_UPDATE: 当前组织:{} 当前sku:{} 未查询到对应的sellerSku映射信息", orgId, sku);
                return;
            }
            List<Integer> ids = list.stream().map(ProductChannels::getId).collect(Collectors.toList());
            log.info("SELLER_SKU_PROJECT_UPDATE: 当前组织:{} 当前sku:{} 需要更新sellerSku数据为:{}", orgId, sku, ids);
            lambdaUpdate()
                    .set(ProductChannels::getLineId, lineId)
                    .in(ProductChannels::getId, ids).update();
            return;
        }
        log.info("SELLER_SKU_PROJECT_UPDATE: 当前组织:{} 开启sku多版本", orgId);
        List<SkuChildren> skuChildrens = skuChildrenService.selectByOrgIdAndSkus(orgId, Collections.singletonList(sku));
        if (CollectionUtils.isEmpty(skuChildrens)) {
            log.info("SELLER_SKU_PROJECT_UPDATE: 当前组织:{} 当前sku:{} 未查询到对应的子sku信息", orgId, sku);
            return;
        }

        List<String> parentSkus = skuChildrens.stream().map(SkuChildren::getParentSku).collect(Collectors.toList());
        List<SkuParents> skuParents = skuParentsService.selectByOrgIdAndSkus(orgId, parentSkus);
        if (CollectionUtils.isEmpty(skuParents)) {
            log.info("SELLER_SKU_PROJECT_UPDATE: 当前组织:{} 当前sku:{} 未查询到对应的父sku信息", orgId, sku);
            return;
        }

        List<String> parentSkuList = skuParents.stream().map(SkuParents::getParentSku).distinct().collect(Collectors.toList());
        List<SkuChildren> skuChildrenList = skuChildrenService.selectChildren(orgId, parentSkuList);
        if (CollectionUtils.isEmpty(skuChildrenList)) {
            log.info("SELLER_SKU_PROJECT_UPDATE: 当前组织:{} 当前sku:{} 通过父sku未查询到对应的子sku信息", orgId, sku);
            return;
        }
        List<String> erpSkus = skuChildrenList.stream().map(SkuChildren::getSku).collect(Collectors.toList());

        for (String skus : erpSkus) {
            List<ProductChannels> list = lambdaQuery()
                    .select(ProductChannels::getId)
                    .eq(ProductChannels::getOrgId, orgId)
                    .like(ProductChannels::getErpSku, skus).list();
            if (CollectionUtils.isEmpty(list)) continue;
            List<Integer> ids = list.stream().map(ProductChannels::getId).collect(Collectors.toList());
            lambdaUpdate()
                    .set(ProductChannels::getLineId, lineId)
                    .in(ProductChannels::getId, ids).update();
        }

    }

    private void initMultipleSkuVersionWithBundleChannels(Integer orgId, List<ProductChannels> channels) {


        boolean enableConfig = systemGlobalConfigService.isEnableConfig(orgId, SystemGlobalConfigEnum.SKU_VERSION_INIT_CONFIG);

        if (!enableConfig) {
            throw new CommonException("SKU多版本配置未开启");
        }


        List<Integer> productChannelIds = channels.stream().map(ProductChannels::getId)
                .collect(Collectors.toList());


        List<ProductChannelRelate> channelRelates = productChannelRelateService.lambdaQuery()
                .in(ProductChannelRelate::getProductChannelId, productChannelIds)
                .list();

        Map<Integer, List<ProductChannelRelate>> relateMap = channelRelates.stream()
                .collect(Collectors.groupingBy(ProductChannelRelate::getProductChannelId));

        Set<String> erpSkus = channels.stream().filter(c -> StrUtil.isNotBlank(c.getErpSku()))
                .map(c -> c.getErpSku().split(","))
                .flatMap(Stream::of)
                .collect(Collectors.toSet());


        if (CollectionUtil.isNotEmpty(channelRelates)) {
            erpSkus.addAll(
                    channelRelates.stream().map(ProductChannelRelate::getErpsku).collect(Collectors.toList())
            );
        }

        if (CollectionUtil.isEmpty(erpSkus)) {
            return;
        }


        List<SkuChildren> children = skuChildrenService.selectByOrgIdAndSkus(orgId, CollectionUtil.newArrayList(erpSkus));

        if (CollectionUtil.isEmpty(children)) {
            throw new CommonException("子SKU数据未获取到");
        }

        List<String> parentSkus = children.stream().map(SkuChildren::getParentSku).distinct().collect(Collectors.toList());

        List<SkuParents> skuParents = skuParentsService.selectByOrgIdAndSkus(orgId, parentSkus);

        if (CollectionUtil.isEmpty(parentSkus)) {
            throw new CommonException("父SKU数据未获取到");
        }

        Map<String,SkuParents> parentSkuMap = skuParents.stream().collect(Collectors.toMap(SkuParents::getParentSku, Function.identity(), (v1, v2) -> v1));

        Map<String, String> childParentMapping = children.stream().collect(Collectors.toMap(SkuChildren::getSku, SkuChildren::getParentSku, (v1, v2) -> v1));




        CommonDataContext<ProductChannels> context = new CommonDataContext<>();

        for (ProductChannels channel : channels) {

            if (StrUtil.isBlank(channel.getErpSku())) {
                continue;
            }
            if (StrUtil.isBlank(channel.getType())) {
                continue;
            }
            try {
                findParentByErpSku(channel, childParentMapping, parentSkuMap, relateMap, context);
            } catch (CheckException e) {
                throw new RuntimeException(e);
            }
        }
    }

    void findParentByErpSku(ProductChannels channel,
                               Map<String, String> childParentMapping,
                               Map<String, SkuParents> parentMap,
                               Map<Integer, List<ProductChannelRelate>> relateMap,
                               CommonDataContext<ProductChannels> context
    ) {
        String erpSku = channel.getErpSku();
        String type = channel.getType();
        // 直接设置标志
        channel.setIsParentSku(1);

        if (Objects.equals(type, ProductRelateTypeEnum.SINGLE.getValue())) {
            handleMultipleSkuWithSingleChannels(channel,
                    childParentMapping,
                    parentMap,
                    context
            );
        }


        List<ProductChannelRelate> relates = relateMap.get(channel.getId());
        if (CollectionUtil.isEmpty(relates)) {
            context.getL3().add(channel);
            return;
        }

        List<String> erpSkus = new ArrayList<>();

        for (ProductChannelRelate relate : relates) {
            String erpsku = relate.getErpsku();
            String parentSku = childParentMapping.get(erpsku);
            if (StrUtil.isBlank(parentSku)) {
                log.info("SKU对应子SKU不存在 - {} - {}", channel.getSellerSku(), channel.getErpSku());
                context.getL1().add(channel);
                return;
            }
            SkuParents parents = parentMap.get(parentSku);
            if (Objects.isNull(parents)) {
                log.info("SKU对应子父SKU不存在 - {} - {}", channel.getSellerSku(), channel.getErpSku());
                context.getL2().add(channel);
                return;
            }
            relate.setErpsku(parents.getParentSku());
            relate.setProductId(parents.getId());

            erpSkus.add(parents.getParentSku());
        }
        channel.setErpSku(erpSkus.stream().sorted().collect(Collectors.joining(",")));
    }

    private static void handleMultipleSkuWithSingleChannels(ProductChannels channel,
                                                            Map<String, String> childParentMapping,
                                                            Map<String,SkuParents> parentSkuMap,
                                                            CommonDataContext<ProductChannels> context
    ) {
        String parentSku = childParentMapping.get(channel.getErpSku());
        if (StrUtil.isBlank(parentSku)) {
            log.info("SKU对应子SKU不存在 - {} - {}", channel.getSellerSku(), channel.getErpSku());
            context.getL1().add(channel);
            return;
        }
        SkuParents parents = parentSkuMap.get(parentSku);
        if (Objects.isNull(parents)) {
            context.getL2().add(channel);
            return;
        }
        channel.setErpSku(parents.getParentSku());
        channel.setProductId(parents.getId());
    }

}
