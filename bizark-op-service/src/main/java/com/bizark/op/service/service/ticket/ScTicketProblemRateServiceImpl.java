package com.bizark.op.service.service.ticket;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.op.api.enm.ticket.CustomerComplainDateTypeEnum;
import com.bizark.op.api.enm.ticket.CustomerComplaintCategoryTypeEnum;
import com.bizark.op.api.entity.op.document.ConfTicketProblem;
import com.bizark.op.api.entity.op.ticket.ScTicketProblemRate;
import com.bizark.op.api.entity.op.ticket.customercomplaint.VO.*;
import com.bizark.op.api.entity.op.ticket.customercomplaint.query.ScTicketProblemRateMainQuery;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.ticket.ScTicketProblemRateService;
import com.bizark.op.common.enm.ContentTypeEnum;
import com.bizark.op.common.util.*;
import com.bizark.op.common.util.sql.SqlUtil;
import com.bizark.op.service.mapper.ticket.ConfTicketProblemMapper;
import com.bizark.op.service.mapper.ticket.ScTicketProblemRateMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品问题分析服务实现类
 *
 * @Author: Ailill
 * @Date: 2025-07-07 15:38
 */
@Service
@Slf4j
public class ScTicketProblemRateServiceImpl extends ServiceImpl<ScTicketProblemRateMapper, ScTicketProblemRate> implements ScTicketProblemRateService {

    @Autowired
    private TaskCenterService taskCenterService;


    @Value("${task.center.file.path}")
    private String filePath;

    @Value("${home_query_bi_url}")
    private String QUERY_SALE_NUM_MASTER_URL;

    @Autowired
    private ConfTicketProblemMapper confTicketProblemMapper;

    /**
     * 产品问题分析列表
     *
     * @param query
     * @return
     */
    @Override
    public List<ScTicketProblemRateDetailVO> selectProblemRateDetailList(ScTicketProblemRateMainQuery query) {

        query.reSetQuery();
        query.setOnlyQualityProblemCategoryIds(this.reSetOnlyQualityProblem(query));
        if (StringUtils.isNotEmpty(query.getSidx()) && StringUtils.isNotEmpty(query.getSord())) {
            String orderBy = SqlUtil.escapeOrderBySql(StringUtils.toUnderScoreCase(query.getSidx()) + " " + query.getSord());
            PageHelper.startPage(query.getPage(), query.getRows(), orderBy);
        } else {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<ScTicketProblemRateDetailVO> result = this.baseMapper.selectProblemRateDetailList(query);

        return ConvertUtils.dictConvert(result);
    }

    /**
     * 产品问题分析列表导出
     *
     * @param query
     * @param authUserEntity
     */
    @Override
    public void problemRateDetailListExport(ScTicketProblemRateMainQuery query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(query.getOrganizationId());
        request.setTaskCode("op.problemRateDetailListExport.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        this.taskCenterService.startTask(request, authUserEntity);
    }

    /**
     * 产品问题分析列表导出异步
     *
     * @param query
     * @return
     */
    @Override
    public String problemRateDetailListExportAsync(String query) {
        log.info("problemRateDetailListExportAsync start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "产品问题分析明细" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, CustomerComplaintDetailVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "产品问题分析明细").head(ScTicketProblemRateDetailVO.class).build();
            while (true) {

                ScTicketProblemRateMainQuery complaintMainQuery = JSONObject.parseObject(query, ScTicketProblemRateMainQuery.class);
                complaintMainQuery.setPage(pageNo);
                complaintMainQuery.setRows(pageSize);
                List<ScTicketProblemRateDetailVO> exportList = this.selectProblemRateDetailList(complaintMainQuery);
                if (CollUtil.isEmpty(exportList)) {
                    break;
                }
                log.info(" 产品问题分析明细 query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("产品问题分析明细 exportList.size = " + exportList.size());
                writer.write(exportList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), Files.newInputStream(file.toPath()), "erp/scticketproblemrate/");
            log.info(" 产品问题分析明细 upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info(" 产品问题分析明细 upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * 产品问题分析趋势查询
     *
     * @param query
     * @return
     */
    @Override
    public List<ScTicketProblemRateTendencyVO> selectProblemRateTendencyList(ScTicketProblemRateMainQuery query) {

        query.reSetQuery();
        query.setOnlyQualityProblemCategoryIds(this.reSetOnlyQualityProblem(query));
        List<ScTicketProblemRateTendencyVO> result = new ArrayList<>();
        List<String> dateStr = new ArrayList<>();
        //按月
        if (CustomerComplainDateTypeEnum.MONTH.getType().equalsIgnoreCase(query.getDateType())) {
            List<ScTicketProblemRateTendencyVO> list = this.baseMapper.selectTendencyList(query);
            List<SpiltDateUtil.Range> ranges = SpiltDateUtil.splitToMonths(DateUtils.parseDateToStr("yyyy-MM-dd", query.getDateFrom()), DateUtils.parseDateToStr("yyyy-MM-dd", query.getDateTo()));
            ranges.forEach(range -> {
                String date = DateUtil.convertDateToString(range.getStart(), "yyyy-MM");
                dateStr.add(date);
                result.add(setCustomerComplaintNum(list, range, date, date));
            });
            //按周
        } else if (CustomerComplainDateTypeEnum.WEEK.getType().equalsIgnoreCase(query.getDateType())) {
            List<ScTicketProblemRateTendencyVO> list = this.baseMapper.selectTendencyList(query);
            List<SpiltDateUtil.Range> ranges = SpiltDateUtil.splitToWeeks(DateUtils.parseDateToStr("yyyy-MM-dd", query.getDateFrom()), DateUtils.parseDateToStr("yyyy-MM-dd", query.getDateTo()));
            ranges.forEach(range -> {
                String date = DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd") + "至" + DateUtil.convertDateToString(range.getEnd());
                dateStr.add(date);
                String date1 = DateUtil.convertDateToString(cn.hutool.core.date.DateUtil.beginOfWeek(range.getStart()), "yyyy-MM-dd") + "至" + DateUtil.convertDateToString(cn.hutool.core.date.DateUtil.endOfWeek(range.getEnd()));
                result.add(setCustomerComplaintNum(list, range, date, date1));
            });

            //按日
        } else if (CustomerComplainDateTypeEnum.DAY.getType().equalsIgnoreCase(query.getDateType())) {
            List<ScTicketProblemRateTendencyVO> list = this.baseMapper.selectTendencyList(query);

            List<SpiltDateUtil.Range> ranges = SpiltDateUtil.splitToDays(DateUtils.parseDateToStr("yyyy-MM-dd", query.getDateFrom()), DateUtils.parseDateToStr("yyyy-MM-dd", query.getDateTo()));
            ranges.forEach(range -> {
                String date = DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd");
                dateStr.add(date);
                result.add(setCustomerComplaintNum(list, range, date, date));
            });
        } else {
            throw new ErpCommonException("时间类型错误");
        }


        //设置销量和问题比例
        long saleNumQueryBegin = System.currentTimeMillis();
        setSaleNumAndCustomerComplaintRate(result, query);
        long saleNumQueryEnd = System.currentTimeMillis();
        log.info("查询销量耗时:{}", saleNumQueryEnd - saleNumQueryBegin);
        result.forEach(t -> t.setDateStr(dateStr));
        return result;
    }

    /**
     * 产品问题分析趋势查询导出
     *
     * @param query
     * @param response
     */
    @Override
    public void problemRateTendencyListExport(ScTicketProblemRateMainQuery query, HttpServletResponse response) {
        query.reSetQuery();
        List<ScTicketProblemRateTendencyVO> list = this.selectProblemRateTendencyList(query);
        if (CollectionUtil.isEmpty(list)) {
            throw new ErpCommonException("导出数据为空");
        }
        try {
            response.setContentType(ContentTypeEnum.xlsx.getName());
            String fileName = URLEncoder.encode("产品问题分析趋势" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            Set<String> strings = new HashSet<>();
            if ("num".equals(query.getDimension())) {
                strings.add("problemHandleCost");
                strings.add("salesAmount");
            } else {
                strings.add("problemNum");
                strings.add("salesNum");
            }
            EasyExcelFactory.write(response.getOutputStream(), ScTicketProblemRateTendencyVO.class).excludeColumnFieldNames(strings).sheet("产品问题分析趋势").doWrite(list);
        } catch (Exception e) {
            throw new ErpCommonException("导出失败");
        }
    }

    /**
     * 产品问题分析维度查询
     *
     * @param query
     * @return
     */
    @Override
    public List<ScTicketProblemRateDimensionVO> selectProblemRateDimensionList(ScTicketProblemRateMainQuery query) {

        query.reSetQuery();
        query.setOnlyQualityProblemCategoryIds(this.reSetOnlyQualityProblem(query));

        if (StringUtils.isNotEmpty(query.getSidx()) && StringUtils.isNotEmpty(query.getSord())) {
            String orderBy = SqlUtil.escapeOrderBySql(StringUtils.toUnderScoreCase(query.getSidx()) + " " + query.getSord());
            PageHelper.startPage(query.getPage(), query.getRows(), orderBy);
        } else {
            PageHelper.startPage(query.getPage(), query.getRows());
        }

        List<ScTicketProblemRateDimensionVO> list = this.baseMapper.selectProblemRateDimensionList(query);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<ScTicketProblemRateDimensionVO> childList = this.baseMapper.selectProblemRateDimensionChildList(query);
        JSONObject dimensionSale = querySold(query, childList);
        if (dimensionSale != null && dimensionSale.containsKey("data")) {
            JSONArray data = dimensionSale.getJSONArray("data");
            if (CollectionUtil.isNotEmpty(data)) {
                list.forEach(vo -> {
                    vo.setDimension(query.getDimension());
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        if ("无SKU".equals(vo.getSku())) {
                            setSaleAndAmount(vo, data, vo.getParentSku(), "parentSku", "parent");
                        } else {
                            setSaleAndAmount(vo, data, vo.getSku(), "erpSku", "parent");
                        }
                    } else if (CustomerComplaintCategoryTypeEnum.PARENTSKU.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getParentSku(), "parentSku", "parent");
                    } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getModel(), "productMidModel", "parent");
                    } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getCategoryId(), "categorySecondId", "parent");
                    } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getChannel(), "channel", "parent");
                    } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getShopId() != null ? vo.getShopId().toString() : null, "channelId", "parent");
                    } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getAsin(), "asin", "parent");
                    }
                });

                childList.forEach(vo -> {
                    vo.setDimension(query.getDimension());
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        if ("无SKU".equals(vo.getSku())) {
                            setSaleAndAmount(vo, data, vo.getParentSku(), "parentSku", "child");
                        } else {
                            setSaleAndAmount(vo, data, vo.getSku(), "erpSku", "child");
                        }
                    } else if (CustomerComplaintCategoryTypeEnum.PARENTSKU.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getParentSku(), "parentSku", "child");
                    } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getModel(), "productMidModel", "child");
                    } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getCategoryId(), "categorySecondId", "child");
                    } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getChannel(), "channel", "child");
                    } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getShopId() != null ? vo.getShopId().toString() : null, "channelId", "child");
                    } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                        setSaleAndAmount(vo, data, vo.getAsin(), "asin", "child");
                    }
                });
            }
        }

        list.forEach(vo -> {
            if (StringUtils.isNotEmpty(query.getSord()) && "problemNum".equalsIgnoreCase(query.getSidx())) {
                Comparator<ScTicketProblemRateDimensionVO> comparing = Comparator.comparing(ScTicketProblemRateDimensionVO::getProblemNum, Comparator.nullsLast(Comparator.naturalOrder()));
                if ("desc".equalsIgnoreCase(query.getSord())) {
                    comparing = comparing.reversed();
                }
                vo.setChildList(childList.stream().filter(t -> vo.getCategoryValue().equals(t.getCategoryValue())).sorted(comparing).collect(Collectors.toList()));
            } else if (StringUtils.isNotEmpty(query.getSord()) && "problemHandleCost".equalsIgnoreCase(query.getSidx())) {
                Comparator<ScTicketProblemRateDimensionVO> comparing = Comparator.comparing(ScTicketProblemRateDimensionVO::getProblemHandleCost, Comparator.nullsLast(Comparator.naturalOrder()));
                if ("desc".equalsIgnoreCase(query.getSord())) {
                    comparing = comparing.reversed();
                }
                vo.setChildList(childList.stream().filter(t -> vo.getCategoryValue().equals(t.getCategoryValue())).sorted(comparing).collect(Collectors.toList()));
            } else {
                vo.setChildList(childList.stream().filter(t -> vo.getCategoryValue().equals(t.getCategoryValue())).sorted(Comparator.comparing(ScTicketProblemRateDimensionVO::getBusinessDate)).collect(Collectors.toList()));
            }

        });
        return list;
    }


    private void setSaleAndAmount(ScTicketProblemRateDimensionVO vo, JSONArray data, String value, String type, String itemFlag) {
        if (StringUtils.isEmpty(value)) {
            return;
        }
        Object o = data.stream().filter(t -> value.equals(((JSONObject) t).getString(type))).findFirst().orElse(null);
        if (o != null) {
            if ("child".equals(itemFlag)) {
                JSONArray dataItems = ((JSONObject) o).getJSONArray("dataItems");
                dataItems.stream().filter(t -> vo.getBusinessDate().equals(((JSONObject) t).getString("orderDate"))).findFirst().ifPresent(t -> {
                    vo.setSaleNum(((JSONObject) t).getInteger("saleNum"));
                    vo.setSaleAmount(((JSONObject) t).getBigDecimal("saleAmount"));
                    if ("num".equals(vo.getDimension())) {
                        vo.setProblemRate((vo.getProblemNum() == null || vo.getProblemNum().equals(0) || vo.getSaleNum() == null || vo.getSaleNum().equals(0)) ? null : new BigDecimal(vo.getProblemNum().toString()).divide(new BigDecimal(vo.getSaleNum().toString()), 4, RoundingMode.HALF_UP));
                    } else {
                        vo.setProblemRate((vo.getProblemHandleCost() == null || BigDecimal.ZERO.compareTo(vo.getProblemHandleCost()) == 0 || vo.getSaleAmount() == null || BigDecimal.ZERO.compareTo(vo.getSaleAmount()) == 0) ? null : vo.getProblemHandleCost().divide(vo.getSaleAmount(), 4, RoundingMode.HALF_UP));
                    }
                });
            } else {
                vo.setSaleNum(((JSONObject) o).getInteger("saleNumSum"));
                vo.setSaleAmount(((JSONObject) o).getBigDecimal("saleAmountSum"));
                if ("num".equals(vo.getDimension())) {
                    vo.setProblemRate((vo.getProblemNum() == null || vo.getProblemNum().equals(0) || vo.getSaleNum() == null || vo.getSaleNum().equals(0)) ? null : new BigDecimal(vo.getProblemNum().toString()).divide(new BigDecimal(vo.getSaleNum().toString()), 4, RoundingMode.HALF_UP));
                } else {
                    vo.setProblemRate((vo.getProblemHandleCost() == null || BigDecimal.ZERO.compareTo(vo.getProblemHandleCost()) == 0 || vo.getSaleAmount() == null || BigDecimal.ZERO.compareTo(vo.getSaleAmount()) == 0) ? null : vo.getProblemHandleCost().divide(vo.getSaleAmount(), 4, RoundingMode.HALF_UP));
                }
            }
        }
    }

    /**
     * 产品问题分析维度合计查询
     *
     * @param query
     * @return
     */
    @Override
    public ScTicketProblemRateDimensionVO selectProblemRateDimensionListTotal(ScTicketProblemRateMainQuery query) {
        query.reSetQuery();
        query.setOnlyQualityProblemCategoryIds(this.reSetOnlyQualityProblem(query));

        ScTicketProblemRateDimensionVO result = this.baseMapper.selectProblemRateDimensionListTotal(query);
        List<ScTicketProblemRateDimensionVO> childTotalList = this.baseMapper.selectProblemRateDimensionListChildTotal(query);
        if (result == null) {
            return null;
        }
        List<ScTicketProblemRateDimensionVO> dimensionValueTotal = this.baseMapper.selectProblemRateDimensionTotal(query);
        JSONObject jsonObject = this.queryTotalSold(query, dimensionValueTotal);
        Integer saleNumSum = null;
        BigDecimal saleAmountSum = null;
        JSONArray childSaleList = null;
        if (jsonObject != null && jsonObject.containsKey("saleNumSum")) {
            saleNumSum = jsonObject.getInteger("saleNumSum");
            saleAmountSum = jsonObject.getBigDecimal("saleAmountSum");
            childSaleList = jsonObject.getJSONArray("saleNumSumItem");
            if ("num".equals(query.getDimension())) {
                result.setSaleNum(saleNumSum);
                result.setProblemRate((result.getProblemNum() == null || result.getProblemNum().equals(0) || saleNumSum == null || saleNumSum.equals(0)) ? null : new BigDecimal(result.getProblemNum().toString()).divide(new BigDecimal(saleNumSum.toString()), 4, RoundingMode.HALF_UP));
                for (ScTicketProblemRateDimensionVO child : childTotalList) {
                    childSaleList.stream().filter(t -> child.getBusinessDate().equals(((JSONObject) t).getString("createdAt"))).findFirst().ifPresent(t -> {
                        child.setSaleNum(((JSONObject) t).getInteger("quantity"));
                        child.setProblemRate((child.getProblemNum() == null || child.getProblemNum().equals(0) || child.getSaleNum() == null || child.getSaleNum().equals(0)) ? null : new BigDecimal(child.getProblemNum().toString()).divide(new BigDecimal(child.getSaleNum().toString()), 4, RoundingMode.HALF_UP));
                    });
                }
            } else {
                result.setSaleAmount(saleAmountSum);
                result.setProblemRate((result.getProblemHandleCost() == null || result.getProblemHandleCost().compareTo(BigDecimal.ZERO) == 0 || saleAmountSum == null || saleAmountSum.compareTo(BigDecimal.ZERO) == 0) ? null : result.getProblemHandleCost().divide(saleAmountSum, 4, RoundingMode.HALF_UP));
                for (ScTicketProblemRateDimensionVO child : childTotalList) {
                    childSaleList.stream().filter(t -> child.getBusinessDate().equals(((JSONObject) t).getString("createdAt"))).findFirst().ifPresent(t -> {
                        child.setSaleAmount(((JSONObject) t).getBigDecimal("saleAmount"));
                        child.setProblemRate((child.getProblemHandleCost() == null || child.getProblemHandleCost().compareTo(BigDecimal.ZERO) == 0 || child.getSaleAmount() == null || child.getSaleAmount().compareTo(BigDecimal.ZERO) == 0) ? null : child.getProblemHandleCost().divide(child.getSaleAmount(), 4, RoundingMode.HALF_UP));
                    });
                }
            }
        }
        result.setChildList(childTotalList);
        return result;
    }


    @Override
    public void problemRateDimensionListExport(ScTicketProblemRateMainQuery query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(query.getOrganizationId());
        request.setTaskCode("op.problemRateDimensionList.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        this.taskCenterService.startTask(request, authUserEntity);
    }

    /**
     * 产品问题分析维度查询导出异步
     *
     * @param query
     * @return
     */
    @Override
    public String problemRateDimensionListExportAsync(String query) {
        log.info("selectProblemRateDimensionListExportAsync start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "产品问题分析维度" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, CustomerComplaintDetailVO.class).build();
            ScTicketProblemRateMainQuery complaintMainQuery = JSONObject.parseObject(query, ScTicketProblemRateMainQuery.class);
            Set<String> includeColumnFieldNames = new HashSet<>();
            includeColumnFieldNames.add("businessDate");
            includeColumnFieldNames.add("problemRate");
            if (CustomerComplaintCategoryTypeEnum.PARENTSKU.getType().equals(complaintMainQuery.getCategoryType())) {
                includeColumnFieldNames.add("parentSku");
            } else if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(complaintMainQuery.getCategoryType())) {
                includeColumnFieldNames.add("sku");
                includeColumnFieldNames.add("parentSku");
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(complaintMainQuery.getCategoryType())) {
                includeColumnFieldNames.add("model");
            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(complaintMainQuery.getCategoryType())) {
                includeColumnFieldNames.add("category");
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(complaintMainQuery.getCategoryType())) {
                includeColumnFieldNames.add("asin");
            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(complaintMainQuery.getCategoryType())) {
                includeColumnFieldNames.add("shopTitle");
            } else {

            }
            if ("num".equals(complaintMainQuery.getDimension())) {
                includeColumnFieldNames.add("problemNum");
                includeColumnFieldNames.add("saleNum");
            } else {
                includeColumnFieldNames.add("problemHandleCost");
                includeColumnFieldNames.add("saleAmount");
            }

            WriteSheet writeSheet = EasyExcel.writerSheet(0, "产品问题分析维度").includeColumnFieldNames(includeColumnFieldNames).head(ScTicketProblemRateDimensionVO.class).build();
            while (true) {
                complaintMainQuery.setPage(pageNo);
                complaintMainQuery.setRows(pageSize);
                List<ScTicketProblemRateDimensionVO> exportList = this.selectProblemRateDimensionList(complaintMainQuery);
                if (CollUtil.isEmpty(exportList)) {
                    break;
                }
                exportList = exportList.stream().flatMap(t -> t.getChildList().stream()).collect(Collectors.toList());
                log.info("产品问题分析维度 query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("产品问题分析维度exportList.size = " + exportList.size());
                writer.write(exportList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), Files.newInputStream(file.toPath()), "erp/scticketproblemrate/");
            log.info("产品问题分析维度 upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("产品问题分析维度 upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    private ScTicketProblemRateTendencyVO setCustomerComplaintNum(List<ScTicketProblemRateTendencyVO> list, SpiltDateUtil.Range range, String date, String date1) {
        ScTicketProblemRateTendencyVO vo = new ScTicketProblemRateTendencyVO();
        vo.setTendencyDate(date);
        if (CollectionUtil.isEmpty(list)) {
            vo.setProblemNum(0);
            vo.setProblemHandleCost(BigDecimal.ZERO);
        } else {
            ScTicketProblemRateTendencyVO vo1 = list.stream().filter(t -> date1.equalsIgnoreCase(t.getTendencyDate())).findFirst().orElse(null);
            vo.setProblemNum(vo1 == null ? new Integer(0) : vo1.getProblemNum());
            vo.setProblemHandleCost(vo1 == null ? BigDecimal.ZERO : vo1.getProblemHandleCost());
        }
        return vo;
    }


    private void setSaleNumAndCustomerComplaintRate(List<ScTicketProblemRateTendencyVO> result, ScTicketProblemRateMainQuery query) {
        try {
            JSONObject object = selectSaleNum(query);
            JSONArray data = object.getObject("data", JSONArray.class);
            log.info("查询销量返回数据:data,{}", data);
            if (data != null && data.size() > 0) {
                data.forEach(t -> {
                    Integer saleNum = ((JSONObject) t).getInteger("saleNum");
                    String orderDate = ((JSONObject) t).getString("orderDate");
                    BigDecimal saleAmount = ((JSONObject) t).getBigDecimal("saleAmount");
                    Integer currentMonthSaleNum = ((JSONObject) t).getInteger("saleCurrentNum");
                    result.stream().filter(q -> {
                        //按周区分，销量接口日期返回格式 2024-01-02~2024-01-15
                        if (CustomerComplainDateTypeEnum.WEEK.getType().equalsIgnoreCase(query.getDateType())) {
                            String returnDate = q.getTendencyDate().substring(0, q.getTendencyDate().indexOf("至"));
                            return returnDate.equalsIgnoreCase(orderDate.substring(0, orderDate.indexOf("~")));
                        } else {
                            //按月区分，销量接口日期返回格式 2024-01，按日区分，返回2024-01-01
                            return q.getTendencyDate().equalsIgnoreCase(orderDate);
                        }
                    }).forEach(vo -> {
                        if ("num".equals(query.getDimension())) {
                            vo.setSalesNum(saleNum);
                            vo.setProblemRate(vo.getSalesNum().equals(0) ? new BigDecimal("0.0000").setScale(4, RoundingMode.HALF_UP) : new BigDecimal(vo.getProblemNum().toString()).divide(new BigDecimal(vo.getSalesNum().toString()), 4, RoundingMode.HALF_UP));
                            if (CustomerComplainDateTypeEnum.MONTH.getType().equals(query.getDateType()))
                                vo.setSalesNum(currentMonthSaleNum);
                        } else {
                            vo.setSalesAmount(saleAmount == null ? BigDecimal.ZERO : saleAmount);
                            vo.setProblemRate(BigDecimal.ZERO.compareTo(vo.getSalesAmount()) == 0 ? new BigDecimal("0.0000").setScale(4, RoundingMode.HALF_UP) : new BigDecimal(vo.getProblemHandleCost().toString()).divide(vo.getSalesAmount(), 4, RoundingMode.HALF_UP));
                        }
                    });
                });
            }


        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询销量错误,{}", e.getMessage());
            result.forEach(vo -> {
                vo.setSalesNum(0);
                vo.setSalesAmount(BigDecimal.ZERO);
                vo.setProblemRate(new BigDecimal("0.0000").setScale(4, RoundingMode.HALF_UP));
            });
        }
    }


    public JSONObject selectSaleNum(ScTicketProblemRateMainQuery query) {
        HashMap<String, Object> reqParm = new LinkedHashMap<>();
        if (query.getOrganizationId() != null) {
            reqParm.put("orgId", query.getOrganizationId());
        }
        if (CollectionUtil.isNotEmpty(query.getChannel())) {
            reqParm.put("channelList", query.getChannel());
        }
        if (CollectionUtil.isNotEmpty(query.getShopIds())) {
            reqParm.put("shopIdList", query.getShopIds());
        }
        if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equalsIgnoreCase(query.getDataType())) {
            reqParm.put("modelList", query.getDataTypeValue());
        }
        if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equalsIgnoreCase(query.getDataType())) {
            reqParm.put("asinList", query.getDataTypeValue());
        }
        if (CustomerComplaintCategoryTypeEnum.SKU.getType().equalsIgnoreCase(query.getDataType())) {
            reqParm.put("skuList", query.getDataTypeValue());
        }
        if (CustomerComplaintCategoryTypeEnum.PARENTSKU.getType().equalsIgnoreCase(query.getDataType())) {
            reqParm.put("parentSkuList", query.getDataTypeValue());
        }
        if (CollectionUtil.isNotEmpty(query.getProductCateGoryIds())) {
            reqParm.put("categoryIdList", query.getProductCateGoryIds());
        }
        if (query.getNewProductFlag() != null) {
            reqParm.put("newProductFlag", query.getNewProductFlag());
        }
        reqParm.put("dateStart", DateUtils.parseDateToStr("yyyy-MM-dd", query.getDateFrom()));
        reqParm.put("dateEnd", DateUtils.parseDateToStr("yyyy-MM-dd", query.getDateTo()));
        reqParm.put("dateType", query.getDateType());
        HttpRequest request = HttpUtil.createPost(QUERY_SALE_NUM_MASTER_URL + "/api/v1/CustomerAnalysisTrandSales");
        request.header("Content-Type", "application/json");
        request.body(JSON.toJSONString(reqParm));
        log.info("问题分析selectSaleNum--请求{}",JSONObject.toJSONString(reqParm));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new IllegalArgumentException("查询销量统计接口异常");
        }
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("问题分析selectSaleNum--响应{}",jsonObject.toJSONString());
        return jsonObject;
    }

    public List<Integer> reSetOnlyQualityProblem(ScTicketProblemRateMainQuery query) {

        if (query.getOnlyQualityProblem() == null) {
            return null;
        }
        List<ConfTicketProblem> confTicketProblems = confTicketProblemMapper.selectOnlyQualityProblem(query.getOrganizationId());
        return CollectionUtil.isEmpty(confTicketProblems) ? null : confTicketProblems.stream().map(t -> t.getProblemId()).distinct().collect(Collectors.toList());

    }

    /**
     * 维度查询销量
     *
     * @param query
     * @param resultList
     * @return
     */
    private JSONObject querySold(ScTicketProblemRateMainQuery query, List<ScTicketProblemRateDimensionVO> resultList) {
        List<Integer> shopIdList = resultList.stream().map(t -> t.getShopId()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<String> asinList = resultList.stream().map(t -> t.getAsin()).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> modelList = resultList.stream().map(t -> t.getModel()).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> skuList = resultList.stream().map(t -> t.getSku()).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> parentSkuList = resultList.stream().map(t -> t.getParentSku()).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> categoryIdList = resultList.stream().filter(t -> StringUtils.isNotEmpty(t.getCategoryId())).map(t -> t.getCategoryId()).distinct().collect(Collectors.toList());
        List<String> channelList = resultList.stream().filter(t -> StringUtils.isNotEmpty(t.getChannel())).map(t -> t.getChannel()).distinct().collect(Collectors.toList());

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orgId", query.getOrganizationId());
        paramMap.put("dateStart", DateUtil.convertDateToString(query.getDateFrom()));
        paramMap.put("dateEnd", DateUtil.convertDateToString(query.getDateTo()));
        paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
        paramMap.put("dateType", query.getDateType());
        paramMap.put("newProductFlag", query.getNewProductFlag());


        if (CustomerComplaintCategoryTypeEnum.SKU.getType().equalsIgnoreCase(query.getCategoryType())) {

            List<String> hasSkuList = resultList.stream().filter(t -> !"无SKU".equals(t.getSku())).map(t -> t.getSku()).distinct().collect(Collectors.toList());
            List<String> noSkuList = resultList.stream().filter(t -> "无SKU".equals(t.getSku())).map(t -> t.getParentSku()).distinct().collect(Collectors.toList());
            JSONObject hasSkuJsonObject = new JSONObject();
            JSONObject noSkuJsonObject = new JSONObject();
            if (CollectionUtil.isNotEmpty(hasSkuList)) {
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("skuList", skuList);
                hasSkuJsonObject = this.sendCustomerComplainMessage(paramMap);
            }
            if (CollectionUtil.isNotEmpty(noSkuList)) {
                paramMap.put("skuList", null);
                paramMap.put("parentSkuList", noSkuList);
                paramMap.put("Type", CustomerComplaintCategoryTypeEnum.PARENTSKU.getType());
                noSkuJsonObject = this.sendCustomerComplainMessage(paramMap);
            }
            if (!hasSkuJsonObject.containsKey("data") && !noSkuJsonObject.containsKey("data")) {
                return new JSONObject();
            }
            if (hasSkuJsonObject.containsKey("data")) {
                if (!noSkuJsonObject.containsKey("data")) {
                    return hasSkuJsonObject;
                }
                JSONArray hasSkuData = hasSkuJsonObject.getJSONArray("data");
                JSONArray noSkuData = noSkuJsonObject.getJSONArray("data");
                hasSkuData.addAll(noSkuData);
                JSONObject result = new JSONObject();
                result.put("data", hasSkuData);
                return result;
            }
            if (noSkuJsonObject.containsKey("data")) {
                return noSkuJsonObject;
            }
            return new JSONObject();

        } else {
            paramMap.put("Type", query.getCategoryType());
            if (CustomerComplaintCategoryTypeEnum.PARENTSKU.getType().equals(query.getCategoryType())) {
                paramMap.put("parentSkuList", parentSkuList);
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                paramMap.put("asinList", asinList);
            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                paramMap.put("categoryIdList", categoryIdList);
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                paramMap.put("modelList", modelList);
            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                paramMap.put("channelList", channelList);
            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                paramMap.put("shopIdList", shopIdList);
            }

            JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
            if (jsonObject == null || !jsonObject.containsKey("data")) {
                return new JSONObject();
            }
            return jsonObject;
        }

    }


    private JSONObject queryTotalSold(ScTicketProblemRateMainQuery query, List<ScTicketProblemRateDimensionVO> dimensionValueTotal) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orgId", query.getOrganizationId());
        paramMap.put("dateStart", DateUtil.convertDateToString(query.getDateFrom()));
        paramMap.put("dateEnd", DateUtil.convertDateToString(query.getDateTo()));
        paramMap.put("apiType", "SUM");//SUM：汇总业务 ，ITEM:为明细行业务
        paramMap.put("dateType", query.getDateType());
        paramMap.put("newProductFlag", query.getNewProductFlag());
        paramMap.put("channelList", query.getChannel());
        paramMap.put("shopIdList", query.getShopIds());
        paramMap.put("categoryIdList", query.getProductCateGoryIds());
        if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
            List<String> hasSkuList = dimensionValueTotal.stream().filter(t -> !"无SKU".equals(t.getSku())).map(t -> t.getSku()).distinct().collect(Collectors.toList());
            List<String> noSkuList = dimensionValueTotal.stream().filter(t -> "无SKU".equals(t.getSku())).map(t -> t.getParentSku()).distinct().collect(Collectors.toList());
            JSONObject hasSkuListJsonObject = null;
            JSONObject noSkuListJsonObject = null;
            if (CollectionUtil.isNotEmpty(hasSkuList)) {
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("skuList", hasSkuList);
                hasSkuListJsonObject = this.sendCustomerComplainMessage(paramMap);
                if (hasSkuListJsonObject == null || !hasSkuListJsonObject.containsKey("saleNumSum")) {
                    hasSkuListJsonObject = null;
                }
            }
            if (CollectionUtil.isNotEmpty(noSkuList)) {
                paramMap.put("Type", CustomerComplaintCategoryTypeEnum.PARENTSKU.getType());
                paramMap.put("parentSkuList", noSkuList);
                noSkuListJsonObject = this.sendCustomerComplainMessage(paramMap);
                if (noSkuListJsonObject == null || !noSkuListJsonObject.containsKey("saleNumSum")) {
                    noSkuListJsonObject = null;
                }
            }
            return mergeTotalSold(hasSkuListJsonObject, noSkuListJsonObject);

        } else if (CustomerComplaintCategoryTypeEnum.PARENTSKU.getType().equals(query.getCategoryType())) {
            paramMap.put("Type", query.getCategoryType());
            paramMap.put("parentSkuList", dimensionValueTotal.stream().map(t -> t.getParentSku()).distinct().collect(Collectors.toList()));
        } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
            paramMap.put("Type", query.getCategoryType());
            paramMap.put("asinList", dimensionValueTotal.stream().map(t -> t.getAsin()).distinct().collect(Collectors.toList()));
        } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
            paramMap.put("Type", query.getCategoryType());
            paramMap.put("modelList", dimensionValueTotal.stream().map(t -> t.getModel()).distinct().collect(Collectors.toList()));
        } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
            paramMap.put("Type", query.getCategoryType());
            paramMap.put("channelList", dimensionValueTotal.stream().map(t -> t.getChannel()).distinct().collect(Collectors.toList()));
        } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
            paramMap.put("Type", query.getCategoryType());
            paramMap.put("shopIdList", dimensionValueTotal.stream().map(t -> t.getShopId()).distinct().collect(Collectors.toList()));
        } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
            paramMap.put("Type", query.getCategoryType());
            paramMap.put("categoryIdList", dimensionValueTotal.stream().filter(t -> StringUtils.isNotEmpty(t.getCategoryId())).flatMap(t -> Arrays.stream(t.getCategoryId().split(","))).map(Long::parseLong).distinct().collect(Collectors.toList()));
        } else {

        }

        JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
        if (jsonObject == null || !jsonObject.containsKey("saleNumSum")) {
            return new JSONObject();
        }
        return jsonObject;

    }

    private JSONObject mergeTotalSold(JSONObject json1, JSONObject json2) {
        if (json1 == null && json2 == null) {
            return null;
        }
        if (json1 == null) {
            return json2;
        }
        if (json2 == null) {
            return json1;
        }
        JSONObject merged = new JSONObject();
        merged.put("saleNumSum", json1.getLong("saleNumSum") + json2.getLong("saleNumSum"));
        merged.put("saleAmountSum", json1.getBigDecimal("saleAmountSum").add(json2.getBigDecimal("saleAmountSum")));
        Map<String, JSONObject> monthMap = new HashMap<>();

        JSONArray items1 = json1.getJSONArray("saleNumSumItem");
        for (int i = 0; i < items1.size(); i++) {
            JSONObject item = items1.getJSONObject(i);
            String month = item.getString("createdAt");
            monthMap.put(month, new JSONObject()
                    .fluentPut("createdAt", month)
                    .fluentPut("quantity", item.getInteger("quantity"))
                    .fluentPut("saleAmount", item.getBigDecimal("saleAmount")));
        }

        JSONArray items2 = json2.getJSONArray("saleNumSumItem");
        for (int i = 0; i < items2.size(); i++) {
            JSONObject item = items2.getJSONObject(i);
            String month = item.getString("createdAt");
            if (monthMap.containsKey(month)) {
                JSONObject existing = monthMap.get(month);
                existing.put("quantity", existing.getInteger("quantity") + item.getInteger("quantity"));
                existing.put("saleAmount", existing.getBigDecimal("saleAmount").add(item.getBigDecimal("saleAmount")));
            } else {
                monthMap.put(month, new JSONObject()
                        .fluentPut("createdAt", month)
                        .fluentPut("quantity", item.getInteger("quantity"))
                        .fluentPut("saleAmount", item.getBigDecimal("saleAmount")));
            }
        }

        JSONArray mergedItems = new JSONArray();
        monthMap.values().forEach(mergedItems::add);

        merged.put("saleNumSumItem", mergedItems);

        return merged;
    }

    public JSONObject sendCustomerComplainMessage(Map<String, Object> paramMap) {
        try {

            String url = QUERY_SALE_NUM_MASTER_URL + "/api/v1/CustomerAnalysisSales";
            HttpRequest request = HttpUtil.createPost(url);
            log.error("查询问题分析销量统计参数{}", JSON.toJSONString(paramMap));
            request.body(JSON.toJSONString(paramMap));
            HttpResponse response = request.execute();
            if (!response.isOk()) {
                log.error("查询问题分析销量统计异常--{}---异常原因{}", JSONObject.toJSONString(paramMap), response.body());
                return new JSONObject();
            }
            JSONObject result = JSONObject.parseObject(response.body());
            log.info("查询问题分析销量统计参数--{}---返回结果{}", JSONObject.toJSONString(paramMap), result.toJSONString());
            return result;
        } catch (Exception e) {
            return new JSONObject();
        }

    }

}
