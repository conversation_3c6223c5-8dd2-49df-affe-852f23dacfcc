package com.bizark.op.service.api;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.enm.sale.walmart.WalmartUrlEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.inventory.response.query.WalmartBatchInventorySelectResponse;
import com.bizark.op.api.entity.op.inventory.response.query.WalmartInventorySelectResponse;
import com.bizark.op.api.entity.op.sale.response.WalmartOrderCancelResponse;
import com.bizark.op.api.entity.op.sale.response.WalmartWfsInventoryResponse;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.common.util.AliyunOssClientUtil;
import com.bizark.op.common.util.Base64;
import com.bizark.op.service.util.AccessTokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class WalmartApi {

    @Autowired
    private InventorySelectApi inventorySelectApi;


    @Autowired
    private AccountService accountService;


    @Autowired
    private AccessTokenUtils accessTokenUtils;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public WalmartBatchInventorySelectResponse selectWalmartInventory(Account account, Integer limit, String nextCursor) {
        return inventorySelectApi.selectWalmartInventory(account, limit, nextCursor);
    }


    public WalmartInventorySelectResponse selectWalmartInventory(Account account, String sellerSku) {
        return inventorySelectApi.selectWalmartInventory(account, sellerSku);
    }


    public WalmartWfsInventoryResponse getWfsInventory(Account account, String startRange, String endRange, Integer offset, Integer limit) {
        limit = Objects.isNull(limit) ? 300 : limit;
        offset = Objects.isNull(offset) ? 0 : offset;
        JSONObject jsonObject = checkAccount(account);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            log.error("Walmart wfs inventory request error {} - 未获取到token信息", account.getFlag());
            throw new CommonException("未获取到token信息");
        }
        String str = jsonObject.get("clientId") + ":" + jsonObject.get("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("WM_SVC.NAME", "My API Key");
        headers.put("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
        headers.put("WM_SEC.ACCESS_TOKEN", token);

        String url = WalmartUrlEnum.WFS_INVENTORY.getValue() + "?offset=" + offset + "&limit=" + limit;

        if (StrUtil.isNotBlank(startRange)) {
            url += "&fromModifiedDate=" + startRange;
        }
        if (StrUtil.isNotBlank(endRange)) {
            url += "&toModifiedDate=" + endRange;
        }

        HttpRequest request = HttpUtil.createGet(url);
        request.setConnectionTimeout(1000 * 20);
        request.headerMap(headers, true);

        HttpResponse httpResponse = request.execute();

        if (!httpResponse.isOk()) {
            log.error("Walmart wfs inventory request error {} - {} - {}", account.getFlag(), httpResponse.getStatus(), httpResponse.body());
            return null;
        }

        String body = httpResponse.body();
        log.info("Walmart wfs inventory response - {} - {}", account.getFlag(), body);

        if (Objects.equals(httpResponse.getStatus(), 204)) {
            return new WalmartWfsInventoryResponse();
        }

        if (StrUtil.isBlank(body)) {
            return null;
        }
        return JSON.parseObject(body, WalmartWfsInventoryResponse.class);
    }


    public JSONObject checkAccount(Account account) {
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("connectStr is not empty");
            throw new CommonException("connectStr is not empty :" + account.getFlag());
        }
        //获取ID与秘钥
        JSONObject json = JSON.parseObject(connectStr);

        if (!json.containsKey("clientId")) {
            log.error("clientId is not empty:{}", account.getFlag());
            throw new CommonException("clientId is not empty :" + account.getFlag());

        }

        if (!json.containsKey("clientSecret")) {
            log.error("clientSecret is not empty:{}", account.getFlag());
            throw new CommonException("clientSecret is not empty :" + account.getFlag());
        }

        return json;
    }

    public WalmartOrderCancelResponse walmartSaleOrderCancel(Account account, String url, String purchaseOrderId, String jsonBody) {

        JSONObject jsonObject = checkAccount(account);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            throw new CommonException("未获取到token信息");
        }
        String str = jsonObject.get("clientId") + ":" + jsonObject.get("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("WM_SVC.NAME", "My API Key");
        headers.put("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
        headers.put("WM_SEC.ACCESS_TOKEN", token);
        log.info("订单取消请求body:{}", jsonBody);
        HttpRequest request = HttpUtil.createPost(url.replace("{purchaseOrderId}", purchaseOrderId));
        request.headerMap(headers, true);
        request.body(jsonBody);
        HttpResponse httpResponse = request.execute();

        if (!httpResponse.isOk()) {
            log.error("Walmart order cancel request error {} - {} - {}", account.getFlag(), httpResponse.getStatus(), httpResponse.body());
            return null;
        }

        String body = httpResponse.body();
        if (StrUtil.isBlank(body)) {
            log.error("Walmart order cancel response body is empty:{}", account.getFlag());
            return null;
        }
        log.info("Walmart order cancel response - {} - {}", account.getFlag(), body);
        return JSON.parseObject(body, WalmartOrderCancelResponse.class);
    }

    public JSONObject walmartDsvSaleOrderCancel(Account account, String url, String purchaseOrderId, String jsonBody,String shipNode) {

        JSONObject jsonObject = checkAccount(account);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            throw new CommonException("未获取到token信息");
        }
        String str = jsonObject.get("clientId") + ":" + jsonObject.get("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("Content-Type", "application/json");
        headers.put("WM_SVC.NAME", "My API Key");
        headers.put("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
        headers.put("WM_SEC.ACCESS_TOKEN", token);
        log.info("walmart_dsv订单取消请求body:{}", jsonBody);
        HttpRequest request = HttpUtil.createPost(url.replace("{purchaseOrderId}", purchaseOrderId)  + "?shipNode=" + shipNode);
        request.headerMap(headers, true);
        request.body(jsonBody);
        HttpResponse httpResponse = request.execute();

        if (!httpResponse.isOk()) {
            log.error("Walmart_dsv order cancel request error {} - {} - {}", account.getFlag(), httpResponse.getStatus(), httpResponse.body());
            return null;
        }

        String body = httpResponse.body();
        if (StrUtil.isBlank(body)) {
            log.error("Walmart_dsv order cancel response body is empty:{}", account.getFlag());
            return null;
        }
        log.info("Walmart_dsv order cancel response - {} - {}", account.getFlag(), body);
        return JSONObject.parseObject(body, JSONObject.class);
    }

    public String walmartShipment(Account account, String url, String jsonBody, String fileName, String folder) {

        String uuid = UUID.randomUUID().toString();
        JSONObject jsonObject = checkAccount(account);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            return null;
        }
        int maxRetries = 2; // 重试次数
        int retryCount = 0;
        int timeout = 30 * 60 * 1000;// 超时时间 30分钟

        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
        connManager.setMaxTotal(100);
        connManager.setDefaultMaxPerRoute(20);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connManager)
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setConnectTimeout(30000)
                        .setSocketTimeout(timeout)
                        .build())
                .build();

        while (retryCount < maxRetries) {
            File tempFile = null;
            try {
                String str = jsonObject.get("clientId") + ":" + jsonObject.get("clientSecret");
                String authorization = "Basic " + Base64.encode(str.getBytes());

                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", authorization);
                headers.put("WM_SVC.NAME", "My API Key");
                headers.put("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
                headers.put("WM_SEC.ACCESS_TOKEN", token);
                headers.put("Accept", "application/pdf");
                headers.put("Connection", "keep-alive"); // 保持连接

                tempFile = File.createTempFile("walmart_pdf_", ".tmp");
                tempFile.deleteOnExit();


                log.info("{}--开始下载文件--{}", uuid, url);
                long startTime = System.currentTimeMillis();

                HttpPost httpPost = new HttpPost(url);
                headers.forEach(httpPost::setHeader);
                httpPost.setEntity(new StringEntity(jsonBody, ContentType.APPLICATION_JSON));

                try (CloseableHttpResponse response = httpClient.execute(httpPost);
                     FileOutputStream fos = new FileOutputStream(tempFile)) {
                    long fileSize = 0;
                    Header contentLengthHeader = response.getFirstHeader("Content-Length");
                    if (contentLengthHeader != null) {
                        fileSize = Long.parseLong(contentLengthHeader.getValue());
                        log.info("{}--文件大小: {} MB",uuid, fileSize / (1024 * 1024));
                    }

                    ReadableByteChannel channel = Channels.newChannel(response.getEntity().getContent());
                    FileChannel fileChannel = fos.getChannel();
                    long transferred = 0;
                    long lastLogTime = System.currentTimeMillis();
                    ByteBuffer buffer = ByteBuffer.allocateDirect(1024 * 1024);

                    while (channel.read(buffer) != -1) {
                        buffer.flip();
                        transferred += fileChannel.write(buffer);
                        buffer.clear();
                        long currentTime = System.currentTimeMillis();
                        if (currentTime - lastLogTime > 5000) {
                            log.info("{}--已下载: {}/{} MB", uuid,transferred / (1024 * 1024),
                                    fileSize > 0 ? fileSize / (1024 * 1024) : "未知");
                            lastLogTime = currentTime;
                        }
                    }
                    if (fileSize > 0 && transferred != fileSize) {
                        throw new IOException(uuid + "文件下载不完整: 期望 " + fileSize + " bytes, 实际 " + transferred + " bytes");
                    }

                    log.info("{}--文件下载完成, 大小: {} MB, 耗时: {}秒",
                            uuid,
                            transferred / (1024 * 1024),
                            (System.currentTimeMillis() - startTime) / 1000);
                    File uploadFile = tempFile;
                    CompletableFuture<String> uploadFuture = CompletableFuture.supplyAsync(() -> {
                        try (FileInputStream fis = new FileInputStream(uploadFile)) {
                            log.info("{}开始上传到阿里云OSS", uuid);
                            long uploadStartTime = System.currentTimeMillis();
                            String result = AliyunOssClientUtil.resumableUpload(
                                    fileName,
                                    fis,
                                    folder,
                                    uploadFile.length()
                            );
                            log.info("{}---上传完成, 耗时: {}秒",
                                    uuid,
                                    (System.currentTimeMillis() - uploadStartTime) / 1000);
                            return result;
                        } catch (Exception e) {
                            log.error("{}--上传失败: {}",uuid, e.getMessage());
                            return null;
                        }
                    }, threadPoolTaskExecutor);
                    return uploadFuture.get(15, TimeUnit.MINUTES);
                }
            } catch (Exception e) {
                retryCount++;
                log.error("{}---处理失败 (尝试 {}/{}): {}", uuid,retryCount, maxRetries, e.getMessage());
                if (retryCount >= maxRetries) {
                    log.error("{}--所有重试均失败", uuid);
                    return null;
                }
                try {
                    Thread.sleep((long) (Math.pow(2, retryCount) * 1000));
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            } finally {
                // 清理临时文件
                if (tempFile != null && tempFile.exists()) {
                    try {
                        Files.delete(tempFile.toPath());
                    } catch (IOException e) {
                        log.warn("{}--删除临时文件失败: {}",uuid, e.getMessage());
                    }
                }
            }
        }

        try {
            httpClient.close();
        } catch (IOException e) {
            log.warn("{}--关闭HTTP客户端失败: {}", uuid,e.getMessage());
        }

        return null;
    }


    /**
     * Description: walmart 订单行退款
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/2/17
     */
    public JSONObject walmartRefundOrderLines(Account account, String url, String purchaseOrderId, String jsonBody) {

        JSONObject jsonObject = checkAccount(account);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            throw new CommonException("未获取到token信息");
        }
        String str = jsonObject.get("clientId") + ":" + jsonObject.get("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("Content-Type", "application/json");
        headers.put("WM_SVC.NAME", "My API Key");
        headers.put("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
        headers.put("WM_SEC.ACCESS_TOKEN", token);
        log.info("Walmart订单行退款响应请求body:{}", jsonBody);
        HttpRequest request = HttpUtil.createPost(url.replace("{purchaseOrderId}", purchaseOrderId));
        request.headerMap(headers, true);
        request.body(jsonBody);
        HttpResponse httpResponse = request.execute();

        if (!httpResponse.isOk()) {
            log.error("Walmart 订单行退款异常{} - {} - {}", account.getFlag(), httpResponse.getStatus(), httpResponse.body());
            return null;
        }

        String body = httpResponse.body();
        if (StrUtil.isBlank(body)) {
            log.error("Walmart订单行退款响应为空:{}", account.getFlag());
            return null;
        }
        log.info("Walmart订单行退款响应 - {} - {}", account.getFlag(), body);
        return JSONObject.parseObject(body, JSONObject.class);
    }

    /**
     * Description: walmart 退货退款
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/2/17
     */
    public JSONObject walmartReturnRefund(Account account, String url, String reverseOrderId, String jsonBody) {

        JSONObject jsonObject = checkAccount(account);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            throw new CommonException("未获取到token信息");
        }
        String str = jsonObject.get("clientId") + ":" + jsonObject.get("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("Content-Type", "application/json");
        headers.put("WM_SVC.NAME", "My API Key");
        headers.put("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
        headers.put("WM_SEC.ACCESS_TOKEN", token);
        log.info("Walmart退货退款响应请求body:{}", jsonBody);
        HttpRequest request = HttpUtil.createPost(url.replace("{returnOrderId}", reverseOrderId));
        request.headerMap(headers, true);
        request.body(jsonBody);
        HttpResponse httpResponse = request.execute();

        if (!httpResponse.isOk()) {
            log.error("Walmart退货退款退款异常{} - {} - {}", account.getFlag(), httpResponse.getStatus(), httpResponse.body());
            return null;
        }

        String body = httpResponse.body();
        if (StrUtil.isBlank(body)) {
            log.error("Walmart退货退款退款响应为空:{}", account.getFlag());
            return null;
        }
        log.info("Walmart退货退款退款响应 - {} - {}", account.getFlag(), body);
        return JSONObject.parseObject(body, JSONObject.class);
    }

    public WalmartWfsInventoryResponse getWfsInventoryWithRetry(Account account, String startRange, String endRange, int offset, int limit,int retryCount) {
        retryCount = retryCount + 1;
        for (int i = 0; i < retryCount; i++) {
            try {
                WalmartWfsInventoryResponse wfsInventory = getWfsInventory(account, startRange, endRange, offset, limit);
                if (Objects.nonNull(wfsInventory)) {
                    return wfsInventory;
                }
            } catch (Exception e) {
                log.info("Walmart平台仓库存获取异常 - {} ", account.getFlag(), e);
            }

        }
        return null;
    }




    public <T> T walmartCommonPost(Account account, String url, String jsonBody, Class<T> tClass) {

        JSONObject jsonObject = checkAccount(account);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            throw new CommonException("未获取到token信息");
        }
        String str = jsonObject.get("clientId") + ":" + jsonObject.get("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("Content-Type", "application/json");
        headers.put("WM_SVC.NAME", "My API Key");
        headers.put("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
        headers.put("WM_SEC.ACCESS_TOKEN", token);
        log.info("walmart渠道请求请求体--{}--url--{}", jsonBody, url);
        HttpRequest request = HttpUtil.createPost(url);
        request.headerMap(headers, true);
        request.body(jsonBody);
        HttpResponse httpResponse = request.execute();

        if (!httpResponse.isOk()) {
            log.error("walmart渠道请求响应异常请求体--{}--url--{}--店铺--{}---响应状态--{}--响应体--{}", jsonBody, url, account.getFlag(), httpResponse.getStatus(), httpResponse.body());
            return null;
        }

        String body = httpResponse.body();
        if (StrUtil.isBlank(body)) {
            log.error("walmart渠道请求响应为空请求体--{}--url--{}--店铺--{}", jsonBody, url, account.getFlag());
            return null;
        }
        log.info("walmart渠道请求响应请求体--{}--url--{}--店铺--{}--响应体--{}", jsonBody, url, account.getFlag(), body);
        return JSONObject.parseObject(body, tClass);
    }



    public <T> T walmartCommonDelete(Account account, String url, String jsonBody, Class<T> tClass) {

        JSONObject jsonObject = checkAccount(account);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            throw new CommonException("walmart渠道delete请求未获取到token信息");
        }
        String str = jsonObject.get("clientId") + ":" + jsonObject.get("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("Content-Type", "application/json");
        headers.put("WM_SVC.NAME", "My API Key");
        headers.put("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
        headers.put("WM_SEC.ACCESS_TOKEN", token);
        log.info("walmart渠道delete请求请求体--{}--url--{}", jsonBody, url);
        HttpRequest request = HttpUtil.createRequest(Method.DELETE, url);
        request.headerMap(headers, true);
        if (StrUtil.isNotBlank(jsonBody)) {
            request.body(jsonBody);
        }
        HttpResponse httpResponse = request.execute();

        if (!httpResponse.isOk()) {
            log.error("walmart渠道delete请求响应异常请求体--{}--url--{}--店铺--{}---响应状态--{}--响应体--{}", jsonBody, url, account.getFlag(), httpResponse.getStatus(), httpResponse.body());
            return null;
        }

        String body = httpResponse.body();
        if (StrUtil.isBlank(body)) {
            log.error("walmart渠道delete请求响应为空请求体--{}--url--{}--店铺--{}", jsonBody, url, account.getFlag());
            return null;
        }
        log.info("walmart渠道delete请求响应请求体--{}--url--{}--店铺--{}--响应体--{}", jsonBody, url, account.getFlag(), body);
        return JSONObject.parseObject(body, tClass);
    }
}

