package com.bizark.op.service.service.amazon.fba;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.framework.security.AuthContextHolder;
import com.bizark.op.api.amazon.vendor.fba.model.*;
import com.bizark.op.api.cons.SaleConstant;
import com.bizark.op.api.enm.amazon.fba.*;
import com.bizark.op.api.enm.finance.FinanceErrorEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.amazon.AmazonApiEnum;
import com.bizark.op.api.entity.op.amazon.AmazonApiURLEnum;
import com.bizark.op.api.entity.op.amazon.fba.DO.MarStaCartonSpecDO;
import com.bizark.op.api.entity.op.amazon.fba.DO.MarStaPackingSkuDO;
import com.bizark.op.api.entity.op.amazon.fba.DTO.*;
import com.bizark.op.api.entity.op.amazon.fba.*;
import com.bizark.op.api.entity.op.amazon.fba.VO.*;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.amazon.fba.*;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.amazon.fba.MarStaTaskInfoMapper;
import com.bizark.op.service.util.AmazonRequestUtils;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.core.util.CollectionUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class MarStaTaskInfoServiceImpl extends ServiceImpl<MarStaTaskInfoMapper, MarStaTaskInfo>
        implements MarStaTaskInfoService {
    private static final BigDecimal CUBIC_INCHES_PER_CUBIC_FOOT = new BigDecimal("1728");

    @Autowired
    private MarStaPackingSkusService marStaPackingSkusService;

    @Autowired
    private MarStaShipmentSkusService marStaShipmentSkusService;

    @Autowired
    private MarStaShipmentAddressService marStaShipmentAddressService;

    @Autowired
    private MarPlatformWarehouseAddressService warehouseAddressService;

    @Autowired
    private MarStaCartonSpecService marStaCartonSpecService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AmazonRequestUtils amazonRequestUtils;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private MarStaPackGroupInfoService marStaPackGroupInfoService;

    @Autowired
    @Lazy
    private MarStaTaskInfoService marStaTaskInfoProxyService;

    @Autowired
    @Lazy
    private MarFbaShipmentInfoService marFbaShipmentInfoService;

    @Autowired
    private MarFbaCartonSkusService marFbaCartonSkusService;

    @Autowired
    private MarFbaCartonSpecService marFbaCartonSpecService;

    @Autowired
    private MarFbaAddressService marFbaAddressService;


    @Value("${task.center.file.path}")
    private String filePath;


    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private TaskCenterService taskCenterService;

    @Autowired
    private MarFbaPalletDetailService marFbaPalletDetailService;


    /**
     * @param
     * @param query
     * @param contextId
     * @param
     * @description: 查询商品列表
     * @author: Moore
     * @date: 2025/7/28 11:33
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarStaProductSkuVO>
     **/
    @Override
    public List<MarStaProductSkuVO> selectProductList(MarStaProductQueryDTO query, Integer contextId) {
        query.setOrgId(contextId.longValue());
        if (query.getShopId() == null) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectProductList(query);
    }


    /**
     * @param
     * @param contextId
     * @param erpSKu    产品ID
     * @description: 查询箱规下拉信息
     * @author: Moore
     * @date: 2025/7/28 11:33
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarStaProductSkuVO>
     **/
    @Override
    public List<MarStaProductSkuVO> selectCartonSpecList(Integer contextId, String erpSKu) {
        if (contextId == null || StringUtil.isEmpty(erpSKu)) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectProductCartonSpecList(contextId, erpSKu);
    }


    /**
     * STA任务列表
     *
     * @param query
     * @return
     */
    @Override
    public List<MarStaTaskInfoVO> selectStaTaskList(MarStaTaskListQueryDTO query) {

        List<MarStaTaskInfoVO> marStaTaskInfoVOS = this.baseMapper.selectStaTaskList(query);
        if (!CollectionUtils.isEmpty(marStaTaskInfoVOS)) {
            //查询FBA信息
            List<Long> staIds = marStaTaskInfoVOS.stream().map(MarStaTaskInfoVO::getId).distinct().collect(Collectors.toList());
            List<MarFbaShipmentInfo> marFbaShipmentInfoList = marFbaShipmentInfoService.lambdaQuery().in(MarFbaShipmentInfo::getTaskId, staIds).list();
            if (!CollectionUtils.isEmpty(marFbaShipmentInfoList)) {
                Map<Long, List<MarFbaShipmentInfo>> taskIdMap = marFbaShipmentInfoList.stream().collect(Collectors.groupingBy(MarFbaShipmentInfo::getTaskId));
                for (MarStaTaskInfoVO marStaTaskInfoVO : marStaTaskInfoVOS) {
                    List<MarFbaShipmentInfo> marFbaShipmentInfoListInfo = taskIdMap.get(marStaTaskInfoVO.getId());
                    if (!CollectionUtils.isEmpty(marFbaShipmentInfoListInfo)) {
                        int applyNum = marFbaShipmentInfoListInfo.stream().filter(item -> item.getApplyNum() != null).mapToInt(MarFbaShipmentInfo::getApplyNum).sum();
                        marStaTaskInfoVO.setApplyNum(applyNum);
                        marStaTaskInfoVO.setFbaInfoList(this.copyList(marFbaShipmentInfoListInfo, MarStaTaskInfoVO.FbaInfo::new));
                    }
                }
            }
        }
        return marStaTaskInfoVOS;
    }


    /**
     * @param
     * @param taskId
     * @description: 查询发货商品节点信息
     * @author: Moore
     * @date: 2025/7/24 11:31
     * @return: com.bizark.op.api.entity.op.amazon.fba.DTO.MarStaTaskPackInfoDTO
     **/
    @Override
    public MarStaTaskShipmentInfoDTO selectStaTaskListByShipmentSku(Integer taskId) {
        MarStaTaskShipmentInfoDTO marStaTaskShipmentInfoDTO = new MarStaTaskShipmentInfoDTO();
        MarStaTaskInfo marStaTaskInfo = this.getById(taskId);
        if (marStaTaskInfo == null) {
            throw new CustomException("任务信息获取失败!");
        }
        BeanUtils.copyProperties(marStaTaskInfo, marStaTaskShipmentInfoDTO);

        //先取存储，再取默认
        MarStaShipmentAddress dbShippingAddress = marStaShipmentAddressService.lambdaQuery().eq(MarStaShipmentAddress::getTaskId, taskId).one();
        if (dbShippingAddress != null) {
            marStaTaskShipmentInfoDTO.setAddressStr(String.join(",",
                    (StringUtil.isEmpty(PlatformCountryEnum.getZhNameByCode(dbShippingAddress.getShipmentCountry())) ? dbShippingAddress.getShipmentCountry() : PlatformCountryEnum.getZhNameByCode(dbShippingAddress.getShipmentCountry())), //发货国家地区
                    dbShippingAddress.getStreetAddress1(), // 街道地址1
                    StringUtil.isEmpty(dbShippingAddress.getStreetAddress2()) ? "" : dbShippingAddress.getStreetAddress2(), //街道地址2
                    dbShippingAddress.getCity(), //城市
                    dbShippingAddress.getStateProvince(),// 州/省/地区
                    dbShippingAddress.getPostalCode(),//邮编
                    dbShippingAddress.getShipmentCountry(), //发货国家或地区
                    dbShippingAddress.getPhone()));
        }
        //明细信息
        List<MarStaShipmentSkus> list = marStaShipmentSkusService.lambdaQuery().eq(MarStaShipmentSkus::getTaskId, taskId).list();
        List<MarStaShipmentSkusDTO> marStaShipmentSkusDTOS = this.copyList(list, MarStaShipmentSkusDTO::new);
        marStaTaskShipmentInfoDTO.setShipmentSkusList(marStaShipmentSkusDTOS);
        return marStaTaskShipmentInfoDTO;
    }


    /**
     * @param
     * @param saveDTO
     * @description: 保存暂存STA信息
     * @author: Moore
     * @date: 2025/7/24 11:39
     * @return: void
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MarStaTaskInfo saveStaTaskList(MarStaTaskShipmentInfoDTO saveDTO) {

        //操作主表
        MarStaTaskInfo marStaTaskInfoUpdate = new MarStaTaskInfo();
        marStaTaskInfoUpdate.setShopId(saveDTO.getShopId());
        marStaTaskInfoUpdate.setTaskName(saveDTO.getTaskName());
        marStaTaskInfoUpdate.setDistributionMode(saveDTO.getDistributionMode());
        marStaTaskInfoUpdate.setId(saveDTO.getId());
        if (saveDTO.getId() == null) {
            marStaTaskInfoUpdate.setTaskNode(StaTaskNodeEnum.SELECT_GOODS.getNode()); //初始节点
            marStaTaskInfoUpdate.setOrgId(saveDTO.getOrgId());
            marStaTaskInfoUpdate.settingDefaultCreate();
            marStaTaskInfoUpdate.setRemark(saveDTO.getRemark()); //备注信息
            marStaTaskInfoUpdate.setStaCreatedAt(marStaTaskInfoUpdate.getCreatedAt()); //首次STA创建时间等于当前时间
            this.save(marStaTaskInfoUpdate);
        } else {
            marStaTaskInfoUpdate.setRemark(StringUtil.isEmpty(saveDTO.getRemark()) ? "" : saveDTO.getRemark()); //备注信息
            marStaTaskInfoUpdate.settingDefaultSystemUpdate();
            this.updateById(marStaTaskInfoUpdate);
        }

        //地址更新
        if (saveDTO.getAddressId() != null) {
            MarPlatformWarehouseAddress platformWarehouseAddress = warehouseAddressService.getById(saveDTO.getAddressId());
            if (platformWarehouseAddress != null) {
                MarStaShipmentAddress addressSave = new MarStaShipmentAddress();
                addressSave.setStreetAddress1(platformWarehouseAddress.getStreetAddress1());// 街道地址1
                addressSave.setStreetAddress2(platformWarehouseAddress.getStreetAddress2()); //街道地址2(业务，非必填)
                addressSave.setCity(platformWarehouseAddress.getCity());//城市
                addressSave.setCompanyName(platformWarehouseAddress.getCompanyName()); //公司名称（业务，非必填）
                addressSave.setShipmentCountry(platformWarehouseAddress.getShipmentCountry()); //发货国家
                addressSave.setEmail(platformWarehouseAddress.getEmail()); //邮箱 （非必填）
                addressSave.setSenderName(platformWarehouseAddress.getSenderName()); //发货人
                addressSave.setPhone(platformWarehouseAddress.getPhoneNumber());//手机号
                addressSave.setPostalCode(platformWarehouseAddress.getPostalCode());//邮编
                addressSave.setStateProvince(platformWarehouseAddress.getStateProvince());// 州/省/地区
                MarStaShipmentAddress marStaShipmentAddress = marStaShipmentAddressService.lambdaQuery().eq(MarStaShipmentAddress::getTaskId, marStaTaskInfoUpdate.getId()).one();
                if (marStaShipmentAddress == null) {
                    addressSave.setOrgId(saveDTO.getOrgId());
                    addressSave.setTaskId(marStaTaskInfoUpdate.getId());
                    addressSave.settingDefaultSystemCreate();
                    marStaShipmentAddressService.save(addressSave);
                } else {
                    addressSave.setId(marStaShipmentAddress.getId());
                    addressSave.settingDefaultSystemUpdate();
                    marStaShipmentAddressService.updateById(addressSave);
                }
            }
        }

        //操作明细表
        List<MarStaShipmentSkusDTO> shipmentSkusList = saveDTO.getShipmentSkusList();
        List<MarStaShipmentSkus> ts = this.copyList(shipmentSkusList, MarStaShipmentSkus::new);

        ts.stream().forEach(item -> {
                    if (item.getId() == null) {
                        item.setOrgId(saveDTO.getOrgId());
                        item.setTaskId(marStaTaskInfoUpdate.getId());
                        item.settingDefaultCreate();
                    } else {
                        item.settingDefaultUpdate();
                    }
                }
        );

        //回写STA主信息申报量
        Integer sumApplyNum = ts.stream().filter(item -> item.getApplyNum() != null).mapToInt(MarStaShipmentSkus::getApplyNum).sum();
        marStaTaskInfoUpdate.setApplyNum(sumApplyNum);
        this.updateById(marStaTaskInfoUpdate);
        //更新或保存 SKU明细信息
        marStaShipmentSkusService.saveOrUpdateBatch(ts);
        return marStaTaskInfoUpdate;
    }


    /**
     * @param
     * @param query
     * @description: 提交STA任务
     * @author: Moore
     * @date: 2025/7/28 15:55
     * @return: void
     **/
    @Override
    public InboundOperationStatusResponse subStaTask(MarStaTaskShipmentInfoDTO query) {
        List<MarStaShipmentSkusDTO> shipmentSkusList = query.getShipmentSkusList();
        if (CollectionUtils.isEmpty(shipmentSkusList)) {
            throw new CustomException("商品信息为空，不可操作创建!");
        }
        // 提交STA 校验店铺是否授权
        Account account = accountService.getById(query.getShopId());
        if (account == null || !"runing".equals(account.getStatus())) {
            throw new CustomException("店铺未授权，不可操作创建!");
        }
        // 步骤一 暂存货件 （异常不回滚）
        MarStaTaskInfo marStaTaskInfo = marStaTaskInfoProxyService.saveStaTaskList(query);

        //步骤二 创建STA
        query.setId(marStaTaskInfo.getId());
        CreateInboundPlanResponse inboundPlan = this.createInboundPlan(query, account);
        if (inboundPlan == null || StringUtils.isEmpty(inboundPlan.getInboundPlanId())) {
            throw new CustomException("获取货件任务ID为空!");
        }


        InboundOperationStatusResponse inboundOperationStatus = this.getInboundOperationStatus(inboundPlan.getOperationId(), query.getShopId());
        if ("SUCCESS".equals(inboundOperationStatus.getOperationStatus())) {
            marStaTaskInfo.setOperationId(inboundPlan.getOperationId()); //回写操作ID
            marStaTaskInfo.setInboundPlanId(inboundPlan.getInboundPlanId()); //回写任务编号
            marStaTaskInfo.settingDefaultUpdate();
            this.updateById(marStaTaskInfo);

            //对所有明细行更新
            marStaShipmentSkusService.lambdaUpdate().eq(MarStaShipmentSkus::getTaskId, marStaTaskInfo.getId())
                    .set(MarStaShipmentSkus::getInboundPlanId, inboundPlan.getInboundPlanId()).update();
            //异步刷新原始状态信息
            MarStaTaskInfoServiceImpl marStaTaskInfoService = applicationContext.getBean(MarStaTaskInfoServiceImpl.class);
            marStaTaskInfoService.refershWfsIboundStatus(inboundPlan.getInboundPlanId(), query.getShopId(), marStaTaskInfo.getId());
        } else if ("FAILED".equals(inboundOperationStatus.getOperationStatus())) { //对创建失败的货件信息，进行取消操作
            log.info("第一步货件创建失败：{},创建结果：{}",JSONObject.toJSONString(inboundOperationStatus),JSONObject.toJSONString(inboundPlan));
            AmazonApiResult<String> cancelResult = amazonRequestUtils.sendPutQuery(
                    AmazonApiURLEnum.CANCEL_INBOUNDPLAN.getPath().replace("{inboundPlanId}", inboundPlan.getInboundPlanId()),
                    marStaTaskInfo.getShopId(), null);
            if (cancelResult == null) {
                log.info("创建失败的STA任务，取消失败：{}", marStaTaskInfo.getInboundPlanId());
            } else if (!cancelResult.isSuccess()) {
                log.info("创建失败的STA任务，取消失败:{},货件单号:{}", cancelResult.getMessage(), marStaTaskInfo.getInboundPlanId());
            } else {
                log.info("创建失败的STA任务，取消成功：{}", JSONObject.toJSONString(marStaTaskInfo.getInboundPlanId()));
            }
        }
        inboundOperationStatus.setTaskId(marStaTaskInfo.getId()); //用于前端流转任务ID
        return inboundOperationStatus;
    }


    /**
     * @param
     * @param query
     * @description:预处理方式校验
     * @author: Moore
     * @date: 2025/8/5 15:42
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarFbaVerifyPrepDetailsVO>
     **/
    @Override
    public List<MarFbaVerifyPrepDetailsVO> pretreatmentMethodVerify(MarStaTaskShipmentInfoDTO query) {
        if (query == null || CollectionUtils.isEmpty(query.getShipmentSkusList()) || query.getShopId() == null) {
            return Collections.emptyList();
        }
        //获取所有SellerSKu行信息
        String selleSkus = query.getShipmentSkusList().stream().map(MarStaShipmentSkusDTO::getSellerSku).distinct().collect(Collectors.joining(","));
        if (StringUtils.isEmpty(selleSkus)) {
            return Collections.emptyList();
        }
        //调用AMZ获取预处理方式
        PrepDetails prepDetails = getPrepDetails(query.getShopId(), selleSkus);
        return this.verifyPrepDetails(prepDetails.getMskuPrepDetails(), query.getShipmentSkusList());
    }


    /**
     * @param
     * @param contextId
     * @param taskId    任务主键
     * @description: 获取包装选项
     * @author: Moore
     * @date: 2025/8/5 18:28
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarStaInboundSelectPackVO>
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InboundOperationStatusResponse confirmPackingOption(Integer contextId, Long taskId) {

        //防止重复确认包装方案
        MarStaTaskInfo marStaTaskInfo = this.getById(taskId);
        if (marStaTaskInfo == null || StringUtils.isEmpty(marStaTaskInfo.getInboundPlanId())) {
            throw new CustomException("STA信息获取失败!");
        }
        if (!marStaTaskInfo.getTaskNode().equals(StaTaskNodeEnum.SELECT_GOODS.getNode())) {
            throw new CustomException("非选择商品节点不可操作!");
        }
        String inboundPlanId = marStaTaskInfo.getInboundPlanId();
        //1.根据STA ID 获取包装组方案
        AmazonApiResult<String> amazonApiResult = amazonRequestUtils.sendGetQuery(AmazonApiURLEnum.LIST_PACKING_OPTIONS.getPath().replace("{inboundPlanId}", inboundPlanId), null, marStaTaskInfo.getShopId());
        if (amazonApiResult == null) {
            throw new CustomException("获取包装方案失败!");
        }
        if (!amazonApiResult.isSuccess()) {
            throw new CustomException(amazonApiResult.getMessage());
        }
        ListPackingOptionsResonse listPackingOptionsResonse = JSONObject.parseObject(amazonApiResult.getData(), ListPackingOptionsResonse.class);

        List<ListPackingOptionsResonse.PackingOptionsBean> packingOptions = listPackingOptionsResonse.getPackingOptions();
        if (CollectionUtils.isEmpty(packingOptions)) {
            throw new CustomException("获取包装方案为空!");
        }

        //2.获取包装组信息
        ListPackingOptionsResonse.PackingOptionsBean packingOptionsBean = packingOptions.get(0);
        List<String> packingGroups = packingOptionsBean.getPackingGroups(); //包装组编号
        if (CollectionUtils.isEmpty(packingGroups)) {
            throw new CustomException("获取包装方案组信息为空!");
        }

        //3.获取包装组下SelelrSKu明细信息，并保存至包装组表信息
        AtomicInteger counter = new AtomicInteger(0);
        List<MarStaPackGroupInfo> groupInfoList = packingGroups.stream().map(item -> {
            MarStaPackGroupInfo marStaPackGroupInfo = new MarStaPackGroupInfo();
            marStaPackGroupInfo.setOrgId(contextId.longValue()); //组织ID
            marStaPackGroupInfo.setPackGroup(counter.incrementAndGet()); //组别
            marStaPackGroupInfo.setInboundPlanId(inboundPlanId); //货件编号
            marStaPackGroupInfo.setPackingGroupId(item); //组编号
            marStaPackGroupInfo.setPackingOptionId(packingOptionsBean.getPackingOptionId()); //操作ID
            marStaPackGroupInfo.setPackMethod(StaTaskPackGroupTypeEnum.ONE_ITEM_PER_BOX.getType()); //装箱方式每箱一款      //TODO 装箱方式在暂只有一款，后续增加需去除默认设置
            marStaPackGroupInfo.settingDefaultCreate();
            return marStaPackGroupInfo;
        }).collect(Collectors.toList());
        marStaPackGroupInfoService.saveBatch(groupInfoList);

        //查询选择商品中的SKU信息
        List<MarStaShipmentSkus> marStaShipmentSkuses = marStaShipmentSkusService.lambdaQuery().eq(MarStaShipmentSkus::getTaskId, taskId).list();
        Map<String, MarStaShipmentSkus> marStaShipmentSkusMap = marStaShipmentSkuses.stream().collect(Collectors.toMap(MarStaShipmentSkus::getSellerSku, Function.identity(), (a, b) -> a));

        //获取各组下具体SelelrSKu信息
        for (String packingGroupId : packingGroups) {
            AmazonApiResult<String> packResult = amazonRequestUtils.sendGetQuery(AmazonApiURLEnum.LIST_PACKING_GROUP_ITEMS.getPath().replace("{inboundPlanId}", inboundPlanId).replace("{packingGroupId}", packingGroupId), null, marStaTaskInfo.getShopId());
            if (packResult == null) {
                throw new CustomException("获取包装详情信息失败!");
            }
            if (!packResult.isSuccess()) {
                throw new CustomException(packResult.getMessage());
            }
            //单个包装组下SKU信息
            ListPackingGroupItems listPackingGroupItems = JSONObject.parseObject(packResult.getData(), ListPackingGroupItems.class);
            for (ListPackingGroupItems.ItemsBean item : listPackingGroupItems.getItems()) {

                MarStaShipmentSkus marStaShipmentSkusDb = marStaShipmentSkusMap.get(item.getMsku());
                //sellerSKU信息保存
                MarStaPackingSkus marStaPackingSkusSave = new MarStaPackingSkus();
                BeanUtils.copyProperties(marStaShipmentSkusDb, marStaPackingSkusSave);
                marStaPackingSkusSave.setId(null);
                marStaPackingSkusSave.setPackingGroupId(packingGroupId); //设置包装组ID
                marStaPackingSkusSave.setBoxedQuantity(marStaShipmentSkusDb.getApplyNum()); //（已装箱）首次保存，申请数量=已装箱数量
                marStaPackingSkusSave.settingDefaultCreate();
                marStaPackingSkusService.save(marStaPackingSkusSave);

                //sellerSKu对应箱规信息保存
                MarStaCartonSpec marStaCartonSpec = new MarStaCartonSpec();
                marStaCartonSpec.setOrgId(marStaPackingSkusSave.getOrgId());
                marStaCartonSpec.setSkuId(marStaPackingSkusSave.getId()); //维护对应SellerSKu ID
                marStaCartonSpec.setManufacturer(marStaShipmentSkusDb.getManufacturer()); //生产商
                marStaCartonSpec.setBoxLengthCm(marStaShipmentSkusDb.getBoxLengthCm());//长
                marStaCartonSpec.setBoxWidthCm(marStaShipmentSkusDb.getBoxWidthCm());//宽
                marStaCartonSpec.setBoxHeightCm(marStaShipmentSkusDb.getBoxHeightCm());//高
                marStaCartonSpec.setBoxQuantity(marStaShipmentSkusDb.getBoxQuantity());//箱数
                marStaCartonSpec.setUnitsPerBox(marStaShipmentSkusDb.getUnitsPerBox());//每箱个数
                marStaCartonSpec.setBoxWeightKg(marStaShipmentSkusDb.getBoxWeightKg()); //毛重
                marStaCartonSpec.settingDefaultCreate();
                marStaCartonSpecService.save(marStaCartonSpec);
            }
        }

        //4.调用 确认包装方案
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPostQuery(
                AmazonApiURLEnum.CONFIRM_PACKING_OPTION.getPath().replace("{inboundPlanId}", inboundPlanId).replace("{packingOptionId}", packingOptionsBean.getPackingOptionId()),
                marStaTaskInfo.getShopId(), null);

        //5.确认失败， 取消货件
        if (confrmResult == null || !confrmResult.isSuccess()) {
            AmazonApiResult<String> cancelResult = amazonRequestUtils.sendPutQuery(
                    AmazonApiURLEnum.CANCEL_INBOUNDPLAN.getPath().replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId()),
                    marStaTaskInfo.getShopId(), null);
            if (cancelResult == null) {
                throw new CustomException("取消FBA失败，请稍后重试!");
            }
            if (!cancelResult.isSuccess()) {
                throw new CustomException(cancelResult.getMessage());
            }

            //删除 主表及 关联选择商品表的 货件ID
            this.lambdaUpdate()
                    .eq(MarStaTaskInfo::getId, taskId)
                    .set(MarStaTaskInfo::getInboundPlanId, null)
                    .set(MarStaTaskInfo::getTaskStatusApi, null)
                    .update();

            marStaShipmentSkusService.lambdaUpdate().eq(MarStaShipmentSkus::getTaskId, taskId)
                    .set(MarStaShipmentSkus::getInboundPlanId, null).update();


            //刷新货件原始状态
            MarStaTaskInfoServiceImpl marStaTaskInfoService = applicationContext.getBean(MarStaTaskInfoServiceImpl.class);
            marStaTaskInfoService.refershWfsIboundStatus(marStaTaskInfo.getInboundPlanId(), marStaTaskInfo.getShopId(), marStaTaskInfo.getId());
        }


        if (confrmResult == null) {
            throw new CustomException("获取包装详情信息失败!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }

        //节点更新为第二步
        if (StaTaskNodeEnum.SELECT_GOODS.getNode().equals(marStaTaskInfo.getTaskNode())) {
            marStaTaskInfo.setTaskNode(StaTaskNodeEnum.GOODS_PACK.getNode()); //节点2
        }
        marStaTaskInfo.setTaskStatus(StaTaskStatusEnum.IN_PROGRESS.getStatus()); //业务状态 -进行中
        marStaTaskInfo.settingDefaultUpdate();
        this.updateById(marStaTaskInfo);

        //刷新货件原始STA状态
        MarStaTaskInfoServiceImpl marStaTaskInfoService = applicationContext.getBean(MarStaTaskInfoServiceImpl.class);
        marStaTaskInfoService.refershWfsIboundStatus(marStaTaskInfo.getInboundPlanId(), marStaTaskInfo.getShopId(), marStaTaskInfo.getId());

        //复用resonse，取操作ID
        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(confrmResult.getData(), CreateInboundPlanResponse.class);
        //查询操作状态二次校验是否确认成功
        return this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), marStaTaskInfo.getShopId());
    }


    /**
     * @param
     * @param contextId 组织ID
     * @param taskId    任务主键
     * @description:装箱节点，包装组信息
     * @author: Moore
     * @date: 2025/8/6 19:45
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarStaPackGroupVO>
     **/
    @Override
    public List<MarStaPackGroupVO> staPickingGroupNodeSkus(Integer contextId, Long taskId) {

        MarStaTaskInfo marStaTaskInfo = this.getById(taskId);
        if (marStaTaskInfo == null) {
            throw new CustomException("Sta任务获取失败!");
        }
        String inboundPlanId = marStaTaskInfo.getInboundPlanId();

        //查询组信息
        List<MarStaPackGroupInfo> groupInfoList = marStaPackGroupInfoService.lambdaQuery().eq(MarStaPackGroupInfo::getInboundPlanId, inboundPlanId).list();
        if (CollectionUtils.isEmpty(groupInfoList)) {
            throw new CustomException("未获取到包装组信息!");
        }

        //查询组下SellerSKu明细
        List<MarStaPackingSkuDO> marStaPackingSkuVOS = marStaPackingSkusService.selectSkusByCartonSpec(inboundPlanId);
        if (CollectionUtils.isEmpty(marStaPackingSkuVOS)) {
            throw new CustomException("包装组商品信息获取失败!");
        }

        //sellerSku数据根据组编号分组
        Map<String, List<MarStaPackingSkuDO>> packingSkuGroup = marStaPackingSkuVOS.stream().collect(Collectors.groupingBy(MarStaPackingSkuDO::getPackingGroupId));


        //遍历组信息
        List<MarStaPackGroupVO> marStaPackGroupVOList = groupInfoList.stream().map(item -> {
            //设置组信息
            MarStaPackGroupVO marStaPackGroupVO = new MarStaPackGroupVO();
            marStaPackGroupVO.setPackGroup(item.getPackGroup()); // 组别
            marStaPackGroupVO.setPackMethod(item.getPackMethod());//包装方式 1.每箱一款SKU
            marStaPackGroupVO.setInboundPlanId(item.getInboundPlanId()); //任务ID
            marStaPackGroupVO.setPackingGroupId(item.getPackingGroupId());//包装组ID

            //获取SKU信息
            List<MarStaPackingSkuDO> itemSellerSKuList = packingSkuGroup.get(item.getPackingGroupId());

            //有效期标识（一个有效即有效）
            marStaPackGroupVO.setIndateFlag(itemSellerSKuList.stream().anyMatch(itemSellerSKu -> itemSellerSKu.getExpireTime() != null) ? 1 : 0); //含有效期

            //装箱标识
//            boolean boxedflag = itemSellerSKuList.stream().allMatch(sellerskuInfo -> sellerskuInfo.getMarStaCartonSpecList().stream()
//                    .mapToInt(specVO -> specVO.getUnitsPerBox() * specVO.getBoxQuantity())
//                    .sum() == sellerskuInfo.getApplyNum()
//            );
            //装箱数与申报数一致
            boolean boxedflag = itemSellerSKuList.stream().allMatch(itemSku -> itemSku.getApplyNum() == (itemSku.getBoxedQuantity() == null ? 0 : itemSku.getBoxedQuantity()));
            //必填均填写
            boolean boxedflagSpec = itemSellerSKuList.stream().allMatch(sellerskuInfo -> sellerskuInfo.getMarStaCartonSpecList().stream()
                    .allMatch(spec -> spec.getBoxQuantity() != null && spec.getBoxQuantity() > 0 // 箱数
                            && spec.getBoxWeightKg() != null && spec.getBoxWeightKg().compareTo(BigDecimal.ZERO) > 0 //单箱毛重
                            && spec.getBoxLengthCm() != null && spec.getBoxLengthCm().compareTo(BigDecimal.ZERO) > 0 // 长
                            && spec.getBoxWidthCm() != null && spec.getBoxWidthCm().compareTo(BigDecimal.ZERO) > 0// 宽
                            && spec.getBoxHeightCm() != null && spec.getBoxHeightCm().compareTo(BigDecimal.ZERO) > 0// 高
                            && spec.getUnitsPerBox() != null && spec.getUnitsPerBox() > 0) // 每箱数量
            );
            marStaPackGroupVO.setBoxedFlag(boxedflag && boxedflagSpec ? 1 : 0);

            List<MarStaPackingSkuVO> sellerSkuLsit = new ArrayList<>();

            //sellersku明细行信息
            for (MarStaPackingSkuDO marStaPackingSkuDO : itemSellerSKuList) {
                for (MarStaCartonSpecDO spectInfo : marStaPackingSkuDO.getMarStaCartonSpecList()) {

                    MarStaPackingSkuVO marStaPackingSkuVO = new MarStaPackingSkuVO();
                    BeanUtil.copyProperties(marStaPackingSkuDO, marStaPackingSkuVO); //copy Sellersku相关信息
                    //箱规信息
                    marStaPackingSkuVO.setSpecId(spectInfo.getId()); //箱规主键
                    marStaPackingSkuVO.setOrgId(contextId.longValue());
                    marStaPackingSkuVO.setSkuId(spectInfo.getId());
                    marStaPackingSkuVO.setManufacturer(spectInfo.getManufacturer()); //箱规名称
                    marStaPackingSkuVO.setBoxLengthCm(spectInfo.getBoxLengthCm()); //长
                    marStaPackingSkuVO.setBoxWidthCm(spectInfo.getBoxWidthCm()); //宽
                    marStaPackingSkuVO.setBoxHeightCm(spectInfo.getBoxHeightCm());//高
                    marStaPackingSkuVO.setBoxQuantity(spectInfo.getBoxQuantity());//箱数
                    marStaPackingSkuVO.setUnitsPerBox(spectInfo.getUnitsPerBox());//每箱数量
                    marStaPackingSkuVO.setBoxWeightKg(spectInfo.getBoxWeightKg());//单箱毛重
                    sellerSkuLsit.add(marStaPackingSkuVO);
                }
            }
            marStaPackGroupVO.setPackingSkuVOList(sellerSkuLsit); //包装组明细信息 SellerSku
            return marStaPackGroupVO;
        }).collect(Collectors.toList());


        return marStaPackGroupVOList;
    }


    /**
     * @param
     * @param contextId
     * @param marStaPackGroupVO 平级
     * @param inboundPlanId     任务编号
     * @param packingGroupId    组编号
     * @param packMethod        装箱方式 1.每箱一款SKU
     * @description: 保存商品装箱信息，《单包装箱》信息
     * @author: Moore
     * @date: 2025/8/11 12:38
     * @return: void
     **/
    @Override
    @Transactional
    public void staPickingNodeSkusSave(Integer contextId, MarStaPackGroupVO marStaPackGroupVO, String inboundPlanId, String packingGroupId,
                                       String packMethod) {
        if (marStaPackGroupVO == null || CollectionUtils.isEmpty(marStaPackGroupVO.getPackingSkuVOList())) {
            throw new CustomException("未获取到包装组信息!");
        }
        MarStaTaskInfo marStaTaskInfo = this.lambdaQuery().eq(MarStaTaskInfo::getInboundPlanId, inboundPlanId).one();
        if (marStaTaskInfo == null) {
            throw new CustomException("STA任务获取失败!");
        }
        if (!StaTaskNodeEnum.GOODS_PACK.getNode().equals(marStaTaskInfo.getTaskNode())) {
            throw new CustomException("非商品装箱节点不可保存!");
        }


        //单包装组信息
        List<MarStaPackingSkuVO> marStaPackingSkuVO = marStaPackGroupVO.getPackingSkuVOList();

        AuthUserDetails authUserDetails = null;
        try {
            authUserDetails = AuthContextHolder.getAuthUserDetails();
        } catch (Exception e) {
            e.printStackTrace();
        }
        //此处为 mar_sta_packing_skus 主键ID
        if (marStaPackingSkuVO.stream().anyMatch(item -> item.getId() == null)) {
            throw new CustomException("装箱信息获取失败!");
        }

        if (StringUtil.isNotEmpty(packMethod)) {
            marStaPackGroupInfoService.lambdaUpdate().set(MarStaPackGroupInfo::getPackMethod, packMethod).eq(MarStaPackGroupInfo::getPackingGroupId, packingGroupId).update();
        }
        //更新包装组包装方案


        //根据SellerSku分组，单个箱组下SellerSku唯一
        Map<String, List<MarStaPackingSkuVO>> sellerSkuMap = marStaPackingSkuVO.stream().collect(Collectors.groupingBy(MarStaPackingSkuVO::getSellerSku));


        //获取箱组下所有箱规信息
        Map<Long, List<MarStaCartonSpec>> marStrCartonSpecMap = marStaCartonSpecService.lambdaQuery().in(MarStaCartonSpec::getSkuId, marStaPackingSkuVO.stream().map(MarStaPackingSkuVO::getId).distinct().collect(Collectors.toList())).list().stream().collect(Collectors.groupingBy(MarStaCartonSpec::getSkuId));


        //遍历所有SellerSKu行信息
        for (String sellerSku : sellerSkuMap.keySet()) {

            List<MarStaPackingSkuVO> marStaPackingSkuVOS = sellerSkuMap.get(sellerSku); //所有行对应的箱规信息信息

            //sellersku下数据均一致，随机取一个
            MarStaPackingSkuVO marStaPackingSkuVOMain = marStaPackingSkuVOS.get(0);


            //回写已申请数量（单箱数量 * 箱数）
            Integer boxedQuantity = marStaPackingSkuVOS.stream().mapToInt(specVO -> (specVO.getUnitsPerBox() == null ? 0 : specVO.getUnitsPerBox()) * (specVO.getBoxQuantity() == null ? 0 : specVO.getBoxQuantity()))
                    .sum();

            if (boxedQuantity > marStaPackingSkuVOMain.getApplyNum()) {
                throw new CustomException("装箱量不能大于申请量");
            }

            //更新主行SellerSku信息
//            MarStaPackingSkus marStaPackingSkus = new MarStaPackingSkus();
//            marStaPackingSkus.setId(marStaPackingSkuVOMain.getId());
//            marStaPackingSkus.setLabelVendor(marStaPackingSkuVOMain.getLabelVendor()); //贴标方
//            marStaPackingSkus.setLabelType(marStaPackingSkuVOMain.getLabelType());//贴标方式
//            marStaPackingSkus.setBoxedQuantity(boxedQuantity); //已装箱数量
//            marStaPackingSkusService.updateById(marStaPackingSkus);
            LambdaUpdateChainWrapper<MarStaPackingSkus> staUpdate = marStaPackingSkusService.lambdaUpdate();
            staUpdate.eq(MarStaPackingSkus::getId, marStaPackingSkuVOMain.getId());
            staUpdate.set(MarStaPackingSkus::getLabelVendor, marStaPackingSkuVOMain.getLabelVendor());//贴标方
            staUpdate.set(MarStaPackingSkus::getLabelType, marStaPackingSkuVOMain.getLabelType());//贴标方式
            staUpdate.set(MarStaPackingSkus::getBoxedQuantity, boxedQuantity); //已装箱数量
            staUpdate.set(MarStaPackingSkus::getExpireTime, marStaPackingSkuVOMain.getExpireTime());//有效时间
            staUpdate.update();
            //DB箱规
            List<MarStaCartonSpec> dbSpec = marStrCartonSpecMap.get(marStaPackingSkuVOMain.getId());
            //过滤save中已入库箱规
            List<MarStaPackingSkuVO> marStaCartonSpecVOList = marStaPackingSkuVOS.stream().filter(item -> item.getSpecId() != null).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(marStaCartonSpecVOList)) {
                //获取不存在的箱规信息
                List<Long> removeIds = dbSpec.stream()
                        .filter(dbItem -> marStaCartonSpecVOList.stream()
                                .noneMatch(vo -> Objects.equals(vo.getSpecId(), dbItem.getId()))
                        ).map(MarStaCartonSpec::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(removeIds)) {
                    marStaCartonSpecService.removeByIds(removeIds);  //删除无效箱规
                }
                for (MarStaPackingSkuVO marStaCartonSpecVO : marStaCartonSpecVOList) {
                    UpdateWrapper<MarStaCartonSpec> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("id", marStaCartonSpecVO.getSpecId())
                            .set("manufacturer", marStaCartonSpecVO.getManufacturer()) //箱规名称
                            .set("box_length_cm", marStaCartonSpecVO.getBoxHeightCm())
                            .set("box_width_cm", marStaCartonSpecVO.getBoxWidthCm())
                            .set("box_height_cm", marStaCartonSpecVO.getBoxHeightCm())
                            .set("box_quantity", marStaCartonSpecVO.getBoxQuantity())
                            .set("units_per_box", marStaCartonSpecVO.getUnitsPerBox())
                            .set("box_weight_kg", marStaCartonSpecVO.getBoxWeightKg())
                            .set("updated_by", authUserDetails == null ? -1 : authUserDetails.getId())
                            .set("updated_name", authUserDetails == null ? "system" : authUserDetails.getName())
                            .set("updated_at", DateUtils.getNowDate());
                    marStaCartonSpecService.update(updateWrapper);
                }
            }


            //新增
            List<MarStaPackingSkuVO> marStaCartonSpecVOListSave = marStaPackingSkuVOS.stream().filter(item -> item.getSpecId() == null).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(marStaCartonSpecVOListSave)) {
                List<MarStaCartonSpec> deleteSpec = marStaCartonSpecVOListSave.stream().map(item -> {
                    MarStaCartonSpec marStaCartonSpec = new MarStaCartonSpec();
                    marStaCartonSpec.setOrgId(contextId.longValue());
                    marStaCartonSpec.setSkuId(marStaPackingSkuVOMain.getId());
                    marStaCartonSpec.setManufacturer(item.getManufacturer()); //箱规名称
                    marStaCartonSpec.setBoxLengthCm(item.getBoxLengthCm()); //长
                    marStaCartonSpec.setBoxWidthCm(item.getBoxWidthCm()); //宽
                    marStaCartonSpec.setBoxHeightCm(item.getBoxHeightCm());//高
                    marStaCartonSpec.setBoxQuantity(item.getBoxQuantity());//箱数
                    marStaCartonSpec.setUnitsPerBox(item.getUnitsPerBox());//每箱数量
                    marStaCartonSpec.setBoxWeightKg(item.getBoxWeightKg());//单箱毛重
                    marStaCartonSpec.settingDefaultCreate();
                    return marStaCartonSpec;
                }).collect(Collectors.toList());
                marStaCartonSpecService.saveBatch(deleteSpec);
            }
        }
        //每次操作获取所有申请数量，回写至STA主表
        List<MarStaShipmentSkus> marStaShipmentSkuses = marStaShipmentSkusService.lambdaQuery().eq(MarStaShipmentSkus::getInboundPlanId, inboundPlanId).list();
        int applyAllNum = marStaShipmentSkuses.stream().filter(item -> item.getApplyNum() != null).mapToInt(MarStaShipmentSkus::getApplyNum).sum();
        this.lambdaUpdate().eq(MarStaTaskInfo::getInboundPlanId, inboundPlanId).set(MarStaTaskInfo::getApplyNum, applyAllNum).update();
    }


    /**
     * 调用更装箱组信息(此接口在业务角度可以多次调用)
     *
     * @param contextId
     * @param inboundPlanId
     */
    @Override
    public InboundOperationStatusResponse updateAmzPackInfo(Integer contextId, String inboundPlanId) {

        MarStaTaskInfo marStaTaskInfo = this.lambdaQuery().eq(MarStaTaskInfo::getInboundPlanId, inboundPlanId).one();
        if (marStaTaskInfo == null) {
            throw new CustomException("sta任务获取失败!");
        }
        Long shopId = marStaTaskInfo.getShopId();


        //装箱组信息
        List<MarStaPackGroupInfo> groupInfoList = marStaPackGroupInfoService.lambdaQuery().eq(MarStaPackGroupInfo::getInboundPlanId, inboundPlanId).list();

        //seller list<spec>
        List<MarStaPackingSkuDO> marStaPackingSkuVOS = marStaPackingSkusService.selectSkusByCartonSpec(inboundPlanId);


        //判断是否已装箱
        if (!marStaPackingSkuVOS.stream().allMatch(itemSku -> itemSku.getApplyNum() == (itemSku.getBoxedQuantity() == null ? 0 : itemSku.getBoxedQuantity()))) {
            throw new CustomException("装箱量不等于申报量，请完成装箱后再进行提交");
        }


        //组-> seller ->list<spec>
        Map<String, List<MarStaPackingSkuDO>> groupPackingGroupMap = marStaPackingSkuVOS.stream().collect(Collectors.groupingBy(MarStaPackingSkuDO::getPackingGroupId));


        //请求参数封装
        SetPackingInformationRequest setPackingInformationRequest = new SetPackingInformationRequest();
        List<SetPackingInformationRequest.PackageGroupingsBean> packageGroupings = new ArrayList<>();
        setPackingInformationRequest.setPackageGroupings(packageGroupings); //包装组信息

        for (MarStaPackGroupInfo marStaPackGroupInfo : groupInfoList) {

            //单个包装组信息
            SetPackingInformationRequest.PackageGroupingsBean packageGroupingsBean = new SetPackingInformationRequest.PackageGroupingsBean();
            packageGroupingsBean.setPackingGroupId(marStaPackGroupInfo.getPackingGroupId()); //组编号
            packageGroupingsBean.setShipmentId(null); //货件ID
            //包装组下 不同箱规进行封装
            ArrayList<SetPackingInformationRequest.PackageGroupingsBean.BoxesBean> boxList = new ArrayList<>();
            packageGroupingsBean.setBoxes(boxList);
            packageGroupings.add(packageGroupingsBean);


            //获取组下所有sellersku
            List<MarStaPackingSkuDO> groupSellerSkuItems = groupPackingGroupMap.get(marStaPackGroupInfo.getPackingGroupId());

            if (groupSellerSkuItems.stream().anyMatch(item -> item.getMarStaCartonSpecList().stream().anyMatch(item2 ->
                    item2.getBoxQuantity() == null || item2.getBoxQuantity().equals(new Integer(0)) || //箱数
                            item2.getBoxWeightKg() == null || item2.getBoxWeightKg().compareTo(BigDecimal.ZERO) == 0 || //箱子毛重
                            item2.getBoxLengthCm() == null || item2.getBoxLengthCm().compareTo(BigDecimal.ZERO) == 0 ||//长
                            item2.getBoxWidthCm() == null || item2.getBoxWidthCm().compareTo(BigDecimal.ZERO) == 0 || //宽
                            item2.getBoxHeightCm() == null || item2.getBoxHeightCm().compareTo(BigDecimal.ZERO) == 0 || //高
                            item2.getUnitsPerBox() == null || item2.getUnitsPerBox().equals(new Integer(0)) //单箱数量
            ))) {
                throw new CustomException("箱子的箱数、毛重和尺寸、单箱数量不能为空或为0，请确认!");
            }

            for (MarStaPackingSkuDO groupSellerSkuItem : groupSellerSkuItems) { //SellerSKu
                for (MarStaCartonSpecDO marStaCartonSpecDO : groupSellerSkuItem.getMarStaCartonSpecList()) { //多个箱规信息
                    if (marStaCartonSpecDO.getUnitsPerBox() == null || marStaCartonSpecDO.getBoxQuantity() == null) {
                        throw new CustomException("单箱数量或箱数不能为空!");
                    }

                    //设置每个box信息（一个箱规，一个box）
                    SetPackingInformationRequest.PackageGroupingsBean.BoxesBean boxesBeanItem = new SetPackingInformationRequest.PackageGroupingsBean.BoxesBean();
                    boxList.add(boxesBeanItem);

                    //设置方式
                    boxesBeanItem.setContentInformationSource(ContentInformationSourceEnum.BOX_CONTENT_PROVIDED.getType());

                    //设置包装信息
                    SetPackingInformationRequest.PackageGroupingsBean.BoxesBean.DimensionsBean dimensionsBean = new SetPackingInformationRequest.PackageGroupingsBean.BoxesBean.DimensionsBean();
                    dimensionsBean.setHeight(marStaCartonSpecDO.getBoxHeightCm().intValue());
                    dimensionsBean.setLength(marStaCartonSpecDO.getBoxLengthCm().intValue());
                    dimensionsBean.setWidth(marStaCartonSpecDO.getBoxWidthCm().intValue());
                    dimensionsBean.setUnitOfMeasurement("CM");
                    boxesBeanItem.setDimensions(dimensionsBean);

                    //设置selelrsku信息
                    SetPackingInformationRequest.PackageGroupingsBean.BoxesBean.ItemsBean itemsBean = new SetPackingInformationRequest.PackageGroupingsBean.BoxesBean.ItemsBean();
                    itemsBean.setExpiration(groupSellerSkuItem.getExpireTime() == null ? null : DateUtil.format(groupSellerSkuItem.getExpireTime(), DatePattern.NORM_DATE_PATTERN)); //有效期
                    itemsBean.setLabelOwner(groupSellerSkuItem.getLabelType()); //标签类型
                    itemsBean.setPrepOwner(groupSellerSkuItem.getLabelVendor());//预处理方
                    itemsBean.setManufacturingLotCode(null);
                    itemsBean.setMsku(groupSellerSkuItem.getSellerSku()); //sellerSku
                    itemsBean.setQuantity(marStaCartonSpecDO.getUnitsPerBox()); //单箱个数
                    boxesBeanItem.setItems(Arrays.asList(itemsBean)); //设置SellerSKu信息


                    boxesBeanItem.setQuantity(marStaCartonSpecDO.getBoxQuantity()); //箱数

                    SetPackingInformationRequest.PackageGroupingsBean.BoxesBean.WeightBean weightBean = new SetPackingInformationRequest.PackageGroupingsBean.BoxesBean.WeightBean();
                    weightBean.setUnit("KG");
                    weightBean.setValue(marStaCartonSpecDO.getBoxWeightKg()); //重量
                    boxesBeanItem.setWeight(weightBean);

                }
            }
        }


        //调用 确认包装方案
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPostQuery(
                AmazonApiURLEnum.SET_PACKINGINFORMATION.getPath().replace("{inboundPlanId}", inboundPlanId),
                shopId, setPackingInformationRequest);

        if (confrmResult == null) {
            throw new CustomException("设置包装箱组信息失败!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }
        //复用resonse，取操作ID
        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(confrmResult.getData(), CreateInboundPlanResponse.class);
        log.info("设置包装箱组响应：{}", JSONObject.toJSONString(confrmResult.getData()));
        //查询操作状态二次校验是否确认成功
        return this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), shopId);
    }


    /**
     * @param
     * @param contextId
     * @param taskId
     * @description: 生成预览信息
     * @author: Moore
     * @date: 2025/8/18 9:37
     * @return: void
     **/
    @Override
    public InboundOperationStatusResponse generatePlacementOptions(Integer contextId, Long taskId) {
        MarStaTaskInfo marStaTaskInfo = this.getById(taskId);
        if (marStaTaskInfo == null) {
            throw new CustomException("STA任务获取失败!");
        }

        //生成预览
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPostQuery(
                AmazonApiURLEnum.GENERATE_PLACEMENTOPTIONS.getPath().replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId()),
                marStaTaskInfo.getShopId(), null);

        if (confrmResult == null) {
            throw new CustomException("生成预览失败!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }
        //复用resonse，取操作ID
        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(confrmResult.getData(), CreateInboundPlanResponse.class);
        return this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), marStaTaskInfo.getShopId());
    }


    /**
     * 获取预览结果
     *
     * @param contextId
     * @param taskId
     * @return
     */
    @Override
    public List<MarStaPlacementOptionsVO> listplacementoptions(Integer contextId, Long taskId) {
        MarStaTaskInfo marStaTaskInfo = this.getById(taskId);
        if (marStaTaskInfo == null) {
            throw new CustomException("STA任务获取失败!");
        }
        //所有明细SellerSKu信息
        List<MarStaPackingSkus> packingSkuses = marStaPackingSkusService.lambdaQuery().eq(MarStaPackingSkus::getTaskId, marStaTaskInfo.getId()).list();
        Map<String, MarStaPackingSkus> sellerSkuGroup = packingSkuses.stream().collect(Collectors.toMap(MarStaPackingSkus::getSellerSku, Function.identity(), (a, b) -> a));


        // 获取预览方案，拿到对应shipemnt方案
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendGetQuery(
                AmazonApiURLEnum.LIST_PLACEMENT_OPTIONS.getPath().replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId()),
                null, marStaTaskInfo.getShopId());
        if (confrmResult == null) {
            throw new CustomException("生成预览失败!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }
        ListPlacementOptionsResponse listPlacementOptionsResponse = JSONObject.parseObject(confrmResult.getData(), ListPlacementOptionsResponse.class);


        ArrayList<MarStaPlacementOptionsVO> response = new ArrayList<>();
        //操作所有shipemt
        AtomicInteger counter = new AtomicInteger(0);
        listPlacementOptionsResponse.getPlacementOptions().stream().forEach(item -> {
            //货件组别标识
            Integer groupFlagNum = counter.incrementAndGet();
            //shipmet费用
            List<CompletableFuture<MarStaPlacementOptionsVO>> futures = item.getShipmentIds().stream()
                    .map(shipmentId -> CompletableFuture.supplyAsync(() -> {

                        MarStaPlacementOptionsVO marStaPlacementOptionsVO = new MarStaPlacementOptionsVO();
                        marStaPlacementOptionsVO.setGroupNo(groupFlagNum); //货件组（自定义）
                        marStaPlacementOptionsVO.setFee(CollectionUtils.isEmpty(item.getFees()) ? null : item.getFees().get(0).getValue().getAmount()); //费用
                        marStaPlacementOptionsVO.setCurrency(CollectionUtils.isEmpty(item.getFees()) ? null : item.getFees().get(0).getValue().getCode()); //币种
                        marStaPlacementOptionsVO.setShipmentId(shipmentId);
                        marStaPlacementOptionsVO.setWarehouseArea(null); //TODO 区域暂无
                        marStaPlacementOptionsVO.setPlacementOptionId(item.getPlacementOptionId());    //操作ID，用于确认方案
                        // 1.调用 获取shipment对应多个BOX（箱子）
                        ShipmentInfoResponse shipment = getShipment(marStaTaskInfo.getInboundPlanId(), shipmentId, marStaTaskInfo.getShopId());
                        if (shipment.getDestination() != null) {
                            marStaPlacementOptionsVO.setWarehouseId(shipment.getDestination().getWarehouseId()); //wareHouseId
                            ShipmentInfoResponse.DestinationBean.AddressBean address = shipment.getDestination().getAddress();
                            if (address != null) {
                                BeanUtil.copyProperties(address, marStaPlacementOptionsVO, CopyOptions.create().setIgnoreNullValue(true));  //地址信息设置，null值不覆盖
                            }
                        }


                        //2.调用 获取box中所有明细信息
                        ListShipmentBoxes listShipmentBoxes = getListShipmentBoxes(marStaTaskInfo.getInboundPlanId(), shipmentId, marStaTaskInfo.getShopId());


                        //单一shipmentID下，所有 BOX
                        List<ListShipmentBoxes.BoxesBean.ItemsBean> boxSellerskuAll = listShipmentBoxes.getBoxes().stream()
                                .filter(Objects::nonNull)
                                .map(ListShipmentBoxes.BoxesBean::getItems)
                                .filter(Objects::nonNull)
                                .flatMap(List::stream)
                                .collect(Collectors.toList());

                        log.info("shipmentId:{},BoxItems:{}", shipmentId, JSONObject.toJSONString(boxSellerskuAll));
                        //BOX 下所有SelleSKu
                        Map<String, List<ListShipmentBoxes.BoxesBean.ItemsBean>> amzSellerSkuGroup =
                                boxSellerskuAll.stream().collect(Collectors.groupingBy(ListShipmentBoxes.BoxesBean.ItemsBean::getMsku));

                        //封装返回的明细SellerSKu信息
                        List<MarStaPlacementOptionsVO.GoodsInfo> goodsInfoList = new ArrayList<>();
                        marStaPlacementOptionsVO.setGoodsInfoList(goodsInfoList);

                        //一个SKU一个规格信息模块
                        for (String sellerSku : amzSellerSkuGroup.keySet()) {
                            List<ListShipmentBoxes.BoxesBean.ItemsBean> itemsBeans = amzSellerSkuGroup.get(sellerSku);
                            ListShipmentBoxes.BoxesBean.ItemsBean itemsBeanFlag = itemsBeans.get(0);

                            MarStaPlacementOptionsVO.GoodsInfo goodsInfoitem = new MarStaPlacementOptionsVO.GoodsInfo();
                            MarStaPackingSkus marStaPackingSkus = sellerSkuGroup.get(sellerSku);
                            if (marStaPackingSkus != null) {
                                goodsInfoitem.setImageUrl(marStaPackingSkus.getImageUrl()); //图片
                                goodsInfoitem.setTitle(marStaPackingSkus.getTitle());//标题
                            }
                            goodsInfoitem.setMsku(itemsBeanFlag.getMsku());//mkSKu即SellerSKu
                            goodsInfoitem.setFnSku(itemsBeanFlag.getFnsku());
                            goodsInfoitem.setApplyNum(itemsBeans.stream().mapToInt(ListShipmentBoxes.BoxesBean.ItemsBean::getQuantity).sum());//申请数量
                            goodsInfoList.add(goodsInfoitem);
                        }
                        marStaPlacementOptionsVO.setSkuNum(amzSellerSkuGroup.size()); //SellerSku个数
                        marStaPlacementOptionsVO.setPcsNum(boxSellerskuAll.stream().mapToInt(ListShipmentBoxes.BoxesBean.ItemsBean::getQuantity).sum()); //总申请数量

                        BigDecimal weightFlag = BigDecimal.ZERO;
                        BigDecimal volumeFlag = BigDecimal.ZERO;
                        for (ListShipmentBoxes.BoxesBean box : listShipmentBoxes.getBoxes()) {
                            weightFlag = weightFlag.add(box.getWeight().getValue());
                            ListShipmentBoxes.BoxesBean.DimensionsBean dimensions = box.getDimensions();
                            if (dimensions != null && dimensions.getLength() != null && dimensions.getWidth() != null && dimensions.getHeight() != null) {
                                BigDecimal volumeCm3 = dimensions.getLength().multiply(dimensions.getWidth()).multiply(dimensions.getHeight());
                                volumeFlag = volumeFlag.add(volumeCm3.divide(new BigDecimal("1000000"), 2, RoundingMode.HALF_UP));
                            }
                        }
                        marStaPlacementOptionsVO.setWeight(weightFlag); //重量
                        marStaPlacementOptionsVO.setVolume(volumeFlag); //体积

                        return marStaPlacementOptionsVO;
                    }, threadPoolTaskExecutor))
                    .collect(Collectors.toList());

            // 等待所有任务完成并收集结果
            response.addAll(futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList()));

//        commonPreviewSta(marStaTaskInfo, sellerSkuGroup, response, item, groupFlagNum, feesBean);
        });
        return response;
    }


    /**
     * 同步促销预览
     *
     * @param marStaTaskInfo
     * @param sellerSkuGroup
     * @param response
     * @param item
     * @param groupFlagNum
     * @param feesBean
     */
    private void commonPreviewSta(MarStaTaskInfo marStaTaskInfo, Map<String, MarStaPackingSkus> sellerSkuGroup, ArrayList<MarStaPlacementOptionsVO> response, ListPlacementOptionsResponse.PlacementOptionsBean item, Integer groupFlagNum, ListPlacementOptionsResponse.PlacementOptionsBean.FeesBean feesBean) {
        //获取每个shipmentId 详细信息
        for (String shipmentId : item.getShipmentIds()) {
            MarStaPlacementOptionsVO marStaPlacementOptionsVO = new MarStaPlacementOptionsVO();
            marStaPlacementOptionsVO.setGroupNo(groupFlagNum); //自定义组别
            marStaPlacementOptionsVO.setFee(feesBean.getValue().getAmount());//费用
            marStaPlacementOptionsVO.setShipmentId(shipmentId);
            marStaPlacementOptionsVO.setWarehouseArea(null); //入库区域
            //入库地址等基础信息
            ShipmentInfoResponse shipment = getShipment(marStaTaskInfo.getInboundPlanId(), shipmentId, marStaTaskInfo.getShopId());
            ShipmentInfoResponse.DestinationBean.AddressBean address = shipment.getDestination().getAddress();
            if (address != null) {
                BeanUtil.copyProperties(address, marStaPlacementOptionsVO, CopyOptions.create().setIgnoreNullValue(true));
            }
            ListShipmentBoxes listShipmentBoxes = getListShipmentBoxes(marStaTaskInfo.getInboundPlanId(), shipmentId, marStaTaskInfo.getShopId());

            //shipmentId 所有sellersku
            List<ListShipmentBoxes.BoxesBean.ItemsBean> boxSellerskuAll = listShipmentBoxes.getBoxes().stream()
                    .filter(Objects::nonNull)
                    .map(ListShipmentBoxes.BoxesBean::getItems)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            Map<String, List<ListShipmentBoxes.BoxesBean.ItemsBean>> amzSellerSkuGroup = boxSellerskuAll.stream().collect(Collectors.groupingBy(ListShipmentBoxes.BoxesBean.ItemsBean::getMsku));

            List<MarStaPlacementOptionsVO.GoodsInfo> goodsInfoList = new ArrayList<MarStaPlacementOptionsVO.GoodsInfo>();
            marStaPlacementOptionsVO.setGoodsInfoList(goodsInfoList);
            for (String sellerSku : amzSellerSkuGroup.keySet()) {
                List<ListShipmentBoxes.BoxesBean.ItemsBean> itemsBeans = amzSellerSkuGroup.get(sellerSku);
                ListShipmentBoxes.BoxesBean.ItemsBean itemsBeanFlag = itemsBeans.get(0); //相同SellerSku下 基础信息一致

                //封装预览信息
                MarStaPlacementOptionsVO.GoodsInfo goodsInfoitem = new MarStaPlacementOptionsVO.GoodsInfo();
                MarStaPackingSkus marStaPackingSkus = sellerSkuGroup.get(sellerSku);
                if (marStaPackingSkus != null) {
                    goodsInfoitem.setImageUrl(marStaPackingSkus.getImageUrl());
                    goodsInfoitem.setTitle(marStaPackingSkus.getTitle());
                }
                goodsInfoitem.setMsku(itemsBeanFlag.getMsku());
                goodsInfoitem.setFnSku(itemsBeanFlag.getFnsku());
                goodsInfoitem.setApplyNum(itemsBeans.stream().mapToInt(ListShipmentBoxes.BoxesBean.ItemsBean::getQuantity).sum()); //申报量
                goodsInfoList.add(goodsInfoitem);

            }
            marStaPlacementOptionsVO.setSkuNum(amzSellerSkuGroup.size()); //SellerSku数量
            marStaPlacementOptionsVO.setPcsNum(boxSellerskuAll.stream().mapToInt(ListShipmentBoxes.BoxesBean.ItemsBean::getQuantity).sum()); //总数


            BigDecimal weightFlag = new BigDecimal("0");
            BigDecimal volumeFlag = new BigDecimal("0");
            for (ListShipmentBoxes.BoxesBean box : listShipmentBoxes.getBoxes()) {
                weightFlag = weightFlag.add(box.getWeight().getValue());
                ListShipmentBoxes.BoxesBean.DimensionsBean dimensions = box.getDimensions();

                if (dimensions == null || dimensions.getLength() == null || dimensions.getWidth() == null || dimensions.getHeight() == null) {
                    continue;
                }
                BigDecimal volumeCm3 = dimensions.getLength().multiply(dimensions.getWidth()).multiply(dimensions.getHeight());
                volumeFlag = volumeFlag.add(volumeCm3.divide(new BigDecimal("1000000"), 2, RoundingMode.HALF_UP));  // 保留6位小数，可调
            }
            marStaPlacementOptionsVO.setWeight(weightFlag); //重量
            marStaPlacementOptionsVO.setVolume(volumeFlag); //体积

            response.add(marStaPlacementOptionsVO);
        }
    }


    //获取FBA信息
    @Override
    public ShipmentInfoResponse getShipment(String inboundPlanId, String shipmentId, Long shopId) {
        //同步任务Amz会有延迟
        try {
            Thread.sleep(3000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        AmazonApiResult<String> packResult = amazonRequestUtils.sendGetQuery(AmazonApiURLEnum.GET_SHIPMENT.getPath().replace("{inboundPlanId}", inboundPlanId).replace("{shipmentId}", shipmentId), null, shopId);
        if (packResult == null) {
            throw new CustomException("获取包装详情信息失败!");
        }

        if (!packResult.isSuccess()) {
            String message = packResult.getMessage();
            if (!StringUtil.isEmpty(message) && message.contains("Could not find shipment")) {
                try {
                    Thread.sleep(2000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                log.info("Shipment二次获取：{}", shipmentId);
                packResult = amazonRequestUtils.sendGetQuery(AmazonApiURLEnum.GET_SHIPMENT.getPath().replace("{inboundPlanId}", inboundPlanId).replace("{shipmentId}", shipmentId), null, shopId);
            }
        }
        if (packResult == null) {
            throw new CustomException("获取包装详情信息失败!");
        }
        if (!packResult.isSuccess()) {
            //判断是是否未查询到shipmentID
            throw new CustomException(packResult.getMessage());
        }
        ShipmentInfoResponse shipmentInfoResponse = JSONObject.parseObject(packResult.getData(), ShipmentInfoResponse.class);
        return shipmentInfoResponse;
    }

    /**
     * @param
     * @param contextId
     * @param taskId
     * @description:FBA 步骤三信息
     * @author: Moore
     * @date: 2025/8/24 21:18
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarFbaShipmentNodeVO>
     **/
    @Override
    public List<MarFbaShipmentNodeVO> getFbaNodeThree(Integer contextId, Long taskId) {

        //查询FBA信息
        List<MarFbaShipmentInfo> marFbaShipmentInfoList = marFbaShipmentInfoService.lambdaQuery().eq(MarFbaShipmentInfo::getTaskId, taskId).list();
        if (CollectionUtils.isEmpty(marFbaShipmentInfoList)) {
            throw new CustomException("未获取到FBA信息!");
        }

        List<Long> fbaIds = marFbaShipmentInfoList.stream().map(MarFbaShipmentInfo::getId).collect(Collectors.toList());
        //FBA地址信息
        List<MarFbaAddress> marFbaAddressList = marFbaAddressService.lambdaQuery().in(MarFbaAddress::getFbaId, fbaIds).list();
        Map<Long, List<MarFbaAddress>> addressGroup = marFbaAddressList.stream().collect(Collectors.groupingBy(MarFbaAddress::getFbaId));

        //FBA箱号信息
        List<MarFbaCartonSpec> marFbaCartonSpecList = marFbaCartonSpecService.lambdaQuery().in(MarFbaCartonSpec::getShipmentBusinessId, fbaIds).list();
        Map<Long, List<MarFbaCartonSpec>> marFbaCartonSpecMap = marFbaCartonSpecList.stream().collect(Collectors.groupingBy(MarFbaCartonSpec::getShipmentBusinessId));

        //FBA 箱号对应 明细信息
        List<MarFbaCartonSkus> marFbaCartonSkusList = marFbaCartonSkusService.lambdaQuery().in(MarFbaCartonSkus::getShipmentBusinessId, fbaIds).list();
        Map<Long, List<MarFbaCartonSkus>> marfabSellerSKuGroup = marFbaCartonSkusList.stream().collect(Collectors.groupingBy(MarFbaCartonSkus::getShipmentBusinessId));


        List<MarFbaShipmentNodeVO> res = marFbaShipmentInfoList.stream().map(item -> {
            MarFbaShipmentNodeVO marFbaShipmentNodeVO = new MarFbaShipmentNodeVO();
            marFbaShipmentNodeVO.setId(item.getId()); //FBA信息主键
            marFbaShipmentNodeVO.setOrgId(item.getOrgId());
            marFbaShipmentNodeVO.setShopId(item.getShopId());
            marFbaShipmentNodeVO.setTaskId(item.getTaskId()); //任务主键
            marFbaShipmentNodeVO.setInboundPlanId(item.getInboundPlanId()); //任务编号
            marFbaShipmentNodeVO.setPlacementOptionId(item.getPlacementOptionId()); //选项ID
            marFbaShipmentNodeVO.setShipmentId(item.getShipmentId()); //货件ID
            marFbaShipmentNodeVO.setShipmentName(item.getShipmentName());
            marFbaShipmentNodeVO.setRefereceId(item.getRefereceId());
            marFbaShipmentNodeVO.setShipmentConfirmationId(item.getShipmentConfirmationId()); //FBA编号
            marFbaShipmentNodeVO.setLogisticsCenter(item.getLogisticsCenter());//物流中心编号
            marFbaShipmentNodeVO.setShipmentState(item.getShipmentState()); //状态
            List<MarFbaAddress> addresses = addressGroup.get(item.getId());
            for (MarFbaAddress address : addresses) {
                String joinAddress = String.join(",",
                        address.getName(),
                        address.getAddressLine1(), // 街道地址1
                        StringUtil.isEmpty(address.getAddressLine2()) ? "" : address.getAddressLine2(), //街道地址2
                        address.getCity(), //城市
                        address.getStateProvince(),// 州/省/地区
                        address.getPostalCode(),//邮编
                        address.getCountryCode(), //发货国家或地区
                        StringUtil.isEmpty(address.getPhoneNumber()) ? "" : address.getPhoneNumber());

                if (address.getAddressType().equals(FbaAddressTypeEnum.SHIP_FROM.getType())) {
                    marFbaShipmentNodeVO.setShipFromAddress(joinAddress);
                    marFbaShipmentNodeVO.setShipFrom(address.getName()); //发货方
                }
                if (address.getAddressType().equals(FbaAddressTypeEnum.SHIP_TO.getType())) {
                    marFbaShipmentNodeVO.setShipToAddress(joinAddress);
                }
            }

            List<MarFbaCartonSkus> marFbaSelleSKu = marfabSellerSKuGroup.get(item.getId());
            marFbaShipmentNodeVO.setSellerSkuNumber(marFbaSelleSKu.stream().map(MarFbaCartonSkus::getSellerSku).distinct().collect(Collectors.toList()).size()); //sellerSku 个量
            marFbaShipmentNodeVO.setApplyNum(item.getApplyNum()); //申报量

            List<MarFbaCartonSpec> marFbaCartonSpecs = marFbaCartonSpecMap.get(item.getId());
            marFbaShipmentNodeVO.setBoxCount(marFbaCartonSpecs.stream().map(MarFbaCartonSpec::getBoxId).distinct().collect(Collectors.toList()).size()); //箱数

            //SellerSKu分组
            Map<String, List<MarFbaCartonSkus>> sellerGropu = marFbaSelleSKu.stream().collect(Collectors.groupingBy(MarFbaCartonSkus::getSellerSku));

            List<MarFbaShipmentNodeVO.GoodsInfo> goodsInfoList = new ArrayList<>();
            for (String sellerSKu : sellerGropu.keySet()) {
                List<MarFbaCartonSkus> marFbaCartonSkusListItem = sellerGropu.get(sellerSKu);
                MarFbaShipmentNodeVO.GoodsInfo goodsInfo = new MarFbaShipmentNodeVO.GoodsInfo();
                goodsInfo.setApplyNum(marFbaCartonSkusListItem.stream().filter(item2 -> item2.getApplyNum() != null).mapToInt(MarFbaCartonSkus::getApplyNum).sum());
                goodsInfo.setImageUrl(marFbaCartonSkusListItem.get(0).getImageUrl());
                goodsInfoList.add(goodsInfo);
            }
            marFbaShipmentNodeVO.setGoodsInfos(goodsInfoList); //箱内SellerSKu详细信息

            marFbaShipmentNodeVO.setShippingTime(item.getShippingTime());//发货时间
            marFbaShipmentNodeVO.setCarrier(item.getCarrier()); //承运人
            marFbaShipmentNodeVO.setCarrierStr(MarStaTransportCarrierEnum.getDescriptionByCode(item.getCarrier())); //承运人（转义）
            marFbaShipmentNodeVO.setShippingType(item.getShippingType()); //运输类型  GROUND_SMALL_PARCEL(小包裹快递)  FREIGHT_LTL(汽运零担)


            //汽运零担相关
            marFbaShipmentNodeVO.setFreightLevel(item.getFreightLevel()); //货物等级
            marFbaShipmentNodeVO.setDeclareValue(item.getDeclareValue()); //申报价值
            marFbaShipmentNodeVO.setDeclareCurrency(item.getDeclareCurrency());//申报价值币种
            marFbaShipmentNodeVO.setBolNumber(item.getBolNumber()); //提货单号(BOL)
            marFbaShipmentNodeVO.setProNumber(item.getProNumber()); //跟踪编号(PRO)

            if (MarStaTransportShippingTypeEnum.FREIGHT_LTL.getCode().equals(item.getShippingType())) {

                //汽运零担单独查询
                List<MarFbaPalletDetail> fbaPalletList = marFbaPalletDetailService.lambdaQuery().eq(MarFbaPalletDetail::getFbaId, marFbaShipmentNodeVO.getId()).list();
                if (!CollectionUtils.isEmpty(fbaPalletList)) {
                    //托盘数
                    marFbaShipmentNodeVO.setPalletQuantity(fbaPalletList.stream().filter(row -> row.getPalletQuantity() != null).mapToInt(MarFbaPalletDetail::getPalletQuantity).sum());
                    //总重量
                    marFbaShipmentNodeVO.setTotalWeight(fbaPalletList.stream().filter(row -> row.getTotalWeight() != null).map(MarFbaPalletDetail::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add)); //总重量

                    //总体积FLAG
                    BigDecimal totalVolumeFt3 = BigDecimal.ZERO;
                    //总体积
                    for (MarFbaPalletDetail marFbaPalletDetail : fbaPalletList) {
                        BigDecimal length = marFbaPalletDetail.getLength(); //长
                        BigDecimal width = marFbaPalletDetail.getWidth();//宽
                        BigDecimal height = marFbaPalletDetail.getHeight();//高
                        Integer palletQuantity = marFbaPalletDetail.getPalletQuantity(); //托拍数


                        // 体积 (in³) = 长 × 宽 × 高
                        BigDecimal volumeInCubicInches = length.multiply(width).multiply(height).multiply(new BigDecimal(palletQuantity));

                        // 转换成 (ft³)
                        BigDecimal volumeInCubicFeet = volumeInCubicInches.divide(CUBIC_INCHES_PER_CUBIC_FOOT, 10, RoundingMode.HALF_UP);

                        // 累加
                        totalVolumeFt3 = totalVolumeFt3.add(volumeInCubicFeet);
                    }
                    //保留两位小数，并四舍五入
                    BigDecimal result = totalVolumeFt3.setScale(2, RoundingMode.HALF_UP);

                    //总体积
                    marFbaShipmentNodeVO.setTotalVolume(result);
                }
            }

            marFbaShipmentNodeVO.setShippingTypeStr(MarStaTransportShippingTypeEnum.getDescriptionByCode(item.getShippingType()));//运输类型转义
            marFbaShipmentNodeVO.setShippingMethod(item.getShippingMethod()); //运输方式 1.空运、2.海运、3.陆运
            marFbaShipmentNodeVO.setShippingMethodStr(MarStaTransportModeEnum.getDescriptionByCode(item.getShippingMethod() != null ? item.getShippingMethod().intValue() : null)); //转换后字段（转义）
            marFbaShipmentNodeVO.setCarrierMethod(item.getCarrierMethod()); //承运方式
            marFbaShipmentNodeVO.setDeliveryTime(item.getDeliveryTime() != null ? DateUtil.format(item.getDeliveryTime(), DatePattern.NORM_DATE_PATTERN) : null); //送达开始
            marFbaShipmentNodeVO.setDeliveryTimeTo(item.getDeliveryTimeTo() != null ? DateUtil.format(item.getDeliveryTimeTo(), DatePattern.NORM_DATE_PATTERN) : null); //送达结束
            return marFbaShipmentNodeVO;
        }).collect(Collectors.toList());
        return res;
    }


    /**
     * FBA 更新名称
     *
     * @param contextId
     * @param fbaId
     * @param name
     */
    @Override
    public void updateFbaName(Integer contextId, Long fbaId, String name) {
        MarFbaShipmentInfo marFbaShipmentInfo = marFbaShipmentInfoService.getById(fbaId);
        if (marFbaShipmentInfo == null) {
            throw new CustomException("未获取到FBA信息!");
        }

        MarStaTaskInfo marStaTaskInfo = this.getById(marFbaShipmentInfo.getTaskId());


        if (name.length() > 100) {
            throw new CustomException("字符长度不可大于100!");
        }

        JSONObject reqJson = new JSONObject();
        reqJson.put("name", name);

        //修改名称
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPutQuery(
                AmazonApiURLEnum.UPDATE_SHIPMENT_NAME.getPath().replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId()).replace("{shipmentId}", marFbaShipmentInfo.getShipmentId()),
                marStaTaskInfo.getShopId(), reqJson);

        if (confrmResult == null) {
            throw new CustomException("修改名称失败!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }
        marFbaShipmentInfoService.lambdaUpdate().set(MarFbaShipmentInfo::getShipmentName, name)
                .eq(MarFbaShipmentInfo::getId, fbaId).update();
    }


    /**
     * @param
     * @param inboundPlanId
     * @param shipmentId
     * @param shopId
     * @description: 获取FBA信息下（具体SellerSku信息）
     * @author: Moore
     * @date: 2025/9/9 16:18
     * @return: com.bizark.op.api.amazon.vendor.fba.model.ListShipmentBoxes
     **/
    @Override
    public ListShipmentBoxes getListShipmentBoxes(String inboundPlanId, String shipmentId, Long shopId) {
        AmazonApiResult<String> packResult = amazonRequestUtils.sendGetQuery(AmazonApiURLEnum.LIST_SHIPMENTBOXES.getPath().replace("{inboundPlanId}", inboundPlanId).replace("{shipmentId}", shipmentId), null, shopId);
        if (packResult == null) {
            throw new CustomException("获取包装详情信息失败!");
        }
        if (!packResult.isSuccess()) {
            throw new CustomException(packResult.getMessage());
        }
        ListShipmentBoxes listShipmentBoxes = JSONObject.parseObject(packResult.getData(), ListShipmentBoxes.class);
        return listShipmentBoxes;
    }


    /**
     * @param
     * @param contextId  组织ID
     * @param taskId     任务ID
     * @param shipmentId
     * @description: 获取送达时间
     * @author: Moore
     * @date: 2025/8/25 16:46
     * @return: void
     **/
    @Override
    public List<ListDeliveryWindowOptions.DeliveryWindowOptionsBean> deliveryTimeSelect(Integer contextId, Long taskId, String shipmentId) {
        MarStaTaskInfo marStaTaskInfo = this.getById(taskId);
        if (marStaTaskInfo == null) {
            throw new CustomException("STA任务获取失败!");
        }
        //首先生成送达时间
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPostQuery(
                AmazonApiURLEnum.LIST_DELIVERY_WINDOW_OPTIONS.getPath().
                        replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId())
                        .replace("{shipmentId}", shipmentId), marStaTaskInfo.getShopId(), null
        );

        if (confrmResult == null) {
            throw new CustomException("生成送达时间失败!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }

        List<ListDeliveryWindowOptions.DeliveryWindowOptionsBean> res = new ArrayList<>();
        getListDeliveryWindowOptions(res, shipmentId, marStaTaskInfo, null);
        return res;
    }


    /**
     * @param
     * @param res
     * @param shipmentId
     * @param marStaTaskInfo
     * @param pageToken
     * @description: 获取FBA送达时间-APi
     * @author: Moore
     * @date: 2025/9/3 10:32
     * @return: void
     **/
    private void getListDeliveryWindowOptions(List<ListDeliveryWindowOptions.DeliveryWindowOptionsBean> res,
                                              String shipmentId,
                                              MarStaTaskInfo marStaTaskInfo,
                                              String pageToken) {
        HashMap<String, String> pathParm = new HashMap<>();
        pathParm.put("pageSize", "100");
        if (StringUtil.isNotEmpty(pageToken)) {
            pathParm.put("paginationToken", pageToken);
        }

        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendGetQuery(
                AmazonApiURLEnum.LIST_DELIVERY_WINDOW_OPTIONS.getPath().
                        replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId())
                        .replace("{shipmentId}", shipmentId), pathParm,
                marStaTaskInfo.getShopId());

        if (confrmResult == null) {
            throw new CustomException("确认FBA失败,请稍后重试!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }
        //复用resonse，取操作ID
        ListDeliveryWindowOptions listDeliveryWindowOptions = JSONObject.parseObject(confrmResult.getData(), ListDeliveryWindowOptions.class);

        log.info("时间窗口结果：{}", JSONObject.toJSONString(listDeliveryWindowOptions));
        listDeliveryWindowOptions.getDeliveryWindowOptions().forEach(item -> item.formatDates()); //格式化时间返回
        res.addAll(listDeliveryWindowOptions.getDeliveryWindowOptions());
        if (listDeliveryWindowOptions.getPagination() != null && StringUtil.isNotEmpty(listDeliveryWindowOptions.getPagination().getNextToken())) {
            this.getListDeliveryWindowOptions(res, shipmentId, marStaTaskInfo, pageToken);
        }
    }


    /**
     * 生成FBA承运商
     *
     * @param contextId
     * @param fbaTransportationOptionsDTOList
     */
    @Override
    public void generateTransports(Integer contextId, List<FbaTransportationOptionsDTO> fbaTransportationOptionsDTOList) {
        if (CollectionUtils.isEmpty(fbaTransportationOptionsDTOList)) {
            throw new CustomException("FBA货件信息获取失败!");
        }

        FbaTransportationOptionsDTO fbaTransportationOptionsDTO = fbaTransportationOptionsDTOList.get(0);

        List<MarFbaShipmentInfo> marFbaShipmentInfoList = marFbaShipmentInfoService.lambdaQuery().eq(MarFbaShipmentInfo::getInboundPlanId, fbaTransportationOptionsDTO.getInboundPlanId()).list();
        if (CollectionUtils.isEmpty(fbaTransportationOptionsDTOList)) {
            throw new CustomException("FBA货件信息获取失败!");
        }
        MarFbaShipmentInfo marFbaShipmentInfo = marFbaShipmentInfoList.get(0);


        GenerateTransportationOptionsRequest generateTransportationOptionsRequest = new GenerateTransportationOptionsRequest();
        generateTransportationOptionsRequest.setPlacementOptionId(fbaTransportationOptionsDTO.getPlacementOptionId()); //FBA操作ID


        List<GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean> items = new ArrayList<>();
        //
        fbaTransportationOptionsDTOList.stream().forEach(item -> {
            GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean shipmentTransportationConfigurationsBean = new GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean();

            //发货时间 封装
            GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean.ReadyToShipWindowBean readyToShipWindowBean = new GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean.ReadyToShipWindowBean();
            item.convertShippingTime(); //转换格式为 yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
            readyToShipWindowBean.setStart(item.getShippingTime());
            shipmentTransportationConfigurationsBean.setReadyToShipWindow(readyToShipWindowBean);

            //货件编号 封装
            shipmentTransportationConfigurationsBean.setShipmentId(item.getShipmentId());

            //托拍信息 封装
            if (!CollectionUtils.isEmpty(item.getPalletList())){
                List<GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean.PalletsBean> palletsInfo = item.getPalletList().stream().map(itemPallet -> {
                    //1.托拍主信息
                    GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean.PalletsBean palletsBean = new GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean.PalletsBean();
                    //1.1托拍尺寸信息
                    GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean.PalletsBean.DimensionsBean dimensionsBean = new GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean.PalletsBean.DimensionsBean();
                    dimensionsBean.setLength(itemPallet.getLength()); //长
                    dimensionsBean.setWidth(itemPallet.getWidth()); //宽
                    dimensionsBean.setHeight(itemPallet.getHeight());//高
                    dimensionsBean.setUnitOfMeasurement(StringUtil.isNotEmpty(itemPallet.getLengthUnit()) ? itemPallet.getLengthUnit() : "IN"); //长度单位 IN或CM
                    palletsBean.setDimensions(dimensionsBean);
                    //1.2托拍重量信息
                    GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean.PalletsBean.WeightBean weightBean = new GenerateTransportationOptionsRequest.ShipmentTransportationConfigurationsBean.PalletsBean.WeightBean();
                    weightBean.setUnit(StringUtil.isNotEmpty(itemPallet.getWeightUnit()) ? itemPallet.getWeightUnit() : "LB");
                    weightBean.setValue(itemPallet.getWeight()); //重量（非总重）
                    palletsBean.setWeight(weightBean);

                    //1.3托盘数量
                    palletsBean.setQuantity(itemPallet.getPalletQuantity()); //托盘数

                    //1.4是否可堆叠
                    if (itemPallet.getStackable() == null || itemPallet.getStackable() == 0) {
                        palletsBean.setStackability("NON_STACKABLE");
                    } else if (itemPallet.getStackable() == 1) {
                        palletsBean.setStackability("STACKABLE");
                    }

                    return palletsBean;
                }).collect(Collectors.toList());
                shipmentTransportationConfigurationsBean.setPallets(palletsInfo); //设置托拍信息
            }

            items.add(shipmentTransportationConfigurationsBean);
        });
        generateTransportationOptionsRequest.setShipmentTransportationConfigurations(items);


        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPostQuery(
                AmazonApiURLEnum.GENERATE_TRANSPORTATIONOPTIONS.getPath().replace("{inboundPlanId}", fbaTransportationOptionsDTO.getInboundPlanId())
                , marFbaShipmentInfo.getShopId(), generateTransportationOptionsRequest);

        if (confrmResult == null) {
            throw new CustomException("生成FBA承运商失败!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }

        //递归获取状态，直至操作状态为SUCESS在结束
        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(confrmResult.getData(), CreateInboundPlanResponse.class);
        //查询操作状态二次校验是否确认成功
        this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), marFbaShipmentInfo.getShopId());
    }

    /**
     * 获取FBA承运商信息
     *
     * @param contextId
     * @param fbaTransportationOptionsDTO
     * @return
     */
    @Override
    public ListTransportationOptionsVO listTransportationOptions(Integer contextId, FbaTransportationOptionsDTO fbaTransportationOptionsDTO) {
        if (fbaTransportationOptionsDTO == null) {
            throw new CustomException("FBA货件信息获取失败!");
        }

        //TODO 递归调用

        //查询fba货件
        List<MarFbaShipmentInfo> marFbaShipmentInfoList = marFbaShipmentInfoService.lambdaQuery().eq(MarFbaShipmentInfo::getShipmentId, fbaTransportationOptionsDTO.getShipmentId()).list();
        if (CollectionUtils.isEmpty(marFbaShipmentInfoList)) {
            throw new CustomException("FBA货件信息获取失败!");
        }

        ListTransportationOptionsVO listTransportationRes = new ListTransportationOptionsVO();
        listTransportationRes.setShipmentId(fbaTransportationOptionsDTO.getShipmentId());
        List<ListTransportationOptionsVO.TransportVOListBean> transportVOListBeans = new ArrayList<>();


        MarFbaShipmentInfo marFbaShipmentInfo = marFbaShipmentInfoList.get(0);

        ArrayList<ListTransportationOptions.TransportationOptionsBean> transportationOptions = new ArrayList<>();
        getTransportationOptionsBeans(transportationOptions, fbaTransportationOptionsDTO, marFbaShipmentInfo, null);

        for (ListTransportationOptions.TransportationOptionsBean transportationOption : transportationOptions) {
            ListTransportationOptionsVO.TransportVOListBean transportVOListBea = new ListTransportationOptionsVO.TransportVOListBean();
            ListTransportationOptions.TransportationOptionsBean.CarrierBean carrierFee = transportationOption.getCarrier();
            if (carrierFee != null) {
                transportVOListBea.setAlphaCode(carrierFee.getAlphaCode());
                transportVOListBea.setAlphaName(carrierFee.getName());
                transportVOListBea.setTransportationOptionId(transportationOption.getTransportationOptionId());
                transportVOListBea.setShippingMode(transportationOption.getShippingMode());
                transportVOListBea.setShippingSolution(transportationOption.getShippingSolution());
                transportVOListBeans.add(transportVOListBea);
            }
        }
        if (StringUtil.isNotEmpty(fbaTransportationOptionsDTO.getCarrier())) { //承运人
            transportVOListBeans = transportVOListBeans.stream().filter(item -> fbaTransportationOptionsDTO.getCarrier().equals(item.getShippingSolution())).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(fbaTransportationOptionsDTO.getShippingType())) { //运输类型
            transportVOListBeans = transportVOListBeans.stream().filter(item -> fbaTransportationOptionsDTO.getShippingType().equals(item.getShippingMode())).collect(Collectors.toList());

        }
        listTransportationRes.setTransportVOList(transportVOListBeans);
        return listTransportationRes;
    }

    /**
     * 获取承运商下拉
     *
     * @param
     * @param marFbaShipmentInfo
     * @return
     */
    private void getTransportationOptionsBeans(List<ListTransportationOptions.TransportationOptionsBean> res,
                                               FbaTransportationOptionsDTO optionsDTO,
                                               MarFbaShipmentInfo marFbaShipmentInfo,
                                               String pageToken
    ) {
        HashMap<String, String> pathParm = new HashMap<>();
        pathParm.put("placementOptionId", optionsDTO.getPlacementOptionId());
        pathParm.put("shipmentId", optionsDTO.getShipmentId());
        pathParm.put("pageSize", "20");
        if (StringUtil.isNotEmpty(pageToken)) {
            pathParm.put("paginationToken", pageToken);
        }

        //调用 确认包装方案
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendGetQuery(
                AmazonApiURLEnum.LIST_TRANSPORTATION_OPTIONS.getPath().replace("{inboundPlanId}", marFbaShipmentInfo.getInboundPlanId())
                , pathParm,
                marFbaShipmentInfo.getShopId());

        if (confrmResult == null) {
            throw new CustomException("确认FBA失败,请稍后重试!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }

        //复用resonse，取操作ID
        ListTransportationOptions listTransportationOptions = JSONObject.parseObject(confrmResult.getData(), ListTransportationOptions.class);
        res.addAll(listTransportationOptions.getTransportationOptions());

        //递归调用
        if (listTransportationOptions.getPagination() != null && StringUtil.isNotEmpty(listTransportationOptions.getPagination().getNextToken())) {
            this.getTransportationOptionsBeans(res, optionsDTO, marFbaShipmentInfo, listTransportationOptions.getPagination().getNextToken());
        }
    }


    /**
     * 确认送达时间
     *
     * @param contextId
     * @param inboundPlanId          任务ID
     * @param shipmentId             FBAID
     * @param deliveryWindowOptionId 送达时间窗口ID
     * @return
     */
    @Override
    public InboundOperationStatusResponse confirmDeliveryWindowOptions(Integer contextId, String inboundPlanId, String shipmentId, String deliveryWindowOptionId) {
        //先确认，确认成功后，调用明细接口获取对应FBA货件信息，存储地址，及基础信息(可重复确认)
        List<MarFbaShipmentInfo> marFbaShipmentInfoList = marFbaShipmentInfoService.lambdaQuery().eq(MarFbaShipmentInfo::getShipmentId, shipmentId).list();
        if (CollectionUtils.isEmpty(marFbaShipmentInfoList)) {
            throw new CustomException("FBA货件信息获取失败!");
        }
        MarFbaShipmentInfo marFbaShipmentInfo = marFbaShipmentInfoList.get(0);
        //调用预览接口
        //调用 确认包装方案
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPostQuery(
                AmazonApiURLEnum.CONFIRM_DELIVERY_WINDOW_OPTIONS.getPath().replace("{inboundPlanId}", inboundPlanId)
                        .replace("{shipmentId}", shipmentId).replace("{deliveryWindowOptionId}", deliveryWindowOptionId),
                marFbaShipmentInfo.getShopId(), null);
        if (confrmResult == null) {
            throw new CustomException("送达时间确认失败，请稍后重试!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }
        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(confrmResult.getData(), CreateInboundPlanResponse.class);

        AuthUserDetails authUserDetails = null;
        try {
            authUserDetails = AuthContextHolder.getAuthUserDetails();
        } catch (Exception e) {
            e.printStackTrace();
        }
        //更新确认信息 时间to from 。不采用异步，从接口获取
        this.refershFbaInfo(inboundPlanId, shipmentId, marFbaShipmentInfo.getShopId(), authUserDetails);
        return this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), marFbaShipmentInfo.getShopId());
    }


    /**
     * @param
     * @param inboundPlanId
     * @param shipmentId
     * @param shopId
     * @param authUserDetails
     * @description:更新FBA货件信息
     * @author: Moore
     * @date: 2025/8/27 10:32
     * @return: void
     **/
    @Async("threadPoolTaskExecutor")
    public void refershFbaInfo(String inboundPlanId, String shipmentId, Long shopId, AuthUserDetails authUserDetails) {
        if (authUserDetails == null) {
            try {
                authUserDetails = AuthContextHolder.getAuthUserDetails();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        ShipmentInfoResponse shipment = this.getShipment(inboundPlanId, shipmentId, shopId);
        if (shipment == null) {
            return;
        }
        log.info("shipment信息：{}", JSONObject.toJSONString(shipment));
        LambdaUpdateChainWrapper<MarFbaShipmentInfo> updateChainWrapper = marFbaShipmentInfoService.lambdaUpdate();
        updateChainWrapper.eq(MarFbaShipmentInfo::getShipmentId, shipmentId);
        ShipmentInfoResponse.SelectedDeliveryWindowBean selectedDeliveryWindow = shipment.getSelectedDeliveryWindow();
        if (selectedDeliveryWindow != null) {
            if (selectedDeliveryWindow.getStartDate() != null) {
                updateChainWrapper.set(MarFbaShipmentInfo::getDeliveryTime, DateUtil.parse(selectedDeliveryWindow.getStartDate(), "yyyy-MM-dd'T'HH:mm'Z'"));
            }
            if (selectedDeliveryWindow.getEndDate() != null) {
                updateChainWrapper.set(MarFbaShipmentInfo::getDeliveryTimeTo, DateUtil.parse(selectedDeliveryWindow.getEndDate(), "yyyy-MM-dd'T'HH:mm'Z'"));
            }
        }
        updateChainWrapper.set(MarFbaShipmentInfo::getShipmentState, shipment.getStatus())
                .set(MarFbaShipmentInfo::getUpdatedAt, DateUtils.getNowDate())
                .set(MarFbaShipmentInfo::getUpdatedBy, (authUserDetails == null ? -1 : authUserDetails.getId()))
                .set(MarFbaShipmentInfo::getUpdatedName, authUserDetails == null ? "system" : authUserDetails.getName())
                .update();

        //若FBA货件有一个状态为SHIPPED   更新STA状态为已发货
        if (StaTaskStatusApiEnum.SHIPPED.getStatus().equals(shipment.getStatus())) {
            this.lambdaUpdate().eq(MarStaTaskInfo::getInboundPlanId, inboundPlanId).set(MarStaTaskInfo::getTaskStatus, StaTaskStatusEnum.SHIPPED.getStatus()).update();
        }

    }

    /**
     * 确认FBA
     *
     * @param contextId
     * @param fbaConfirmCarrierDTOS
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InboundOperationStatusResponse confirmTransportationOptions(Integer contextId, List<FbaConfirmCarrierDTO> fbaConfirmCarrierDTOS) {

        //保存FBA基础信息
        this.updateFabShipmentInfo(fbaConfirmCarrierDTOS,contextId);

        //先确认，确认成功后，调用明细接口获取对应FBA货件信息，存储地址，及基础信息
        ConfirmTransportationOptions confirmTransportationOptions = new ConfirmTransportationOptions();
        List<ConfirmTransportationOptions.TransportationSelectionsBean> transportationSelections = fbaConfirmCarrierDTOS.stream().map(item -> {
            ConfirmTransportationOptions.TransportationSelectionsBean transportationSelectionsBean = new ConfirmTransportationOptions.TransportationSelectionsBean();
            transportationSelectionsBean.setShipmentId(item.getShipmentId());   //货件ID
            transportationSelectionsBean.setTransportationOptionId(item.getTransportationOptionId()); //承运商选择ID
            return transportationSelectionsBean;

        }).collect(Collectors.toList());
        confirmTransportationOptions.setTransportationSelections(transportationSelections); //设置确认承运商信息


        FbaConfirmCarrierDTO fbaConfirmCarrierDTO = fbaConfirmCarrierDTOS.get(0); //原则上所有明细行 taskId一致，或者inbound_plan_id 一致
        MarStaTaskInfo marStaTaskInfo = this.getById(fbaConfirmCarrierDTO.getTaskId());
        if (marStaTaskInfo == null) {
            throw new CustomException("STA任务获取失败，请稍后重试!");
        }

        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPostQuery(
                AmazonApiURLEnum.CONFIRM_TRANSPORTATION_OPTIONS.getPath().replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId()),
                marStaTaskInfo.getShopId(), confirmTransportationOptions);
        if (confrmResult == null) {
            throw new CustomException("确认FBA承运商失败，请稍后重试!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }

        marStaTaskInfo.setTaskNode(StaTaskNodeEnum.GOODS_LABLE.getNode());
        marStaTaskInfo.settingDefaultUpdate();
        this.updateById(marStaTaskInfo);

        //获取确认承运操作状态
        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(confrmResult.getData(), CreateInboundPlanResponse.class);
        InboundOperationStatusResponse inboundOperationStatus = this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), marStaTaskInfo.getShopId());

        // 异步更新所有单据FBA状态
        MarStaTaskInfoServiceImpl marStaTaskInfoService = applicationContext.getBean(MarStaTaskInfoServiceImpl.class);
        for (FbaConfirmCarrierDTO confirmCarrierDTO : fbaConfirmCarrierDTOS) {
            marStaTaskInfoService.refershFbaInfo(marStaTaskInfo.getInboundPlanId(), confirmCarrierDTO.getShipmentId(), marStaTaskInfo.getShopId(), null);
        }

        return inboundOperationStatus;
    }

    @Override
    public List<MarFnSkuPrintVO> printFnskuSelect(Integer contextId, FbaFnskuPrintDTO fbaFnskuPrintDTO) {
        List<Long> taskId = fbaFnskuPrintDTO.getTaskId();//STA节点
        List<String> shipmentConfirmationIdList = fbaFnskuPrintDTO.getShipmentConfirmationIdList();//FBA节点


        ArrayList<MarFnSkuPrintVO> marFnSkuPrintVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(taskId)) { //任务ID
            List<MarStaTaskInfo> marStaTaskInfos = this.lambdaQuery().in(MarStaTaskInfo::getId, taskId).list();
            Map<Integer, List<MarStaTaskInfo>> nodeGroup = marStaTaskInfos.stream().collect(Collectors.groupingBy(MarStaTaskInfo::getTaskNode));
            Map<Long, List<MarFnSkuPrintVO.SellerSkuBean>> taskIdMap = new HashMap();
            for (Integer nodeFlag : nodeGroup.keySet()) {
                List<MarStaTaskInfo> marStaTaskInfosNode = nodeGroup.get(nodeFlag);
                List<Long> ids = marStaTaskInfosNode.stream().map(MarStaTaskInfo::getId).distinct().collect(Collectors.toList());
                if (StaTaskNodeEnum.SELECT_GOODS.getNode().equals(nodeFlag)) {
                    List<MarFnSkuPrintVO.SellerSkuBean> marFnSku = marStaShipmentSkusService.selectPrintFnsku(ids);
                    taskIdMap = marFnSku.stream().collect(Collectors.groupingBy(MarFnSkuPrintVO.SellerSkuBean::getTaskId));
                } else if (StaTaskNodeEnum.GOODS_PACK.getNode().equals(nodeFlag)) {
                    List<MarFnSkuPrintVO.SellerSkuBean> marFnSku = marStaPackingSkusService.selectPrintFnsku(ids);
                    taskIdMap = marFnSku.stream().collect(Collectors.groupingBy(MarFnSkuPrintVO.SellerSkuBean::getTaskId));
                } else {
                    List<MarFnSkuPrintVO.SellerSkuBean> marFnSku = marFbaCartonSkusService.selectFnSkuPrintInfoList(fbaFnskuPrintDTO);
                    taskIdMap = marFnSku.stream().collect(Collectors.groupingBy(MarFnSkuPrintVO.SellerSkuBean::getTaskId));

                }

                for (MarStaTaskInfo marStaTaskInfo : marStaTaskInfosNode) {
                    MarFnSkuPrintVO marFnSkuPrintVO = new MarFnSkuPrintVO();
                    marFnSkuPrintVO.setCommonBusinessId(marStaTaskInfo.getTaskName());
                    List<MarFnSkuPrintVO.SellerSkuBean> sellerSkuBeans = taskIdMap.get(marStaTaskInfo.getId());
                    marFnSkuPrintVO.setItms(sellerSkuBeans);
                    marFnSkuPrintVOS.add(marFnSkuPrintVO);
                }

            }
        }

        //FBA节点
        if (!CollectionUtils.isEmpty(shipmentConfirmationIdList)) {
            //查询所有FBA信息
            List<MarFnSkuPrintVO.SellerSkuBean> marFnSku = marFbaCartonSkusService.selectFnSkuPrintInfoList(fbaFnskuPrintDTO);
            Map<String, List<MarFnSkuPrintVO.SellerSkuBean>> listMap = marFnSku.stream().collect(Collectors.groupingBy(MarFnSkuPrintVO.SellerSkuBean::getShipmentConfirmationId));
            for (String fnaNumber : listMap.keySet()) {
                MarFnSkuPrintVO marFnSkuPrintVO = new MarFnSkuPrintVO();
                marFnSkuPrintVO.setCommonBusinessId(fnaNumber);
                List<MarFnSkuPrintVO.SellerSkuBean> sellerSkuBeans = listMap.get(fnaNumber);
                marFnSkuPrintVO.setItms(sellerSkuBeans);
                marFnSkuPrintVOS.add(marFnSkuPrintVO);
            }
        }


        return marFnSkuPrintVOS;
    }


    /**
     * @param
     * @param marFbaCartonSpecs
     * @description: 步骤5设置 运输类型：小包裹 对应箱号tracking箱号信息
     * @author: Moore
     * @date: 2025/8/27 16:24
     * @return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateInboundPlanResponse updateBoxTraking(String shipmentId, List<MarFbaCartonSpec> marFbaCartonSpecs) {


        AuthUserDetails authUserDetails = null;
        try {
            authUserDetails = AuthContextHolder.getAuthUserDetails();
        } catch (Exception e) {
            e.printStackTrace();
        }


        if (marFbaCartonSpecs.stream().allMatch(item -> StringUtil.isEmpty(item.getTrackingNo()))) {
            throw new CustomException("请填写信息!");
        }


        //查询FBA信息获取信息
        MarFbaShipmentInfo marFbaShipmentInfo = marFbaShipmentInfoService.lambdaQuery().eq(MarFbaShipmentInfo::getShipmentId, shipmentId).one();


        UpdateShipmentTrackingDetailsRequest detailsRequest = new UpdateShipmentTrackingDetailsRequest();

        //判断运输类型
        if (marFbaShipmentInfo.getShippingType().equals("GROUND_SMALL_PARCEL")) { //小包裹快递
            UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean trackingDetailsBean = new UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean();
            detailsRequest.setTrackingDetails(trackingDetailsBean);

            //设置tracking details信息
            UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean.SpdTrackingDetailBean spdTrackingDetailBean = new UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean.SpdTrackingDetailBean();
            trackingDetailsBean.setSpdTrackingDetail(spdTrackingDetailBean);

            //设置具体 箱号对应tracking信息
            List<UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean.SpdTrackingDetailBean.SpdTrackingItemsBean> collect = marFbaCartonSpecs.stream().filter(item -> StringUtil.isNotEmpty(item.getTrackingNo())).map(item -> {
                UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean.SpdTrackingDetailBean.SpdTrackingItemsBean spdTrackingItemsBean = new UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean.SpdTrackingDetailBean.SpdTrackingItemsBean();
                spdTrackingItemsBean.setBoxId(item.getBoxId());
                spdTrackingItemsBean.setTrackingId(item.getTrackingNo());
                return spdTrackingItemsBean;
            }).collect(Collectors.toList());
            spdTrackingDetailBean.setSpdTrackingItems(collect);
        } else if (marFbaShipmentInfo.getShippingType().equals("FREIGHT_LTL")) { //汽运零担
            throw new CustomException("暂不支持!");
        }


        //设置Trakcing信息
        AmazonApiResult<String> udpateResult = null;
//        AmazonApiResult<String> udpateResult = amazonRequestUtils.sendPutQuery(
//                AmazonApiURLEnum.UPDATE_SHIPMENT_TRACKING_DETAILS.getPath().replace("{inboundPlanId}", marFbaShipmentInfo.getInboundPlanId()).replace("{shipmentId}", shipmentId),
//                        marFbaShipmentInfo.getShopId(), detailsRequest);
        if (udpateResult == null) {
            throw new CustomException("设置tracking信息失败，请稍后重试!");
        }
        if (!udpateResult.isSuccess()) {
            throw new CustomException(udpateResult.getMessage());
        }

        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(udpateResult.getData(), CreateInboundPlanResponse.class);

        InboundOperationStatusResponse inboundOperationStatus = this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), marFbaShipmentInfo.getShopId());
        if ("SUCCESS".equals(inboundOperationStatus.getOperationStatus())) {

            //更新库中tracking信息
            for (MarFbaCartonSpec marFbaCartonSpec : marFbaCartonSpecs) {
                LambdaUpdateChainWrapper<MarFbaCartonSpec> updateChainWrapper = marFbaCartonSpecService.lambdaUpdate();
                updateChainWrapper.eq(MarFbaCartonSpec::getBoxId, marFbaCartonSpec.getBoxId());
                updateChainWrapper.set(MarFbaCartonSpec::getTrackingNo, marFbaCartonSpec.getTrackingNo());
                updateChainWrapper.update();
            }
            //异步刷新FBA原始状态信息
            MarStaTaskInfoServiceImpl marStaTaskInfoService = applicationContext.getBean(MarStaTaskInfoServiceImpl.class);
            marStaTaskInfoService.refershFbaInfo(marFbaShipmentInfo.getInboundPlanId(), marFbaShipmentInfo.getShipmentId(), marFbaShipmentInfo.getShopId(), authUserDetails);
        }
        return createInboundPlanResponse;
    }


    /** 更新FBA_bol标志和跟踪编号信息
     *
     * @param contextId
     * @param marFbaShipmentInfo
     * @return
     */
    @Override
    public CreateInboundPlanResponse setFbaPalletBolAndPro(Integer contextId, MarFbaShipmentInfo marFbaShipmentInfoSave) {

        AuthUserDetails authUserDetails = null;
        try {
            authUserDetails = AuthContextHolder.getAuthUserDetails();
        } catch (Exception e) {
            e.printStackTrace();
        }


        if (StringUtil.isEmpty(marFbaShipmentInfoSave.getProNumber())) {
            throw new CustomException("LTL 跟踪编号不能为空");
        }
        MarFbaShipmentInfo marFbaShipmentInfo = marFbaShipmentInfoService.getById(marFbaShipmentInfoSave.getId());
        if (marFbaShipmentInfo == null) {
            throw new CustomException("FBA信息获取失败！");
        }

        UpdateShipmentTrackingDetailsRequest detailsRequest = new UpdateShipmentTrackingDetailsRequest();

        //判断运输类型
        if (marFbaShipmentInfo.getShippingType().equals("FREIGHT_LTL")) { //汽运零担
            UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean trackingDetailsBean = new UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean();
            detailsRequest.setTrackingDetails(trackingDetailsBean);
            //设置为LTL
            UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean.LtlTrackingDetail ltlTrackingDetail = new UpdateShipmentTrackingDetailsRequest.TrackingDetailsBean.LtlTrackingDetail();
            trackingDetailsBean.setLtlTrackingDetail(ltlTrackingDetail);
            //设置LTL明细信息
            //提货单号(BOL)
            ltlTrackingDetail.setBillOfLadingNumber(StringUtil.isEmpty(marFbaShipmentInfoSave.getBolNumber()) ? null : marFbaShipmentInfoSave.getBolNumber());
            //跟踪编号(PRO)
            ltlTrackingDetail.setFreightBillNumber(Arrays.asList(marFbaShipmentInfoSave.getProNumber()));
        } else if (marFbaShipmentInfo.getShippingType().equals("GROUND_SMALL_PARCEL")) { //此业务接口不支持汽运零担
            throw new CustomException("暂不支持此类型!");
        }
        //设置FBA BOL及跟踪编号
        AmazonApiResult<String> udpateResult = null;
//        AmazonApiResult<String> udpateResult = amazonRequestUtils.sendPutQuery(
//                AmazonApiURLEnum.UPDATE_SHIPMENT_TRACKING_DETAILS.getPath().replace("{inboundPlanId}", marFbaShipmentInfo.getInboundPlanId()).replace("{shipmentId}", shipmentId),
//                        marFbaShipmentInfo.getShopId(), detailsRequest);
//        if (udpateResult == null) {
//            throw new CustomException("设置货件追踪信息失败，请稍后重试!");
//        }
        if (!udpateResult.isSuccess()) {
            throw new CustomException(udpateResult.getMessage());
        }

        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(udpateResult.getData(), CreateInboundPlanResponse.class);

        InboundOperationStatusResponse inboundOperationStatus = this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), marFbaShipmentInfo.getShopId());
        if ("SUCCESS".equals(inboundOperationStatus.getOperationStatus())) {
            //更新库中tracking信息
            marFbaShipmentInfoService.lambdaUpdate()
                    .eq(MarFbaShipmentInfo::getId, marFbaShipmentInfo.getId())
                    .set(MarFbaShipmentInfo::getBolNumber, marFbaShipmentInfoSave.getBolNumber()) //提货单号(BOL)
                    .set(MarFbaShipmentInfo::getProNumber, marFbaShipmentInfoSave.getProNumber()) //跟踪编号(PRO)
                    .set(MarFbaShipmentInfo::getUpdatedBy, (authUserDetails == null ? -1 : authUserDetails.getId()))
                    .set(MarFbaShipmentInfo::getUpdatedName, authUserDetails == null ? "system" : authUserDetails.getName())
                    .update();

            //异步刷新FBA原始状态信息
            MarStaTaskInfoServiceImpl marStaTaskInfoService = applicationContext.getBean(MarStaTaskInfoServiceImpl.class);
            marStaTaskInfoService.refershFbaInfo(marFbaShipmentInfo.getInboundPlanId(), marFbaShipmentInfo.getShipmentId(), marFbaShipmentInfo.getShopId(), authUserDetails);
        }
        return createInboundPlanResponse;
    }


    /**
     * 删除STA信息
     *
     * @param staId
     */
    @Override
    public void deleteFbaInfo(Long staId) {
        MarStaTaskInfo marStaTaskInfo = this.getById(staId);
        if (marStaTaskInfo == null) {
            throw new CustomException("STA任务获取失败!");
        }
        if (!StaTaskStatusEnum.DRAFT.getStatus().equals(marStaTaskInfo.getTaskStatus())) {
            throw new CustomException("非草稿状态不可删除");
        }
        this.removeById(staId);
    }


    /**
     * 取消STA信息
     *
     * @param staId
     */
    @Override
    public InboundOperationStatusResponse cancelSta(Long staId) {
        MarStaTaskInfo marStaTaskInfo = this.getById(staId);
        if (marStaTaskInfo == null) {
            throw new CustomException("STA任务获取失败!");
        }
        if (StringUtils.isEmpty(marStaTaskInfo.getInboundPlanId())) {
            throw new CustomException("未获取到InboundPlanId");
        }

        //调用预览接口
        //调用 确认包装方案
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPutQuery(
                AmazonApiURLEnum.CANCEL_INBOUNDPLAN.getPath().replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId()),
                marStaTaskInfo.getShopId(), null);
        if (confrmResult == null) {
            throw new CustomException("取消FBA失败，请稍后重试!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }

        //异步刷新货件状态
        MarStaTaskInfoServiceImpl marStaTaskInfoService = applicationContext.getBean(MarStaTaskInfoServiceImpl.class);
        marStaTaskInfoService.refershWfsIboundStatus(marStaTaskInfo.getInboundPlanId(), marStaTaskInfo.getShopId(), marStaTaskInfo.getId());

        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(confrmResult.getData(), CreateInboundPlanResponse.class);
        InboundOperationStatusResponse inboundOperationStatus = this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), marStaTaskInfo.getShopId());
        return inboundOperationStatus;
    }


    /**
     * @param
     * @param contextId
     * @param taskId
     * @description:
     * @author: Moore
     * @date: 2025/9/4 15:47
     * @return: void
     **/
    @Override
    public void udpateNodeNextstep(Integer contextId, Long taskId) {
        MarStaTaskInfo marStaTaskInfo = this.getById(taskId);
        if (marStaTaskInfo != null) {
            Integer taskNode = marStaTaskInfo.getTaskNode();
            if (StaTaskNodeEnum.GOODS_LABLE.getNode().equals(taskNode)) {
                marStaTaskInfo.setTaskNode(StaTaskNodeEnum.PARCEL_TRACKING.getNode());
                marStaTaskInfo.settingDefaultUpdate();
                this.updateById(marStaTaskInfo);
            }
        }
    }

    @Override
    public void orgExportStaList(MarStaTaskListQueryDTO multilOrderRequest, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(multilOrderRequest.getOrgId().intValue());
        request.setTaskCode("mar.sta.list.export");
        List<Object> list = new ArrayList<>();
        list.add(multilOrderRequest);
//        list.add(contextId);
        request.setArgs(list);
        this.taskCenterService.startTask(request, authUserEntity);
        AssertUtil.isFail(FinanceErrorEnum.ASNYC_EXPORT_GENERATED);
    }


    /**
     * 定时刷新FBA 信息
     *
     * @param contextId
     */
    @Override
    public void refreshFbaStatusJob(Integer contextId) {
        LambdaQueryChainWrapper<MarFbaShipmentInfo> queryChainWrapper = marFbaShipmentInfoService.lambdaQuery();
        if (contextId != null) {
            queryChainWrapper.eq(MarFbaShipmentInfo::getOrgId, contextId);
        }
        List<MarFbaShipmentInfo> list = queryChainWrapper.
                notIn(MarFbaShipmentInfo::getShipmentState, Arrays.asList(StaTaskStatusApiEnum.ABANDONED.getStatus(),
                        StaTaskStatusApiEnum.CANCELLED.getStatus(), StaTaskStatusApiEnum.CLOSED.getStatus(), StaTaskStatusApiEnum.DELETED.getStatus())).list();
        for (MarFbaShipmentInfo marStaTaskInfo : list) {

            ShipmentInfoResponse shipment = this.getShipment(marStaTaskInfo.getInboundPlanId(), marStaTaskInfo.getShipmentId(), marStaTaskInfo.getShopId());
            if (shipment == null) {
                return;
            }
            log.info("shipment信息：{}", JSONObject.toJSONString(shipment));
            LambdaUpdateChainWrapper<MarFbaShipmentInfo> updateChainWrapper = marFbaShipmentInfoService.lambdaUpdate();
            updateChainWrapper.eq(MarFbaShipmentInfo::getShipmentId, marStaTaskInfo.getShipmentId());
            ShipmentInfoResponse.SelectedDeliveryWindowBean selectedDeliveryWindow = shipment.getSelectedDeliveryWindow();
            if (selectedDeliveryWindow != null) {
                if (selectedDeliveryWindow.getStartDate() != null) {
                    updateChainWrapper.set(MarFbaShipmentInfo::getDeliveryTime, DateUtil.parse(selectedDeliveryWindow.getStartDate(), "yyyy-MM-dd'T'HH:mm'Z'"));
                }
                if (selectedDeliveryWindow.getEndDate() != null) {
                    updateChainWrapper.set(MarFbaShipmentInfo::getDeliveryTimeTo, DateUtil.parse(selectedDeliveryWindow.getEndDate(), "yyyy-MM-dd'T'HH:mm'Z'"));
                }
            }
            if (StringUtil.isEmpty(marStaTaskInfo.getRefereceId())) {
                updateChainWrapper.set(MarFbaShipmentInfo::getRefereceId, shipment.getAmazonReferenceId());
            }
            updateChainWrapper.set(MarFbaShipmentInfo::getShipmentState, shipment.getStatus()).update();
            //若FBA货件有一个状态为SHIPPED   更新STA状态为已发货
            if (StaTaskStatusApiEnum.SHIPPED.getStatus().equals(shipment.getStatus())) {
                this.lambdaUpdate().eq(MarStaTaskInfo::getInboundPlanId, marStaTaskInfo.getInboundPlanId()).set(MarStaTaskInfo::getTaskStatus, StaTaskStatusEnum.SHIPPED.getStatus()).update();
            }
        }


    }


    /**
     * STA导出
     *
     * @param marStaTaskListQueryDTO
     * @return
     */
    @Override
    public String marStaTaskInfoExport(MarStaTaskListQueryDTO marStaTaskListQueryDTO) {
        log.info("asyncStaTaskInfo start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "STA任务信息" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
//        MarStaTaskListQueryDTO marListingQuery = JSON.parseObject(query, MarStaTaskListQueryDTO.class);

        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, MarStaTaskInfoExportVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "STA信息").head(MarStaTaskInfoExportVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);

                List<MarStaTaskInfoVO> marStaTaskInfoVOS = this.selectStaTaskList(marStaTaskListQueryDTO);
                if (CollUtil.isEmpty(marStaTaskInfoVOS)) {
                    break;
                }

                Map<Long, MarStaTaskInfoVO> mapId = marStaTaskInfoVOS.stream().collect(Collectors.toMap(MarStaTaskInfoVO::getId, Function.identity(), (a, b) -> a));
                List<MarStaTaskInfoExportVO> marStaTaskInfoExportVOS = ConvertUtils.dictConvert(BeanCopyUtils.asmCopyList(marStaTaskInfoVOS, MarStaTaskInfoExportVO.class));
                marStaTaskInfoExportVOS.forEach(item -> {
                    MarStaTaskInfoVO marStaTaskInfoVO = mapId.get(item.getId());
                    List<MarStaTaskInfoVO.FbaInfo> fbaInfoList = marStaTaskInfoVO.getFbaInfoList();
                    if (!CollectionUtils.isEmpty(fbaInfoList)) {
                        item.setFbaNumbers(fbaInfoList.stream().map(MarStaTaskInfoVO.FbaInfo::getShipmentConfirmationId).collect(Collectors.joining(","))); //设置FBA编号信息
                    }
                });
                writer.write(marStaTaskInfoExportVOS, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/fba/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }


    /**
     * 更新FBA基础信息
     *
     * @param fbaConfirmCarrierDTOS
     */
//    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateFabShipmentInfo(List<FbaConfirmCarrierDTO> fbaConfirmCarrierDTOS,Integer contextId) {

        AuthUserDetails authUserDetails = null;
        try {
            authUserDetails = AuthContextHolder.getAuthUserDetails();
        } catch (Exception e) {
            e.printStackTrace();
        }

        for (FbaConfirmCarrierDTO transportationOptionsDTO : fbaConfirmCarrierDTOS) {
            MarFbaShipmentInfo marFbaShipmentInfo = marFbaShipmentInfoService.lambdaQuery().eq(MarFbaShipmentInfo::getShipmentId, transportationOptionsDTO.getShipmentId()).one();
            if (marFbaShipmentInfo == null) {
                throw new CustomException("FBA信息获取失败!");
            }

            LambdaUpdateChainWrapper<MarFbaShipmentInfo> updateChainWrapper = marFbaShipmentInfoService.lambdaUpdate();
            updateChainWrapper.set(MarFbaShipmentInfo::getShippingTime, transportationOptionsDTO.getShippingTime()); //发货日期
            updateChainWrapper.set(MarFbaShipmentInfo::getCarrier, transportationOptionsDTO.getCarrier());//承运人 USE_YOUR_OWN_CARRIER(其他)  AMAZON_PARTNERED_CARRIER(亚马逊合作承运人)
            updateChainWrapper.set(MarFbaShipmentInfo::getShippingType, transportationOptionsDTO.getShippingType());  //运输类型 GROUND_SMALL_PARCEL(小包裹快递)  FREIGHT_LTL(汽运零担)
            updateChainWrapper.set(MarFbaShipmentInfo::getShippingMethod, transportationOptionsDTO.getShippingMethod());//运输方式 海陆空
            updateChainWrapper.set(MarFbaShipmentInfo::getCarrierMethod, transportationOptionsDTO.getCarrierMethod());//承运方式 本质承运方名称
            updateChainWrapper.set(MarFbaShipmentInfo::getCarrierCode, transportationOptionsDTO.getCarrierCode()); //承运商code

            //汽运零担基础信息
            if ("FREIGHT_LTL".equals(transportationOptionsDTO.getShippingType())){
                updateChainWrapper.set(MarFbaShipmentInfo::getFreightLevel, transportationOptionsDTO.getFreightLevel()); //货物等级
                updateChainWrapper.set(MarFbaShipmentInfo::getDeclareValue, transportationOptionsDTO.getDeclareValue()); //申报价值
                updateChainWrapper.set(MarFbaShipmentInfo::getDeclareCurrency, transportationOptionsDTO.getDeclareCurrency()); //申报价值币种
            }


            updateChainWrapper.set(MarFbaShipmentInfo::getUpdatedAt, DateUtils.getNowDate());
            updateChainWrapper.set(MarFbaShipmentInfo::getUpdatedBy, (authUserDetails == null ? -1 : authUserDetails.getId()));
            updateChainWrapper.set(MarFbaShipmentInfo::getUpdatedName, authUserDetails == null ? "system" : authUserDetails.getName());
            updateChainWrapper.eq(MarFbaShipmentInfo::getShipmentId, transportationOptionsDTO.getShipmentId());
            updateChainWrapper.update();
            //汽运零担 单独存储对应托盘信息
            if ("FREIGHT_LTL".equals(transportationOptionsDTO.getShippingType())){
                List<FbaConfirmCarrierDTO.pallet> palletList = transportationOptionsDTO.getPalletList();
                if (!CollectionUtils.isEmpty(palletList)) {
                    List<MarFbaPalletDetail> pelletDetail = palletList.stream().map(itme -> {
                        MarFbaPalletDetail marFbaPalletDetailSave = new MarFbaPalletDetail();
                        marFbaPalletDetailSave.setOrgId(contextId != null ? contextId.longValue() : null);
                        marFbaPalletDetailSave.setFbaId(marFbaShipmentInfo.getId()); //FBA主键
                        marFbaPalletDetailSave.setShipmentConfirmationId(marFbaShipmentInfo.getShipmentConfirmationId()); //FBA 编号
                        marFbaPalletDetailSave.setLength(itme.getLength()); //长
                        marFbaPalletDetailSave.setWidth(itme.getWidth());//宽
                        marFbaPalletDetailSave.setHeight(itme.getHeight());//高
                        marFbaPalletDetailSave.setWeight(itme.getWeight()); //重量
                        marFbaPalletDetailSave.setPalletQuantity(itme.getPalletQuantity()); //托拍数量
                        marFbaPalletDetailSave.settingDefaultCreate();
                        return marFbaPalletDetailSave;
                    }).collect(Collectors.toList());

                    marFbaPalletDetailService.saveBatch(pelletDetail);
                }

            }

        }
    }

    /**
     * STA_STEP2确认申报
     *
     * @param contextId
     * @param taskId
     * @param placementOptionId 操作ID
     */
    @Override
    public InboundOperationStatusResponse confirmPlacementOption(Integer contextId, Long taskId, String placementOptionId) {
        //先确认，确认成功后，调用明细接口获取对应FBA货件信息，存储地址，及基础信息
        MarStaTaskInfo marStaTaskInfo = this.getById(taskId);
        if (marStaTaskInfo == null) {
            throw new CustomException("STA任务获取失败!");
        }
        //调用预览接口
        //调用 确认包装方案
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendPostQuery(
                AmazonApiURLEnum.CONFIRM_PLACEMENT_OPTION.getPath().replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId())
                        .replace("{placementOptionId}", placementOptionId),
                marStaTaskInfo.getShopId(), null);

        if (confrmResult == null) {
            throw new CustomException("确认FBA失败,请稍后重试!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }
        //复用resonse，取操作ID
        CreateInboundPlanResponse createInboundPlanResponse = JSONObject.parseObject(confrmResult.getData(), CreateInboundPlanResponse.class);
        return this.getInboundOperationStatus(createInboundPlanResponse.getOperationId(), marStaTaskInfo.getShopId());
    }


    /**
     * 保存货件信息
     *
     * @param contextId
     * @param taskId
     */
    @Override
    @Transactional
    public void saveFbaInfo(Integer contextId, Long taskId, MarFbaSaveDTO marFbaSaveDTO) {
        MarStaTaskInfo marStaTaskInfo = this.getById(taskId);
        if (marStaTaskInfo == null) {
            throw new CustomException("STA任务获取失败!");
        }

        // 工具STA获取已确认FBA信息
        AmazonApiResult<String> confrmResult = amazonRequestUtils.sendGetQuery(
                AmazonApiURLEnum.LIST_PLACEMENT_OPTIONS.getPath().replace("{inboundPlanId}", marStaTaskInfo.getInboundPlanId()),
                null, marStaTaskInfo.getShopId());
        if (confrmResult == null) {
            throw new CustomException("获取货件信息失败!");
        }
        if (!confrmResult.isSuccess()) {
            throw new CustomException(confrmResult.getMessage());
        }

        ListPlacementOptionsResponse listPlacementOptionsResponse = JSONObject.parseObject(confrmResult.getData(), ListPlacementOptionsResponse.class);

        //2.获取已确定状态FBA信息 （OFFERED, ACCEPTED, EXPIRED）
        List<ListPlacementOptionsResponse.PlacementOptionsBean> placementOptionsBeans = listPlacementOptionsResponse.getPlacementOptions().stream().filter(item -> "ACCEPTED".equals(item.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(placementOptionsBeans)) {
            throw new CustomException("无已确认FBA信息，不可保存!");
        }

        //原则上只会有一个已确认数据
        if (placementOptionsBeans.size() > 1) {
            throw new CustomException("已确认货件信息不唯一!");
        }

        //费用币种等信息
        ListPlacementOptionsResponse.PlacementOptionsBean placementOptionsBean = placementOptionsBeans.get(0);
        String amount = null;
        String currency = null;
        if (!CollectionUtils.isEmpty(placementOptionsBean.getFees())) {
            ListPlacementOptionsResponse.PlacementOptionsBean.FeesBean.ValueBean value = placementOptionsBean.getFees().get(0).getValue();
            amount = value.getAmount(); //金额
            currency = value.getCode(); //币种
        }


        List<CompletableFuture<MarStaShipmentBoxeRes>> futures = marFbaSaveDTO.getShipmentIds().stream()
                .map(shipmentId -> CompletableFuture.supplyAsync(() -> {
                    MarStaShipmentBoxeRes marStaPlacementOptionsVO = new MarStaShipmentBoxeRes();
                    marStaPlacementOptionsVO.setShipmentId(shipmentId);
                    //获取shipment明细
                    ShipmentInfoResponse shipment = getShipment(marStaTaskInfo.getInboundPlanId(), shipmentId, marStaTaskInfo.getShopId());
                    marStaPlacementOptionsVO.setShipmentInfoResponse(shipment);

                    //获取shipenntBox
                    ListShipmentBoxes listShipmentBoxes = getListShipmentBoxes(marStaTaskInfo.getInboundPlanId(), shipmentId, marStaTaskInfo.getShopId());
                    marStaPlacementOptionsVO.setListShipmentBoxes(listShipmentBoxes);
                    return marStaPlacementOptionsVO;
                }, threadPoolTaskExecutor)).collect(Collectors.toList());
        List<MarStaShipmentBoxeRes> results = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        log.info("FBA保存获取信息：{}", JSONObject.toJSONString(results));
        //原始STA信息
        List<MarStaPackingSkus> packingSkuses = marStaPackingSkusService.lambdaQuery().eq(MarStaPackingSkus::getTaskId, marStaTaskInfo.getId()).list();
        Map<String, MarStaPackingSkus> sellerSkuGroup = packingSkuses.stream().collect(Collectors.toMap(MarStaPackingSkus::getSellerSku, Function.identity(), (a, b) -> a));


        //保存FBA信息
        for (MarStaShipmentBoxeRes result : results) {
            String shipmentId = result.getShipmentId();
            //保存货件主信息
            MarFbaShipmentInfo marFbaShipmentInfoDb = marFbaShipmentInfoService.lambdaQuery().eq(MarFbaShipmentInfo::getShipmentId, shipmentId).one();
            if (marFbaShipmentInfoDb != null) {
                log.info("货件信息已存在：{}", shipmentId);
                continue;
            }
            ShipmentInfoResponse shipmentInfoResponse = result.getShipmentInfoResponse();

            MarFbaShipmentInfo marFbaShipmentInfo = new MarFbaShipmentInfo();
            marFbaShipmentInfo.setOrgId(marStaTaskInfo.getOrgId());
            marFbaShipmentInfo.setShopId(marStaTaskInfo.getShopId());
            marFbaShipmentInfo.setTaskId(marStaTaskInfo.getId());
            marFbaShipmentInfo.setInboundPlanId(marStaTaskInfo.getInboundPlanId());
            marFbaShipmentInfo.setShipmentName(shipmentInfoResponse.getName()); //货件名称
            marFbaShipmentInfo.setShipmentId(shipmentId); //货件ID
            marFbaShipmentInfo.setShipmentConfirmationId(shipmentInfoResponse.getShipmentConfirmationId());//货件单号
            marFbaShipmentInfo.setPlanFee(new BigDecimal(amount)); //预计总费用 ，可考虑前端传入
            marFbaShipmentInfo.setCurrency(currency); //币种
            ShipmentInfoResponse.DestinationBean destination = shipmentInfoResponse.getDestination();
            marFbaShipmentInfo.setLogisticsCenter(destination.getWarehouseId()); //物流中心编码
            marFbaShipmentInfo.setRefereceId(shipmentInfoResponse.getAmazonReferenceId()); //refereceID
            marFbaShipmentInfo.setShipmentState(shipmentInfoResponse.getStatus()); //货件状态
            //此处未设置申请数量 （用于前端查询），暂时使用

            //设置申报量

            marFbaShipmentInfo.settingDefaultCreate();
            marFbaShipmentInfoService.save(marFbaShipmentInfo);


            //地址信息（发货地址及配送地址保存）
            saveFbaAddress(shipmentInfoResponse, marFbaShipmentInfo);

            ListShipmentBoxes listShipmentBoxes = result.getListShipmentBoxes();
            List<ListShipmentBoxes.BoxesBean> boxes = listShipmentBoxes.getBoxes();

            Integer applyNumber = 0;

            for (ListShipmentBoxes.BoxesBean box : boxes) {

                //箱子信息
                MarFbaCartonSpec marFbaCartonSpecSave = new MarFbaCartonSpec();
                marFbaCartonSpecSave.setOrgId(marStaTaskInfo.getOrgId());
                marFbaCartonSpecSave.setTaskId(marStaTaskInfo.getId());
                marFbaCartonSpecSave.setShipmentBusinessId(marFbaShipmentInfo.getId()); //FBA主键
                marFbaCartonSpecSave.setShipmentConfirmationId(marFbaShipmentInfo.getShipmentConfirmationId()); //货件号，唯一
                marFbaCartonSpecSave.setBoxId(box.getBoxId()); //箱号
                marFbaCartonSpecSave.setExternalContainerId(box.getExternalContainerIdentifier()); //箱子外部标识符
                marFbaCartonSpecSave.setContainerType(box.getExternalContainerIdentifierType()); //箱标类型
                marFbaCartonSpecSave.setTemplateName(box.getTemplateName());
                marFbaCartonSpecSave.setPackageId(box.getPackageId()); //包装编号
                marFbaCartonSpecSave.setQuantity(box.getQuantity()); //（MKSKU数量） The number of the specified MSKU.（sellerSKu个数）
                //箱子-重量信息
                ListShipmentBoxes.BoxesBean.WeightBean weight = box.getWeight();
                if (weight != null) {
                    marFbaCartonSpecSave.setWeightValue(weight.getValue());
                    marFbaCartonSpecSave.setWeightUnit(weight.getUnit());//重量单位
                }
                //箱子-尺寸信息
                ListShipmentBoxes.BoxesBean.DimensionsBean dimensions = box.getDimensions();
                if (dimensions != null) {
                    marFbaCartonSpecSave.setLength(dimensions.getLength());
                    marFbaCartonSpecSave.setWidth(dimensions.getWidth());
                    marFbaCartonSpecSave.setHeight(dimensions.getHeight());
                    marFbaCartonSpecSave.setUnit(dimensions.getUnitOfMeasurement()); //尺寸单位
                }
                marFbaCartonSpecSave.setContentInfoSource(box.getContentInformationSource()); //交付方式
                marFbaCartonSpecSave.settingDefaultCreate();
                marFbaCartonSpecService.save(marFbaCartonSpecSave);

                //保存箱子明细信息
                List<MarFbaCartonSkus> marFbaCartonSkusList = box.getItems().stream().map(item -> {
                    MarFbaCartonSkus marFbaCartonSkus = new MarFbaCartonSkus();
                    marFbaCartonSkus.setShipmentBusinessId(marFbaShipmentInfo.getId()); //货件主键ID
                    marFbaCartonSkus.setOrgId(marStaTaskInfo.getOrgId());
                    marFbaCartonSkus.setFbaCartonId(marFbaCartonSpecSave.getId()); //装箱信息主键
                    marFbaCartonSkus.setBoxId(box.getBoxId()); //箱号

                    //设置图片等信息
                    MarStaPackingSkus marStaPackingSkus = sellerSkuGroup.get(item.getMsku());
                    if (marStaPackingSkus != null) {
                        marFbaCartonSkus.setImageUrl(marStaPackingSkus.getImageUrl());
                        marFbaCartonSkus.setParentAsin(marStaPackingSkus.getParentAsin());
                        marFbaCartonSkus.setTitle(marStaPackingSkus.getTitle());
                        marFbaCartonSkus.setErpSku(marStaPackingSkus.getErpSku());
                        marFbaCartonSkus.setProductName(marStaPackingSkus.getProductName());
                    }
                    marFbaCartonSkus.setSellerSku(item.getMsku());
                    marFbaCartonSkus.setFnSku(item.getFnsku());
                    marFbaCartonSkus.setAsin(item.getAsin());
                    marFbaCartonSkus.setLabelType(item.getLabelOwner()); //贴标方式

                    List<ListShipmentBoxes.BoxesBean.ItemsBean.PrepInstructionsBean> prepInstructions = item.getPrepInstructions();
                    if (!CollectionUtils.isEmpty(prepInstructions)) {
                        ListShipmentBoxes.BoxesBean.ItemsBean.PrepInstructionsBean prepInstructionsBean = prepInstructions.get(0);
                        marFbaCartonSkus.setLabelVendor(prepInstructionsBean.getPrepOwner()); //贴标方
                    }
                    marFbaCartonSkus.setApplyNum(item.getQuantity()); //申报数量
                    marFbaCartonSkus.settingDefaultCreate();
                    return marFbaCartonSkus;
                }).collect(Collectors.toList());
                marFbaCartonSkusService.saveBatch(marFbaCartonSkusList);

                applyNumber += marFbaCartonSkusList.stream().mapToInt(MarFbaCartonSkus::getApplyNum).sum();
            }
            //回写FBA申报数量（box下所有装箱信息）
            marFbaShipmentInfo.setApplyNum(applyNumber);
            marFbaShipmentInfoService.updateById(marFbaShipmentInfo);

        }

        //所有均成功，节点调整为3
        MarStaTaskInfo marStaTaskInfoUpdate = new MarStaTaskInfo();
        marStaTaskInfoUpdate.setId(marStaTaskInfo.getId());
        marStaTaskInfoUpdate.settingDefaultUpdate();
        marStaTaskInfoUpdate.setTaskNode(StaTaskNodeEnum.GOODS_DELIVERY.getNode());
        this.updateById(marStaTaskInfoUpdate);

        //异步刷新原始STA状态信息
        MarStaTaskInfoServiceImpl marStaTaskInfoService = applicationContext.getBean(MarStaTaskInfoServiceImpl.class);
        marStaTaskInfoService.refershWfsIboundStatus(marStaTaskInfo.getInboundPlanId(), marStaTaskInfo.getShopId(), marStaTaskInfo.getId());
    }


    /**
     * 保存FBA地址信息
     *
     * @param shipmentInfoResponse FBA接口信息
     * @param marFbaShipmentInfo   FBA信息
     */
    public void saveFbaAddress(ShipmentInfoResponse shipmentInfoResponse, MarFbaShipmentInfo marFbaShipmentInfo) {
        marFbaShipmentInfo.setPlacementOptionId(shipmentInfoResponse.getPlacementOptionId()); //选项ID

        List<MarFbaAddress> addresses = marFbaAddressService.lambdaQuery().eq(MarFbaAddress::getFbaId, marFbaShipmentInfo.getId()).list();
        if (!CollectionUtils.isEmpty(addresses)) {
            return;
        }
        if (shipmentInfoResponse.getSource() != null && shipmentInfoResponse.getSource().getAddress() != null) {
            ShipmentInfoResponse.SourceBean.AddressBeanX address = shipmentInfoResponse.getSource().getAddress();
            MarFbaAddress marFbaAddress = new MarFbaAddress();
            marFbaAddress.setOrgId(marFbaShipmentInfo.getOrgId());
            marFbaAddress.setFbaId(marFbaShipmentInfo.getId());
            marFbaAddress.setAddressType(FbaAddressTypeEnum.SHIP_FROM.getType());
            marFbaAddress.setShipmentConfirmationId(marFbaShipmentInfo.getShipmentConfirmationId());
            marFbaAddress.setAddressLine1(address.getAddressLine1());
            marFbaAddress.setAddressLine2(address.getAddressLine2());
            marFbaAddress.setCity(address.getCity());
            marFbaAddress.setCompanyName(address.getCompanyName());
            marFbaAddress.setCountryCode(address.getCountryCode());
            marFbaAddress.setEmail(address.getEmail());
            marFbaAddress.setName(address.getName());
            marFbaAddress.setPhoneNumber(address.getPhoneNumber());
            marFbaAddress.setPostalCode(address.getPostalCode());
            marFbaAddress.setStateProvince(address.getStateOrProvinceCode());
            marFbaAddressService.save(marFbaAddress);
        }

        if (shipmentInfoResponse.getDestination() != null && shipmentInfoResponse.getDestination().getAddress() != null) {
            ShipmentInfoResponse.DestinationBean.AddressBean address = shipmentInfoResponse.getDestination().getAddress();
            MarFbaAddress marFbaAddress = new MarFbaAddress();
            marFbaAddress.setOrgId(marFbaShipmentInfo.getOrgId());
            marFbaAddress.setFbaId(marFbaShipmentInfo.getId());
            marFbaAddress.setAddressType(FbaAddressTypeEnum.SHIP_TO.getType());
            marFbaAddress.setShipmentConfirmationId(marFbaShipmentInfo.getShipmentConfirmationId());
            marFbaAddress.setAddressLine1(address.getAddressLine1());
            marFbaAddress.setAddressLine2(address.getAddressLine2());
            marFbaAddress.setCity(address.getCity());
            marFbaAddress.setCompanyName(address.getCompanyName());
            marFbaAddress.setCountryCode(address.getCountryCode());
            marFbaAddress.setEmail(address.getEmail());
            marFbaAddress.setName(address.getName());
            marFbaAddress.setPhoneNumber(address.getPhoneNumber());
            marFbaAddress.setPostalCode(address.getPostalCode());
            marFbaAddress.setStateProvince(address.getStateOrProvinceCode());
            marFbaAddressService.save(marFbaAddress);
        }

    }


    /**
     * @param
     * @param shopId
     * @param selleSkus
     * @description: 单独调用获取预处理方，贴标方
     * @author: Moore
     * @date: 2025/8/5 19:38
     * @return: com.bizark.op.api.amazon.vendor.fba.model.PrepDetails
     **/
    public PrepDetails getPrepDetails(Long shopId, String selleSkus) {
        HashMap<String, String> requestMap = new HashMap<>();
        requestMap.put("mskus", selleSkus);
        AmazonApiResult<String> amazonApiResult = amazonRequestUtils.sendGetQuery(AmazonApiURLEnum.LIST_PREP_DETAILS.getPath(), requestMap, shopId);
        if (amazonApiResult == null) {
            throw new CustomException("获取预处理方,标签类型失败!");
        }
        if (amazonApiResult.isSuccess()) {
            PrepDetails prepDetails = JSONObject.parseObject(amazonApiResult.getData(), PrepDetails.class);
            log.info("预处理方接口结果：{}", amazonApiResult.getData());
            return prepDetails;
        }
        throw new CustomException(amazonApiResult.getMessage());
    }


    /**
     * @param
     * @description:获取操作状态（操作状态返回值，较为特殊）
     * @author: Moore
     * @date: 2025/8/4 19:50
     * @return: void
     **/
    public InboundOperationStatusResponse getInboundOperationStatus(String operationId, Long shopId) {
        AmazonApiResult<String> amazonApiResult = amazonRequestUtils.sendGetQueryCommon(AmazonApiURLEnum.GET_INBOUND_OPERATION_STATUS.getPath().replace("{operationId}", operationId), null, shopId);
        if (amazonApiResult == null) {
            throw new CustomException("获取操作状态失败!");
        }
        if (!amazonApiResult.isSuccess()) {
            throw new CustomException(amazonApiResult.getMessage());
        }
        log.info("operation操作结果：{},参数：{}", JSONObject.toJSONString(amazonApiResult.getData()), operationId);
        InboundOperationStatusResponse inboundOperationStatusResponse = JSONObject.parseObject(amazonApiResult.getData(), InboundOperationStatusResponse.class);
        //递归直至结束
        if ("IN_PROGRESS".equals(inboundOperationStatusResponse.getOperationStatus())) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return this.getInboundOperationStatus(operationId, shopId);
        }
        return inboundOperationStatusResponse;

    }


    /**
     * @param aipInfo          api返回标签结果
     * @param aipInfo          shipmentSkusList 用户填写标签结果
     * @param shipmentSkusList
     * @description: 校验预处理及标签类型
     * @author: Moore
     * @date: 2025/7/31 14:49
     * @return:
     **/
    public List<MarFbaVerifyPrepDetailsVO> verifyPrepDetails(List<PrepDetails.MskuPrepDetailsBean> aipInfo, List<MarStaShipmentSkusDTO> shipmentSkusList) {
        Map<String, MarStaShipmentSkusDTO> saveStaShipmentSKus = shipmentSkusList.stream().collect(Collectors.toMap(MarStaShipmentSkusDTO::getSellerSku, Function.identity(), (a, b) -> a));
        List<MarFbaVerifyPrepDetailsVO> prepDetailsVOS = new ArrayList<>();
        aipInfo.stream().forEach(item -> {
            String msku = item.getMsku(); //SellerSKu
            MarStaShipmentSkusDTO marStaShipmentSkus = saveStaShipmentSKus.get(msku);
            if (marStaShipmentSkus != null) {
                //接口类型转换
                String labelOwnerConstraint = StaTaskPrepOrlabelOwnerEnum.getOwnerByOrgOwner(item.getLabelOwnerConstraint()); //贴标方式
                String PrepOwnerConstraint = StaTaskPrepOrlabelOwnerEnum.getOwnerByOrgOwner(item.getPrepOwnerConstraint()); //贴标类型

                //接口与save比较
                if (StringUtils.isNotEmpty(labelOwnerConstraint) && !marStaShipmentSkus.getLabelType().equals(labelOwnerConstraint)) {
                    MarFbaVerifyPrepDetailsVO resDetailsVO = new MarFbaVerifyPrepDetailsVO();
                    resDetailsVO.setSellerSku(msku);
                    resDetailsVO.setType(2);
                    resDetailsVO.setCommonParam(labelOwnerConstraint);
                    prepDetailsVOS.add(resDetailsVO);
                }
                if (StringUtils.isNotEmpty(PrepOwnerConstraint) && !marStaShipmentSkus.getLabelVendor().equals(PrepOwnerConstraint)) {
                    MarFbaVerifyPrepDetailsVO resDetailsVO = new MarFbaVerifyPrepDetailsVO();
                    resDetailsVO.setSellerSku(msku);
                    resDetailsVO.setType(1);
                    resDetailsVO.setCommonParam(PrepOwnerConstraint);
                    prepDetailsVOS.add(resDetailsVO);
                }
            }
        });
        return prepDetailsVOS;
    }


    /**
     * @param
     * @param query
     * @description: 创建货件
     * @author: Moore
     * @date: 2025/8/6 15:08
     * @return: com.bizark.op.api.amazon.vendor.fba.model.CreateInboundPlanResponse
     **/
    public CreateInboundPlanResponse createInboundPlan(MarStaTaskShipmentInfoDTO query, Account account) {
        MarStaShipmentAddress address = marStaShipmentAddressService.lambdaQuery().eq(MarStaShipmentAddress::getTaskId, query.getId()).one();
        CreateInboundPlan createInboundPlan = new CreateInboundPlan();
        createInboundPlan.setName(query.getTaskName()); //任务名称

        //设置区域
        String countryCode = StrUtil.isNotBlank(account.getCountryCode()) ?
                account.getCountryCode() : JSON.parseObject(account.getConnectStr()).getString(SaleConstant.COUNTRY_CODE);
        AmazonApiEnum amazonApiEnum = AmazonApiEnum.getByCountryCode(countryCode);
        createInboundPlan.setDestinationMarketplaces(Arrays.asList(amazonApiEnum.getMarketPlace()));

        //设置货件信息
        List<CreateInboundPlan.ItemsBean> items = query.getShipmentSkusList().stream().map(item -> {
            CreateInboundPlan.ItemsBean itemsBean = new CreateInboundPlan.ItemsBean();
            if (item.getExpireTime() != null) {
                itemsBean.setExpiration(DateUtil.format(item.getExpireTime(), DatePattern.NORM_DATE_PATTERN));  //有效期
            }
            itemsBean.setLabelOwner(item.getLabelType()); //标签处理
            itemsBean.setMsku(item.getSellerSku()); //sellerSKu
            itemsBean.setPrepOwner(item.getLabelVendor()); //处理方
            itemsBean.setQuantity(item.getApplyNum()); //申报数量
            return itemsBean;
        }).collect(Collectors.toList());
        createInboundPlan.setItems(items);
        //地址信息
        CreateInboundPlan.SourceAddressBean sourceAddressBean = new CreateInboundPlan.SourceAddressBean();
        sourceAddressBean.setAddressLine1(address.getStreetAddress1()); //街道地址1
        sourceAddressBean.setAddressLine2(address.getStreetAddress2()); //地址2
        sourceAddressBean.setCity(address.getCity()); //城市
        sourceAddressBean.setCompanyName(address.getCompanyName()); //公司名
        sourceAddressBean.setCountryCode(address.getShipmentCountry()); //国家
        sourceAddressBean.setEmail(address.getEmail()); //邮件
        sourceAddressBean.setName(address.getSenderName()); //发货人
        sourceAddressBean.setPhoneNumber(address.getPhone()); //联系方式
        sourceAddressBean.setPostalCode(address.getPostalCode()); //邮编
        sourceAddressBean.setStateOrProvinceCode(address.getStateProvince()); //州/城市/地区、
        createInboundPlan.setSourceAddress(sourceAddressBean);


        //调用
        AmazonApiResult<String> amazonApiResult = amazonRequestUtils.sendPostQuery(AmazonApiURLEnum.CREATED_INBOUND_PLANS.getPath(), query.getShopId(), createInboundPlan);
        if (amazonApiResult == null) {
            throw new CustomException("创建货件调用异常!");
        }
        if (!amazonApiResult.isSuccess()) {
            throw new CustomException(amazonApiResult.getMessage());
        }
        return JSONObject.parseObject(amazonApiResult.getData(), CreateInboundPlanResponse.class);
    }


    /**
     * @param
     * @param inboundPlanId
     * @param shopId
     * @description: 获取单货件信息
     * @author: Moore
     * @date: 2025/8/5 16:53
     * @return: void
     **/
    public InboundPlanResponse getInboundPlan(String inboundPlanId, Long shopId) {
        AmazonApiResult<String> amazonApiResult = amazonRequestUtils.sendGetQuery(AmazonApiURLEnum.GET_INBOUND_PLANS.getPath().replace("{inboundPlanId}", inboundPlanId), null, shopId);
        if (amazonApiResult != null && amazonApiResult.isSuccess()) {
            return JSONObject.parseObject(amazonApiResult.getData(), InboundPlanResponse.class);
        }
        return null;
    }


    /**
     * 刷新货件状态
     *
     * @param inboundPlanId
     * @param shopId
     * @param id
     */
    @Async
    public void refershWfsIboundStatus(String inboundPlanId, Long shopId, Long id) {
        if (id == null) {
            return;
        }
        InboundPlanResponse inboundPlan = this.getInboundPlan(inboundPlanId, shopId);
        if (inboundPlan != null && StringUtils.isNotEmpty(inboundPlan.getStatus())) {
            MarStaTaskInfo marStaTaskInfo = new MarStaTaskInfo();
            marStaTaskInfo.setId(id);
            marStaTaskInfo.setTaskStatusApi(inboundPlan.getStatus()); //原始Api状态
            if (StaTaskStatusApiEnum.CANCELLED.equals(inboundPlan.getStatus())) {
                marStaTaskInfo.setTaskStatus(StaTaskStatusEnum.CANCELLED.getStatus()); //业务状态
            }
            if (!StringUtil.isEmpty(inboundPlan.getCreatedAt())) {
                try {
                    marStaTaskInfo.setStaCreatedAt(DateUtil.parse(inboundPlan.getCreatedAt(), DatePattern.UTC_PATTERN));
                } catch (Exception e) {
                    log.info("STA转换时间异常：{},时间：{}", e, inboundPlan.getCreatedAt());
                }
            }
            this.updateById(marStaTaskInfo);
        }
    }


    /**
     * @param
     * @param marStaShipmentSkuses
     * @description: 批量copy数据 发货商品到装箱商品
     * @author: Moore
     * @date: 2025/7/28 15:49
     * @return: void
     **/
    @Transactional
    public void copyShipmentToPackingSKus(List<MarStaShipmentSkus> marStaShipmentSkuses) {
        //TODO 调整为多线程
        marStaShipmentSkuses.stream().forEach(item -> {
            MarStaPackingSkus marStaPackingSkus = BeanCopyUtils.copyBean(item, MarStaPackingSkus.class);
            marStaPackingSkus.setId(null);
            marStaPackingSkus.settingDefaultCreate();
            marStaPackingSkusService.save(marStaPackingSkus);
            //操作关联箱规表
            MarStaCartonSpec marStaCartonSpec = BeanCopyUtils.copyBean(item, MarStaCartonSpec.class);
            marStaCartonSpec.setId(null);
            marStaCartonSpec.setRemark(null);
            marStaCartonSpec.setSkuId(marStaPackingSkus.getId());
            marStaCartonSpec.settingDefaultCreate();
            marStaCartonSpecService.save(marStaCartonSpec);
        });

    }

    public static <S, T> List<T> copyList(List<S> src, Supplier<T> targetSupplier) {
        if (src == null || src.isEmpty()) return Collections.emptyList();
        return src.stream().filter(Objects::nonNull).map(s -> {
            T t = targetSupplier.get();        // 需要 DTO 有无参构造
            BeanUtils.copyProperties(s, t);
            return t;
        }).collect(Collectors.toList());
    }

    @Override
    public String asyncExportPackingList(String json) {

        AsyncExportPackingListDTO request = JSON.parseObject(json, AsyncExportPackingListDTO.class);
        String uploadPath;
        File file;

        MarStaTaskInfo marStaTaskInfo = query()
                .eq("inbound_plan_id", request.getInboundPlanId())
                .one();
        if (marStaTaskInfo == null) {
            throw new CustomException("任务信息不存在!");
        }

        //1.获取STA货件包装组信息
        MarStaPackGroupInfo groupInfo = marStaPackGroupInfoService
                .query()
                .eq("inbound_plan_id", request.getInboundPlanId())
                .eq("packing_group_id", request.getPackingGroupId())
                .one();
        if (groupInfo == null) {
            log.warn("包装组信息不存在，请求参数：inboundPlanId={}, packingGroupId={}",
                    request.getInboundPlanId(), request.getPackingGroupId());
            throw new RuntimeException("包装组信息不存在");


        }
//        filePath = "/Users/<USER>/Downloads/";
        // 使用 UUID 避免文件名冲突，并加入业务标识便于追踪
        String fileName = StrUtil.format("【{}】_【{}】_PACKING_LIST", marStaTaskInfo.getTaskName(), groupInfo.getPackingGroupId());

        Path pathObj = Paths.get(filePath, fileName);
        String path = pathObj.toString();

        try {
            // 明确指定临时文件的父目录，增强安全性
            file = File.createTempFile(path, ".xlsx", new File(filePath));
            file.deleteOnExit(); // 确保 JVM 退出时清理临时文件
        } catch (IOException e) {
            log.error("创建临时文件失败，请求参数：inboundPlanId={}, packingGroupId={}",
                    request.getInboundPlanId(), request.getPackingGroupId(), e);
            throw new RuntimeException("创建临时文件失败", e);
        }

        //2.获取对应的sku信息
        List<MarStaPackingSkus> packingSkusList = marStaPackingSkusService
                .query()
                .eq("inbound_plan_id", request.getInboundPlanId())
                .eq("packing_group_id", request.getPackingGroupId())
                .list();
        if (CollUtil.isEmpty(packingSkusList)) {
            log.warn("没有sku信息，请求参数：inboundPlanId={}, packingGroupId={}",
                    request.getInboundPlanId(), request.getPackingGroupId());
            throw new RuntimeException("没有sku信息");
        }
        List<Long> skuIds = packingSkusList.stream().map(MarStaPackingSkus::getId).collect(Collectors.toList());

        //3.获取箱子信息
        List<MarStaCartonSpec> boxList = marStaCartonSpecService
                .query()
                .in("sku_id", skuIds)
                .list();
        List<PackingListExporter.BoxData> boxDataList = new ArrayList<>();

        Map<Long, MarStaPackingSkus> boxTimeMap = packingSkusList.stream()
                .collect(Collectors.toMap(MarStaPackingSkus::getId, e -> e));

        for (MarStaCartonSpec box : boxList) {
            MarStaPackingSkus marStaPackingSkus = boxTimeMap.get(box.getSkuId());
            if (marStaPackingSkus == null) {
                log.warn("SKU {} 对应的过期时间为空", box.getSkuId());
                continue;
            }

            try {
                for (int i = 0; i < box.getBoxQuantity(); i++) {
                    PackingListExporter.BoxData boxData = PackingListExporter.BoxData.builder()
                            .sellerSku(marStaPackingSkus.getSellerSku())
                            .boxId(IdUtil.fastUUID())
                            .boxHeightCm(box.getBoxHeightCm())
                            .boxLengthCm(box.getBoxLengthCm())
                            .boxWidthCm(box.getBoxWidthCm())
                            .boxQuantity(box.getUnitsPerBox())
                            .boxWeightKg(box.getBoxWeightKg())
                            .expiryDate(DateUtil.formatDate(marStaPackingSkus.getExpireTime()))
                            .build();
                    boxDataList.add(boxData);
                }
            } catch (ArithmeticException e) {
                log.error("数值转换异常，SKU ID: {}, 数值: {}", box.getSkuId(), box.getBoxHeightCm(), e);
                throw new RuntimeException("数值转换异常", e);
            }
        }

        PackingListExporter.exportPackingList(file,
                groupInfo.getPackingGroupId(),
                "每箱一款SKU",
                getSkuItems(packingSkusList),
                boxDataList,
                1);

        try (FileInputStream fis = new FileInputStream(file)) {
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), fis, "op/marappeal/");
            log.info("上传OSS成功，上传路径: {}, 请求参数：inboundPlanId={}, packingGroupId={}",
                    uploadPath, request.getInboundPlanId(), request.getPackingGroupId());
        } catch (IOException e) {
            log.error("读取临时文件失败，请求参数：inboundPlanId={}, packingGroupId={}",
                    request.getInboundPlanId(), request.getPackingGroupId(), e);
            throw new RuntimeException("读取临时文件失败", e);
        }

        return uploadPath;
    }

    @NotNull
    private static List<PackingListExporter.SkuItem> getSkuItems(List<MarStaPackingSkus> packingSkusList) {
        List<PackingListExporter.SkuItem> skuItems = new ArrayList<>();

        for (MarStaPackingSkus sellerSkus : packingSkusList) {
            PackingListExporter.SkuItem item = new PackingListExporter.SkuItem();
            item.setSellerSku(sellerSkus.getSellerSku());
            item.setFnSku(sellerSkus.getFnSku());
            item.setProductName(sellerSkus.getProductName());
            item.setSku(sellerSkus.getErpSku());
            item.setBoxedQuantity(sellerSkus.getBoxedQuantity()); // 逐渐增加的数量
            item.setShipmentQuantity(sellerSkus.getApplyNum());
            skuItems.add(item);
        }
        return skuItems;
    }
}




