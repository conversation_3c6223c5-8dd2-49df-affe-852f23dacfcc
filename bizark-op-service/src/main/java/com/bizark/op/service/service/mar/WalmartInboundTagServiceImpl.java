package com.bizark.op.service.service.mar;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.mar.MarWalmartInboundTagReceive;
import com.bizark.op.api.entity.op.mar.WalmartInboundTag;
import com.bizark.op.api.entity.op.walmart.WalmartInboundShipment;
import com.bizark.op.api.entity.op.walmart.WalmartInboundShipmentItem;
import com.bizark.op.api.entity.op.walmart.WalmartShipmentGtinWithShipmentId;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.mar.WalmartInboundTagService;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.ValidateUtil;
import com.bizark.op.service.mapper.mar.WalmartInboundTagMapper;
import com.bizark.op.service.mapper.walmart.WalmartInboundShipmentItemMapper;
import com.bizark.op.service.mapper.walmart.WalmartInboundShipmentMapper;
import com.bizark.op.service.util.PdfUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 下载沃尔玛的GTIN标签服务实现类
 *
 * @Author: Ailill
 * @Date: 2024/12/11 18:05
 */
@Service
@Slf4j
public class WalmartInboundTagServiceImpl extends ServiceImpl<WalmartInboundTagMapper, WalmartInboundTag> implements WalmartInboundTagService {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AccountService accountService;

    @Autowired
    private WalmartInboundShipmentMapper walmartInboundShipmentMapper;

    @Autowired
    private WalmartInboundShipmentItemMapper walmartInboundShipmentItemMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processWalmartInboundTag(String messageJson) {

        if (StringUtils.isEmpty(messageJson)) {
            log.error("download walmart inbound tag failed, message is empty");
            return;
        }
        MarWalmartInboundTagReceive receive = JSONObject.parseObject(messageJson, MarWalmartInboundTagReceive.class);
        ValidateUtil.beanValidate(receive);
        String key = "walmart-inbound-tag-receive-lock-" + receive.getGtin() + receive.getSellerSku();
        RLock lock = redissonClient.getLock(key);
        try {
            if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {

                Account account = accountService.lambdaQuery().eq(Account::getFlag, receive.getAccount()).eq(Account::getType, "walmart").eq(Account::getActive, "Y").one();
                if (account == null) {
                    log.error("download walmart inbound tag failed, account not found, message: {}", messageJson);
                    return;
                }
                List<WalmartInboundTag> saveOrUpdateList = new ArrayList<>();
                LambdaQueryWrapper<WalmartInboundTag> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(WalmartInboundTag::getGtin, receive.getGtin())
                        .eq(WalmartInboundTag::getSellerSku, receive.getSellerSku())
                        .eq(WalmartInboundTag::getOrganizationId, account.getOrgId())
                        .eq(WalmartInboundTag::getChannelId, account.getFlag());
                List<WalmartInboundTag> walmartInboundTagList = this.list(wrapper);
                MarWalmartInboundTagReceive.Type typeList = receive.getTypeList();
                if (CollectionUtil.isEmpty(walmartInboundTagList)) {
                    if (StringUtils.isNotEmpty(typeList.getOnePageOne())) {
                        WalmartInboundTag tag = new WalmartInboundTag();
                        tag.setGtin(receive.getGtin());
                        tag.setSellerSku(receive.getSellerSku());
                        tag.setChannelId(account.getFlag());
                        tag.setType("onePageOne");
                        tag.setUrl(typeList.getOnePageOne());
                        tag.setOrganizationId(account.getOrgId());
                        tag.settingDefaultSystemCreate();
                        tag.settingDefaultSystemUpdate();
                        if (!PdfUtil.isPdfUrl(tag.getUrl())) {
                            tag.setTitleChange(1);
                        }
                        saveOrUpdateList.add(tag);
                    }
                    if (StringUtils.isNotEmpty(typeList.getOnePageThirty())) {
                        WalmartInboundTag tag = new WalmartInboundTag();
                        tag.setGtin(receive.getGtin());
                        tag.setSellerSku(receive.getSellerSku());
                        tag.setChannelId(account.getFlag());
                        tag.setType("onePageThirty");
                        tag.setUrl(typeList.getOnePageThirty());
                        tag.setOrganizationId(account.getOrgId());
                        tag.settingDefaultSystemCreate();
                        tag.settingDefaultSystemUpdate();
                        if (!PdfUtil.isPdfUrl(tag.getUrl())) {
                            tag.setTitleChange(1);
                        }
                        saveOrUpdateList.add(tag);
                    }

                } else {
                    if (StringUtils.isNotEmpty(typeList.getOnePageOne())) {
                        WalmartInboundTag tag = new WalmartInboundTag();
                        tag.setGtin(receive.getGtin());
                        tag.setSellerSku(receive.getSellerSku());
                        tag.setChannelId(account.getFlag());
                        tag.setType("onePageOne");
                        tag.setUrl(typeList.getOnePageOne());
                        tag.setOrganizationId(account.getOrgId());
                        WalmartInboundTag fromDb = walmartInboundTagList.stream().filter(w -> w.getChannelId().equals(tag.getChannelId()) && w.getGtin().equals(tag.getGtin()) && w.getSellerSku().equals(tag.getSellerSku()) && w.getOrganizationId().equals(tag.getOrganizationId()) && w.getType().equals(tag.getType())).findFirst().orElse(null);
                        if (fromDb == null) {
                            tag.settingDefaultSystemCreate();
                        } else {
                            tag.setId(fromDb.getId());
                            if (fromDb.getTitleChange() != null && fromDb.getTitleChange() == 1) {
                                tag.setTitleChange(0);
                            }
                        }
                        if (!PdfUtil.isPdfUrl(tag.getUrl())) {
                            tag.setTitleChange(1);
                        }
                        tag.settingDefaultSystemUpdate();
                        saveOrUpdateList.add(tag);
                    }
                    if (StringUtils.isNotEmpty(typeList.getOnePageThirty())) {
                        WalmartInboundTag tag = new WalmartInboundTag();
                        tag.setGtin(receive.getGtin());
                        tag.setSellerSku(receive.getSellerSku());
                        tag.setChannelId(account.getFlag());
                        tag.setType("onePageThirty");
                        tag.setUrl(typeList.getOnePageThirty());
                        tag.setOrganizationId(account.getOrgId());
                        WalmartInboundTag fromDb = walmartInboundTagList.stream().filter(w -> w.getChannelId().equals(tag.getChannelId()) && w.getGtin().equals(tag.getGtin()) && w.getSellerSku().equals(tag.getSellerSku()) && w.getOrganizationId().equals(tag.getOrganizationId()) && w.getType().equals(tag.getType())).findFirst().orElse(null);
                        if (fromDb == null) {
                            tag.settingDefaultSystemCreate();
                        } else {
                            tag.setId(fromDb.getId());
                            if (fromDb.getTitleChange() != null && fromDb.getTitleChange() == 1) {
                                tag.setTitleChange(0);
                            }
                        }
                        if (!PdfUtil.isPdfUrl(tag.getUrl())) {
                            tag.setTitleChange(1);
                        }
                        tag.settingDefaultSystemUpdate();
                        saveOrUpdateList.add(tag);
                    }

                }

                if (CollectionUtil.isNotEmpty(saveOrUpdateList)) {
                    if (saveOrUpdateList.stream().anyMatch(t -> new Integer(1).equals(t.getTitleChange()))) {
                        saveOrUpdateList.forEach(t -> t.setTitleChange(1));
                    }
                    this.saveOrUpdateBatch(saveOrUpdateList);
                }
            }
        } catch (Exception e) {
            log.error("processWalmartInboundTag error", e);
            throw new CommonException(e.getMessage());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void skuMappingTitleChange(String messageJson) {
        if (StringUtils.isEmpty(messageJson)) {
            log.error("sku mapping title change failed, message is empty");
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(messageJson, JSONObject.class);
        String accountId = jsonObject.getString("account_id");
        String gtin = jsonObject.getString("gtin");
        String sellerSku = jsonObject.getString("seller_sku");
        if (StringUtils.isEmpty(accountId) || StringUtils.isEmpty(gtin) || StringUtils.isEmpty(sellerSku)) {
            log.error("sku mapping title change failed, account_id or gtin or seller_sku is empty, message: {}", messageJson);
            return;
        }
        LambdaQueryWrapper<WalmartInboundTag> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WalmartInboundTag::getChannelId, accountId)
                .eq(WalmartInboundTag::getSellerSku, sellerSku)
                .eq(WalmartInboundTag::getGtin, gtin);
        List<WalmartInboundTag> list = this.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            log.error("sku mapping title change failed, walmart inbound tag not found, message: {}", messageJson);
            return;
        }
        list.forEach(t -> t.setTitleChange(1));
        this.updateBatchById(list);
    }


    @Override
    public List<WalmartShipmentGtinWithShipmentId> getGtinByShipmentIdList(List<String> shipmentIdList) {
        List<WalmartShipmentGtinWithShipmentId> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(shipmentIdList)) {
            return Collections.emptyList();
        }
        List<WalmartInboundShipment> walmartInboundShipments = walmartInboundShipmentMapper.selectList(new LambdaQueryWrapper<WalmartInboundShipment>()
                .in(WalmartInboundShipment::getShipmentId, shipmentIdList));
        if (CollectionUtil.isEmpty(walmartInboundShipments)) {
            return result;
        }
        List<WalmartInboundShipmentItem> items = walmartInboundShipmentItemMapper.selectList(new LambdaQueryWrapper<WalmartInboundShipmentItem>()
                .in(WalmartInboundShipmentItem::getShipmentId, shipmentIdList));
        if (CollectionUtil.isEmpty(items)) {
            return result;
        }

        List<WalmartInboundTag> tagList = this.list(new LambdaQueryWrapper<WalmartInboundTag>()
                .in(WalmartInboundTag::getChannelId, walmartInboundShipments.stream().filter(t -> StringUtils.isNotEmpty(t.getChannelId())).map(t -> t.getChannelId()).distinct().collect(Collectors.toList()))
                .in(WalmartInboundTag::getSellerSku, items.stream().filter(t -> StringUtils.isNotEmpty(t.getSku())).map(t -> t.getSku()).distinct().collect(Collectors.toList()))
                .in(WalmartInboundTag::getGtin, items.stream().filter(t -> StringUtils.isNotEmpty(t.getGtin())).map(t -> t.getGtin()).distinct().collect(Collectors.toList()))
                .eq(WalmartInboundTag::getType, "onePageOne"));
        if (CollectionUtil.isEmpty(tagList)) {
            return result;
        }
        for (WalmartInboundShipment shipment : walmartInboundShipments) {

            List<WalmartInboundShipmentItem> shipmentItems = items.stream().filter(t -> shipment.getShipmentId().equals(t.getShipmentId())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(shipmentItems)) {
                continue;
            }
            List<WalmartInboundTag> matchTagList = tagList.stream().filter(t -> shipment.getChannelId().equals(t.getChannelId()) && shipmentItems.stream().anyMatch(i -> i.getSku().equals(t.getSellerSku()) && i.getGtin().equals(t.getGtin()))).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(matchTagList)) {
                continue;
            }
            WalmartShipmentGtinWithShipmentId withShipmentId = new WalmartShipmentGtinWithShipmentId();
            withShipmentId.setShipmentId(shipment.getShipmentId());
            withShipmentId.setTagList(matchTagList);
            result.add(withShipmentId);

        }
        return result;
    }
}
