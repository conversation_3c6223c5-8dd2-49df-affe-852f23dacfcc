package com.bizark.op.service.service.mar;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.fbb.api.enm.TransferAttachmentEnum;
import com.bizark.fbb.api.entity.fbb.attachment.CommonAttachInfoItemRequest;
import com.bizark.fbb.api.entity.fbb.transfer.TransferOutboundPlanEntity;
import com.bizark.fbb.api.entity.fbb.transfer.TransferPlanAttachInfoRequest;
import com.bizark.fbb.api.service.fbbtransfer.TransferOutboundPlanService;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.enm.mar.MarWalmartInboundShipmentType;
import com.bizark.op.api.enm.mar.MarWalmartTagType;
import com.bizark.op.api.enm.sale.walmart.WalmartUrlEnum;
import com.bizark.op.api.enm.walmart.ShipmentTypeEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.mar.WalmartInboundTag;
import com.bizark.op.api.entity.op.mar.WalmartShipmentAttach;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.walmart.WalmartInboundShipment;
import com.bizark.op.api.entity.op.walmart.WalmartInboundShipmentItem;
import com.bizark.op.api.entity.op.walmart.WalmartShipmentGtin;
import com.bizark.op.api.entity.op.walmart.vo.WalmartInboundShipmentAttachInfo;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.mar.WalmartShipmentAttachService;
import com.bizark.op.api.service.walmart.WalmartInboundShipmentService;
import com.bizark.op.common.util.*;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.api.WalmartApi;
import com.bizark.op.service.mapper.mar.WalmartInboundTagMapper;
import com.bizark.op.service.mapper.mar.WalmartShipmentAttachMapper;
import com.bizark.op.service.mapper.walmart.WalmartInboundShipmentItemMapper;
import com.bizark.op.service.mapper.walmart.WalmartInboundShipmentMapper;
import com.bizark.op.service.util.PdfUtil;
import com.bizark.op.service.util.third.qywx.QywxUtil;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.bizark.usercenter.api.service.UserService;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * walmart 货件附件服务实现类
 *
 * @Author: Ailill
 * @Date: 2024/12/13 18:08
 */
@Service
@Slf4j
public class WalmartShipmentAttachServiceImpl extends ServiceImpl<WalmartShipmentAttachMapper, WalmartShipmentAttach> implements WalmartShipmentAttachService {

    @Autowired
    @Lazy
    private WalmartInboundShipmentService walmartInboundShipmentService;

    @Autowired
    private WalmartInboundShipmentMapper walmartInboundShipmentMapper;

    @Autowired
    private WalmartInboundShipmentItemMapper walmartInboundShipmentItemMapper;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private WalmartApi walmartApi;

    @Autowired
    private AccountService accountService;

    @Autowired
    private WalmartInboundTagMapper walmartInboundTagMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private TransferOutboundPlanService transferOutboundPlanService;

    @Value("${enviorment}")
    private String env;


    @Autowired
    private TaskCenterService taskCenterService;



    /**
     * @Description: 定时拉取托盘标和箱标：
     * 拉取前提条件：1：提货方式不为空 2 货件ID对应的托盘数和箱数不为空且未发生变化（托盘数和箱数发生变化时且无托盘标或者箱标时打个标记，不发送变化但是无托盘标或者箱标时不打）
     * （分2次获取到pallet label（loadtype是pallet，count取托盘数）
     * * 和carton label（货件的SellerSKU单个loadtype就是single sku，多个loadtype就是mixed sku，count取箱数）。
     * 拉取完成后更新托盘数或箱数的变化标记为否
     * *
     * @Author: wly
     * @Date: 2024/12/13 18:17
     * @Params: []
     * @Return: void
     **/
    @Override
    @Async("threadPoolTaskExecutor")
    public void updateSyncAttachmentByPalletLabelAndCartonLabel() {

        int pageNum = 1;
        int pageSize = 200;
        /*//查询提货方式不为空，and ((拖盘数不为空and 拖盘数发生变化) or (箱数不为空 and 箱数发生变化))*/
        //承运方为平台承运且货运类型为(LTL/FTL)且（托盘数或箱数不为空 且 发生变化）
        LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WalmartInboundShipment::getShipmentCarrier, Arrays.asList(1,2))
                .in(WalmartInboundShipment::getShippingType, Arrays.asList(ShipmentTypeEnum.LTL.getValue(), ShipmentTypeEnum.FTL.getValue()))
                .and(q -> q.and(w -> w.isNotNull(WalmartInboundShipment::getPalletNumber).gt(WalmartInboundShipment::getPalletNumber,0).eq(WalmartInboundShipment::getPalletTagIsChanged, 1))
                        .or(t -> t.isNotNull(WalmartInboundShipment::getCartonNumber).eq(WalmartInboundShipment::getCartonTagIsChanged, 1)));
        //需要保存或更新的货件附件
        List<WalmartShipmentAttach> needSaveOrUpdateList = new CopyOnWriteArrayList<>();
        List<WalmartInboundShipment> changePalletList = new CopyOnWriteArrayList<>();
        List<WalmartInboundShipment> changeCartonList = new CopyOnWriteArrayList<>();
        List<WalmartShipmentAttach> needSecondPullList = new CopyOnWriteArrayList<>();
        while (true) {
            PageHelper.startPage(pageNum, pageSize);
            List<WalmartInboundShipment> walmartInboundShipments = walmartInboundShipmentMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(walmartInboundShipments)) {
                break;
            }
            List<Account> accountList = accountService.list(Wrappers.lambdaQuery(Account.class)
                    .in(Account::getFlag, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                    .in(Account::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .eq(Account::getActive, "Y"));
            if (CollectionUtil.isEmpty(accountList)) {
                log.error("没有找到对应的账户信息，无法进行拉取托盘标和箱标");
                break;
            }

            LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WalmartShipmentAttach::getShipmentId, walmartInboundShipments.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()))
                    .in(WalmartShipmentAttach::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .in(WalmartShipmentAttach::getType, Arrays.asList(MarWalmartTagType.PALLET.getType(), MarWalmartTagType.CARTON.getType()));
            //已有的货件附件
            List<WalmartShipmentAttach> existWalmartShipmentAttaches = this.list(wrapper);


            //取出托盘数不为空和发生变化的
            List<WalmartInboundShipment> palletLabelList = walmartInboundShipments.stream()
                    .filter(w -> w.getPalletNumber() != null && w.getPalletNumber() > 0 && w.getPalletTagIsChanged())
                    .collect(Collectors.toList());
            //取出箱数不为空和发生变化的
            List<WalmartInboundShipment> cartonLabelList = walmartInboundShipments.stream()
                    .filter(w -> w.getCartonNumber() != null && w.getCartonTagIsChanged())
                    .collect(Collectors.toList());

            CompletableFuture<Void> palletLabelFuture = CompletableFuture.runAsync(() -> {
                if (CollectionUtil.isNotEmpty(palletLabelList)) {

                    List<List<WalmartInboundShipment>> partition = ListUtils.partition(palletLabelList, (int) Math.ceil(palletLabelList.size() / 10.0));
                    List<CompletableFuture<Void>> futureList = new ArrayList<>();
                    for (List<WalmartInboundShipment> subList : partition) {
                        futureList.add(CompletableFuture.runAsync(() -> {
                            subList.forEach(inboundShipment -> {
                                JSONObject paramsJson = new JSONObject();
                                paramsJson.put("shipmentId", inboundShipment.getShipmentId());
                                JSONArray loadTypes = new JSONArray();
                                JSONObject loadType = new JSONObject();
                                loadType.put("loadType", "PALLET");
                                loadType.put("count", inboundShipment.getPalletNumber());
                                loadTypes.add(loadType);
                                paramsJson.put("loadTypes", loadTypes);
                                Account account = accountList.stream().filter(t -> t.getOrgId().equals(inboundShipment.getOrgId()) && t.getFlag().equals(inboundShipment.getChannelId())).findFirst().orElse(null);
                                if (account != null) {
                                    try {
                                        String url = walmartApi.walmartShipment(account, WalmartUrlEnum.WALMART_CREATE_INBOUND_SHIPMENT_LABEL.getValue(), paramsJson.toJSONString(), inboundShipment.getShipmentId() + "-pallet label.pdf", "erp/attachPallet");
                                        //TODO 测试
//                                    String url = inboundShipment.getShipmentId() + "-pallet label.pdf";
                                        if (StringUtils.isNotEmpty(url)) {
                                            WalmartShipmentAttach walmartShipmentAttach = new WalmartShipmentAttach();
                                            walmartShipmentAttach.setShipmentId(inboundShipment.getShipmentId());
                                            walmartShipmentAttach.setOrgId(inboundShipment.getOrgId());
                                            walmartShipmentAttach.setType(MarWalmartTagType.PALLET.getType());
                                            walmartShipmentAttach.setUrl(url);
                                            walmartShipmentAttach.settingDefaultSystemUpdate();
                                            walmartShipmentAttach.setRetryCount(1);
                                            if (CollectionUtil.isNotEmpty(existWalmartShipmentAttaches)) {
                                                WalmartShipmentAttach orElse = existWalmartShipmentAttaches.stream().filter(t -> t.getShipmentId().equals(walmartShipmentAttach.getShipmentId()) && t.getOrgId().equals(walmartShipmentAttach.getOrgId()) && t.getType().equals(walmartShipmentAttach.getType())).findFirst().orElse(null);
                                                if (orElse != null) {
                                                    walmartShipmentAttach.setId(orElse.getId());
                                                } else {
                                                    walmartShipmentAttach.settingDefaultSystemCreate();
                                                }

                                            }
                                            needSaveOrUpdateList.add(walmartShipmentAttach);
                                            WalmartInboundShipment shipment = new WalmartInboundShipment();
                                            shipment.setId(inboundShipment.getId());
                                            shipment.setPalletTagIsChanged(false);
                                            changePalletList.add(shipment);
                                            needSecondPullList.add(walmartShipmentAttach);
//                                            threadPoolTaskExecutor.execute(()->this.pullPalletLabelAndCartonLabelRetry(walmartShipmentAttach,1));
                                        }


                                    } catch (Exception e) {
                                        log.error("货件信息:{},拉取托盘标签失败，原因：{}", JSONObject.toJSONString(inboundShipment), e.getMessage());
                                    }
                                }

                            });
                        }, threadPoolTaskExecutor));
                    }
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();

                }
            },threadPoolTaskExecutor);

            CompletableFuture<Void> cartonLabelFuture = CompletableFuture.runAsync(() -> {
                if (CollectionUtil.isNotEmpty(cartonLabelList)) {

                    LambdaQueryWrapper<WalmartInboundShipmentItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.in(WalmartInboundShipmentItem::getChannelId, cartonLabelList.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                            .in(WalmartInboundShipmentItem::getInboundOrderId, cartonLabelList.stream().map(WalmartInboundShipment::getInboundOrderId).distinct().collect(Collectors.toList()))
                            .in(WalmartInboundShipmentItem::getShipmentId, cartonLabelList.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()));
                    List<WalmartInboundShipmentItem> walmartInboundShipmentItems = walmartInboundShipmentItemMapper.selectList(lambdaQueryWrapper);


                    List<List<WalmartInboundShipment>> partition = ListUtils.partition(cartonLabelList, (int) Math.ceil(cartonLabelList.size() / 10.0));
                    List<CompletableFuture<Void>> futureList = new ArrayList<>();
                    for (List<WalmartInboundShipment> subList : partition) {
                        futureList.add(CompletableFuture.runAsync(() -> {
                            subList.forEach(inboundShipment -> {
                                JSONObject paramsJson = new JSONObject();
                                paramsJson.put("shipmentId", inboundShipment.getShipmentId());
                                JSONArray loadTypes = new JSONArray();
                                JSONObject loadType = new JSONObject();

                                if (CollectionUtil.isNotEmpty(walmartInboundShipmentItems)) {
                                    List<WalmartInboundShipmentItem> shipmentItems = walmartInboundShipmentItems.stream().filter(t -> inboundShipment.getChannelId().equals(t.getChannelId())
                                            && inboundShipment.getInboundOrderId().equals(t.getInboundOrderId())
                                            && inboundShipment.getShipmentId().equals(t.getShipmentId())).collect(Collectors.toList());
                                    if (CollectionUtil.isNotEmpty(shipmentItems)) {
                                        loadType.put("count", inboundShipment.getCartonNumber());
                                        loadType.put("loadType", "SINGLE SKU");
                                        loadTypes.add(loadType);
                                        paramsJson.put("loadTypes", loadTypes);
                                        Account account = accountList.stream().filter(t -> t.getOrgId().equals(inboundShipment.getOrgId()) && t.getFlag().equals(inboundShipment.getChannelId())).findFirst().orElse(null);
                                        if (account != null) {
                                            try {
//                                            String url = inboundShipment.getShipmentId() + "-carton label.pdf";
                                                //TODO
                                                String url = walmartApi.walmartShipment(account, WalmartUrlEnum.WALMART_CREATE_INBOUND_SHIPMENT_LABEL.getValue(), paramsJson.toJSONString(), inboundShipment.getShipmentId() + "-carton label.pdf", "erp/attachCarton");
                                                if (StringUtils.isNotEmpty(url)) {
                                                    WalmartShipmentAttach walmartShipmentAttach = new WalmartShipmentAttach();
                                                    walmartShipmentAttach.setShipmentId(inboundShipment.getShipmentId());
                                                    walmartShipmentAttach.setOrgId(inboundShipment.getOrgId());
                                                    walmartShipmentAttach.setType(MarWalmartTagType.CARTON.getType());
                                                    walmartShipmentAttach.setUrl(url);
                                                    walmartShipmentAttach.settingDefaultSystemUpdate();
                                                    walmartShipmentAttach.setRetryCount(1);
                                                    if (CollectionUtil.isNotEmpty(existWalmartShipmentAttaches)) {
                                                        WalmartShipmentAttach oredElse = existWalmartShipmentAttaches.stream().filter(t -> t.getShipmentId().equals(walmartShipmentAttach.getShipmentId()) && t.getOrgId().equals(walmartShipmentAttach.getOrgId()) && t.getType().equals(walmartShipmentAttach.getType())).findFirst().orElse(null);
                                                        if (oredElse != null) {
                                                            walmartShipmentAttach.setId(oredElse.getId());
                                                        } else {
                                                            walmartShipmentAttach.settingDefaultSystemCreate();
                                                        }
                                                    }
                                                    needSaveOrUpdateList.add(walmartShipmentAttach);

                                                    WalmartInboundShipment shipment = new WalmartInboundShipment();
                                                    shipment.setId(inboundShipment.getId());
                                                    shipment.setCartonTagIsChanged(false);
                                                    changeCartonList.add(shipment);
                                                    needSecondPullList.add(walmartShipmentAttach);
//                                                    threadPoolTaskExecutor.execute(()->this.pullPalletLabelAndCartonLabelRetry(walmartShipmentAttach,1));
                                                }

                                            } catch (Exception e) {
                                                log.error("货件信息:{},拉取箱标标签失败，原因：{}", JSONObject.toJSONString(inboundShipment), e.getMessage());
                                            }
                                        }
                                    }
                                }

                            });
                        }, threadPoolTaskExecutor));
                    }
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
                }
            },threadPoolTaskExecutor);

            CompletableFuture.allOf(palletLabelFuture, cartonLabelFuture).join();
            pageNum++;

        }

        if (CollectionUtil.isNotEmpty(needSaveOrUpdateList)) {
            //TODO 上传附件到fbb
            threadPoolTaskExecutor.execute(() -> this.uploadFileToFbb(needSaveOrUpdateList));
            if (needSaveOrUpdateList.size() >= 1000) {
                List<List<WalmartShipmentAttach>> partition = ListUtils.partition(needSaveOrUpdateList, 1000);
                partition.forEach(t -> {
                    this.saveOrUpdateBatch(t);
                });
            } else {
                this.saveOrUpdateBatch(needSaveOrUpdateList);
            }

        }
        if (CollectionUtil.isNotEmpty(changePalletList)) {
            if (changePalletList.size() >= 1000) {
                List<List<WalmartInboundShipment>> partition = ListUtils.partition(changePalletList, 1000);
                partition.forEach(t -> {
                    walmartInboundShipmentService.updateBatchById(t);
                });
            } else {
                walmartInboundShipmentService.updateBatchById(changePalletList);
            }

        }
        if (CollectionUtil.isNotEmpty(changeCartonList)) {
            if (changeCartonList.size() >= 1000) {
                List<List<WalmartInboundShipment>> partition = ListUtils.partition(changeCartonList, 1000);
                partition.forEach(t -> {
                    walmartInboundShipmentService.updateBatchById(t);
                });
            } else {
                walmartInboundShipmentService.updateBatchById(changeCartonList);
            }

        }

        if (CollectionUtil.isNotEmpty(needSecondPullList)) {
            threadPoolTaskExecutor.execute(() -> {
                needSecondPullList.forEach(t -> pullPalletLabelAndCartonLabelRetry(t, t.getRetryCount()));
            });
        }

    }


    /**
     * 拉取托盘标和箱标重试
     */
    public void pullPalletLabelAndCartonLabelRetry(WalmartShipmentAttach walmartShipmentAttach, Integer retryCount) {

        try {
            Thread.sleep(10000L);
            log.info("开始重新拉取货件---{}--的托盘标和箱标--第{}次重试--类型--{}", walmartShipmentAttach.getShipmentId(), retryCount, walmartShipmentAttach.getType());
            LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WalmartInboundShipment::getShipmentId, walmartShipmentAttach.getShipmentId())
                    .eq(WalmartInboundShipment::getOrgId, walmartShipmentAttach.getOrgId())
                    .last("limit 1");
            WalmartInboundShipment one = walmartInboundShipmentService.getOne(queryWrapper);
            if (one == null) {
                log.error("没有找到对应的货件信息--无法进行拉取托盘标和箱标--{}---第{}次重试", JSONObject.toJSONString(walmartShipmentAttach), retryCount);
                return;
            }
            if (PdfUtil.isPdfUrl(walmartShipmentAttach.getUrl())) {
                log.info("已经是pdf格式的标签--无需重新拉取--{}--第{}次重试", walmartShipmentAttach.getShipmentId(), retryCount);
                return;
            }
            if (retryCount <= 1) {
                log.info("拉取失败重新拉取货件---{}--的托盘标和箱标--第{}次重试", walmartShipmentAttach.getShipmentId(), retryCount);
                this.updateSyncAttachmentByPalletLabelAndCartonLabel(one.getId());
            } else {
                log.info("拉取失败重新拉取货件---{}--的托盘标和箱标--第{}次重试", walmartShipmentAttach.getShipmentId(), retryCount);
                //手动处理
                String advice = String.format("shipmentId:[%s],id:[%s]二次拉取失败", walmartShipmentAttach.getShipmentId(), one.getId());
                String phone = SpringUtils.getBean(UserService.class).getByName("Ailill").getPhone();
                QywxUtil.wxMessageSend(advice, phone);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("拉取托盘标和箱标失败后重新拉取失败，原因：{}--第{}次重试", e.getMessage(), retryCount);
        }

    }

    @Async("threadPoolTaskExecutor")
    public void updateSyncAttachmentByPalletLabelAndCartonLabel(Long id) {

        //查询提货方式不为空，and ((拖盘数不为空and 拖盘数发生变化) or (箱数不为空 and 箱数发生变化))
        LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WalmartInboundShipment::getId,Arrays.asList(id));
        //需要保存或更新的货件附件
        List<WalmartShipmentAttach> needSaveOrUpdateList = new CopyOnWriteArrayList<>();
        List<WalmartInboundShipment> changePalletList = new CopyOnWriteArrayList<>();
        List<WalmartInboundShipment> changeCartonList = new CopyOnWriteArrayList<>();
        List<WalmartShipmentAttach> needSecondPullList = new CopyOnWriteArrayList<>();

            List<WalmartInboundShipment> walmartInboundShipments = walmartInboundShipmentMapper.selectList(queryWrapper);

            List<Account> accountList = accountService.list(Wrappers.lambdaQuery(Account.class)
                    .in(Account::getFlag, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                    .in(Account::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .eq(Account::getActive, "Y"));
            if (CollectionUtil.isEmpty(accountList)) {
                log.error("没有找到对应的账户信息，无法进行拉取托盘标和箱标");
                return;
            }

            LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WalmartShipmentAttach::getShipmentId, walmartInboundShipments.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()))
                    .in(WalmartShipmentAttach::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .in(WalmartShipmentAttach::getType, Arrays.asList(MarWalmartTagType.PALLET.getType(), MarWalmartTagType.CARTON.getType()));
            //已有的货件附件
            List<WalmartShipmentAttach> existWalmartShipmentAttaches = this.list(wrapper);


            //取出托盘数不为空和发生变化的
            List<WalmartInboundShipment> palletLabelList = walmartInboundShipments.stream()
                    .filter(w -> w.getPalletNumber() != null && w.getPalletNumber() >0)
                    .collect(Collectors.toList());
            //取出箱数不为空和发生变化的
            List<WalmartInboundShipment> cartonLabelList = walmartInboundShipments.stream()
                    .filter(w -> w.getCartonNumber() != null)
                    .collect(Collectors.toList());

            CompletableFuture<Void> palletLabelFuture = CompletableFuture.runAsync(() -> {
                if (CollectionUtil.isNotEmpty(palletLabelList)) {

                    List<List<WalmartInboundShipment>> partition = ListUtils.partition(palletLabelList, (int) Math.ceil(palletLabelList.size() / 10.0));
                    List<CompletableFuture<Void>> futureList = new ArrayList<>();
                    for (List<WalmartInboundShipment> subList : partition) {
                        futureList.add(CompletableFuture.runAsync(() -> {
                            subList.forEach(inboundShipment -> {
                                JSONObject paramsJson = new JSONObject();
                                paramsJson.put("shipmentId", inboundShipment.getShipmentId());
                                JSONArray loadTypes = new JSONArray();
                                JSONObject loadType = new JSONObject();
                                loadType.put("loadType", "PALLET");
                                loadType.put("count", inboundShipment.getPalletNumber());
                                loadTypes.add(loadType);
                                paramsJson.put("loadTypes", loadTypes);
                                Account account = accountList.stream().filter(t -> t.getOrgId().equals(inboundShipment.getOrgId()) && t.getFlag().equals(inboundShipment.getChannelId())).findFirst().orElse(null);
                                if (account != null) {
                                    try {
                                        String url = walmartApi.walmartShipment(account, WalmartUrlEnum.WALMART_CREATE_INBOUND_SHIPMENT_LABEL.getValue(), paramsJson.toJSONString(), inboundShipment.getShipmentId() + "-pallet label.pdf", "erp/attachPallet");
                                        //TODO 测试
//                                    String url = inboundShipment.getShipmentId() + "-pallet label.pdf";
                                        if (StringUtils.isNotEmpty(url)) {
                                            WalmartShipmentAttach walmartShipmentAttach = new WalmartShipmentAttach();
                                            walmartShipmentAttach.setShipmentId(inboundShipment.getShipmentId());
                                            walmartShipmentAttach.setOrgId(inboundShipment.getOrgId());
                                            walmartShipmentAttach.setType(MarWalmartTagType.PALLET.getType());
                                            walmartShipmentAttach.setUrl(url);
                                            walmartShipmentAttach.settingDefaultSystemUpdate();
                                            walmartShipmentAttach.setRetryCount(2);
                                            if (CollectionUtil.isNotEmpty(existWalmartShipmentAttaches)) {
                                                WalmartShipmentAttach orElse = existWalmartShipmentAttaches.stream().filter(t -> t.getShipmentId().equals(walmartShipmentAttach.getShipmentId()) && t.getOrgId().equals(walmartShipmentAttach.getOrgId()) && t.getType().equals(walmartShipmentAttach.getType())).findFirst().orElse(null);
                                                if (orElse != null) {
                                                    walmartShipmentAttach.setId(orElse.getId());
                                                } else {
                                                    walmartShipmentAttach.settingDefaultSystemCreate();
                                                }

                                            }
                                            needSaveOrUpdateList.add(walmartShipmentAttach);
                                            WalmartInboundShipment shipment = new WalmartInboundShipment();
                                            shipment.setId(inboundShipment.getId());
                                            shipment.setPalletTagIsChanged(false);
                                            changePalletList.add(shipment);
                                            needSecondPullList.add(walmartShipmentAttach);
//                                            threadPoolTaskExecutor.execute(()->this.pullPalletLabelAndCartonLabelRetry(walmartShipmentAttach,2));
                                        }


                                    } catch (Exception e) {
                                        log.error("货件信息:{},拉取托盘标签失败，原因：{}", JSONObject.toJSONString(inboundShipment), e.getMessage());
                                    }
                                }

                            });
                        }, threadPoolTaskExecutor));
                    }
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();

                }
            },threadPoolTaskExecutor);

            CompletableFuture<Void> cartonLabelFuture = CompletableFuture.runAsync(() -> {
                if (CollectionUtil.isNotEmpty(cartonLabelList)) {

                    LambdaQueryWrapper<WalmartInboundShipmentItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.in(WalmartInboundShipmentItem::getChannelId, cartonLabelList.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                            .in(WalmartInboundShipmentItem::getInboundOrderId, cartonLabelList.stream().map(WalmartInboundShipment::getInboundOrderId).distinct().collect(Collectors.toList()))
                            .in(WalmartInboundShipmentItem::getShipmentId, cartonLabelList.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()));
                    List<WalmartInboundShipmentItem> walmartInboundShipmentItems = walmartInboundShipmentItemMapper.selectList(lambdaQueryWrapper);


                    List<List<WalmartInboundShipment>> partition = ListUtils.partition(cartonLabelList, (int) Math.ceil(cartonLabelList.size() / 10.0));
                    List<CompletableFuture<Void>> futureList = new ArrayList<>();
                    for (List<WalmartInboundShipment> subList : partition) {
                        futureList.add(CompletableFuture.runAsync(() -> {
                            subList.forEach(inboundShipment -> {
                                JSONObject paramsJson = new JSONObject();
                                paramsJson.put("shipmentId", inboundShipment.getShipmentId());
                                JSONArray loadTypes = new JSONArray();
                                JSONObject loadType = new JSONObject();

                                if (CollectionUtil.isNotEmpty(walmartInboundShipmentItems)) {
                                    List<WalmartInboundShipmentItem> shipmentItems = walmartInboundShipmentItems.stream().filter(t -> inboundShipment.getChannelId().equals(t.getChannelId())
                                            && inboundShipment.getInboundOrderId().equals(t.getInboundOrderId())
                                            && inboundShipment.getShipmentId().equals(t.getShipmentId())).collect(Collectors.toList());
                                    if (CollectionUtil.isNotEmpty(shipmentItems)) {
                                        loadType.put("count", inboundShipment.getCartonNumber());
                                        loadType.put("loadType", "SINGLE SKU");
                                        loadTypes.add(loadType);
                                        paramsJson.put("loadTypes", loadTypes);
                                        Account account = accountList.stream().filter(t -> t.getOrgId().equals(inboundShipment.getOrgId()) && t.getFlag().equals(inboundShipment.getChannelId())).findFirst().orElse(null);
                                        if (account != null) {
                                            try {
//                                            String url = inboundShipment.getShipmentId() + "-carton label.pdf";
                                                //TODO
                                                String url = walmartApi.walmartShipment(account, WalmartUrlEnum.WALMART_CREATE_INBOUND_SHIPMENT_LABEL.getValue(), paramsJson.toJSONString(), inboundShipment.getShipmentId() + "-carton label.pdf", "erp/attachCarton");
                                                if (StringUtils.isNotEmpty(url)) {
                                                    WalmartShipmentAttach walmartShipmentAttach = new WalmartShipmentAttach();
                                                    walmartShipmentAttach.setShipmentId(inboundShipment.getShipmentId());
                                                    walmartShipmentAttach.setOrgId(inboundShipment.getOrgId());
                                                    walmartShipmentAttach.setType(MarWalmartTagType.CARTON.getType());
                                                    walmartShipmentAttach.setUrl(url);
                                                    walmartShipmentAttach.settingDefaultSystemUpdate();
                                                    walmartShipmentAttach.setRetryCount(2);
                                                    if (CollectionUtil.isNotEmpty(existWalmartShipmentAttaches)) {
                                                        WalmartShipmentAttach oredElse = existWalmartShipmentAttaches.stream().filter(t -> t.getShipmentId().equals(walmartShipmentAttach.getShipmentId()) && t.getOrgId().equals(walmartShipmentAttach.getOrgId()) && t.getType().equals(walmartShipmentAttach.getType())).findFirst().orElse(null);
                                                        if (oredElse != null) {
                                                            walmartShipmentAttach.setId(oredElse.getId());
                                                        } else {
                                                            walmartShipmentAttach.settingDefaultSystemCreate();
                                                        }
                                                    }
                                                    needSaveOrUpdateList.add(walmartShipmentAttach);

                                                    WalmartInboundShipment shipment = new WalmartInboundShipment();
                                                    shipment.setId(inboundShipment.getId());
                                                    shipment.setCartonTagIsChanged(false);
                                                    changeCartonList.add(shipment);
                                                    needSecondPullList.add(walmartShipmentAttach);
//                                                    threadPoolTaskExecutor.execute(()->this.pullPalletLabelAndCartonLabelRetry(walmartShipmentAttach,2));
                                                }

                                            } catch (Exception e) {
                                                log.error("货件信息:{},拉取箱标标签失败，原因：{}", JSONObject.toJSONString(inboundShipment), e.getMessage());
                                            }
                                        }
                                    }
                                }

                            });
                        }, threadPoolTaskExecutor));
                    }
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();

                }
            },threadPoolTaskExecutor);

            CompletableFuture.allOf(palletLabelFuture, cartonLabelFuture).join();




        if (CollectionUtil.isNotEmpty(needSaveOrUpdateList)) {
            //TODO 上传附件到fbb
            threadPoolTaskExecutor.execute(() -> this.uploadFileToFbb(needSaveOrUpdateList));
            if (needSaveOrUpdateList.size() >= 1000) {
                List<List<WalmartShipmentAttach>> partition = ListUtils.partition(needSaveOrUpdateList, 1000);
                partition.forEach(t -> {
                    this.saveOrUpdateBatch(t);
                });
            } else {
                this.saveOrUpdateBatch(needSaveOrUpdateList);
            }

        }
        if (CollectionUtil.isNotEmpty(changePalletList)) {
            if (changePalletList.size() >= 1000) {
                List<List<WalmartInboundShipment>> partition = ListUtils.partition(changePalletList, 1000);
                partition.forEach(t -> {
                    walmartInboundShipmentService.updateBatchById(t);
                });
            } else {
                walmartInboundShipmentService.updateBatchById(changePalletList);
            }

        }
        if (CollectionUtil.isNotEmpty(changeCartonList)) {
            if (changeCartonList.size() >= 1000) {
                List<List<WalmartInboundShipment>> partition = ListUtils.partition(changeCartonList, 1000);
                partition.forEach(t -> {
                    walmartInboundShipmentService.updateBatchById(t);
                });
            } else {
                walmartInboundShipmentService.updateBatchById(changeCartonList);
            }

        }


        if (CollectionUtil.isNotEmpty(needSecondPullList)) {
            threadPoolTaskExecutor.execute(() -> needSecondPullList.forEach(t -> this.pullPalletLabelAndCartonLabelRetry(t, t.getRetryCount())));
        }
    }

    public void uploadFileToFbb(List<WalmartShipmentAttach> walmartShipmentAttachList) {

        log.info("开始上传附件到fbb--{}", JSONObject.toJSONString(walmartShipmentAttachList));
        List<String> shipmentIdList = walmartShipmentAttachList.stream().map(t -> t.getShipmentId()).distinct().collect(Collectors.toList());
        List<TransferOutboundPlanEntity> allByShipmentIds = transferOutboundPlanService.findAllByShipmentIds(shipmentIdList);
        if (CollectionUtil.isEmpty(allByShipmentIds)) {
            log.info("shipmentIdList--{}---获取附件后没有找到对应的调拨单信息", shipmentIdList);
            return;
        }
        List<TransferOutboundPlanEntity> existNoCancelTransferOutboundPlan = allByShipmentIds.stream().filter(t -> StringUtils.isNotEmpty(t.getTransferOutboundPlanNo()) && t.getTransferOutboundPlanStatus() != null && !t.getTransferOutboundPlanStatus().equals(110)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(existNoCancelTransferOutboundPlan)) {
            log.info("shipmentIdList--{}---获取附件后没有找到未取消的调拨单信息", shipmentIdList);
            return;
        }
        List<TransferOutboundPlanEntity> needUploadList = new ArrayList<>();
        for (TransferOutboundPlanEntity transferOutboundPlanEntity : existNoCancelTransferOutboundPlan) {
            walmartShipmentAttachList.stream().filter(t -> transferOutboundPlanEntity.getOrganizationId().equals(t.getOrgId())
                    && transferOutboundPlanEntity.getShipmentId().equals(t.getShipmentId())).findFirst().ifPresent(r -> {
                needUploadList.add(transferOutboundPlanEntity);
            });
        }
        if (CollectionUtil.isEmpty(needUploadList)) {
            log.info("shipmentIdList--{}---获取附件后没有找到需要上传的调拨单信息", shipmentIdList);
            return;
        }
        UserEntity userEntity = new UserEntity();
        userEntity.setId(-1);
        userEntity.setName("system");
        needUploadList.forEach(t -> {
            List<CommonAttachInfoItemRequest> attachInfoItemRequestList = new ArrayList<>();
            List<WalmartShipmentAttach> attachList = walmartShipmentAttachList.stream().filter(r -> t.getOrganizationId().equals(r.getOrgId()) && t.getShipmentId().equals(r.getShipmentId())).collect(Collectors.toList());
            for (WalmartShipmentAttach walmartShipmentAttach : attachList) {
                CommonAttachInfoItemRequest itemRequest = new CommonAttachInfoItemRequest();
                itemRequest.setUrl(walmartShipmentAttach.getUrl().replace(" ", "%20"));
                if (MarWalmartTagType.CARTON.getType().equals(walmartShipmentAttach.getType())) {
                    itemRequest.setType(TransferAttachmentEnum.Cartonlabel.value());
                    itemRequest.setName(walmartShipmentAttach.getUrl().split("attachCarton")[1]);
                } else if (MarWalmartTagType.PALLET.getType().equals(walmartShipmentAttach.getType())) {
                    itemRequest.setType(TransferAttachmentEnum.Palletlabel.value());
                    itemRequest.setName(walmartShipmentAttach.getUrl().split("attachPallet")[1]);
                } else if (MarWalmartTagType.BOL.getType().equals(walmartShipmentAttach.getType())) {
                    itemRequest.setType(TransferAttachmentEnum.Bol.value());
                    itemRequest.setName(walmartShipmentAttach.getUrl().substring(walmartShipmentAttach.getUrl().lastIndexOf("/") + 1));
                }
                attachInfoItemRequestList.add(itemRequest);
            }

            TransferPlanAttachInfoRequest transferPlanAttachInfoRequest = new TransferPlanAttachInfoRequest();
            transferPlanAttachInfoRequest.setTransferOutboundPlanNo(t.getTransferOutboundPlanNo());
            transferPlanAttachInfoRequest.setAttachInfoItemRequestList(attachInfoItemRequestList);
            log.info("开始上传附件到调拨单--{}--附件信息--{}", t.getTransferOutboundPlanNo(), JSONObject.toJSONString(attachInfoItemRequestList));
//            transferPlanAttachInfoRequest.setIsTypeCover(true);//不覆盖只更新
            try {
                transferOutboundPlanService.easAttachUpload(userEntity, t.getOrganizationId(), env, transferPlanAttachInfoRequest);
                log.info("上传附件到调拨单--{}成功--{}", t.getTransferOutboundPlanNo(), JSONObject.toJSONString(attachInfoItemRequestList));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("上传附件到调拨单--{}失败--{}", t.getTransferOutboundPlanNo(), e.getMessage());
            }




        });

    }

    /**
     * @Description: 定时拉取BOL标：（拉取完成后，另起任务调用接口上传BOL.pdf拿到BOL编号更新）
     * 拉取前提条件：1 货件中有carrier(即walmart_inbound_shipment. carrier_name不为空)
     * 2.货件对应的BOL标不存在 (BOL编号存在，BOL标签一定存在，BOL编号不存在，BOL标签可能存在或不存在)
     * @Author: wly
     * @Date: 2024/12/13 18:18
     * @Params: []
     * @Return: void
     **/
    @Override
    public void updateSyncAttachmentByCarrierLabel() {

        /*int pageNum = 1;
        int pageSize = 200;
        //查询carrier不为空，and BOL标签不存在
        LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(WalmartInboundShipment::getCarrierName)
                .ne(WalmartInboundShipment::getCarrierName, "")
                .eq(WalmartInboundShipment::getBolTagExist, false);
        //需要保存或更新的货件附件
        List<WalmartShipmentAttach> needSaveOrUpdateList = new CopyOnWriteArrayList<>();

        List<Long> shipmentIdList = new CopyOnWriteArrayList<>();
        while (true) {
            PageHelper.startPage(pageNum, pageSize);
            List<WalmartInboundShipment> walmartInboundShipments = walmartInboundShipmentMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(walmartInboundShipments)) {
                break;
            }
            List<Account> accountList = accountService.list(Wrappers.lambdaQuery(Account.class)
                    .in(Account::getFlag, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                    .in(Account::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .eq(Account::getActive, "Y"));
            if (CollectionUtil.isEmpty(accountList)) {
                log.error("没有找到对应的账户信息，无法进行拉取BOL标签");
                break;
            }

            LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WalmartShipmentAttach::getShipmentId, walmartInboundShipments.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()))
                    .in(WalmartShipmentAttach::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .eq(WalmartShipmentAttach::getType, MarWalmartTagType.BOL.getType());
            //已有的货件附件
            List<WalmartShipmentAttach> existWalmartShipmentAttaches = this.list(wrapper);


            if (CollectionUtil.isNotEmpty(walmartInboundShipments)) {

                List<List<WalmartInboundShipment>> partition = ListUtils.partition(walmartInboundShipments, (int) Math.ceil(walmartInboundShipments.size() / 10.0));
                List<CompletableFuture<Void>> futureList = new ArrayList<>();
                for (List<WalmartInboundShipment> subList : partition) {
                    futureList.add(CompletableFuture.runAsync(() -> {
                        subList.forEach(inboundShipment -> {
                            JSONObject paramsJson = new JSONObject();
                            //TOdo 日期
                            paramsJson.put("shipDate", DateUtil.convertDateToString(inboundShipment.getCreatedDate(), "yyyy-MM-dd'T'HH:mm:ss'Z'"));

                            Account account = accountList.stream().filter(t -> t.getOrgId().equals(inboundShipment.getOrgId()) && t.getFlag().equals(inboundShipment.getChannelId())).findFirst().orElse(null);
                            if (account != null) {
                                try {
                                    String url = walmartApi.walmartShipment(account, WalmartUrlEnum.WALMART_PRINT_CARRIER_LABEL.getValue().replace("{shipmentId}", inboundShipment.getShipmentId()), paramsJson.toJSONString(), inboundShipment.getBolNumber() + "BOL.pdf", "erp/walmartShipmentsAttach");
                                    //todo
//                                    String url = inboundShipment.getShipmentId();

                                    if (StringUtils.isNotEmpty(url)) {
                                        WalmartShipmentAttach walmartShipmentAttach = new WalmartShipmentAttach();
                                        walmartShipmentAttach.setShipmentId(inboundShipment.getShipmentId());
                                        walmartShipmentAttach.setOrgId(inboundShipment.getOrgId());
                                        walmartShipmentAttach.setType(MarWalmartTagType.BOL.getType());
                                        walmartShipmentAttach.setUrl(url);
                                        walmartShipmentAttach.settingDefaultSystemUpdate();
                                        if (CollectionUtil.isNotEmpty(existWalmartShipmentAttaches)) {
                                            WalmartShipmentAttach oredElse = existWalmartShipmentAttaches.stream().filter(t -> t.getShipmentId().equals(walmartShipmentAttach.getShipmentId()) && t.getOrgId().equals(walmartShipmentAttach.getOrgId()) && t.getType().equals(walmartShipmentAttach.getType())).findFirst().orElse(null);
                                            if (oredElse != null) {
                                                walmartShipmentAttach.setId(oredElse.getId());
                                            } else {
                                                walmartShipmentAttach.settingDefaultSystemCreate();
                                            }
                                        }
                                        needSaveOrUpdateList.add(walmartShipmentAttach);

                                        shipmentIdList.add(inboundShipment.getId());
                                    }


                                } catch (Exception e) {
                                    log.error("货件信息:{},拉取BOL标签失败，原因：{}", JSONObject.toJSONString(inboundShipment), e.getMessage());
                                }
                            }

                        });
                    }, threadPoolTaskExecutor));
                }
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();

            }

            pageNum++;

        }

        if (CollectionUtil.isNotEmpty(needSaveOrUpdateList)) {
            this.saveOrUpdateBatch(needSaveOrUpdateList);
        }

        if (CollectionUtil.isNotEmpty(shipmentIdList)) {
            walmartInboundShipmentService.lambdaUpdate()
                    .in(WalmartInboundShipment::getId, shipmentIdList)
                    .set(WalmartInboundShipment::getBolTagExist, true)
                    .set(WalmartInboundShipment::getUpdatedAt, DateUtils.getNowDate())
                    .set(WalmartInboundShipment::getUpdatedBy, -1)
                    .set(WalmartInboundShipment::getUpdatedName, "system")
                    .update();
        }*/
    }


    /**
     * @Description: 定时更新同步附件是否完整
     * 查询条件 1承运方为为平台承运 且 同步附件已完整字段为否的   ，如果托盘标，箱标，BOL标，GTIN标此时都有的话，更新为是
     * 2.承运方为自约承运，且 同步附件为已完整字段为否的，  箱标，托盘标，GTIN标都有的话，更新为是。
     * @Author: wly
     * @Date: 2024/12/16 14:42
     * @Params: []
     * @Return: void
     **/
    @Override
    public void updateSyncAttachComplete() {

        int pageNum = 1;
        int pageSize = 5000;
        LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WalmartInboundShipment::getSyncAttachmentComplete, 0)
                .isNotNull(WalmartInboundShipment::getPalletNumber)
                .isNotNull(WalmartInboundShipment::getCartonNumber)
                .and(i -> i.eq(WalmartInboundShipment::getShipmentCarrier, 1)
                        .or(t -> t.eq(WalmartInboundShipment::getShipmentCarrier, 2)));
        List<Long> shipmentIdList = new ArrayList<>();
        while (true) {

            PageHelper.startPage(pageNum, pageSize);
            //货件信息
            List<WalmartInboundShipment> walmartInboundShipments = walmartInboundShipmentMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(walmartInboundShipments)) {
                break;
            }
            //货件明细
            List<WalmartInboundShipmentItem> walmartInboundShipmentItemList = walmartInboundShipmentItemMapper.selectList(Wrappers.lambdaQuery(WalmartInboundShipmentItem.class)
                    .in(WalmartInboundShipmentItem::getChannelId, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                    .in(WalmartInboundShipmentItem::getInboundOrderId, walmartInboundShipments.stream().map(WalmartInboundShipment::getInboundOrderId).distinct().collect(Collectors.toList()))
                    .in(WalmartInboundShipmentItem::getShipmentId, walmartInboundShipments.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList())));

            if (CollectionUtil.isEmpty(walmartInboundShipmentItemList)) {
                break;
            }
            LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WalmartShipmentAttach::getShipmentId, walmartInboundShipments.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()))
                    .in(WalmartShipmentAttach::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()));
            //货件附件
            List<WalmartShipmentAttach> walmartShipmentAttaches = this.list(wrapper);
            if (CollectionUtil.isEmpty(walmartShipmentAttaches)) {
                break;
            }
            LambdaQueryWrapper<WalmartInboundTag> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.in(WalmartInboundTag::getChannelId, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                    .in(WalmartInboundTag::getSellerSku, walmartInboundShipmentItemList.stream().map(WalmartInboundShipmentItem::getSku).distinct().collect(Collectors.toList()))
                    .in(WalmartInboundTag::getGtin, walmartInboundShipmentItemList.stream().map(WalmartInboundShipmentItem::getGtin).distinct().collect(Collectors.toList()));
            //货件GTIN标签
            List<WalmartInboundTag> walmartInboundTags = walmartInboundTagMapper.selectList(wrapper1);
            if (CollectionUtil.isEmpty(walmartInboundTags)) {
                break;
            }

           /* walmartInboundTags = walmartInboundTags.stream().filter(r -> walmartInboundShipmentItemList.stream().anyMatch(q -> r.getSellerSku().equals(q.getSku()) && r.getGtin().equals(q.getGtin()))).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(walmartInboundTags)) {
                break;
            }*/
            for (WalmartInboundShipment t : walmartInboundShipments) {
                //判断托盘标，箱标，GTIN标是否都有
                List<String> palletTagList = Arrays.asList(MarWalmartTagType.CARTON.getType(), MarWalmartTagType.PALLET.getType());
                List<String> cartonTagList = Arrays.asList(MarWalmartTagType.CARTON.getType(), MarWalmartTagType.PALLET.getType(), MarWalmartTagType.BOL.getType());
                List<WalmartShipmentAttach> collect = walmartShipmentAttaches.stream().filter(w -> w.getShipmentId().equals(t.getShipmentId()) && w.getOrgId().equals(t.getOrgId())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    continue;
                }
                //承运方为平台承运 ，需满足托盘标，箱标，BOL标，GTIN标都有
                if (new Integer(1).equals(t.getShipmentCarrier())) {
                    Map<String, List<WalmartShipmentAttach>> stringListMap = collect.stream().filter(q -> cartonTagList.contains(q.getType())).collect(Collectors.groupingBy(WalmartShipmentAttach::getType));
                    if (CollectionUtil.isEmpty(stringListMap) || stringListMap.size() < 3) {
                        continue;
                    }
                } else {
                    //承运方为自约承运，需满足托盘标，箱标，GTIN标都有
                    Map<String, List<WalmartShipmentAttach>> stringListMap = collect.stream().filter(q -> palletTagList.contains(q.getType())).collect(Collectors.groupingBy(WalmartShipmentAttach::getType));
                    if (CollectionUtil.isEmpty(stringListMap) || stringListMap.size() < 2) {
                        continue;
                    }
                }

                List<WalmartInboundShipmentItem> shipmentItems = walmartInboundShipmentItemList.stream().filter(w -> t.getChannelId().equals(w.getChannelId()) && t.getInboundOrderId().equals(w.getInboundOrderId()) && t.getShipmentId().equals(w.getShipmentId()) && StringUtils.isNotEmpty(w.getSku())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(shipmentItems)) {
                    continue;
                }
                boolean gtinTagExist = true;
                //sellerSku gtin 在gtin标签中全部存在
                for (WalmartInboundShipmentItem item : shipmentItems) {
                    boolean b = walmartInboundTags.stream().anyMatch(q -> item.getChannelId().equals(q.getChannelId())
                            && item.getSku().equals(q.getSellerSku()) && item.getGtin().equals(q.getGtin()));
                    if (!b) {
                        gtinTagExist = false;
                        break;
                    }
                }
                if (gtinTagExist) {
                    shipmentIdList.add(t.getId());
                }
            }
            pageNum++;
        }
        if (CollectionUtil.isNotEmpty(shipmentIdList)) {
            walmartInboundShipmentService.lambdaUpdate()
                    .in(WalmartInboundShipment::getId, shipmentIdList)
                    .set(WalmartInboundShipment::getSyncAttachmentComplete, 1)
                    .set(WalmartInboundShipment::getUpdatedAt, DateUtils.getNowDate())
                    .set(WalmartInboundShipment::getUpdatedBy, -1)
                    .set(WalmartInboundShipment::getUpdatedName, "system")
                    .update();
        }
    }

    @Override
    public void testUpdateSyncAttachComplete() {

        int pageNum = 1;
        int pageSize = 5000;
        LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .isNotNull(WalmartInboundShipment::getPalletNumber)
                .isNotNull(WalmartInboundShipment::getCartonNumber)
                .and(i -> i.eq(WalmartInboundShipment::getShipmentCarrier, 1)
                        .or(t -> t.eq(WalmartInboundShipment::getShipmentCarrier, 2)));
        List<Long> shipmentIdList = new ArrayList<>();
        while (true) {

            PageHelper.startPage(pageNum, pageSize);
            //货件信息
            List<WalmartInboundShipment> walmartInboundShipments = walmartInboundShipmentMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(walmartInboundShipments)) {
                break;
            }
            //货件明细
            List<WalmartInboundShipmentItem> walmartInboundShipmentItemList = walmartInboundShipmentItemMapper.selectList(Wrappers.lambdaQuery(WalmartInboundShipmentItem.class)
                    .in(WalmartInboundShipmentItem::getChannelId, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                    .in(WalmartInboundShipmentItem::getInboundOrderId, walmartInboundShipments.stream().map(WalmartInboundShipment::getInboundOrderId).distinct().collect(Collectors.toList()))
                    .in(WalmartInboundShipmentItem::getShipmentId, walmartInboundShipments.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList())));

            if (CollectionUtil.isEmpty(walmartInboundShipmentItemList)) {
                break;
            }
            LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WalmartShipmentAttach::getShipmentId, walmartInboundShipments.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()))
                    .in(WalmartShipmentAttach::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()));
            //货件附件
            List<WalmartShipmentAttach> walmartShipmentAttaches = this.list(wrapper);
            if (CollectionUtil.isEmpty(walmartShipmentAttaches)) {
                break;
            }
            LambdaQueryWrapper<WalmartInboundTag> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.in(WalmartInboundTag::getChannelId, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                    .in(WalmartInboundTag::getSellerSku, walmartInboundShipmentItemList.stream().map(WalmartInboundShipmentItem::getSku).distinct().collect(Collectors.toList()))
                    .in(WalmartInboundTag::getGtin, walmartInboundShipmentItemList.stream().map(WalmartInboundShipmentItem::getGtin).distinct().collect(Collectors.toList()));
            //货件GTIN标签
            List<WalmartInboundTag> walmartInboundTags = walmartInboundTagMapper.selectList(wrapper1);
            if (CollectionUtil.isEmpty(walmartInboundTags)) {
                break;
            }

           /* walmartInboundTags = walmartInboundTags.stream().filter(r -> walmartInboundShipmentItemList.stream().anyMatch(q -> r.getSellerSku().equals(q.getSku()) && r.getGtin().equals(q.getGtin()))).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(walmartInboundTags)) {
                break;
            }*/
            for (WalmartInboundShipment t : walmartInboundShipments) {
                //判断托盘标，箱标，GTIN标是否都有
                List<String> palletTagList = Arrays.asList(MarWalmartTagType.CARTON.getType(), MarWalmartTagType.PALLET.getType());
                List<String> cartonTagList = Arrays.asList(MarWalmartTagType.CARTON.getType(), MarWalmartTagType.PALLET.getType(), MarWalmartTagType.BOL.getType());
                List<WalmartShipmentAttach> collect = walmartShipmentAttaches.stream().filter(w -> w.getShipmentId().equals(t.getShipmentId()) && w.getOrgId().equals(t.getOrgId())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    continue;
                }
                //承运方为平台承运 ，需满足托盘标，箱标，BOL标，GTIN标都有
                if (new Integer(1).equals(t.getShipmentCarrier())) {
                    Map<String, List<WalmartShipmentAttach>> stringListMap = collect.stream().filter(q -> cartonTagList.contains(q.getType())).collect(Collectors.groupingBy(WalmartShipmentAttach::getType));
                    if (CollectionUtil.isEmpty(stringListMap) || stringListMap.size() < 3) {
                        continue;
                    }
                } else {
                    //承运方为自约承运，需满足托盘标，箱标，GTIN标都有
                    Map<String, List<WalmartShipmentAttach>> stringListMap = collect.stream().filter(q -> palletTagList.contains(q.getType())).collect(Collectors.groupingBy(WalmartShipmentAttach::getType));
                    if (CollectionUtil.isEmpty(stringListMap) || stringListMap.size() < 2) {
                        continue;
                    }
                }

                List<WalmartInboundShipmentItem> shipmentItems = walmartInboundShipmentItemList.stream().filter(w -> t.getChannelId().equals(w.getChannelId()) && t.getInboundOrderId().equals(w.getInboundOrderId()) && t.getShipmentId().equals(w.getShipmentId()) && StringUtils.isNotEmpty(w.getSku())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(shipmentItems)) {
                    continue;
                }
                boolean gtinTagExist = true;
                //sellerSku gtin 在gtin标签中全部存在
                for (WalmartInboundShipmentItem item : shipmentItems) {
                    boolean b = walmartInboundTags.stream().anyMatch(q -> item.getChannelId().equals(q.getChannelId())
                            && item.getSku().equals(q.getSellerSku()) && item.getGtin().equals(q.getGtin()));
                    if (!b) {
                        gtinTagExist = false;
                        break;
                    }
                }
                if (gtinTagExist) {
                    shipmentIdList.add(t.getId());
                }
            }
            pageNum++;
        }
        if (CollectionUtil.isNotEmpty(shipmentIdList)) {
            walmartInboundShipmentService.lambdaUpdate()
                    .in(WalmartInboundShipment::getId, shipmentIdList)
                    .set(WalmartInboundShipment::getSyncAttachmentComplete, 1)
                    .set(WalmartInboundShipment::getUpdatedAt, DateUtils.getNowDate())
                    .set(WalmartInboundShipment::getUpdatedBy, -1)
                    .set(WalmartInboundShipment::getUpdatedName, "system")
                    .update();
        }
    }


    /**
     * @Description:定时更新BOL编号(存在BOL标签的货件)
     * @Author: wly
     * @Date: 2024/12/16 19:02
     * @Params: []
     * @Return: void
     **/
    @Override
    public void updateBolNumber() {

        /*int pageNum = 1;
        int pageSize = 200;
        //查询BOL标签存在的货件
        LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WalmartInboundShipment::getBolTagExist, true)
                .and(i -> i.isNull(WalmartInboundShipment::getBolNumber).or(j -> j.eq(WalmartInboundShipment::getBolNumber, "")));
        List<WalmartInboundShipment> updateBolList = new CopyOnWriteArrayList<>();
        while (true) {
            PageHelper.startPage(pageNum, pageSize);
            List<WalmartInboundShipment> walmartInboundShipments = walmartInboundShipmentMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(walmartInboundShipments)) {
                break;
            }
            LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WalmartShipmentAttach::getShipmentId, walmartInboundShipments.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()))
                    .in(WalmartShipmentAttach::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .eq(WalmartShipmentAttach::getType, MarWalmartTagType.BOL.getType());
            List<WalmartShipmentAttach> walmartShipmentAttaches = this.list(wrapper);
            if (CollectionUtil.isEmpty(walmartShipmentAttaches)) {
                break;
            }
            List<List<WalmartInboundShipment>> partition = ListUtils.partition(walmartInboundShipments, (int) Math.ceil(walmartInboundShipments.size() / 10.0));
            List<CompletableFuture<Void>> futureList = new ArrayList<>();
            for (List<WalmartInboundShipment> subList : partition) {

                futureList.add(CompletableFuture.runAsync(() -> {
                    subList.forEach(inboundShipment -> {
                        //获取BOL标签url
                        WalmartShipmentAttach walmartShipmentAttach = walmartShipmentAttaches.stream().filter(attach -> attach.getShipmentId().equals(inboundShipment.getShipmentId()) && attach.getOrgId().equals(inboundShipment.getOrgId())).findFirst().orElse(null);
                        if (walmartShipmentAttach != null && StringUtils.isNotEmpty(walmartShipmentAttach.getUrl())) {

                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("id", inboundShipment.getId());
                            jsonObject.put("url", walmartShipmentAttach.getUrl());
                            try {
                                String result = HttpUtils.doPostByJson("http://*************:9676/api/v1/pdfBOL", JSONObject.toJSONString(jsonObject));
                                if (StringUtils.isNotEmpty(result)) {
                                    JSONObject resultJson = JSONObject.parseObject(result);
                                    if (resultJson != null && resultJson.containsKey("code") && resultJson.getIntValue("code") == 200) {
                                        String bolNumber = resultJson.getString("BOL");
                                        if (StringUtils.isNotEmpty(bolNumber)) {
                                            WalmartInboundShipment shipment = new WalmartInboundShipment();
                                            shipment.setId(inboundShipment.getId());
                                            shipment.setBolNumber(bolNumber);
                                            shipment.settingDefaultSystemUpdate();
                                            updateBolList.add(shipment);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                log.error("更新BOL编号失败，货件信息:{}, 参数:{},原因：{}", inboundShipment, JSONObject.toJSONString(jsonObject), e.getMessage());
                            }

                        }
                    });
                }, threadPoolTaskExecutor));

            }
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
            pageNum++;
        }

        if (CollectionUtil.isNotEmpty(updateBolList)) {
            walmartInboundShipmentService.updateBatchById(updateBolList);
        }*/

    }


    /**
     * @Description:定时拉取BOL标签并更新BOL编号
     * @Author: wly
     * @Date: 2024/12/19 15:47
     * @Params: []
     * @Return: void
     **/
    @Override
    public void pullBolAndUpdateBolNumber() {

        int pageNum = 1;
        int pageSize = 10000;
        /*//查询carrier不为空，and (BOL标签不存在或者BOL编号为空)的货件*/
        //承运方为平台承运且承运商 不为空且货件状态 不是"CANCELLED", "CLOSED"
        LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(WalmartInboundShipment::getCarrierName)
                .in(WalmartInboundShipment::getShipmentCarrier, Arrays.asList(1,2))
                .ne(WalmartInboundShipment::getCarrierName, "")
                .notIn(WalmartInboundShipment::getShipmentStatus, Arrays.asList("CANCELLED", "CLOSED"))
                .and(i -> i.and(j -> j.isNull(WalmartInboundShipment::getBolNumber).or(k -> k.eq(WalmartInboundShipment::getBolNumber, "")))
                        .or(l -> l.eq(WalmartInboundShipment::getBolTagExist, false)));

        while (true) {
            PageHelper.startPage(pageNum, pageSize);
            List<WalmartInboundShipment> walmartInboundShipments = walmartInboundShipmentMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(walmartInboundShipments)) {
                break;
            }
            List<Account> accountList = accountService.list(Wrappers.lambdaQuery(Account.class).in(Account::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .in(Account::getFlag, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList())));
            if (CollectionUtil.isEmpty(accountList)) {
                break;
            }
            Map<String, List<WalmartInboundShipment>> collect = walmartInboundShipments.stream().collect(Collectors.groupingBy(WalmartInboundShipment::getChannelId));
            collect.forEach((channelId, subList) -> {
                JSONObject object = new JSONObject();
                object.put("storeName", accountList.stream().filter(account -> account.getFlag().equals(channelId)).findFirst().get().getStoreName());
                object.put("shipmentIdList", subList.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()));
                log.info("发送拉取BOL标签请求，参数:{}", object);
                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.MAR_WALMART_SHIPMENT_BOL_QUEUE, object);
            });
            pageNum++;
        }
    }

    public void pullBolAndUpdateBolNumberById(Long id) {

        LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WalmartInboundShipment::getId, id);

            List<WalmartInboundShipment> walmartInboundShipments = walmartInboundShipmentMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(walmartInboundShipments)) {
                return;
            }
            List<Account> accountList = accountService.list(Wrappers.lambdaQuery(Account.class).in(Account::getOrgId, walmartInboundShipments.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .in(Account::getFlag, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList())));
            if (CollectionUtil.isEmpty(accountList)) {
                return;
            }
            Map<String, List<WalmartInboundShipment>> collect = walmartInboundShipments.stream().collect(Collectors.groupingBy(WalmartInboundShipment::getChannelId));
            collect.forEach((channelId, subList) -> {
                JSONObject object = new JSONObject();
                object.put("storeName", channelId);
                object.put("shipmentIdList", subList.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList()));
                log.info("发送拉取BOL标签请求，参数:{}", object);
                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.MAR_WALMART_SHIPMENT_BOL_QUEUE, object);
            });
    }

    @Override
    public void processBolAndBolNumber(String message) {
        if (StringUtils.isEmpty(message)) {
            log.error("WFS货件接收BOL消息为空");
            return;
        }
        JSONObject object = JSONObject.parseObject(message, JSONObject.class);
        if (!object.containsKey("storeName") || !object.containsKey("shipmentId")) {
            log.error("WFS货件接收BOL消息缺失店铺和shipmentId字段，消息内容：{}", message);
            return;
        }
        String storeName = (String) object.get("storeName");
        String shipmentId = (String) object.get("shipmentId");
        String key = storeName + "_" + shipmentId;
        RLock lock = redissonClient.getLock(key);
        try {
            if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {

                Account account = accountService.getOne(Wrappers.lambdaQuery(Account.class).eq(Account::getFlag, storeName).eq(Account::getActive, "Y").last("LIMIT 1"));
                LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WalmartInboundShipment::getShipmentId, shipmentId)
                        .eq(WalmartInboundShipment::getChannelId, account.getFlag())
                        .last("LIMIT 1");
                WalmartInboundShipment walmartInboundShipment = walmartInboundShipmentMapper.selectOne(queryWrapper);
                if (walmartInboundShipment == null) {
                    log.error("WFS货件接收BOL消息未找到对应货件信息，消息内容：{}", message);
                    return;
                }
                if (object.containsKey("url")) {
                    String url = (String) object.get("url");
                    if (StringUtils.isNotEmpty(url)) {
                        //查询是否存在BOL标签
                        LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(WalmartShipmentAttach::getShipmentId, shipmentId)
                                .eq(WalmartShipmentAttach::getOrgId, account.getOrgId())
                                .eq(WalmartShipmentAttach::getType, MarWalmartTagType.BOL.getType())
                                .last("LIMIT 1");
                        WalmartShipmentAttach walmartShipmentAttach = this.getOne(wrapper);
                        if (walmartShipmentAttach == null) {
                            //插入BOL标签
                            walmartShipmentAttach = new WalmartShipmentAttach();
                            walmartShipmentAttach.setShipmentId(shipmentId);
                            walmartShipmentAttach.setOrgId(account.getOrgId());
                            walmartShipmentAttach.setType(MarWalmartTagType.BOL.getType());
                            walmartShipmentAttach.setUrl(url);
                            walmartShipmentAttach.settingDefaultSystemCreate();
                            walmartShipmentAttach.settingDefaultSystemUpdate();
                            this.save(walmartShipmentAttach);
                        } else {
                            //更新BOL标签
                            walmartShipmentAttach.setUrl(url);
                            walmartShipmentAttach.settingDefaultSystemUpdate();
                            this.updateById(walmartShipmentAttach);
                        }

                        walmartInboundShipment.settingDefaultSystemUpdate();
                        walmartInboundShipment.setBolTagExist(true);
                        walmartInboundShipmentService.updateById(walmartInboundShipment);

                        WalmartShipmentAttach finalWalmartShipmentAttach = walmartShipmentAttach;
                        threadPoolTaskExecutor.execute(() -> {
                            try {
                                Thread.sleep(10000L);
                            } catch (Exception e) {

                            }
                            log.info("开始上传BOL标签文件到fbb--货件信息--{}", walmartInboundShipment);
                            this.uploadFileToFbb(Arrays.asList(finalWalmartShipmentAttach));
                            log.info("上传BOL标签文件到fbb完成--货件信息--{}", walmartInboundShipment);
                            this.updateSyncAttachComplete();
                        });
                    }
                }

                if (object.containsKey("bolNumber")) {
                    String bolNumber = (String) object.get("bolNumber");
                    if (StringUtils.isNotEmpty(bolNumber)) {
                        walmartInboundShipment.setBolNumber(bolNumber);
                        walmartInboundShipment.settingDefaultSystemUpdate();
                        walmartInboundShipmentService.updateById(walmartInboundShipment);
                    }
                }

            }
        } catch (Exception e) {
            log.error("WFS货件接收BOL消息获取redisson分布式锁失败，key:{},原因：{}", key, e.getMessage());
            throw new ErpCommonException(e.getMessage());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public List<WalmartShipmentGtin> getNoGtinShipment() {
        List<WalmartShipmentGtin> result = new ArrayList<>();
        List<ProductChannels> gtin = this.baseMapper.getGtin();
        if (CollectionUtil.isEmpty(gtin)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WalmartInboundTag> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WalmartInboundTag::getGtin, gtin.stream().map(ProductChannels::getGtin).distinct().collect(Collectors.toList()))
                .in(WalmartInboundTag::getSellerSku, gtin.stream().map(ProductChannels::getSellerSku).distinct().collect(Collectors.toList()))
                .eq(WalmartInboundTag::getOrganizationId, 1000049)
                .ne(WalmartInboundTag::getTitleChange,1);
        List<WalmartInboundTag> walmartInboundTagList = walmartInboundTagMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(walmartInboundTagList)) {
            Map<String, List<ProductChannels>> collect = gtin.stream().collect(Collectors.groupingBy(ProductChannels::getAccountId));
            for (Map.Entry<String, List<ProductChannels>> stringListEntry : collect.entrySet()) {
                WalmartShipmentGtin walmartShipmentGtin = new WalmartShipmentGtin();
                walmartShipmentGtin.setStoreName(stringListEntry.getKey());
                walmartShipmentGtin.setGtin(stringListEntry.getValue().stream().map(ProductChannels::getGtin).distinct().collect(Collectors.toList()));
                result.add(walmartShipmentGtin);
            }
            return result;
        }
        gtin.removeIf(t -> walmartInboundTagList.stream().anyMatch(q -> t.getAccountId().equals(q.getChannelId()) && t.getSellerSku().equals(q.getSellerSku()) && t.getGtin().equals(q.getGtin())));
        if (CollectionUtil.isEmpty(gtin)) {
            return result;
        } else {
            Map<String, List<ProductChannels>> collect = gtin.stream().collect(Collectors.groupingBy(ProductChannels::getAccountId));
            for (Map.Entry<String, List<ProductChannels>> stringListEntry : collect.entrySet()) {
                WalmartShipmentGtin walmartShipmentGtin = new WalmartShipmentGtin();
                walmartShipmentGtin.setStoreName(stringListEntry.getKey());
                walmartShipmentGtin.setGtin(stringListEntry.getValue().stream().map(ProductChannels::getGtin).distinct().collect(Collectors.toList()));
                result.add(walmartShipmentGtin);
            }
        }
        return result;
    }


    @Override
    public String downLoadAttachFileAsync(String query) {
        WalmartShipmentAttach attach = JSONObject.parseObject(query, WalmartShipmentAttach.class);
        Long id = attach.getId();
        List<String> typeList = attach.getTypeList();
        WalmartInboundShipment byId = walmartInboundShipmentService.getById(id);
        if (byId == null) {
            throw new ErpCommonException("无对应货件信息");
        }
        List<WalmartInboundShipmentAttachInfo> attachmentInfo = walmartInboundShipmentService.getNotCompleteAttachmentInfo(id);
        if (CollectionUtil.isEmpty(attachmentInfo)) {
            throw new ErpCommonException("无附件信息");
        }
        if (attachmentInfo.stream().noneMatch(WalmartInboundShipmentAttachInfo::getCompleted)) {
            throw new ErpCommonException("无附件信息");
        }
        if (CollectionUtil.isNotEmpty(typeList)) {
            attachmentInfo.removeIf(t -> !typeList.contains(t.getType()));
        }
        attachmentInfo.removeIf(t -> !t.getCompleted());
        if (CollectionUtil.isEmpty(attachmentInfo)) {
            throw new ErpCommonException("无可选类型附件信息");
        }
        try {
            String foldName = byId.getShipmentId() + "_attachments";

            Map<String, List<String>> urlMapDirList = new HashMap<>();
            for (WalmartInboundShipmentAttachInfo info : attachmentInfo) {
                String fileName = MarWalmartTagType.getDescByType(info.getType());
                urlMapDirList.put(fileName, info.getUrl().stream().distinct().collect(Collectors.toList()));
            }

            File file = ZipFileUtil.generateFolder(foldName, urlMapDirList);
            if (file == null) {
                throw new ErpCommonException("生成文件失败");
            }
            File zipFile = ZipFileUtil.zipFile(foldName, file);
            String path = AliyunOssClientUtil.uploadFile(foldName + ".zip", Files.newInputStream(zipFile.toPath()), "op/shipment/");

            try {
                if (zipFile.exists()) {
                    Files.delete(zipFile.toPath());
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (file.exists()) {
                    ZipFileUtil.deleteDirectory(file.toPath());
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            return path;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("货件--{}下载附件失败，原因{}", JSONObject.toJSONString(attach), e.getMessage());
            return null;
        }
    }

    @Override
    public void downLoadAttachFile(WalmartShipmentAttach attach,UserEntity userEntity) {

        Long id = attach.getId();
        List<String> typeList = attach.getTypeList();
        WalmartInboundShipment byId = walmartInboundShipmentService.getById(id);
        if (byId == null) {
            throw new ErpCommonException("无对应货件信息");
        }
        List<WalmartInboundShipmentAttachInfo> attachmentInfo = walmartInboundShipmentService.getNotCompleteAttachmentInfo(id);
        if (CollectionUtil.isEmpty(attachmentInfo)) {
            throw new ErpCommonException("无附件信息");
        }
        if (attachmentInfo.stream().noneMatch(WalmartInboundShipmentAttachInfo::getCompleted)) {
            throw new ErpCommonException("无附件信息");
        }
        if (CollectionUtil.isNotEmpty(typeList)) {
            attachmentInfo.removeIf(t -> !typeList.contains(t.getType()));
        }
        attachmentInfo.removeIf(t -> !t.getCompleted());
        if (CollectionUtil.isEmpty(attachmentInfo)) {
            throw new ErpCommonException("无可选类型附件信息");
        }
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(attach.getOrgId());
        request.setTaskCode("op.shipment.attach.download");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(attach));
        request.setArgs(list);
        this.taskCenterService.startTask(request, userEntity);
    }

    /**
     * 定时拉取发票信息
     */
    @Override
    public void pullNvoFreightInvoice() {

        Long maxId = 0L;
        while (true) {
            PageHelper.startPage(1, 5000);
            List<WalmartInboundShipment> list = walmartInboundShipmentMapper.selectNeedPullNvoFreightInvoiceShipment(maxId);
            if (CollectionUtil.isEmpty(list)) {
                break;
            }
            maxId = list.get(list.size() - 1).getId();
            list.stream().collect(Collectors.groupingBy(WalmartInboundShipment::getShopTitle)).forEach((title, subList) -> {
                JSONObject object = new JSONObject();
                object.put("account", title);
                List<JSONObject> shipmentItemList = subList.stream().map(t -> {
                    JSONObject item = new JSONObject();
                    item.put("shipmentId", t.getShipmentId());
                    item.put("inboundOrderId", t.getInboundOrderId());
                    return item;
                }).collect(Collectors.toList());
                object.put("shipmentIdList", shipmentItemList);
                log.info("发送拉取发票请求，参数:{}", object.toJSONString());
                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.MAR_WALMART_INBOUND_DOCUMENT_SEND_QUEUE, object);
            });
        }
    }

    @Override
    public void receiveNvoFreightInvoiceQueue(String messageJson) {
        /**
         * {
         * 	"account": "",
         * 	"shipmentId": "1345565GDM",
         * 	"documentName": "附件名称",
         * 	"documentType": "附件类型",
         * 	"lastUpdated": "2025-09-02",
         * 	"url":""
         * }
         */
        log.info("receiveNvoFreightInvoiceQueue message: {}", messageJson);
        if (StringUtils.isEmpty(messageJson)) {
            log.error("receiveNvoFreightInvoiceQueue failed, message is empty");
            return;
        }
        JSONObject msg = JSONObject.parseObject(messageJson);
        String shipmentId = msg.getString("shipmentId");
        String lastUpdated = msg.getString("lastUpdated");
        String url = msg.getString("url");
        if (StringUtils.isEmpty(shipmentId)) {
            log.error("receiveNvoFreightInvoiceQueue failed, shipmentId is empty, message: {}", messageJson);
            return;
        }
        if (StringUtils.isEmpty(url)) {
            log.error("receiveNvoFreightInvoiceQueue failed, url is empty, message: {}", messageJson);
            return;
        }

        String key = "receiveNvoFreightInvoiceQueue-" + shipmentId;
        RLock lock = redissonClient.getLock(key);
        try {
            if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {
                WalmartShipmentAttach one = this.getOne(new LambdaQueryWrapper<>(WalmartShipmentAttach.class)
                        .eq(WalmartShipmentAttach::getShipmentId, shipmentId)
                        .eq(WalmartShipmentAttach::getOrgId, 1000049).last("LIMIT 1"));
                WalmartShipmentAttach attach = new WalmartShipmentAttach();
                attach.setShipmentId(shipmentId);
                attach.setOrgId(1000049);
                attach.setType(MarWalmartTagType.NVO_FREIGHT_INVOICE.getType());
                attach.setUrl(url);
                attach.settingDefaultSystemUpdate();
                if (one == null) {
                    attach.settingDefaultSystemCreate();
                } else {
                    attach.setId(one.getId());
                }
                if (StringUtils.isNotEmpty(lastUpdated)) {
                    attach.setCreatedAt(DateUtil.convertStringToDate(lastUpdated));
                }
                this.saveOrUpdate(attach);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("receiveNvoFreightInvoiceQueue failed, message: {}, cause: {}", messageJson, e.getMessage());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
