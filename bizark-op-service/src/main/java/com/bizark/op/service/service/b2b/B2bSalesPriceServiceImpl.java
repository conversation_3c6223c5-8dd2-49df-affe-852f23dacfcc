package com.bizark.op.service.service.b2b;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.cell.CellUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.bizark.activiti.api.dto.cativiti.DeployStartDTO;
import com.bizark.activiti.api.service.activiti.ProcessStartService;
import com.bizark.activiti.api.vo.activiti.ActProcessVO;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.entity.op.b2b.B2bSalesPrice;
import com.bizark.op.api.entity.op.b2b.B2bSalesPriceLog;
import com.bizark.op.api.entity.op.b2b.dto.*;
import com.bizark.op.api.entity.op.b2b.vo.B2bSalesPriceListVo;
import com.bizark.op.api.service.b2b.B2bSalesPriceService;
import com.bizark.op.api.service.sys.ISysDictDataService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.AliyunOssClientUtil;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.b2b.B2bSalesPriceLogMapper;
import com.bizark.op.service.mapper.b2b.B2bSalesPriceMapper;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class B2bSalesPriceServiceImpl extends ServiceImpl<B2bSalesPriceMapper, B2bSalesPrice> implements B2bSalesPriceService {

    @Resource
    private B2bSalesPriceLogMapper b2bSalesPriceLogMapper;

    @Resource
    private ISysDictDataService sysDictDataService;

    @Resource
    private ProcessStartService processStartService;

    @Override
    public List<B2bSalesPriceListVo> salesPriceList(B2bSalesPriceQueryDTO dto) {
        if (CollUtil.isNotEmpty(dto.getApproverIds())) {
            List<Long> nowUserApproveIds = processStartService.getNowUserApproveIds(dto.getApproverIds(), 13);
            if (CollUtil.isEmpty(nowUserApproveIds)) {
                return Lists.newArrayList();
            }
            dto.setInstanceIds(nowUserApproveIds);
        }
        return baseMapper.selectListByCondition(dto);
    }

    @Override
    public void saveSalesPrice(B2bSalesPriceSaveDTO dto) {
        dto.setOutJsonId(processStartService.getOutJsonId(13, dto.getContextId()));
        dto.setApprovalEnabled(dto.getOutJsonId() != null);
        // 1. 参数校验
        validateRequiredFields(dto);
        // 3. 校验重叠区间
        checkOverlap(dto);

        // 4. 构造实体
        B2bSalesPrice price = BeanCopyUtils.copyBean(dto, B2bSalesPrice.class);
        price.setB2bCustomerId(dto.getCustomerId());
        price.settingDefaultValue();
        price.setIsVoided(0); // 默认未作废

        // 5. 设置状态
        int status = getStatus(dto); //草稿 or 待审批 or 已通过
        price.setStatus(status);

        // 6. 保存主数据
        baseMapper.insert(price);

        // 7. 写入操作日志
        if (status == 0) {
            saveOperationLog(price.getId(), "新增-暂存", "新增价格,状态为草稿", 0);
        }
        if (status == 3) {
            saveOperationLog(price.getId(), "新增-提交", "新增价格,状态为已通过", 3);
        }
        // 8. 如果是提交且审批流开启，则进入待审批流程（可扩展）
        if (status == 1) {
            startApprovalFlow(dto.getOutJsonId(), dto.getContextId(), price); // 调用审批服务
        }
    }

    private int getStatus(B2bSalesPriceSaveDTO dto) {
        if (dto.getStatus() == 0) {
            return 0;
        }
        return dto.getOutJsonId() != null && dto.getApprovalEnabled() ? 1 : 3;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSalesPrice(B2bSalesPriceUpdateDTO dto) {
        B2bSalesPrice price = baseMapper.selectById(dto.getId());
        if (price == null) {
            throw new IllegalArgumentException("价格记录不存在");
        }

        // 参数校验
        validateRequiredFields(dto);

        // 校验重叠区间
        checkOverlap(dto);

        // 更新字段
        price.setSalesOrganization(dto.getSalesOrganization());
        price.setB2bCustomerId(dto.getCustomerId());
        price.setSku(dto.getSku());
        price.setProductName(dto.getProductName());
        price.setCurrency(dto.getCurrency());
        price.setDeliveryMethod(dto.getDeliveryMethod());
        price.setIsTaxIncluded(dto.getIsTaxIncluded());
        price.setUnitPrice(dto.getUnitPrice());
        price.setTaxInclusivePrice(dto.getTaxInclusivePrice());
        price.setQuoteDate(dto.getQuoteDate());
        price.setEffectiveStartDate(dto.getEffectiveStartDate());
        price.setEffectiveEndDate(dto.getEffectiveEndDate());
        price.setRemarks(dto.getRemarks());
        price.settingUpdated();

        baseMapper.updateById(price);

        // 写入操作日志
        saveOperationLog(price.getId(), "修改-暂存", "修改价格", 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitSalesPrices(B2bSalesPriceSubmitDTO dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new IllegalArgumentException("请选择至少一条记录进行提交");
        }

        // 查询选中记录
        List<B2bSalesPrice> prices = baseMapper.selectBatchIds(dto.getIds());
        if (prices.stream().anyMatch(p -> p.getStatus() != 0)) {
            throw new IllegalArgumentException("只能提交草稿状态的记录");
        }

        // 校验重叠区间
        for (B2bSalesPrice price : prices) {
            checkOverlap(BeanCopyUtils.copyBean(price, B2bSalesPriceSaveDTO.class));
        }

        // 更新状态并记录日志
        for (B2bSalesPrice price : prices) {
            Long outJsonId = processStartService.getOutJsonId(13, dto.getContextId());
            int status = outJsonId == null ? 3 : 1;
            price.setStatus(status);
            price.settingUpdated(); // 更新修改人和时间
            baseMapper.updateById(price);

            String action = "修改-提交";
            String remark = "修改价格";
            saveOperationLog(price.getId(), action, remark, status);

            if (status == 1) {
                startApprovalFlow(outJsonId, dto.getContextId(), price);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSalesPrices(B2bSalesPriceDeleteDTO dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new IllegalArgumentException("请选择至少一条记录进行删除");
        }

        // 查询选中记录
        List<B2bSalesPrice> prices = baseMapper.selectBatchIds(dto.getIds());
        if (prices.stream().anyMatch(p -> p.getStatus() != 0 && p.getStatus() != 2)) {
            throw new IllegalArgumentException("只能删除草稿或已驳回状态的记录");
        }

        // 物理删除
        baseMapper.deleteBatchIds(dto.getIds());

        // 删除操作日志（可选）
        for (Long id : dto.getIds()) {
            saveOperationLog(id, "删除", "删除价格", -1);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveSalesPrices(B2bSalesPriceApproveDTO dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new IllegalArgumentException("请选择至少一条记录进行审批");
        }

        // 查询选中记录
        List<B2bSalesPrice> prices = baseMapper.selectBatchIds(dto.getIds());
        if (prices.stream().anyMatch(p -> p.getStatus() != 1)) {
            throw new IllegalArgumentException("只能审批待审批状态的记录");
        }

        // 审批驳回时原因必填
        if (!dto.getApproved() && StringUtils.isEmpty(dto.getReason())) {
            throw new IllegalArgumentException("审批驳回时原因不能为空");
        }

        // 更新状态并记录日志
        for (B2bSalesPrice price : prices) {
            int status = dto.getApproved() ? 3 : 2;
            price.setStatus(status);
            price.settingUpdated();
            baseMapper.updateById(price);

            String action = "审批";
            String remark = dto.getApproved() ? "审批通过" : "审批驳回";
            if (StringUtils.isNotEmpty(dto.getReason())) {
                remark += "，原因：" + dto.getReason();
            }
            saveOperationLog(price.getId(), action, remark, status);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawSalesPrices(B2bSalesPriceWithdrawDTO dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new IllegalArgumentException("请选择至少一条记录进行撤回");
        }

        // 查询选中记录
        List<B2bSalesPrice> prices = baseMapper.selectBatchIds(dto.getIds());
        if (prices.stream().anyMatch(p -> p.getStatus() != 1)) {
            throw new IllegalArgumentException("只能撤回待审批状态的记录");
        }

        // 校验是否为创建人
        AuthUserDetails userDetails = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
        for (B2bSalesPrice price : prices) {
            if (!Objects.equals(price.getCreatedBy(), userDetails.getId())) {
                throw new IllegalArgumentException("只能撤回自己创建的记录");
            }
        }

        // 更新状态并记录日志
        for (B2bSalesPrice price : prices) {
            price.setStatus(0); // 草稿状态
            price.settingUpdated();
            baseMapper.updateById(price);

            saveOperationLog(price.getId(), "撤回", "撤回", 0);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void voidSalesPrices(B2bSalesPriceVoidDTO dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new IllegalArgumentException("请选择至少一条记录进行作废");
        }

        // 查询选中记录
        List<B2bSalesPrice> prices = baseMapper.selectBatchIds(dto.getIds());
        if (prices.stream().anyMatch(p -> p.getStatus() != 3)) {
            throw new IllegalArgumentException("只能作废已通过状态的记录");
        }

        // 检查是否有B2B订单引用（此处为示例，实际需调用订单服务）
        boolean hasOrderRef = checkIfReferencedByOrder(dto.getIds());
        if (hasOrderRef) {
            String msg = dto.getIds().size() > 1
                    ? "存在B2B订单引用此价格，确定作废吗？"
                    : "已有B2B订单引用此价格，确定作废吗？";
            // 前端应处理此提示，后端可抛出特定异常或返回特殊标识
            throw new RuntimeException(msg);
        }

        // 更新是否作废并记录日志
        for (B2bSalesPrice price : prices) {
            price.setIsVoided(1);
            price.settingUpdated();
            baseMapper.updateById(price);

            saveOperationLog(price.getId(), "作废", "作废此条价格记录", price.getStatus());
        }
    }

    @Override
    public void importPrices(MultipartFile file, Integer contextId) throws IOException {
        List<B2bSalesPriceSaveDTO> dtos = parseExcel(file);

        if (CollUtil.isEmpty(dtos)) {
            throw new CustomException("空数据");
        }

        List<String> errors = validateAndCheckOverlap(dtos);

        if (!errors.isEmpty()) {
            // 有错误时导出错误报告并抛出异常
            throw new ImportValidationException("导入数据存在错误", exportErrorReportToStream(errors));
        }

        // 批量保存
        for (B2bSalesPriceSaveDTO dto : dtos) {
            if (dto.getQuoteDate() == null) {
                dto.setQuoteDate(new Date()); // 默认为导入当天
            }
            B2bSalesPrice price = BeanCopyUtils.copyBean(dto, B2bSalesPrice.class);
            price.setB2bCustomerId(dto.getCustomerId());
            price.settingDefaultValue();
            price.setTaxInclusivePrice(price.getTaxInclusivePrice() == null ? BigDecimal.ZERO : price.getTaxInclusivePrice());
            calculateUnitPrice(price);
            price.setStatus(0);
            save(price);

            saveOperationLog(price.getId(), "导入", "导入价格，状态为草稿", 0);
        }
    }

    @Override
    public void export(B2bSalesPriceQueryDTO dto, HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("B2B价格列表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 获取交货方式映射
            Map<String, String> deliveryWayMap = sysDictDataService.oneTypeToMap("delivery_way");

            // 使用EasyExcel流式写入
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .head(B2bSalesPriceExcelDTO.class)
                    .build();

            WriteSheet writeSheet = EasyExcel.writerSheet("销售价格").build();

            // 分页查询并写入数据
            int pageNum = 1;
            int pageSize = 1000;
            List<B2bSalesPriceListVo> pageData;

            do {
                PageHelper.startPage(pageNum, pageSize);
                pageData = baseMapper.selectListByCondition(dto);

                // 转换数据
                List<B2bSalesPriceExcelDTO> excelData = pageData.stream()
                        .map(vo -> convertToExcelDTO(vo, deliveryWayMap))
                        .collect(Collectors.toList());

                // 写入当前页数据
                excelWriter.write(excelData, writeSheet);

                pageNum++;
            } while (pageData.size() == pageSize);

            // 关闭流
            excelWriter.finish();

        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    /**
     * 转换为Excel DTO对象
     */
    private B2bSalesPriceExcelDTO convertToExcelDTO(B2bSalesPriceListVo vo, Map<String, String> deliveryWayMap) {
        B2bSalesPriceExcelDTO dto = new B2bSalesPriceExcelDTO();
        dto.setStatus(getStatusText(vo.getStatus()));
        dto.setSalesOrganization(vo.getSalesOrganizationName());
        dto.setCustomer(vo.getB2bCustomerName());
        dto.setProductNameSku(vo.getProductName() + "-" + vo.getSku());
        dto.setCurrency(vo.getCurrency());
        dto.setIsTaxIncluded(isTaxIncludedText(vo.getIsTaxIncluded()));
        dto.setUnitPrice(vo.getUnitPrice() != null ? vo.getUnitPrice().toString() : "");
        dto.setTaxInclusivePrice(vo.getTaxInclusivePrice() != null ? vo.getTaxInclusivePrice().toString() : "");
        dto.setDeliveryMethod(deliveryWayMap.get(vo.getDeliveryMethod()));
        dto.setQuoteDate(DateUtil.formatDate(vo.getQuoteDate()));
        dto.setEffectiveStatus(getEffectiveStatusText(vo.getEffectiveStatus()));
        dto.setEffectiveStartDate(DateUtil.formatDate(vo.getEffectiveStartDate()));
        dto.setEffectiveEndDate(DateUtil.formatDate(vo.getEffectiveEndDate()));
        dto.setIsVoided(isVoidedText(vo.getIsVoided()));
        dto.setRemarks(vo.getRemarks());
        dto.setCreatedBy(vo.getCreatedName());
        dto.setCreatedAt(formatDate(vo.getCreatedAt()));
        dto.setUpdatedBy(vo.getUpdatedName());
        dto.setUpdatedAt(formatDate(vo.getUpdatedAt()));
        return dto;
    }


    @Override
    public B2bSalesPrice findValidPriceByCondition(
            String salesOrganization,
            String customerId,
            String deliveryMethod,
            String currency,
            Integer isTaxIncluded,
            Date currentDate) {

        // 构建查询条件
        QueryWrapper<B2bSalesPrice> wrapper = new QueryWrapper<>();
        wrapper.eq("sales_organization", salesOrganization)
                .eq("b2b_customer_id", customerId)
                .eq("delivery_method", deliveryMethod)
                .eq("currency", currency)
                .eq("is_tax_included", isTaxIncluded)
                .ne("is_voided", 1) // 排除已作废
                .le("effective_start_date", currentDate)
                .ge("effective_end_date", currentDate);

        List<B2bSalesPrice> prices = baseMapper.selectList(wrapper);

        if (CollUtil.isEmpty(prices)) {
            throw new IllegalArgumentException("SKUD 在 B2B 价格管理找不到对应符合条件的价格");
        }

        return prices.get(0);
    }

    @Override
    public B2bSalesPriceListVo detail(Long id) {
        B2bSalesPrice price = getById(id);
        if (price == null) {
            throw new CustomException("价格记录不存在");
        }

        B2bSalesPriceListVo b2bSalesPriceListVo = BeanCopyUtils.copyBean(price, B2bSalesPriceListVo.class);
        b2bSalesPriceListVo.setB2bCustomerName(this.baseMapper.getCustomerNameByCode(price.getB2bCustomerId()));
        return b2bSalesPriceListVo;
    }

    @Override
    public void handleApprovalProcessCallback(Long priceId, Integer status) {
        // 查询选中记录
        B2bSalesPrice price = getById(priceId);
        if (price == null || price.getStatus() != 1) {
            throw new IllegalArgumentException("只能审批待审批状态的记录");
        }

        String remark = "";
        // 更新状态并记录日志
        if (status == 1) {
            price.setStatus(3);
            remark = "审批通过";
        }
        if (status == 2) {
            price.setStatus(2);
            remark = "审批驳回";
        }
        if (status == 3) {
            B2bSalesPriceWithdrawDTO b2bSalesPriceWithdrawDTO = new B2bSalesPriceWithdrawDTO();
            b2bSalesPriceWithdrawDTO.setIds(Lists.newArrayList(priceId));
            withdrawSalesPrices(b2bSalesPriceWithdrawDTO);
            return;
        }
        price.settingUpdated();
        baseMapper.updateById(price);
        saveOperationLog(price.getId(), "审批", remark, status);
    }

    private String getStatusText(Integer status) {
        switch (status) {
            case 0:
                return "草稿";
            case 1:
                return "待审核";
            case 2:
                return "已驳回";
            case 3:
                return "已通过";
            default:
                return "未知";
        }
    }

    private String getEffectiveStatusText(Integer status) {
        switch (status) {
            case 1:
                return "未生效";
            case 2:
                return "已失效";
            default:
                return "生效中";
        }
    }

    private String isTaxIncludedText(Integer value) {
        return value == 1 ? "是" : "否";
    }

    private String isVoidedText(Integer value) {
        return value == 1 ? "是" : "否";
    }

    private String formatDate(Date date) {
        if (date == null) return "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    // 自定义异常类，用于传递错误报告
    @Getter
    public static class ImportValidationException extends RuntimeException {
        private final String ossUrl;

        public ImportValidationException(String message, String ossUrl) {
            super(message);
            this.ossUrl = ossUrl;
        }

    }

    private String exportErrorReportToStream(List<String> errors) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("导入错误报告");

        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("行号");
        headerRow.createCell(1).setCellValue("错误信息");

        for (int i = 0; i < errors.size(); i++) {
            String[] parts = errors.get(i).split(",", 2); // 只分割成两部分，避免截断错误信息中的逗号
            Row row = sheet.createRow(i + 1);
            row.createCell(0).setCellValue(parts[0]); // 行号

            // 确保错误信息完整显示
            String errorMessage = parts.length > 1 ? parts[1] : "未知错误";
            row.createCell(1).setCellValue(errorMessage);
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        // 上传到OSS并返回地址
        String fileName = "导入错误报告" + System.currentTimeMillis() + ".xlsx";
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        return AliyunOssClientUtil.uploadFile(fileName, inputStream, "op/b2b/error/");
    }


    private List<B2bSalesPriceSaveDTO> parseExcel(MultipartFile file) throws IOException {
        List<B2bSalesPriceSaveDTO> dtos = new ArrayList<>();
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // 跳过标题行

                B2bSalesPriceSaveDTO dto = new B2bSalesPriceSaveDTO();

                Cell salesOrgCell = row.getCell(0);
                String salesOrgValue = salesOrgCell != null ? String.valueOf(CellUtil.getCellValue(salesOrgCell)) : "";
                dto.setSalesOrganization(StringUtils.isNotEmpty(salesOrgValue) ? salesOrgValue : null);

                Cell customerNameCell = row.getCell(1);
                String customerNameValue = customerNameCell != null ? String.valueOf(CellUtil.getCellValue(customerNameCell)) : "";
                dto.setCustomerName(StringUtils.isNotEmpty(customerNameValue) ? customerNameValue : null);

                Cell skuCell = row.getCell(2);
                String skuValue = skuCell != null ? String.valueOf(CellUtil.getCellValue(skuCell)) : "";
                dto.setSku(StringUtils.isNotEmpty(skuValue) ? skuValue : null);

                Cell deliveryMethodCell = row.getCell(3);
                String deliveryMethodValue = deliveryMethodCell != null ? String.valueOf(CellUtil.getCellValue(deliveryMethodCell)) : "";
                dto.setDeliveryMethod(StringUtils.isNotEmpty(deliveryMethodValue) ? deliveryMethodValue : null);

                Cell currencyCell = row.getCell(4);
                String currencyValue = currencyCell != null ? String.valueOf(CellUtil.getCellValue(currencyCell)) : "";
                dto.setCurrency(StringUtils.isNotEmpty(currencyValue) ? currencyValue : null);

                Cell isTaxIncludedCell = row.getCell(5);
                String taxStr = isTaxIncludedCell != null ? String.valueOf(CellUtil.getCellValue(isTaxIncludedCell)) : "";
                dto.setIsTaxIncluded("是".equals(taxStr) ? 1 : 0);

                Cell taxRateCell = row.getCell(6);
                String taxRateStr = taxRateCell != null ? String.valueOf(CellUtil.getCellValue(taxRateCell)) : "";
                dto.setTaxRate(StringUtils.isNotEmpty(taxRateStr) && !"null".equals(taxRateStr) ? new BigDecimal(taxRateStr) : BigDecimal.ZERO);

                Cell unitPriceCell = row.getCell(7);
                String unitPriceStr = unitPriceCell != null ? String.valueOf(CellUtil.getCellValue(unitPriceCell)) : "";
                dto.setUnitPrice(StringUtils.isNotEmpty(unitPriceStr) && !"null".equals(unitPriceStr) ? new BigDecimal(unitPriceStr) : BigDecimal.ZERO);

                Cell taxInclusivePriceCell = row.getCell(8);
                String taxInclusivePriceStr = taxInclusivePriceCell != null ? String.valueOf(CellUtil.getCellValue(taxInclusivePriceCell)) : "";
                dto.setTaxInclusivePrice(StringUtils.isNotEmpty(taxInclusivePriceStr) && !"null".equals(taxInclusivePriceStr) ? new BigDecimal(taxInclusivePriceStr) : null);

                Cell quoteDateCell = row.getCell(9);
                String quoteDateStr = quoteDateCell != null ? String.valueOf(CellUtil.getCellValue(quoteDateCell)) : "";
                dto.setQuoteDate(parseDate(quoteDateStr));

                Cell effectiveStartDateCell = row.getCell(10);
                String effectiveStartDateStr = effectiveStartDateCell != null ? String.valueOf(CellUtil.getCellValue(effectiveStartDateCell)) : "";
                dto.setEffectiveStartDate(parseDate(effectiveStartDateStr));

                Cell effectiveEndDateCell = row.getCell(11);
                String effectiveEndDateStr = effectiveEndDateCell != null ? String.valueOf(CellUtil.getCellValue(effectiveEndDateCell)) : "";
                dto.setEffectiveEndDate(parseDate(effectiveEndDateStr));

                dtos.add(dto);
            }
        }
        return dtos;
    }

    private Date parseDate(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }

        List<String> patterns = Arrays.asList(
                "yyyy/MM/dd", "yyyy-MM-dd", "yyyyMMdd",
                "yyyy/MM/dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss"
        );

        for (String pattern : patterns) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                return sdf.parse(dateStr);
            } catch (ParseException ignored) {
                // 继续尝试下一个格式
            }
        }

        throw new IllegalArgumentException("无法解析日期格式: " + dateStr);
    }

    private List<String> validateAndCheckOverlap(List<B2bSalesPriceSaveDTO> dtos) {
        List<String> errors = new ArrayList<>();

        for (int i = 0; i < dtos.size(); i++) {
            B2bSalesPriceSaveDTO dto = dtos.get(i);
            List<String> rowErrors = new ArrayList<>();

            // 1. 销售组织校验
            if (!salesOrganizationExists(dto)) {
                rowErrors.add("销售组织不存在");
            }

            // 2. 客户名称校验
            if (dto.getCustomerName() == null || !customerExists(dto)) {
                rowErrors.add("客户不存在");
            }

            // 3. SKU 校验
            if (!checkSkuAndGerProductName(dto)) {
                rowErrors.add("SKU未启用或不存在");
            }

            // 4. 交货方式校验（假设从枚举中取值）
            if (!deliveryMethodValid(dto)) {
                rowErrors.add("交货方式不合法");
            }

            // 5. 币种校验
            if (!currencyValid(dto.getCurrency())) {
                rowErrors.add("币种不合法");
            }

            // 6. 是否含税校验
            if (dto.getIsTaxIncluded() != 0 && dto.getIsTaxIncluded() != 1) {
                rowErrors.add("是否含税必须为‘是’或‘否’");
            }

            // 7. 税率校验
            if (dto.getTaxRate() == null || dto.getTaxRate().compareTo(BigDecimal.ZERO) < 0 ||
                    dto.getTaxRate().compareTo(new BigDecimal("100")) > 0) {
                rowErrors.add("税率必须在[0,100]之间");
            }

            // 8. 单价/含税单价校验
            if (dto.getIsTaxIncluded() == 1) { // 含税
                if (dto.getTaxInclusivePrice() == null || dto.getTaxInclusivePrice().compareTo(BigDecimal.ZERO) <= 0) {
                    rowErrors.add("含税单价不能为空且必须大于0");
                }
            } else { // 不含税
                if (dto.getUnitPrice() == null || dto.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    rowErrors.add("单价不能为空且必须大于0");
                }
            }

            // 9. 日期格式校验
            if (dto.getEffectiveStartDate() == null || dto.getEffectiveEndDate() == null) {
                rowErrors.add("生效日期和失效日期不能为空");
            } else if (dto.getEffectiveEndDate().before(dto.getEffectiveStartDate())) {
                rowErrors.add("失效日期不能早于生效日期");
            }

            // 10. 重叠校验（同数据库内未作废的数据）
            try {
                checkOverlap(dto);
            } catch (Exception e) {
                rowErrors.add("相同销售组织+客户+SKU+交货方式的价格区间存在重叠");
            }

            if (!rowErrors.isEmpty()) {
                errors.add((i + 1) + "," + String.join(",", rowErrors));
            }
        }

        return errors;
    }

    private boolean checkSkuAndGerProductName(B2bSalesPriceSaveDTO dto) {
        String productName = this.baseMapper.selectProductNameBySku(dto.getSku());
        dto.setProductName(productName);
        return StrUtil.isNotBlank(productName);
    }

    private boolean salesOrganizationExists(B2bSalesPriceSaveDTO org) {
        // 查询店铺管理表是否存在该销售组织
        Integer orgId = this.baseMapper.getOrgIdByName(org);
        org.setSalesOrganization(String.valueOf(orgId));
        return orgId != null;
    }

    private boolean customerExists(B2bSalesPriceSaveDTO dto) {
        String customerId = this.baseMapper.getCustomerIdByCustomerName(dto.getCustomerName());
        if (StrUtil.isBlank(customerId)) {
            return false;
        }
        dto.setCustomerId(customerId);
        return true;
    }

    private boolean skuEnabled(String sku) {
        return baseMapper.countBySku(sku) > 0;
    }

    private boolean deliveryMethodValid(B2bSalesPriceSaveDTO dto) {
        String deliveryWay = sysDictDataService.selectDictValue("delivery_way", dto.getDeliveryMethod());
        if (deliveryWay == null) {
            return false;
        }
        dto.setDeliveryMethod(deliveryWay);
        return true;
    }

    private boolean currencyValid(String currency) {
        return Arrays.asList(
                "USD", "JPY", "EUR", "GBP", "CAD", "AUD", "MXN", "MXP", "SEK", "PLN",
                "INR", "AED", "SAR", "SGD", "TRY", "KRW", "THB", "IDR", "MYR", "RMB",
                "PHP", "HKD"
        ).contains(currency);
    }


    // 模拟检查是否有订单引用（实际需调用订单服务）
    private boolean checkIfReferencedByOrder(List<Long> ids) {
        // 此处应调用订单服务检查是否引用
        return false; // 示例返回false
    }


    private void validateRequiredFields(B2bSalesPriceSaveDTO dto) {
        if (StringUtils.isEmpty(dto.getSalesOrganization()))
            throw new IllegalArgumentException("销售组织不能为空");
        if (StringUtils.isEmpty(dto.getCustomerId()))
            throw new IllegalArgumentException("客户不能为空");
        if (StringUtils.isEmpty(dto.getSku()))
            throw new IllegalArgumentException("SKU不能为空");
        if (StringUtils.isEmpty(dto.getCurrency()))
            throw new IllegalArgumentException("币种不能为空");
        if (StringUtils.isEmpty(dto.getDeliveryMethod()))
            throw new IllegalArgumentException("交货方式不能为空");
        if (dto.getIsTaxIncluded() == null)
            throw new IllegalArgumentException("是否含税不能为空");
        if (dto.getTaxRate() == null || dto.getTaxRate().compareTo(BigDecimal.ZERO) < 0)
            throw new IllegalArgumentException("税率必须大于0");
        if (dto.getQuoteDate() == null)
            throw new IllegalArgumentException("报价日期不能为空");
        if (dto.getEffectiveStartDate() == null)
            throw new IllegalArgumentException("生效日期不能为空");
        if (dto.getEffectiveEndDate() == null)
            throw new IllegalArgumentException("失效日期不能为空");
        if (dto.getEffectiveEndDate().before(dto.getEffectiveStartDate()))
            throw new IllegalArgumentException("失效日期不能早于生效日期");
    }

    private void calculateUnitPrice(B2bSalesPrice price) {
        if (price.getIsTaxIncluded() == 1) { // 含税
            BigDecimal calculatedUnitPrice = price.getTaxInclusivePrice().divide(BigDecimal.ONE.add(price.getTaxRate().divide(new BigDecimal("100"))), 2, RoundingMode.HALF_UP);
            price.setUnitPrice(calculatedUnitPrice);
        } else { // 不含税
            BigDecimal calculatedTaxInclusivePrice = price.getUnitPrice().multiply(BigDecimal.ONE.add(price.getTaxRate().divide(new BigDecimal("100")))).setScale(2, RoundingMode.HALF_UP);
            price.setTaxInclusivePrice(calculatedTaxInclusivePrice);
        }
    }


    private void checkOverlap(B2bSalesPriceSaveDTO dto) {
        List<B2bSalesPrice> existing = baseMapper.selectList(new QueryWrapper<B2bSalesPrice>()
                .eq("sales_organization", dto.getSalesOrganization())
                .eq("b2b_customer_id", dto.getCustomerId())
                .eq("sku", dto.getSku())
                .eq("currency", dto.getCurrency())
                .eq("delivery_method", dto.getDeliveryMethod())
                .ne(dto.getId() != null, "id", dto.getId())
                .ne("is_voided", 1) // 排除已作废
        );

        for (B2bSalesPrice p : existing) {
            // 正确的时间区间重叠判断
            if (isDateRangeOverlap(p.getEffectiveStartDate(), p.getEffectiveEndDate(),
                    dto.getEffectiveStartDate(), dto.getEffectiveEndDate())) {
                throw new IllegalArgumentException("相同销售组织+客户+SKU+币种+交货方式的价格区间存在重叠！");
            }
        }
    }

    /**
     * 判断两个时间区间是否重叠（包含边界重叠）
     *
     * @param start1 第一个区间的开始时间
     * @param end1   第一个区间的结束时间
     * @param start2 第二个区间的开始时间
     * @param end2   第二个区间的结束时间
     * @return 是否重叠
     */
    private boolean isDateRangeOverlap(Date start1, Date end1, Date start2, Date end2) {
        // 检查空值情况
        if (start1 == null || end1 == null || start2 == null || end2 == null) {
            return false;
        }

        // 正确的重叠判断逻辑：
        // 两个区间不重叠的条件是：一个区间的结束时间 <= 另一个区间的开始时间
        // 所以重叠的条件是：!(end1 <= start2 || end2 <= start1)
        // 即：end1 > start2 && end2 > start1
        return end1.compareTo(start2) > 0 && end2.compareTo(start1) > 0;
    }


    private void saveOperationLog(Long dataId, String action, String remark, Integer state) {
        B2bSalesPriceLog log = new B2bSalesPriceLog();
        log.setOpDataId(dataId);
        log.setOpModel("B2B_SALES_PRICE");
        log.setOpAction(action);
        log.setOpRemark(remark);
        log.setCurrentState(state.toString());
        log.setOpAt(new Date());
        log.setOrganizationId(1); // 假设组织ID为1
        log.setSystemId(1); // 假设系统ID为1
        log.settingDefaultValue();
        log.setOpUserId(log.getCreatedBy());
        log.setOpUserName(log.getCreatedName());
        log.settingDefaultValue();

        b2bSalesPriceLogMapper.insert(log);
    }


    // 模拟审批流启动方法（实际需调用审批服务）
    private void startApprovalFlow(Long outJsonId, Integer contextId, B2bSalesPrice price) {
        DeployStartDTO deployStartDTO = new DeployStartDTO();
        deployStartDTO.setContextId(contextId);
        deployStartDTO.setOutJsonId(outJsonId);
        deployStartDTO.setBusinessId(String.valueOf(price.getId()));
        deployStartDTO.setTitle(StrUtil.format("{}, 销售组织：{}，客户：{}，SKU：{}，币种：{}，交货方式：{}",
                price.getCreatedName() + "提交的b2b价格审批",
                this.baseMapper.getOrgNameById(price.getSalesOrganization()),
                this.baseMapper.getCustomerNameByCode(price.getB2bCustomerId()),
                price.getSku(),
                price.getCurrency(),
                sysDictDataService.oneTypeToMap("delivery_way").get(price.getDeliveryMethod())));
        deployStartDTO.setSummary(StrUtil.format("{}，税率：{}%，含税单价：{}，单价：{}",
                price.getIsTaxIncluded() == 1 ? "含税" : "不含税",
                price.getTaxRate(),
                price.getTaxInclusivePrice(),
                price.getUnitPrice()));
        deployStartDTO.setArgument(StrUtil.format("[{}]", price.getId()));
        AuthUserDetails userDetails = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
        deployStartDTO.setUserId(userDetails.getId());
        ActProcessVO actProcessVO = processStartService.startProcessV2(deployStartDTO);
        if (actProcessVO.getStatus() == 5) {
            price.setStatus(3);
            saveOperationLog(price.getId(), "新增-提交", "新增价格,审批自动通过", 1);
        } else {
            saveOperationLog(price.getId(), "新增-提交", "新增价格,状态为待审批", 1);
        }
        price.setInstanceId(String.valueOf(actProcessVO.getId()));
        updateById(price);
    }
}
