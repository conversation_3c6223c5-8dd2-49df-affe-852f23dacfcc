package com.bizark.op.service.service.promotions;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bizark.op.api.enm.mar.MarPromotionInvitationTypeEnum;
import com.bizark.op.api.enm.promotions.AmzPromotionsOperateTypeEnum;
import com.bizark.op.api.enm.promotions.TemuPromotionsStateEnum;
import com.bizark.op.api.enm.temu.TemuUrl;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.promotions.*;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.temu.request.*;
import com.bizark.op.api.entity.op.temu.response.TemuActivityResponse;
import com.bizark.op.api.entity.op.temu.response.TemuActivitySessionResponse;
import com.bizark.op.api.entity.op.temu.response.TemuSemiPromotionActiveResponse;
import com.bizark.op.api.entity.op.temu.response.TemuSemiPromotionReportProductResponse;
import com.bizark.op.api.request.msg.receive.*;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.promotions.*;
import com.bizark.op.api.service.promotions.fee.AmzPromotionFeeDateHisRecordService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.vo.promotions.BgPromotionActivityCandidateGoodsQueryResponse;
import com.bizark.op.api.vo.promotions.BgPromotionActivityQueryResponse;
import com.bizark.op.api.vo.promotions.MarTemuPromotionActivityGoodsQueryResponse;
import com.bizark.op.common.config.WeChatBootConfigure;
import com.bizark.op.common.util.*;
import com.bizark.op.service.api.TemuApi;
import com.bizark.op.service.mapper.promotions.AmzPromotionsMapper;
import com.bizark.op.service.mapper.promotions.AmzPromotionsSkuDetailMapper;
import com.bizark.op.service.mapper.promotions.MarPlatformPromotionsInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * api拉取temu促销信息服务实现类
 *
 * @Author: Ailill
 * @Date: 2025-03-18 16:25
 */
@Service
@Slf4j
public class MarApiPullTemuPromotionServiceImpl implements MarApiPullTemuPromotionService {


    @Autowired
    private TemuApi temuApi;
    @Autowired
    private IAmzPromotionsService iAmzPromotionsService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AmzPromotionsMapper amzPromotionsMapper;

    @Autowired
    private IAmzPromotionsSkuDetailService iAmzPromotionsSkuDetailService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private IMarPromotionsReportProductService iMarPromotionsReportProductService;

    @Autowired
    private IMarPlatformPromotionsInfoService marPlatformPromotionsInfoService;

    @Autowired
    private MarPlatformPromotionsInfoMapper marPlatformPromotionsInfoMapper;

    @Autowired
    private AmzPromotionFeeDateHisRecordService amzPromotionFeeDateHisRecordService;

    @Autowired
    private IAmzPromotionsOperateLogService amzPromotionsOperateLogService;

    @Autowired
    private AmzWalmartPromotionService amzWalmartPromotionService;

    @Autowired
    private AmzPromotionsSkuDetailMapper amzPromotionsSkuDetailMapper;

    @Autowired
    private WeChatBootConfigure weChatBootConfigure;

    @Autowired
//    @Lazy
    private ProductChannelsService productChannelsService;


    /**
     * @Description: 获取美本已提报活动信息：bg.promotion.activity.goods.query
     * @Author: wly
     * @Date: 2025-03-19 10:28
     * @Params: []
     * @Return: void
     **/
    public void pullMeibenPromotion() {

        List<Integer> meibenType = Arrays.asList(MarPromotionInvitationTypeEnum.SECKILL_ACTIVITY.getType(), MarPromotionInvitationTypeEnum.ADVANCED_BIG_SALE.getType(),
                MarPromotionInvitationTypeEnum.OFFICIAL_BIG_SALE.getType(), MarPromotionInvitationTypeEnum.CLEAR_STOCK_ACTIVITY.getType());

        List<MarPlatformPromotionsInfo> platformPromotionsInfoList = marPlatformPromotionsInfoMapper.selectMeiPlatformList(meibenType, null);
        List<Account> accountList = accountService.listByIds(platformPromotionsInfoList.stream().map(t -> t.getShopId()).distinct().collect(Collectors.toList()));
        List<List<MarPlatformPromotionsInfo>> partition = ListUtils.partition(platformPromotionsInfoList, (int) Math.ceil(platformPromotionsInfoList.size() / 10.0));
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (List<MarPlatformPromotionsInfo> list : partition) {
            futures.add(CompletableFuture.runAsync(() -> {
                list.forEach(t -> {
                    pullMeibenPromotionInfo(t.getInvitationId(), t.getShopId().intValue(), accountList);
                });
            }, threadPoolTaskExecutor));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

    }


    public void pullMeibenPromotionInfo(String invitationId, Integer shopId, List<Account> accounts) {
        Account account = accounts.stream().filter(t -> t.getId().equals(shopId)).findFirst().get();
        TemuPromotionActivityGoodsQuery request = new TemuPromotionActivityGoodsQuery() {
            @Override
            public TemuUrl type() {
                return TemuUrl.BG_PROMOTION_ACTIVITY_GOODS_QUERY;
            }
        };
        request.setActivityId(Long.parseLong(invitationId));
        int pageNumber = 1;
        int pageSize = 50;
        while (true) {
            request.setPageNumber(pageNumber);
            request.setPageSize(pageSize);

            try {
                log.info("拉取美本促销信息请求--平台活动ID---{}--店铺ID---{}--页码---{}--页大小---{}---请求数据---{}", invitationId, shopId, pageNumber, pageSize, JSONObject.toJSONString(request));
                MarTemuPromotionActivityGoodsQueryResponse response = temuApi.requestUsTemu(account, request, MarTemuPromotionActivityGoodsQueryResponse.class);
                log.info("拉取美本促销信息响应--平台活动ID---{}--店铺ID---{}--页码---{}--页大小---{}--返回数据---{}", invitationId, shopId, pageNumber, pageSize, JSONObject.toJSONString(response));
                if (response != null) {

                    if (response.getSuccess() == null) {
                        log.error("拉取美本促销信息失败--平台活动ID---{}--店铺ID---{}--返回数据success为空---失败code---{}---失败msg---{}", invitationId, shopId, response.getErrorCode(), response.getErrorMsg());
                        break;
                    } else {
                        if (response.getSuccess()) {

                            MarTemuPromotionActivityGoodsQueryResponse.Result result = response.getResult();
                            if (result == null || result.getTotal() == null || result.getTotal() == 0) {
                                log.info("拉取美本促销信息成功--平台活动ID---{}--店铺ID---{}--返回数据result为空或total为0为空或total为0", invitationId, shopId);
                                break;
                            } else {

                                List<MarTemuPromotionActivityGoodsQueryResponse.GoodsList> goodsList = result.getGoodsList();
                                if (CollectionUtil.isEmpty(goodsList) || result.getActivityInfo() == null) {
                                    log.info("拉取美本促销信息成功--平台活动ID---{}--店铺ID---{}--返回数据goodsList为空", invitationId, shopId);
                                    break;
                                } else {
                                    /*Map<String, List<MarTemuPromotionActivityGoodsQueryResponse.SkuList>> skcIdMapSkuIdList = goodsList.stream().filter(t -> CollectionUtil.isNotEmpty(t.getSkuList())).collect(Collectors.toMap(t -> t.getGoodsId(), t -> t.getSkuList(), (k1, k2) -> k1));

                                    List<String> skcIdList = goodsList.stream().map(t -> t.getGoodsId()).distinct().collect(Collectors.toList());
                                    List<AmzPromotions> promotionsList = meibenPromotions.stream().filter(t -> skcIdList.contains(t.getSkc())).collect(Collectors.toList());

                                    List<AmzPromotionsSkuDetail> skuDetailList = existAmzPromotionsSkuDetailList.stream().filter(t -> promotionsList.stream().map(q -> q.getId()).collect(Collectors.toList()).contains(t.getPromotionsId())).collect(Collectors.toList());
                                    for (MarTemuPromotionActivityGoodsQueryResponse.GoodsList goods : goodsList) {
                                        String goodsId = goods.getGoodsId();
                                        Integer activityQuantity = goods.getActivityQuantity();
                                        Integer remainingActivityQuantity = goods.getRemainingActivityQuantity();
                                        AmzPromotions existPromotion = promotionsList.stream().filter(t -> t.getSkc().equals(goodsId)).findFirst().orElse(null);
                                        if (existPromotion == null) {
                                            log.info("美本促销信息不存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}", invitationId, shopId, goodsId);
                                        } else {

                                            log.info("美本促销信息存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}--更新前数据---{}", invitationId, shopId, goodsId, existPromotion);
                                            AmzPromotions updatePromotion = new AmzPromotions();
                                            updatePromotion.setId(existPromotion.getId());
                                            updatePromotion.setSkcStock(activityQuantity);
                                            updatePromotion.setRemainStock(remainingActivityQuantity);
                                            updatePromotion.settingDefaultSystemUpdate();
                                            log.info("美本促销信息更新--平台活动ID---{}--店铺ID---{}--父商品ID---{}--更新后数据---{}", invitationId, shopId, goodsId, updatePromotion);
                                            updatePromotions.add(updatePromotion);
                                            AmzPromotions amzPromotionsStock = BeanCopyUtils.copyBean(updatePromotion, AmzPromotions.class);
                                            amzPromotionsStock.setInvitationType(existPromotion.getInvitationType());
                                            amzPromotionsStock.setShopId(existPromotion.getShopId());
                                            amzPromotionsStock.setBeginTime(existPromotion.getBeginTime());
                                            amzPromotionsStock.setEndTime(existPromotion.getEndTime());
                                            amzPromotionsStock.setOrganizationId(existPromotion.getOrganizationId());
                                            if (CollectionUtil.isNotEmpty(updatePromotions)) {

                                                iAmzPromotionsService.updateBatchById(updatePromotions);
                                                updatePromotions.clear();

                                            }
                                            List<AmzPromotionsSkuDetail> syncStockDetailList = new ArrayList<>();
                                            if (CollectionUtil.isNotEmpty(skuDetailList)) {
                                                List<MarTemuPromotionActivityGoodsQueryResponse.SkuList> skuLists = skcIdMapSkuIdList.get(goodsId);
                                                List<AmzPromotionsSkuDetail> promotionSkuDetail = skuDetailList.stream().filter(t -> t.getPromotionsId().equals(existPromotion.getId())).collect(Collectors.toList());
                                                for (MarTemuPromotionActivityGoodsQueryResponse.SkuList skuInfo : skuLists) {
                                                    String skuId = skuInfo.getSkuId();
                                                    BigDecimal activitySupplierPrice = skuInfo.getActivitySupplierPrice();
                                                    if (activitySupplierPrice == null || StringUtils.isEmpty(skuId)) {
                                                        log.info("美本促销sku信息接口返回数据为空详情不存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}--skuInfo---{}", invitationId, shopId, goodsId, skuInfo);
                                                        continue;
                                                    }
                                                    AmzPromotionsSkuDetail existSkuDetail = promotionSkuDetail.stream().filter(t -> t.getAsin().equals(skuId)).findFirst().orElse(null);
                                                    if (existSkuDetail == null) {
                                                        log.info("美本促销sku详情在系统不存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}--skuId---{}", invitationId, shopId, goodsId, skuId);
                                                        continue;
                                                    }
                                                    log.info("美本促销sku详情存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}--skuId---{}--更新前数据---{}", invitationId, shopId, goodsId, skuId, existSkuDetail);
                                                    AmzPromotionsSkuDetail updateSkuDetail = new AmzPromotionsSkuDetail();
                                                    updateSkuDetail.setId(existSkuDetail.getId());

                                                    updateSkuDetail.setLastDiscountPrice(activitySupplierPrice.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                                                    updateSkuDetail.setDiscountPrice(updateSkuDetail.getLastDiscountPrice());
                                                    if (existSkuDetail.getPrice() != null) {
                                                        updateSkuDetail.setDiscount(existSkuDetail.getPrice().subtract(updateSkuDetail.getDiscountPrice()));
                                                        updateSkuDetail.setPerFunding(existSkuDetail.getDiscountFlag() + existSkuDetail.getDiscount().toString());
                                                    }
                                                    updateSkuDetail.settingDefaultSystemUpdate();
                                                    log.info("美本促销sku详情更新--平台活动ID---{}--店铺ID---{}--父商品ID---{}--skuId---{}--更新后数据---{}", invitationId, shopId, goodsId, skuId, updateSkuDetail);
                                                    updateAmzPromotionsSkuDetailList.add(updateSkuDetail);

                                                    AmzPromotionsSkuDetail skuDetailStock = BeanCopyUtils.copyBean(updateSkuDetail, AmzPromotionsSkuDetail.class);
                                                    skuDetailStock.setPrice(existSkuDetail.getPrice().multiply(new BigDecimal("100")));
                                                    skuDetailStock.setDiscountPrice(activitySupplierPrice);
                                                    skuDetailStock.setAsin(existSkuDetail.getAsin());
                                                    skuDetailStock.setSellerSku(existSkuDetail.getSellerSku());
                                                    skuDetailStock.setParentAsin(existSkuDetail.getParentAsin());
                                                    //生成库存历史消耗记录
                                                    generateStockHis(amzPromotionsStock, skuDetailStock);

                                                    syncStockDetailList.add(skuDetailStock);
                                                }
                                                if (CollectionUtil.isNotEmpty(updateAmzPromotionsSkuDetailList)) {

                                                    iAmzPromotionsSkuDetailService.updateBatchById(updateAmzPromotionsSkuDetailList);
                                                    updateAmzPromotionsSkuDetailList.clear();
                                                }
                                            }

                                            recordSyncStatusStockAndPriceChange(amzPromotionsStock, syncStockDetailList, existPromotion);
                                        }
                                    }*/

                                    threadPoolTaskExecutor.execute(() -> {

                                        for (MarTemuPromotionActivityGoodsQueryResponse.GoodsList goods : goodsList) {
                                            TemuPromotionMeiBenReceiveMsg msg = new TemuPromotionMeiBenReceiveMsg();
                                            msg.setPullType("api");
                                            msg.setBeginTime(Optional.ofNullable(getTimeStampDate(result.getActivityInfo().getActivityStartTime())).map(t -> DateUtil.convertDateToString(t, DateUtils.YYYY_MM_DD_HH_MM_SS)).orElse(null));
                                            msg.setEndTime(Optional.ofNullable(getTimeStampDate(result.getActivityInfo().getActivityEndTime())).map(t -> DateUtil.convertDateToString(t, DateUtils.YYYY_MM_DD_HH_MM_SS)).orElse(null));
                                            msg.setActivityType(result.getActivityInfo().getActivityType());
//                                            msg.setSubActivityType();
                                            msg.setActivityName(result.getActivityInfo().getActivityName() + "(" + DateUtil.convertDateToString(DateUtil.convertStringToDate(msg.getBeginTime(), DateUtils.YYYY_MM_DD_HH_MM_SS), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(DateUtil.convertStringToDate(msg.getEndTime(), DateUtils.YYYY_MM_DD_HH_MM_SS), "yyyy-MM-dd") + ")");
//                                            msg.setSiteId();
                                            msg.setStoreName(account.getSellerId());
//                                            msg.setNoMallId();
                                            //todo 促销id暂无
                                            msg.setActivityGoodsId(result.getActivityInfo().getActivityId()+goods.getGoodsId());
                                            msg.setPlatFormActivityId(invitationId);
                                            msg.setGoodsId(goods.getGoodsId());
                                            msg.setMallId(account.getSellerId());
//                                            msg.setGoodsImageUrl();
//                                            msg.setGoodsName();
                                            msg.setRemainStock(goods.getRemainingActivityQuantity());
                                            msg.setNum(goods.getActivityQuantity());
                                            if (CollectionUtil.isEmpty(goods.getSkuList())) {
                                                continue;
                                            }
                                            List<ProductChannels> productChannelsList = productChannelsService.lambdaQuery()
                                                    .select(ProductChannels::getSellerSkuPrice,
                                                            ProductChannels::getSellerSku)
                                                    .eq(ProductChannels::getAccountId, account.getFlag())
                                                    .in(ProductChannels::getSellerSku, goods.getSkuList().stream().map(t -> t.getSkuId()).filter(t -> !StringUtils.isEmpty(t)).collect(Collectors.toList())).list();
                                            List<TemuPromotionMeiBenReceiveMsg.SkuInfoList> skuInfoList = new ArrayList<>();
                                            for (MarTemuPromotionActivityGoodsQueryResponse.SkuList sku : goods.getSkuList()) {
                                                TemuPromotionMeiBenReceiveMsg.SkuInfoList skuInfo = new TemuPromotionMeiBenReceiveMsg.SkuInfoList();
                                                skuInfo.setSkuId(sku.getSkuId());
//                                                skuInfo.setSpecValue();
//                                                skuInfo.setSupplierPrice();
                                                skuInfo.setDailyPrice(Optional.ofNullable(productChannelsList).filter(CollectionUtil::isNotEmpty).map(t -> t.stream().filter(q -> sku.getSkuId().equals(q.getSellerSku())).findFirst().orElse(null)).map(t -> t.getSellerSkuPrice()).map(t->t.multiply(new BigDecimal(100))).orElse(null));
//                                                skuInfo.setActivitySupplierPrice();
                                                skuInfo.setActivityPrice(sku.getActivitySupplierPrice());
//                                                skuInfo.setRecommendSupplierPrice(Optional.ofNullable(sku.getActivitySupplierPrice()).map(BigDecimal::toString).orElse(null));
//                                                skuInfo.setRecommendSupplierPriceValue();
//                                                skuInfo.setCurrencySymbol();
//                                                skuInfo.setSymbolPosition();
                                                skuInfoList.add(skuInfo);

                                            }
                                            msg.setSkuInfoList(skuInfoList);
//                                            msg.setStatus();

                                            amzWalmartPromotionService.temuPromotionMessageMeiBenReceive(JSONObject.toJSONString(msg));
                                        }
                                    });

                                }
                            }
                        } else {
                            log.error("拉取美本促销信息失败--平台活动ID---{}--店铺ID---{}--返回数据success为false---失败code---{}---失败msg---{}", invitationId, shopId, response.getErrorCode(), response.getErrorMsg());
                            break;
                        }
                    }
                } else {

                    log.error("拉取美本促销信息为空--平台活动ID---{}--店铺ID---{}--返回数据为空", invitationId, shopId);
                    break;
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("拉取美本促销信息异常--平台活动ID---{}--店铺ID---{}--异常信息---{}", invitationId, shopId, e.getMessage());
                break;
            }
            pageNumber++;


        }


    }

    private void generateStockHis(AmzPromotions amzPromotionsStock, AmzPromotionsSkuDetail skuDetailStock) {

        Date nowDate = DateUtil.UtcToPacificDate(DateUtils.getNowDate());
        log.info("接口拉取美本促销活动生成库存历史消耗记录--促销--[{}]--sku详情--[{}]", JSONObject.toJSONString(amzPromotionsStock), JSONObject.toJSONString(skuDetailStock));
        try {
            if (DateUtil.compareDate(DateUtil.convertDateToString(amzPromotionsStock.getEndTime(), DateUtils.DEFAULT_DATE_FORMAT), DateUtil.convertDateToString(DateUtil.addDate(nowDate, -1), DateUtils.DEFAULT_DATE_FORMAT)) < 0) {
                log.error("接口拉取美本促销活动生成库存历史消耗记录 with expired promotion--{}", JSONObject.toJSONString(amzPromotionsStock));
                return;
            }
            LambdaQueryWrapper<AmzPromotionFeeDateHisRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AmzPromotionFeeDateHisRecord::getPromotionId, amzPromotionsStock.getId())
                    .eq(AmzPromotionFeeDateHisRecord::getSkcId, skuDetailStock.getParentAsin())
                    .eq(AmzPromotionFeeDateHisRecord::getSellerSku, skuDetailStock.getSellerSku())
                    .ge(AmzPromotionFeeDateHisRecord::getDate, DateUtil.convertDateToString(nowDate, DateUtils.DEFAULT_DATE_FORMAT))
                    .lt(AmzPromotionFeeDateHisRecord::getDate, DateUtil.convertDateToString(DateUtil.addDate(nowDate, 1), DateUtils.DEFAULT_DATE_FORMAT))
                    .orderByDesc(AmzPromotionFeeDateHisRecord::getDate)
                    .last("limit 1");
            AmzPromotionFeeDateHisRecord one = amzPromotionFeeDateHisRecordService.getOne(wrapper);
            AmzPromotionFeeDateHisRecord record = new AmzPromotionFeeDateHisRecord();
            if (one != null) {
                record.setId(one.getId());
                record.settingDefaultSystemUpdate();
            } else {
                record.settingDefaultSystemCreate();
            }
            record.setActivityType(amzPromotionsStock.getInvitationType());
            record.setPromotionId(amzPromotionsStock.getId());
            record.setAmzSkuDetailId(skuDetailStock.getId());
            record.setAsin(skuDetailStock.getAsin());
            record.setSellerSku(skuDetailStock.getSellerSku());
            record.setShopId(amzPromotionsStock.getShopId());
            record.setDailyPrice(skuDetailStock.getPrice());
            record.setActivityPrice(skuDetailStock.getDiscountPrice());
            record.setSkcStock(amzPromotionsStock.getSkcStock());
            record.setRemainStock(amzPromotionsStock.getRemainStock());
            record.setSkcId(skuDetailStock.getParentAsin());
            record.setDate(nowDate);
            record.setRemark("interface_generate_his_record");
            amzPromotionFeeDateHisRecordService.saveOrUpdate(record);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("接口拉取美本促销活动[{}]明细[{}]生成库存历史消耗记录 with error--{}", JSONObject.toJSONString(amzPromotionsStock), JSONObject.toJSONString(skuDetailStock), e.getMessage());
        }

    }

    public void recordSyncStatusStockAndPriceChange(AmzPromotions amzPromotions, List<AmzPromotionsSkuDetail> skuDetailList, AmzPromotions byId) {

        try {
            boolean needRecord = false;
            Integer stock = null;
            boolean b = amzPromotions.getSkcStock() == null || amzPromotions.getRemainStock() == null || byId.getSkcStock() == null || byId.getRemainStock() == null;
            if (b) {
                needRecord = true;
            } else {
                stock = (amzPromotions.getSkcStock() - amzPromotions.getRemainStock()) - (byId.getSkcStock() - byId.getRemainStock());
                if (stock.compareTo(0) != 0) {
                    needRecord = true;
                }
            }

            if (!needRecord) {
                return;
            }
            //本次消耗库存 （返回的提报库存-剩余库存）-（系统里的提报库存-系统里的剩余库存）
            AmzPromotionsOperateLog operateLog = new AmzPromotionsOperateLog();
            StringBuilder content = new StringBuilder(String.format("接口拉取:[提报库存]:%s,[剩余库存]:%s,[本次消耗库存]:%s。", amzPromotions.getSkcStock(), amzPromotions.getRemainStock(), stock));
            if (CollectionUtil.isNotEmpty(skuDetailList)) {
                skuDetailList.forEach(t -> {
                    content.append("[商品ID]:").append(t.getAsin()).append("[活动申报价格]:").append(t.getDiscountPrice() != null ? t.getDiscountPrice().divide(new BigDecimal("100"), RoundingMode.HALF_UP).toString() : null);
                });
            }
            operateLog.setPromotionId(amzPromotions.getId());
            operateLog.setOperateType(AmzPromotionsOperateTypeEnum.SYSTEM_UPDATE.value());
            operateLog.setDetail(content.toString());
            operateLog.settingDefaultSystemCreate();
            operateLog.settingDefaultSystemUpdate();
            operateLog.setOrganizationId(amzPromotions.getOrganizationId());
            amzPromotionsOperateLogService.save(operateLog);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("记录美本促销同步状态异常--促销--[{}]--sku详情--[{}]--原始促销信息--[{}]异常信息---{}", JSONObject.toJSONString(amzPromotions), JSONObject.toJSONString(skuDetailList), JSONObject.toJSONString(byId), e.getMessage());
        }

    }

    /**
     * 获取美本活动对应的提报商品列表 bg.promotion.activity.candidate.goods.query
     */
    @Override
    public void getMeibenActivityGoodsList() {


        List<Integer> meibenType = Arrays.asList(MarPromotionInvitationTypeEnum.SECKILL_ACTIVITY.getType(), MarPromotionInvitationTypeEnum.ADVANCED_BIG_SALE.getType(),
                MarPromotionInvitationTypeEnum.OFFICIAL_BIG_SALE.getType(), MarPromotionInvitationTypeEnum.CLEAR_STOCK_ACTIVITY.getType());

        List<MarPlatformPromotionsInfo> platformPromotionsInfoList = marPlatformPromotionsInfoMapper.selectMeiPlatformList(meibenType, Arrays.asList(-1, 1));

        if (CollectionUtil.isEmpty(platformPromotionsInfoList)) {
            log.info("美本已提报平台活动为空，无需拉取");
            return;
        }

        List<Account> accountList = accountService.listByIds(platformPromotionsInfoList.stream().map(t -> t.getShopId()).distinct().collect(Collectors.toList()));
        List<List<MarPlatformPromotionsInfo>> partition = ListUtils.partition(platformPromotionsInfoList, (int) Math.ceil(platformPromotionsInfoList.size() / 10.0));
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (List<MarPlatformPromotionsInfo> list : partition) {

            futures.add(CompletableFuture.runAsync(() -> {
                handleMeibenActivityGoodsList(list, accountList);
            }, threadPoolTaskExecutor));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    private void handleMeibenActivityGoodsList(List<MarPlatformPromotionsInfo> list, List<Account> accountList) {


        Map<String, List<MarPlatformPromotionsInfo>> invitationIdShopMap = list.stream().collect(Collectors.groupingBy(t -> t.getInvitationId() + "_" + t.getShopId().toString()));
        invitationIdShopMap.forEach((key, value) -> {

            String[] split = key.split("_");
            String invitationId = split[0];
            Integer shopId = Integer.parseInt(split[1]);

            pullMeibenActivityGoodsList(invitationId, shopId, accountList.stream().filter(t -> t.getId().equals(shopId)).findFirst().get());
        });
    }

    public void pullMeibenActivityGoodsList(String invitationId, Integer shopId, Account account) {

        TemuPromotionActivityGoodsQuery request = new TemuPromotionActivityGoodsQuery() {
            @Override
            public TemuUrl type() {
                return TemuUrl.BG_PROMOTION_ACTIVITY_CANDIDATE_GOODS_QUERY;
            }
        };
        request.setActivityId(Long.parseLong(invitationId));
        int pageNumber = 1;
        int pageSize = 100;
        while (true) {
            request.setPageNumber(pageNumber);
            request.setPageSize(pageSize);
//            List<MarPromotionsReportProduct> updatePromotionReportProductList = new ArrayList<>();

            try {
                log.info("拉取美本促销提报商品信息请求--平台活动ID---{}--店铺ID---{}--页码---{}--页大小---{}---请求数据---{}", invitationId, shopId, pageNumber, pageSize, JSONObject.toJSONString(request));
                BgPromotionActivityCandidateGoodsQueryResponse response = temuApi.requestUsTemu(account, request, BgPromotionActivityCandidateGoodsQueryResponse.class);
                log.info("拉取美本促销提报商品信息响应--平台活动ID---{}--店铺ID---{}--页码---{}--页大小---{}--返回数据---{}", invitationId, shopId, pageNumber, pageSize, JSONObject.toJSONString(response));
                if (response != null) {

                    if (response.getSuccess() == null) {
                        log.error("拉取美本促销提报商品信息失败--平台活动ID---{}--店铺ID---{}--返回数据success为空---失败code---{}---失败msg---{}", invitationId, shopId, response.getErrorCode(), response.getErrorMsg());
                        break;
                    } else {
                        if (response.getSuccess()) {

                            BgPromotionActivityCandidateGoodsQueryResponse.Result result = response.getResult();
                            if (result == null || result.getTotal() == null || result.getTotal() == 0) {
                                log.info("拉取美本促销提报商品信息成功--平台活动ID---{}--店铺ID---{}--返回数据result为空或total为0为空或total为0", invitationId, shopId);
                                break;
                            } else {

                                List<BgPromotionActivityCandidateGoodsQueryResponse.GoodsList> goodsList = result.getGoodsList();
                                if (CollectionUtil.isEmpty(goodsList)) {
                                    log.info("拉取美本促销提报商品信息成功--平台活动ID---{}--店铺ID---{}--返回数据goodsList为空", invitationId, shopId);
                                    break;
                                } else {

                                    List<MarPromotionsSkcInfoLocalShopReceIveMsg> list = buildMeibenReportProductList(goodsList, account, invitationId, result.getActivityInfo().getActivityType());

                                    if (CollectionUtil.isNotEmpty(list)) {
                                        list.forEach(t -> {
                                            iMarPromotionsReportProductService.saveTemuSkcInfoLocalShop(t);
                                        });
                                    }
                                    /*Map<String, List<BgPromotionActivityCandidateGoodsQueryResponse.SkuList>> skcIdMapSkuIdList = goodsList.stream().filter(t -> CollectionUtil.isNotEmpty(t.getSkuList())).collect(Collectors.toMap(t -> t.getGoodsId(), t -> t.getSkuList(), (k1, k2) -> k1));

                                    List<String> skcIdList = goodsList.stream().map(t -> t.getGoodsId()).distinct().collect(Collectors.toList());

                                    //查询存在的提报商品信息
                                    LambdaQueryWrapper<MarPromotionsReportProduct> queryWrapper = new LambdaQueryWrapper<>();
                                    queryWrapper.eq(MarPromotionsReportProduct::getShopId, shopId)
                                            .eq(MarPromotionsReportProduct::getInvitationId, invitationId)
                                            .in(MarPromotionsReportProduct::getSkcId, skcIdList)
                                            .eq(MarPromotionsReportProduct::getDataType, 1);
                                    List<MarPromotionsReportProduct> existReportProductList = iMarPromotionsReportProductService.list(queryWrapper);

                                    if (CollectionUtil.isEmpty(existReportProductList)) {
                                        log.info("美本促销提报商品信息在系统里查询不存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}", invitationId, shopId, skcIdList);
                                        continue;
                                    }
                                    for (BgPromotionActivityCandidateGoodsQueryResponse.GoodsList goods : goodsList) {
                                        String goodsId = goods.getGoodsId();
                                        Integer recommendActivityQuantity = goods.getRecommendActivityQuantity();

                                        if (recommendActivityQuantity == null) {
                                            log.info("美本促销提报商品信息在系统里存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}--接口返回建议库存为空", invitationId, shopId, goodsId);
                                        }
                                        List<MarPromotionsReportProduct> matchData = existReportProductList.stream().filter(t -> goods.equals(t.getSkcId())).collect(Collectors.toList());
                                        if (CollectionUtil.isEmpty(matchData)) {
                                            log.info("美本促销提报商品信息在系统里不存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}", invitationId, shopId, goodsId);
                                        } else {
                                            log.info("美本促销提报商品信息在系统里存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}--更新前数据---{}", invitationId, shopId, goodsId, matchData);

                                            matchData.forEach(q -> q.setTargetActivityStock(recommendActivityQuantity));
                                            List<BgPromotionActivityCandidateGoodsQueryResponse.SkuList> skuLists = skcIdMapSkuIdList.get(goodsId);

                                            if (CollectionUtil.isEmpty(skuLists)) {
                                                log.info("美本促销提报商品信息在系统里存在--平台活动ID---{}--店铺ID---{}--父商品ID---{}--接口返回skuList为空", invitationId, shopId, goodsId);
                                                continue;
                                            }
                                            for (BgPromotionActivityCandidateGoodsQueryResponse.SkuList skuInfo : skuLists) {
                                                String skuId = skuInfo.getSkuId();
                                                BigDecimal recommendActivitySupplierPrice = skuInfo.getRecommendActivitySupplierPrice();
                                                if (recommendActivitySupplierPrice == null || StringUtils.isEmpty(skuId)) {
                                                    log.info("美本促销提报商品接口返回sku数据sku为空或建议活动价格为空--平台活动ID---{}--店铺ID---{}--父商品ID---{}--skuInfo---{}", invitationId, shopId, goodsId, skuInfo);
                                                    continue;
                                                }
                                                log.info("美本促销提报商品接口返回sku数据--平台活动ID---{}--店铺ID---{}--父商品ID---{}--skuId---{}--建议活动价格---{}", invitationId, shopId, goodsId, skuId, recommendActivitySupplierPrice);
                                                matchData.forEach(t -> {
                                                    if (skuId.equals(t.getSkuId())) {
                                                        t.setTargetActivityPrice(recommendActivitySupplierPrice.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                                                    }
                                                });

                                            }
                                            updatePromotionReportProductList.addAll(matchData);
                                            if (CollectionUtil.isNotEmpty(updatePromotionReportProductList)) {
                                                iMarPromotionsReportProductService.updateBatchById(updatePromotionReportProductList);
                                                updatePromotionReportProductList.clear();
                                            }
                                        }
                                    }*/
                                }
                            }
                        } else {
                            log.error("拉取美本促销信息失败--平台活动ID---{}--店铺ID---{}--返回数据success为false---失败code---{}---失败msg---{}", invitationId, shopId, response.getErrorCode(), response.getErrorMsg());
                            break;
                        }
                    }
                } else {

                    log.error("拉取美本促销提报商品信息为空--平台活动ID---{}--店铺ID---{}--返回数据为空", invitationId, shopId);
                    break;
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("拉取美本促销提报商品信息异常--平台活动ID---{}--店铺ID---{}--异常信息---{}", invitationId, shopId, e.getMessage());
                break;
            }
            pageNumber++;


        }


    }

    private List<MarPromotionsSkcInfoLocalShopReceIveMsg> buildMeibenReportProductList(List<BgPromotionActivityCandidateGoodsQueryResponse.GoodsList> goodsList, Account account, String invitationId, Integer activityType) {

        List<MarPromotionsSkcInfoLocalShopReceIveMsg> list = new ArrayList<>();
        for (BgPromotionActivityCandidateGoodsQueryResponse.GoodsList goods : goodsList) {

            if (StringUtils.isEmpty(goods.getGoodsId())) {
                log.error("美本促销提报商品信息接口返回数据goodsId为空--平台活动ID---{}--店铺ID---{}--父商品ID---{}--goods---{}", invitationId, account.getId(), goods.getGoodsId(), JSONObject.toJSONString(goods));
                continue;
            }
            if (CollectionUtil.isEmpty(goods.getSkuList())) {
                log.error("美本促销提报商品信息接口返回数据skuList为空--平台活动ID---{}--店铺ID---{}--父商品ID---{}--goods---{}", invitationId, account.getId(), goods.getGoodsId(), JSONObject.toJSONString(goods));
                continue;
            }
            MarPromotionsSkcInfoLocalShopReceIveMsg msg = new MarPromotionsSkcInfoLocalShopReceIveMsg();
            msg.setGoodsId(goods.getGoodsId());
//            msg.setSubmissionGoodsDetailId();
            msg.setMallId(account.getSellerId());

//            msg.setGoodsQuantity();
//            msg.setRecommendActivityQuantity();
            //todo 建议库存通过平台活动队列推送补充
//            msg.setMinTotalActivityQuantity(goods.getRecommendActivityQuantity());
//            msg.setAvailable();
//            msg.setUnAvailableReason();
//            msg.setUnAvailableReasonType();
//            msg.setUnAvailableReasonDesc();
            msg.setInvitationId(invitationId);
            msg.setInvitationType(activityType);

            List<ProductChannels> productChannelsList = productChannelsService.lambdaQuery()
                    .select(ProductChannels::getImageUrl,
                            ProductChannels::getSellerSkuPrice,
                            ProductChannels::getAsin1,
                            ProductChannels::getSellerSku,
                            ProductChannels::getTitle)
                    .eq(ProductChannels::getAccountId, account.getFlag())
                    .in(ProductChannels::getSellerSku, goods.getSkuList().stream().map(BgPromotionActivityCandidateGoodsQueryResponse.SkuList::getSkuId).filter(t -> !StringUtils.isEmpty(t)).collect(Collectors.toList())).list();

            if (CollectionUtil.isEmpty(productChannelsList)) {
                productChannelsList = null;
            }
            msg.setGoodsImageUrl(Optional.ofNullable(productChannelsList).map(t -> t.get(0).getImageUrl()).orElse(null));
            //TODO
            msg.setGoodsName(Optional.ofNullable(productChannelsList).map(t -> t.get(0).getTitle()).orElse(null));
            List<MarPromotionsSkcInfoLocalShopReceIveMsg.SkuInfoListBean> skuInfoList = new ArrayList<>();
            for (BgPromotionActivityCandidateGoodsQueryResponse.SkuList skuInfo : goods.getSkuList()) {
                if (StringUtils.isEmpty(skuInfo.getSkuId())) {
                    log.error("美本促销提报商品信息接口返回数据skuId为空--平台活动ID---{}--店铺ID---{}--父商品ID---{}--skuInfo---{}", invitationId, account.getId(), goods.getGoodsId(), JSONObject.toJSONString(skuInfo));
                    continue;
                }
                MarPromotionsSkcInfoLocalShopReceIveMsg.SkuInfoListBean skuInfoListBean = new MarPromotionsSkcInfoLocalShopReceIveMsg.SkuInfoListBean();
                skuInfoListBean.setSkuId(skuInfo.getSkuId());
//                skuInfoListBean.setSpecValue();
//                skuInfoListBean.setSupplierPrice();
                //todo 日常价
                skuInfoListBean.setSupplierPriceValue(Optional.ofNullable(productChannelsList).map(t -> t.stream().filter(q -> skuInfo.getSkuId().equals(q.getSellerSku())).findFirst().orElse(null)).map(ProductChannels::getSellerSkuPrice).map(t->t.multiply(new BigDecimal(100))).orElse(null));
//                skuInfoListBean.setRecommendSupplierPrice();
                skuInfoListBean.setRecommendSupplierPriceValue(skuInfo.getRecommendActivitySupplierPrice());
//                skuInfoListBean.setCurrencySymbol();
//                skuInfoListBean.setSymbolPosition();
                skuInfoList.add(skuInfoListBean);
            }
            msg.setSkuInfoList(skuInfoList);
            list.add(msg);
        }
        return list;
    }

    /**
     * 获取美本店铺促销平台活动信息
     */
    @Override
    public void getMeibenShopPromotion() {

        //查询temu店铺
        QueryWrapper<Account> gt = new QueryWrapper<Account>()
                .eq("type", "temu")
                .eq("active", "Y")
                .eq("sale_channel", "2")
                .eq("JSON_VALID(connect_str)", 1)
                .gt("JSON_EXTRACT(connect_str, '$.appkey')", 0);
        List<Account> accountList = accountService.list(gt);
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        List<List<Account>> partition = ListUtils.partition(accountList, (int) Math.ceil(accountList.size() / 10.0));
        for (List<Account> accounts : partition) {
            futureList.add(CompletableFuture.runAsync(() -> {
                handleMeibenPlatform(accounts);
            }, threadPoolTaskExecutor));
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

    }

    private void handleMeibenPlatform(List<Account> accounts) {

        accounts.forEach(account -> {
            try {

                TemuPromotionPlatformActiveQuery request = new TemuPromotionPlatformActiveQuery() {
                    @Override
                    public TemuUrl type() {
                        return TemuUrl.BG_PROMOTION_ACTIVITY_QUERY;
                    }
                };

                List<Integer> meibenType = Arrays.asList(MarPromotionInvitationTypeEnum.SECKILL_ACTIVITY.getType(), MarPromotionInvitationTypeEnum.ADVANCED_BIG_SALE.getType(),
                        MarPromotionInvitationTypeEnum.OFFICIAL_BIG_SALE.getType(), MarPromotionInvitationTypeEnum.CLEAR_STOCK_ACTIVITY.getType());
                for (Integer invitationType : meibenType) {

                    int pageNumber = 1;
                    int pageSize = 100;
                    while (true) {
                        List<MarPlatformPromotionsInfo> insertOrUpdateList = new ArrayList<>();
                        try {
                            request.setPageNumber(pageNumber);
                            request.setPageSize(pageSize);
                            request.setActivityType(invitationType);
                            log.info("拉取美本店铺促销平台活动信息请求--店铺ID---{}--请求数据---{}", account.getId(), JSONObject.toJSONString(request));
                            BgPromotionActivityQueryResponse response = temuApi.requestUsTemu(account, request, BgPromotionActivityQueryResponse.class);
                            if (response == null) {
                                log.error("拉取美本店铺促销平台活动信息失败--店铺ID---{}--返回数据为空---请求数据---{}", account.getId(), JSONObject.toJSONString(request));
                                break;
                            }
                            if (response.getSuccess() == null || !response.getSuccess()) {
                                log.error("拉取美本店铺促销平台活动信息失败--店铺ID---{}--返回数据success为空或false---失败code---{}---失败msg---{}---请求数据---{}", account.getId(), response.getErrorCode(), response.getErrorMsg(), JSONObject.toJSONString(request));
                                break;
                            }
                            if (response.getResult() == null) {
                                log.info("拉取美本店铺促销平台活动信息成功--店铺ID---{}--返回数据result为空---请求数据---{}", account.getId(), JSONObject.toJSONString(request));
                                break;
                            }
                            if (response.getResult().getTotal() == null || response.getResult().getTotal() == 0) {
                                log.info("拉取美本店铺促销平台活动信息成功--店铺ID---{}--返回数据result.total为空或0---请求数据---{}", account.getId(), JSONObject.toJSONString(request));
                                break;
                            }
                            if (CollectionUtil.isEmpty(response.getResult().getActivityList())) {
                                log.info("拉取美本店铺促销平台活动信息成功--店铺ID---{}--返回数据result.activityList为空---请求数据---{}", account.getId(), JSONObject.toJSONString(request));
                                break;
                            }
                            log.info("拉取美本店铺促销平台活动信息成功--店铺ID---{}--请求数据---{}--返回数据---{}", account.getId(), JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                            List<BgPromotionActivityQueryResponse.ActivityList> activityList = response.getResult().getActivityList();
                            List<String> invitationIdList = activityList.stream().map(t -> t.getActivityId()).distinct().collect(Collectors.toList());
                            LambdaQueryWrapper<MarPlatformPromotionsInfo> wrapper = new LambdaQueryWrapper<>();
                            wrapper.eq(MarPlatformPromotionsInfo::getShopId, account.getId())
                                    .in(MarPlatformPromotionsInfo::getInvitationId, invitationIdList)
                                    .eq(MarPlatformPromotionsInfo::getInvitationType, invitationType);
                            List<MarPlatformPromotionsInfo> existPlatformPromotionInfoList = marPlatformPromotionsInfoService.list(wrapper);

                            for (BgPromotionActivityQueryResponse.ActivityList insertActivity : activityList) {

                                MarPlatformPromotionsInfo insertPlatformPromotionInfo = new MarPlatformPromotionsInfo();
                                insertPlatformPromotionInfo.setOrganizationId(account.getOrgId());


                                insertPlatformPromotionInfo.setShopId(account.getId().longValue());
                                insertPlatformPromotionInfo.setChannel("temu");

                                insertPlatformPromotionInfo.setInvitationId(insertActivity.getActivityId());
                                insertPlatformPromotionInfo.setInvitationType(invitationType);
                                insertPlatformPromotionInfo.setBeginTime(DateUtil.UtcToPacificDate(DateUtils.timestampToDate(insertActivity.getActivityStartTime() * 1000, DateUtils.DEFAULT_TIME_FORMAT)));
                                insertPlatformPromotionInfo.setEndTime(DateUtil.UtcToPacificDate(DateUtils.timestampToDate(insertActivity.getActivityEndTime() * 1000, DateUtils.DEFAULT_TIME_FORMAT)));
                                insertPlatformPromotionInfo.setInvitationState(getInvitationState(insertPlatformPromotionInfo.getBeginTime(), insertPlatformPromotionInfo.getEndTime()));

                                insertPlatformPromotionInfo.setInvitationName(insertActivity.getActivityName() + "(" + DateUtil.convertDateToString(insertPlatformPromotionInfo.getBeginTime(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(insertPlatformPromotionInfo.getEndTime(), "yyyy-MM-dd") + ")");
                                insertPlatformPromotionInfo.settingDefaultSystemUpdate();
                                if (CollectionUtil.isNotEmpty(existPlatformPromotionInfoList)) {
                                    MarPlatformPromotionsInfo existInfo = existPlatformPromotionInfoList.stream().filter(t -> insertActivity.getActivityId().equals(t.getInvitationId())).findFirst().orElse(null);
                                    if (existInfo == null) {
                                        insertPlatformPromotionInfo.settingDefaultSystemCreate();
                                    } else {
                                        insertPlatformPromotionInfo.setId(existInfo.getId());
                                    }
                                }
                                insertOrUpdateList.add(insertPlatformPromotionInfo);
                            }
                            if (CollectionUtil.isNotEmpty(insertOrUpdateList)) {
                                marPlatformPromotionsInfoService.saveOrUpdateBatch(insertOrUpdateList);
                                insertOrUpdateList.clear();
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("拉取美本店铺促销平台活动信息异常--店铺ID---{}--异常信息---{}", account.getId(), e.getMessage());
                            break;
                        }
                        pageNumber++;

                    }

                }


            } catch (Exception e) {
                e.printStackTrace();
                log.error("拉取美本店铺促销平台活动信息异常--店铺ID---{}--异常信息---{}", account.getId(), e.getMessage());
            }
        });
    }

    private Integer getInvitationState(Date beginTime, Date endTime) {
        Date nowPstDate = DateUtil.UtcToPacificDate(DateUtils.getNowDate());
        if (DateUtil.compareDate(beginTime, nowPstDate) > 0) {
            //未开始
            return -1;
        } else {
            if (DateUtil.compareDate(endTime, nowPstDate) <= 0) {
                //已结束
                return 0;
            }
            //进行中
            return 1;
        }

    }


    /**
     * 定时拉取半托管美本后台促销
     *
     * @param allPull
     */
    @Override
    public void getTemuSemiShopPromotion(Boolean allPull) {
        QueryWrapper<Account> wrapper = new QueryWrapper<Account>()
                .eq("type", "temu")
                .eq("active", "Y")
                .eq("sale_channel", "1")
                .eq("JSON_VALID(connect_str)", 1)
                .gt("JSON_EXTRACT(connect_str, '$.selfCnAccessToken')", 0)
                .in("org_id",Arrays.asList(1000049,1000707,1000704));
        List<Account> accountList = accountService.list(wrapper);
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        List<List<Account>> partition = ListUtils.partition(accountList, (int) Math.ceil(accountList.size() / 10.0));
        for (List<Account> accounts : partition) {
            futureList.add(CompletableFuture.runAsync(() -> {
                handleSemiPromotion(accounts, allPull);
            }, threadPoolTaskExecutor));
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();


    }

    private void handleSemiPromotion(List<Account> accounts, Boolean allPull) {

        accounts.forEach(account -> {
            String sellerId = account.getSellerId();
            try {
                TemuSemiPromotionActiveQuery request = new TemuSemiPromotionActiveQuery() {
                    @Override
                    public TemuUrl type() {
                        return TemuUrl.BG_MARKETING_ACTIVITY_ENROLL_LIST_GET;
                    }
                };

                if (allPull) {
                    pullSemiPromotion(account, sellerId, request, null);
                } else {
                    List<Integer> sessionStatusList = Arrays.asList(1, 2);
                    for (Integer sessionStatus : sessionStatusList) {
                        pullSemiPromotion(account, sellerId, request, sessionStatus);
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
                log.error("拉取半托管店铺促销平台活动信息请求异常--店铺ID---{}--异常信息---{}", account.getId(), e.getMessage());
            }
        });
    }

    private void pullSemiPromotion(Account account, String sellerId, TemuSemiPromotionActiveQuery request, Integer sessionStatus) {
        int pageNumber = 1;
        int pageSize = 50;
        while (true) {
            request.setMallId(Long.parseLong(sellerId));
            if (sessionStatus != null) {
                request.setSessionStatus(2);
            }
            request.setPageNo(pageNumber);
            request.setPageSize(pageSize);
            log.info("拉取半托管店铺促销平台活动信息请求--店铺ID---{}--请求数据---{}", account.getId(), JSONObject.toJSONString(request));
            try {
                TemuSemiPromotionActiveResponse response = temuApi.requestSelfCnTemu(account, request, TemuSemiPromotionActiveResponse.class);
                if (response == null || response.getSuccess() == null || !response.getSuccess() || response.getResult() == null || response.getResult().getTotal() == null || response.getResult().getTotal() == 0 || CollectionUtil.isEmpty(response.getResult().getList())) {
                    log.error("拉取半托管店铺促销平台活动信息请求失败--店铺ID---{}--返回数据为空---请求数据---{}", account.getId(), JSONObject.toJSONString(request));

                    if (response != null && StringUtils.isNotEmpty(response.getErrorMsg())) {
                        threadPoolTaskExecutor.execute(() -> {
                            String advice = "半托管店铺[" + account.getTitle() + "]拉取temu促销活动失败，失败原因:" + response.getErrorMsg();
                            WeComRobotUtil.sendTextMsgNew(advice, "TEMU_SEMI_PROMOTION", weChatBootConfigure);
                        });
                    }

                    break;
                } else {
                    log.info("拉取半托管店铺促销平台活动信息请求成功--店铺ID---{}--请求数据---{}--返回数据---{}", account.getId(), JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                    try {
                        for (TemuSemiPromotionActiveResponse.Item item : response.getResult().getList()) {
                            AmzTemuPromotionMessageLimitReceiveMsg msg = buildMessage(item, sellerId, account.getId());
                            amzWalmartPromotionService.temuPromotionMessageLimitReceive(JSONObject.toJSONString(msg));
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("拉取半托管店铺促销平台活动信息请求异常--店铺ID---{}--异常信息---{}", account.getId(), e.getMessage());
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("拉取半托管店铺促销平台活动信息请求异常--店铺ID---{}--异常信息---{}", account.getId(), e.getMessage());
            }
            pageNumber++;
        }
    }

    private AmzTemuPromotionMessageLimitReceiveMsg buildMessage(TemuSemiPromotionActiveResponse.Item item, String sellerId, Integer shopId) {
        AmzTemuPromotionMessageLimitReceiveMsg receiveMsg = new AmzTemuPromotionMessageLimitReceiveMsg();
        receiveMsg.setGoodsId(item.getGoodsId());
        receiveMsg.setSessionEndTime(item.getSessionEndTime());
        receiveMsg.setActivityThematicName(item.getActivityThematicName());
        receiveMsg.setActivityThematicId(item.getActivityThematicId());

        receiveMsg.setRemainStock(item.getRemainingActivityStock());
        receiveMsg.setNum(item.getActivityStock());
        receiveMsg.setProductId(item.getProductId());
//        receiveMsg.setVersion("0");
        receiveMsg.setEnrollTime(item.getEnrollTime());
        receiveMsg.setEnrollId(item.getEnrollId());
        receiveMsg.setMallId(sellerId);
        receiveMsg.setInvitationType(item.getActivityType());
        if (CollectionUtil.isNotEmpty(item.getAssignSessionList())) {
            List<AmzTemuPromotionMessageLimitReceiveMsg.AssignSessionList> assignSessionList = new ArrayList<>();
            for (TemuSemiPromotionActiveResponse.AssignSession assignSession : item.getAssignSessionList()) {
                AmzTemuPromotionMessageLimitReceiveMsg.AssignSessionList session = new AmzTemuPromotionMessageLimitReceiveMsg.AssignSessionList();
                session.setSessionStatus(assignSession.getSessionStatus());
                session.setSessionId(assignSession.getSessionId());
                session.setStartDateStr(assignSession.getStartDateStr());
                session.setDurationDays(assignSession.getDurationDays());
                session.setSessionName(assignSession.getSessionName());
                session.setStartTime(assignSession.getStartTime());
                session.setEndTime(assignSession.getEndTime());
                session.setEndDateStr(assignSession.getEndDateStr());
                assignSessionList.add(session);

            }
            receiveMsg.setAssignSessionList(assignSessionList);
        }
        if (CollectionUtil.isNotEmpty(item.getSkcList())) {
            if (item.getSkcList().get(0).getSkuList().get(0).getSitePriceList().get(0).getSiteId() != null) {
                List<AmzTemuPromotionMessageLimitReceiveMsg.Sites> sites = new ArrayList<>();
                AmzTemuPromotionMessageLimitReceiveMsg.Sites limitSite = new AmzTemuPromotionMessageLimitReceiveMsg.Sites();
                limitSite.setSiteId(item.getSkcList().get(0).getSkuList().get(0).getSitePriceList().get(0).getSiteId().intValue());
                limitSite.setSiteName(item.getSkcList().get(0).getSkuList().get(0).getSitePriceList().get(0).getSiteName());
                sites.add(limitSite);
                receiveMsg.setSites(sites);
            }
            List<AmzTemuPromotionMessageLimitReceiveMsg.SkcList> skcList = new ArrayList<>();
            for (TemuSemiPromotionActiveResponse.Skc skc : item.getSkcList()) {
                List<AmzPromotionsSkuDetail> sellerSkuMapExtCode = amzPromotionsSkuDetailMapper.getSellerSkuAndExtcodeByShopAndSellerSku(skc.getSkcId(), skc.getSkuList().stream().map(t -> t.getSkuId()).collect(Collectors.toList()), shopId);
                AmzTemuPromotionMessageLimitReceiveMsg.SkcList limitSkc = new AmzTemuPromotionMessageLimitReceiveMsg.SkcList();
                limitSkc.setSkcId(skc.getSkcId());
                List<AmzTemuPromotionMessageLimitReceiveMsg.SkuList> skuListList = new ArrayList<>();
                for (TemuSemiPromotionActiveResponse.Sku sku : skc.getSkuList()) {
                    AmzTemuPromotionMessageLimitReceiveMsg.SkuList limitSku = new AmzTemuPromotionMessageLimitReceiveMsg.SkuList();
                    limitSku.setDailyPrice(sku.getSitePriceList().get(0).getDailyPrice());
                    limitSku.setActivityPrice(sku.getSitePriceList().get(0).getActivityPrice());
                    limitSku.setSellerSku(sku.getSkuId());
                    if (CollectionUtil.isNotEmpty(sellerSkuMapExtCode)) {
                        sellerSkuMapExtCode.stream().filter(t -> t.getParentAsin().equals(skc.getSkcId()) && t.getSellerSku().equals(sku.getSkuId())).findFirst().ifPresent(r -> limitSku.setExtCode(r.getExtCode()));
                    }

                    limitSku.setSitePriceList(JSONObject.parseArray(JSONArray.toJSONString(sku.getSitePriceList()), AmzTemuPromotionMessageLimitReceiveMsg.SitePriceList.class));
                    skuListList.add(limitSku);
                }
                limitSkc.setSkuList(skuListList);
                skcList.add(limitSkc);
            }
            receiveMsg.setSkcList(skcList);
        }
        return receiveMsg;
    }


    /**
     * 定时拉取已存在半托管美本后台促销
     */
    @Override
    public void getTemuSemiShopPromotion() {

        List<Integer> activeType = Arrays.asList(MarPromotionInvitationTypeEnum.TIME_LIMIT.getType(), MarPromotionInvitationTypeEnum.TIME_LIMIT_SECKILL.getType(), MarPromotionInvitationTypeEnum.CLEARANCE_DEALS_SECKILL.getType(),
                MarPromotionInvitationTypeEnum.FLASH_SALE.getType(), MarPromotionInvitationTypeEnum.CLEARANCE_DEALS.getType(), MarPromotionInvitationTypeEnum.GRAND_PROMOTION.getType());
        List<AmzPromotions> promotionsList = amzPromotionsMapper.selectExistSemiShortPromotion(activeType);
        handleExistSemiPromotion(promotionsList);
    }

    private void handleExistSemiPromotion(List<AmzPromotions> amzPromotionsList) {
        if (CollectionUtil.isNotEmpty(amzPromotionsList)) {
            List<Account> accounts = accountService.listByIds(amzPromotionsList.stream().map(t -> t.getShopId()).collect(Collectors.toList()));
            List<CompletableFuture<Void>> futureList = new ArrayList<>();
            List<List<AmzPromotions>> partition = ListUtils.partition(amzPromotionsList, (int) Math.ceil(amzPromotionsList.size() / 10.0));
            for (List<AmzPromotions> promotions : partition) {
                futureList.add(CompletableFuture.runAsync(() -> {
                    try {
                        handleExistSemiPromotion(promotions, accounts);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("并发处理半托管店铺促销平台活动信息请求异常--异常信息---{}", e.getMessage());
                    }

                }, threadPoolTaskExecutor));
            }
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        }
    }

    private void handleExistSemiPromotion(List<AmzPromotions> amzPromotionsList, List<Account> accounts) {

        List<Integer> shopIdList = amzPromotionsList.stream().map(t -> t.getShopId().intValue()).distinct().collect(Collectors.toList());
        shopIdList.forEach(t -> {
            Account account = accounts.stream().filter(r -> t.equals(r.getId())).findFirst().orElse(null);
            if (account != null) {
                String sellerId = account.getSellerId();
                List<Integer> invitationTypeList = amzPromotionsList.stream().filter(r -> t.equals(r.getShopId().intValue())).map(r -> r.getInvitationType()).distinct().collect(Collectors.toList());
                for (Integer type : invitationTypeList) {
                    try {
                        List<String> existSkcIdList = amzPromotionsMapper.selectSkuDetailByShopAndActiveType(t, type);

                        if (existSkcIdList.size() > 50) {
                            List<List<String>> partition = ListUtils.partition(existSkcIdList, 50);
                            for (List<String> strings : partition) {
                                TemuSemiPromotionActiveQuery request = new TemuSemiPromotionActiveQuery() {
                                    @Override
                                    public TemuUrl type() {
                                        return TemuUrl.BG_MARKETING_ACTIVITY_ENROLL_LIST_GET;
                                    }
                                };
                                request.setMallId(Long.parseLong(sellerId));
                                request.setActivityType(type);
                                request.setProductSkcIds(strings.stream().map(skc -> Long.parseLong(skc)).collect(Collectors.toList()));
                                pullSemiPromotion(account, sellerId, request, null);
                            }
                        } else {
                            TemuSemiPromotionActiveQuery request = new TemuSemiPromotionActiveQuery() {
                                @Override
                                public TemuUrl type() {
                                    return TemuUrl.BG_MARKETING_ACTIVITY_ENROLL_LIST_GET;
                                }
                            };
                            request.setMallId(Long.parseLong(sellerId));
                            request.setActivityType(type);
                            request.setProductSkcIds(existSkcIdList.stream().map(skc -> Long.parseLong(skc)).collect(Collectors.toList()));


                            pullSemiPromotion(account, sellerId, request, null);
                        }


                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("拉取半托管店铺促销平台活动信息请求异常--店铺ID---{}--异常信息---{}", account.getId(), e.getMessage());
                    }
                }

            }

        });


    }


    /**
     * 半托管促销创建接口
     * @param request
     * @return
     * @param <T>
     */
    @Override
    public <T> T semiPromotionCreate(TemuSemiPromotionSubmitRequest request, Account account, Class<T> responseClass) {
        JSONObject response = temuApi.requestSelfCnTemu(account, request, JSONObject.class);
        return response != null ? JSONObject.parseObject(response.toString(), responseClass) : null;
    }


    /**
     * 获取temu半托管店铺活动信息
     */
    @Override
    public void getTemuActivityList() {
        QueryWrapper<Account> wrapper = new QueryWrapper<Account>();
        wrapper.eq("type", "temu")
                .eq("active", "Y")
                .eq("sale_channel", "1")
                .eq("JSON_VALID(connect_str)", 1)
                .gt("JSON_EXTRACT(connect_str, '$.selfCnAccessToken')", 0)
                .eq("org_id",1000049);
        List<Account> accountList = accountService.list(wrapper);
        if(CollectionUtils.isNotEmpty(accountList)) {
            for(Account account:accountList) {
                threadPoolTaskExecutor.execute(() ->handle(account));
            }
        }
    }

    void handle(Account account) {
        TemuSemiPromotionActiveQuery request = new TemuSemiPromotionActiveQuery() {
            @Override
            public TemuUrl type() {
                return TemuUrl.BG_MARKETING_ACTIVITY_LIST_GET;
            }
        };
        TemuActivityResponse.ResponseWrapper response = temuApi.requestSelfCnTemu(account, request, TemuActivityResponse.ResponseWrapper.class);
        if (response != null && response.getResult() != null) {
            MarPlatformPromotionsTemuMessageReceiveV2Msg marPlatformPromotionsTemuMessageReceiveV2Msg = new MarPlatformPromotionsTemuMessageReceiveV2Msg();
            copyResult(response, marPlatformPromotionsTemuMessageReceiveV2Msg);
            marPlatformPromotionsTemuMessageReceiveV2Msg.setMallid(account.getSellerId());
            marPlatformPromotionsTemuMessageReceiveV2Msg.setStoreName(account.getStoreName());
            log.info("获取temu半托管店铺活动信息保存前参数：{},店铺sellId:{},storeName:{}", JSONObject.toJSONString(marPlatformPromotionsTemuMessageReceiveV2Msg),account.getSellerId(),account.getStoreName());
            marPlatformPromotionsInfoService.savePlatformTemuInfoV2(marPlatformPromotionsTemuMessageReceiveV2Msg);
        }
    }

    /**
     * 获取temu半托管店铺场次信息
     */
    @Override
    public void getTemuSessionList() {
        List<MarPlatformPromotionsInfo> marPlatformPromotionsInfoList = marPlatformPromotionsInfoMapper.queryShotPlatformPromotionsSession(MarPromotionInvitationTypeEnum.getShortTermTypes());
        if (CollectionUtils.isNotEmpty(marPlatformPromotionsInfoList)) {

            // 批量获取所有需要的accountId（减少数据库查询次数）
            Set<Long> shopIds = marPlatformPromotionsInfoList.stream()
                    .map(MarPlatformPromotionsInfo::getShopId)
                    .collect(Collectors.toSet());

            Map<Integer, Account> accountMap = accountService.listByIds(new ArrayList<>(shopIds))
                    .stream()
                    .collect(Collectors.toMap(Account::getId, Function.identity()));
            if (CollectionUtils.isNotEmpty(marPlatformPromotionsInfoList)) {
                for(MarPlatformPromotionsInfo m:marPlatformPromotionsInfoList) {
                    processSinglePromotion(true, m, accountMap);
                }
            }

//            // 并行处理每个促销信息
//            List<CompletableFuture<Void>> futures = marPlatformPromotionsInfoList.stream()
//                    .map(m -> CompletableFuture.runAsync(() -> processSinglePromotion(true,m, accountMap), threadPoolTaskExecutor))
//                    .collect(Collectors.toList());
//
//            // 等待所有任务完成
//            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    /**
     * 获取temu半托管店铺长场次信息
     */
    @Override
    public void getLongTemuSessionList() {
        List<MarPlatformPromotionsInfo> marPlatformPromotionsInfoList = marPlatformPromotionsInfoMapper.queryLongPlatformPromotionsSession(MarPromotionInvitationTypeEnum.getLongTermTypes());
        if (CollectionUtils.isNotEmpty(marPlatformPromotionsInfoList)) {

            // 批量获取所有需要的accountId（减少数据库查询次数）
            Set<Long> shopIds = marPlatformPromotionsInfoList.stream()
                    .map(MarPlatformPromotionsInfo::getShopId)
                    .collect(Collectors.toSet());

            Map<Integer, Account> accountMap = accountService.listByIds(new ArrayList<>(shopIds))
                    .stream()
                    .collect(Collectors.toMap(Account::getId, Function.identity()));

            if (CollectionUtils.isNotEmpty(marPlatformPromotionsInfoList)) {
                for(MarPlatformPromotionsInfo m:marPlatformPromotionsInfoList) {
                    processSinglePromotion(false, m, accountMap);
                }
             }
            // 并行处理每个促销信息
//            List<CompletableFuture<Void>> futures = marPlatformPromotionsInfoList.stream()
//                    .map(m -> CompletableFuture.runAsync(() -> processSinglePromotion(false,m, accountMap), threadPoolTaskExecutor))
//                    .collect(Collectors.toList());
//
//            // 等待所有任务完成
//            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    // 3. 提取单个处理逻辑到独立方法
        private void processSinglePromotion(Boolean flag,MarPlatformPromotionsInfo m, Map<Integer, Account> accountMap) {
            try {
                List<Long> productIdList = iMarPromotionsReportProductService.getMarPromotionsReportProductByParam(m.getShopId(), m.getInvitationType(), m.getInvitationId());

                if (CollectionUtils.isEmpty(productIdList)) return;

                Account account = accountMap.get(m.getShopId().intValue());
                if (account == null) return;

                TemuActivitySessionListQuery query = new TemuActivitySessionListQuery() {
                    @Override
                    public TemuUrl type() { return TemuUrl.BG_MARKETING_ACTIVITY_SESSION_LIST_GET; }
                };
                query.setProductIds(productIdList);
                query.setActivityType(m.getInvitationType());
                if (flag && m.getInvitationId() != null) {
                    query.setActivityThematicId(Long.valueOf(m.getInvitationId()));
                }

                TemuActivitySessionResponse response = temuApi.requestSelfCnTemu(account, query, TemuActivitySessionResponse.class);
                if (response != null && response.getResult() != null) {
                    MarPlatformPromotionsTemuMsgSessionReceiveV2Msg msg = convertResultToMsg(response.getResult());
                    msg.setMallId(account.getSellerId());
                    msg.setStoreName(account.getStoreName());
                    msg.setActivityType(m.getInvitationType());
                    if(flag) {
                        msg.setInvitationId(m.getInvitationId());
                    }
                    log.info("获取temu半托管店铺场次信息保存前参数：{},店铺sellId:{},storeName:{},setActivityType:{}", JSONObject.toJSONString(msg),account.getSellerId(),account.getStoreName(),m.getInvitationType());
                    marPlatformPromotionsInfoService.savePlatformTemuInfoSessionV2(msg);
                }
            } catch (Exception e) {
                log.error("处理促销信息失败 shopId={}, type={}, id={}",
                        m.getShopId(), m.getInvitationType(), m.getInvitationId(), e);
            }
        }


    /**
     * 将Temu响应结果转换为消息对象
     */
    public MarPlatformPromotionsTemuMsgSessionReceiveV2Msg convertResultToMsg(TemuActivitySessionResponse.Result temuResult) {

        MarPlatformPromotionsTemuMsgSessionReceiveV2Msg msg = new MarPlatformPromotionsTemuMsgSessionReceiveV2Msg();
        if (temuResult == null) return msg;

        msg.setList(convertSessionList(temuResult.getList()));
        return msg;
    }

    /**
     * 转换会话列表
     */
    public List<MarPlatformPromotionsTemuMsgSessionReceiveV2Msg.ListBean> convertSessionList(
            List<TemuActivitySessionResponse.SessionInfo> sessionInfos) {

        if (sessionInfos == null) return null;

        return sessionInfos.stream()
                .map(i -> convertSingleSession(i))
                .collect(Collectors.toList());
    }

    /**
     * 单个会话对象转换
     */
    private MarPlatformPromotionsTemuMsgSessionReceiveV2Msg.ListBean convertSingleSession(
            TemuActivitySessionResponse.SessionInfo sessionInfo) {

        if (sessionInfo == null) return null;

        MarPlatformPromotionsTemuMsgSessionReceiveV2Msg.ListBean listBean =
                new MarPlatformPromotionsTemuMsgSessionReceiveV2Msg.ListBean();

        // 基本类型转换
        listBean.setDurationDays(sessionInfo.getDurationDays());
        listBean.setSessionStatus(sessionInfo.getSessionStatus());

        // int转Long处理
        listBean.setSiteId((long) sessionInfo.getSiteId());
        listBean.setSessionId((long) sessionInfo.getSessionId());

        // 自动装箱处理
        listBean.setStartTime(sessionInfo.getStartTime());
        listBean.setEndTime(sessionInfo.getEndTime());

        // 字符串直接映射
        listBean.setStartDateStr(sessionInfo.getStartDateStr());
        listBean.setEndDateStr(sessionInfo.getEndDateStr());
        listBean.setSessionName(sessionInfo.getSessionName());
        listBean.setSiteName(sessionInfo.getSiteName());

        return listBean;
    }

        public  void copyResult(TemuActivityResponse.ResponseWrapper source, MarPlatformPromotionsTemuMessageReceiveV2Msg target) {
            if (source == null || target == null) return;

            TemuActivityResponse.Result sourceResult = source.getResult();
            if (sourceResult == null) return;

            MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean targetResult = new MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean();
            targetResult.setActivityList(copyActivityList(sourceResult.getActivityList()));
            target.setResult(targetResult);
        }

        private  List<MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean.ActivityListBean> copyActivityList(
                List<TemuActivityResponse.Activity> sourceActivities) {
            if (sourceActivities == null) return null;

            List<MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean.ActivityListBean> targetActivities = new ArrayList<>();
            for (TemuActivityResponse.Activity source : sourceActivities) {
                MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean.ActivityListBean target = new MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean.ActivityListBean();

                // 拷贝基础字段
                target.setActivityContent(source.getActivityContent());
                target.setActivityName(source.getActivityName());
                target.setActivityLabelTag(source.getActivityLabelTag() != null ? source.getActivityLabelTag() : 0);
                target.setActivityType(source.getActivityType() != null ? source.getActivityType() : 0);

                // 拷贝标签列表
                if (source.getBenefitLabelName() != null) {
                    target.setBenefitLabelName(new ArrayList<>(source.getBenefitLabelName()));
                }

                // 拷贝专题列表
                target.setThematicList(copyThematicList(source.getThematicList()));

                targetActivities.add(target);
            }
            return targetActivities;
        }

        private List<MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean.ActivityListBean.ThematicListBean> copyThematicList(
                List<TemuActivityResponse.Thematic> sourceThematics) {
            if (sourceThematics == null) return null;

            List<MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean.ActivityListBean.ThematicListBean> targetThematics = new ArrayList<>();
            for (TemuActivityResponse.Thematic source : sourceThematics) {
                MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean.ActivityListBean.ThematicListBean target =
                        new MarPlatformPromotionsTemuMessageReceiveV2Msg.ResultBean.ActivityListBean.ThematicListBean();

                // 拷贝基础字段
                target.setCanEnrollTotal(0); // 目标类没有对应字段，需要特殊处理或默认值
                target.setEnrollDeadLine(source.getEnrollDeadLine() != null ? source.getEnrollDeadLine() : 0L);
                target.setActivityLabelTag(source.getActivityLabelTag() != null ? source.getActivityLabelTag() : 0);
                target.setActivityThematicId(source.getActivityThematicId());
                target.setDurationDays(source.getDurationDays() != null ? source.getDurationDays() : 0);
                target.setEnrollSource(source.getEnrollSource() != null ? source.getEnrollSource() : 0);
                target.setEnrollStartAt(source.getEnrollStartAt() != null ? source.getEnrollStartAt() : 0L);
                target.setEnrolledCount(0); // 目标类没有对应字段
                target.setStartTime(source.getStartTime());
                target.setEndTime(source.getEndTime());
                target.setActivityThematicName(source.getActivityThematicName());

                // 拷贝站点信息（Object类型需要特殊处理）
                target.setSites(copySites(source.getSites()));

                // 拷贝标签列表
                if (source.getBenefitLabelName() != null) {
                    target.setBenefitLabelName(new ArrayList<>(source.getBenefitLabelName()));
                }

                targetThematics.add(target);
            }
            return targetThematics;
        }

        private  Object copySites(List<TemuActivityResponse.Site> sourceSites) {
            if (sourceSites == null) return null;

            // 根据业务需求转换站点数据，这里直接返回原始列表
            // 如果需要特殊处理可以在这里添加转换逻辑
            return sourceSites;
        }


    /**
     * 拉取temu半托管提报商品信息
     */
    @Override
    public void getTemuSemiReportProduct() {

        List<MarPlatformPromotionsInfo> shortPlatform = marPlatformPromotionsInfoMapper.queryShotPlatformPromotionsSession(MarPromotionInvitationTypeEnum.getShortTermTypes());
        List<MarPlatformPromotionsInfo> longPlatform = marPlatformPromotionsInfoMapper.queryLongPlatformPromotionsSession(MarPromotionInvitationTypeEnum.getLongTermTypes());
        if (CollectionUtils.isNotEmpty(shortPlatform)) {
            threadPoolTaskExecutor.execute(() -> {
                this.handlePullPlatform(shortPlatform,1);
            });
        }
        if (CollectionUtils.isNotEmpty(longPlatform)) {
            threadPoolTaskExecutor.execute(() -> {
                this.handlePullPlatform(longPlatform,2);
            });
        }
    }

    /**
     * l拉取长期平台活动信息
     * @param longPlatform
     */
    private void handlePullPlatform(List<MarPlatformPromotionsInfo> longPlatform, Integer type) {
        List<Account> accountList = accountService.listByIds(longPlatform.stream().map(t -> t.getShopId()).distinct().collect(Collectors.toList()));
        List<List<MarPlatformPromotionsInfo>> partitionList = ListUtils.partition(longPlatform, (int) Math.ceil(longPlatform.size() / 10.0));
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (List<MarPlatformPromotionsInfo> partition : partitionList) {
            futureList.add(CompletableFuture.runAsync(() -> {
                this.handlePullPlatform(partition, type, accountList);
            }, threadPoolTaskExecutor));
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    }


    private void handlePullPlatform(List<MarPlatformPromotionsInfo> partition, Integer type, List<Account> accountList) {

        partition.parallelStream().forEach(platform -> {

            String searchScrollContext = null;
            while (true) {
                try {
                    Integer invitationType = platform.getInvitationType();
                    Long shopId = platform.getShopId();
                    String invitationId = platform.getInvitationId();
                    Account account = accountList.stream().filter(r -> shopId.equals(r.getId().longValue())).findFirst().get();
                    String sellerId = account.getSellerId();
                    TemuSemiPromotionReportProductQuery request = new TemuSemiPromotionReportProductQuery();
                    if (StringUtils.isNotEmpty(searchScrollContext)) {
                        request.setSearchScrollContext(searchScrollContext);
                    }
                    request.setRowCount(50);
                    request.setActivityType(invitationType);
                    if (new Integer(1).equals(type)) {
                        request.setActivityThematicId(Long.parseLong(invitationId));
                    }
                    log.info("半托管提报商品接口查询请求--参数{}--店铺id--{}", JSONObject.toJSONString(request), shopId);
                    TemuSemiPromotionReportProductResponse response = temuApi.requestSelfCnTemu(account, request, TemuSemiPromotionReportProductResponse.class);
                    log.info("半托管提报商品接口查询请求--参数{}--店铺id--{}--响应结果--{}", JSONObject.toJSONString(request), shopId, JSONObject.toJSONString(response));
                    if (response == null || response.getSuccess() == null || !response.getSuccess() || response.getResult() == null
                            || response.getResult().getHasMore() == null || CollectionUtil.isEmpty(response.getResult().getMatchList())) {

                        log.info("半托管提报商品接口查询请求--参数{}--店铺id--{}--响应结果为空--{}", JSONObject.toJSONString(request), shopId, JSONObject.toJSONString(response));
                        break;
                    } else {
                        searchScrollContext = response.getResult().getSearchScrollContext();
                        List<TemuSemiPromotionReportProductResponse.MatchList> matchList = response.getResult().getMatchList();

                        MarPromotionsReportProductNewReceIveMsgList msgList = buildReportProductList(matchList, sellerId, invitationType, invitationId);
                        try {
                            iMarPromotionsReportProductService.saveTemuReportProductNew(msgList);
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("半托管促销拉取提报商品后数据入库异常--平台活动信息--{}", JSONObject.toJSONString(platform));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("半托管促销拉取提报商品异常--平台活动信息--{}", JSONObject.toJSONString(platform));
                    break;
                }
            }

        });
    }

    private MarPromotionsReportProductNewReceIveMsgList buildReportProductList(List<TemuSemiPromotionReportProductResponse.MatchList> matchList, String sellerId, Integer invitationType, String invitationId) {

        MarPromotionsReportProductNewReceIveMsgList receIveMsgList = new MarPromotionsReportProductNewReceIveMsgList();
        receIveMsgList.setInvitationId(invitationId);
        receIveMsgList.setInvitationType(invitationType);
        receIveMsgList.setMallId(sellerId);
        //TODO
//        receIveMsgList.setActivityEndTime();
        receIveMsgList.setSiteId(CollectionUtil.isNotEmpty(matchList.get(0).getSites()) ? matchList.get(0).getSites().get(0).getSiteId() : null);
        receIveMsgList.setSiteName(CollectionUtil.isNotEmpty(matchList.get(0).getSites()) ? matchList.get(0).getSites().get(0).getSiteName() : null);
        List<MarPromotionReportProductNewReceIveMsg> list = new ArrayList<>();
        matchList.forEach(t -> {
            MarPromotionReportProductNewReceIveMsg msg = new MarPromotionReportProductNewReceIveMsg();
            msg.setTargetActivityStock(t.getSuggestActivityStock());
            msg.setProductId(t.getProductId());
//            msg.setPictureUrl();
            if (CollectionUtil.isNotEmpty(matchList.get(0).getSites())) {
                msg.setSites(JSONArray.parseArray(JSONArray.toJSONString(matchList.get(0).getSites()), MarPromotionReportProductNewReceIveMsg.MarPromotionReportProductNewReceIveMsgSites.class));
            }
            //todo
            msg.setProductName(t.getProductName());
//            msg.setActivitySiteInfoList();
            List<MarPromotionReportProductNewReceIveMsg.MarPromotionReportProductNewReceIveMsgSkcList> skcList = new ArrayList<>();
            t.getSkcList().forEach(skc -> {

                MarPromotionReportProductNewReceIveMsg.MarPromotionReportProductNewReceIveMsgSkcList skcInfo = new MarPromotionReportProductNewReceIveMsg.MarPromotionReportProductNewReceIveMsgSkcList();
                skcInfo.setSkcId(skc.getSkcId().toString());
                skcInfo.setDailyPrice(skc.getDailyPrice());
                skcInfo.setActivityPrice(skc.getActivityPrice());

                List<MarPromotionReportProductNewReceIveMsg.MarPromotionReportProductNewReceIveMsgSkuList> skuList = new ArrayList<>();
                skc.getSkuList().forEach(skuId -> {
                    MarPromotionReportProductNewReceIveMsg.MarPromotionReportProductNewReceIveMsgSkuList skuIdInfo = new MarPromotionReportProductNewReceIveMsg.MarPromotionReportProductNewReceIveMsgSkuList();
                    skuIdInfo.setDailyPrice(skuId.getDailyPrice());
                    skuIdInfo.setActivityPrice(skuId.getActivityPrice());
//                    skuIdInfo.setExtCode();
                    skuIdInfo.setSuggestActivityPrice(skuId.getSuggestActivityPrice());
                    skuIdInfo.setCurrency(skuId.getCurrency());
                    skuIdInfo.setSitePriceList(JSONObject.parseArray(JSONArray.toJSONString(skuId.getSitePriceList()), MarPromotionReportProductNewReceIveMsg.MarPromotionReportProductNewReceIveMsgSitePriceList.class));
                    skuIdInfo.setSkuId(skuId.getSkuId().toString());

                    skuList.add(skuIdInfo);
                });
                skcInfo.setSkuList(skuList);


                skcList.add(skcInfo);
            });
            msg.setSkcList(skcList);
            msg.setSuggestEnrollSessionIdList(CollectionUtil.isNotEmpty(t.getEnrollSessionIdList()) ? t.getEnrollSessionIdList() : null);
            list.add(msg);
        });
        receIveMsgList.setList(list);

        return receIveMsgList;
    }


    private Date getTimeStampDate(Long timestamp) {

        if (timestamp == null) {
            return null;
        }
        Integer length = String.valueOf(timestamp).length();
        if (length == 10) {

            return DateUtil.UtcToPacificDate(DateUtils.timestampToDate(timestamp * 1000, DateUtils.DEFAULT_TIME_FORMAT));

        } else if (length == 13) {
            return DateUtil.UtcToPacificDate(DateUtils.timestampToDate(timestamp, DateUtils.DEFAULT_TIME_FORMAT));
        } else {
            return null;
        }
    }
}
