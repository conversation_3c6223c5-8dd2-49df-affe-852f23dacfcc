<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bizark.op.service.mapper.promotions.MarPlatformPromotionsInfoMapper">

    <resultMap id="BaseResultMap" type="com.bizark.op.api.entity.op.promotions.MarPlatformPromotionsInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="organizationId" column="organization_id" jdbcType="INTEGER"/>
            <result property="invitationName" column="invitation_name" jdbcType="VARCHAR"/>
            <result property="invitationState" column="invitation_state" jdbcType="TINYINT"/>
            <result property="shopId" column="shop_id" jdbcType="BIGINT"/>
            <result property="channel" column="channel" jdbcType="VARCHAR"/>
            <result property="invitationId" column="invitation_id" jdbcType="VARCHAR"/>
            <result property="invitationType" column="invitation_type" jdbcType="INTEGER"/>
            <result property="beginTime" column="begin_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="signUpNum" column="sign_up_num" jdbcType="BIGINT"/>
            <result property="signUpTotal" column="sign_up_total" jdbcType="BIGINT"/>
            <result property="createdBy" column="created_by" jdbcType="INTEGER"/>
            <result property="createdName" column="created_name" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="INTEGER"/>
            <result property="updatedName" column="updated_name" jdbcType="VARCHAR"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="disabledBy" column="disabled_by" jdbcType="INTEGER"/>
            <result property="disabledName" column="disabled_name" jdbcType="VARCHAR"/>
            <result property="disabledAt" column="disabled_at" jdbcType="TIMESTAMP"/>
    </resultMap>




    <select id="selectPlatTemuPromotionList" parameterType="com.bizark.op.api.entity.op.promotions.MarPromotionsTemuQueryVO" resultType="com.bizark.op.api.entity.op.promotions.MarPlatformPromotionsInfo">
        SELECT
        i.created_at,
        i.shop_id,
        a.sale_channel,
        a.`investment`,a.title AS shop_name,
        IF((a.sale_channel='1' and i.invitation_type IN (1,5,27)),s.session_state,i.invitation_state) AS invitation_state,
        IF((a.sale_channel='1' and i.invitation_type IN (1,5,27)),s.session_name,i.invitation_name) AS invitation_name,
        IF((a.sale_channel='1' and i.invitation_type IN (1,5,27)),s.session_id,i.invitation_id) AS invitation_id,
        i.invitation_type,
        IF((a.sale_channel='1' and i.invitation_type IN (1,5,27)),s.api_begin_date,i.begin_time) AS begin_time,
        IF((a.sale_channel='1' and i.invitation_type IN (1,5,27)),s.api_end_date,i.end_time) AS end_time,
        ifnull(i.sign_up_num,0) as  sign_up_num , ifnull(i.sign_up_total,0) as  sign_up_total,
        IF((a.sale_channel='1' and i.invitation_type IN (1,5,27)), DATE_ADD(s.updated_at,INTERVAL 8 HOUR), DATE_ADD(i.updated_at,INTERVAL 8 HOUR)) AS updated_at_export,
        address.register_company_name
        FROM erp.mar_platform_promotions_info i
        LEFT JOIN  erp.mar_platform_promotions_session_info s ON  i.id=s.platform_promotions_id
        LEFT JOIN dashboard.`accounts` a ON i.shop_id=a.id
        left join dashboard.amz_shop_address  address ON address.shop_id = a.id
        <where>
            <if test="organizationId != null">
                and i.organization_id=#{organizationId}
            </if>
            <if test="investmentArray != null and investmentArray.size() > 0">
                and a.investment in
                <foreach collection="investmentArray" item="investment" open="(" separator="," close=")">
                    #{investment}
                </foreach>
            </if>
            <if test="shopIdArray != null and shopIdArray.size() > 0">
                and i.shop_id in
                <foreach collection="shopIdArray" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="beginTimeFrom != null ">
                AND
            (i.begin_time <![CDATA[>=]]> #{beginTimeFrom}
              OR
                s.api_begin_date <![CDATA[>=]]> #{beginTimeFrom}
                )
            </if>
            <if test="beginTimeTo != null ">
                AND
                (
                i.begin_time  <![CDATA[<=]]> #{beginTimeTo}
                OR
                s.api_begin_date  <![CDATA[<=]]> #{beginTimeTo}
                )

            </if>
            <if test="endTimeFrom != null">
             and
                (end_time <![CDATA[>=]]> #{endTimeFrom}
                or
                s.api_end_date <![CDATA[>=]]> #{endTimeFrom}
                )
            </if>
            <if test="endTimeTo != null">
                and
                (
                i.end_time <![CDATA[<=]]> #{endTimeTo}
                or
                s.api_end_date  <![CDATA[<=]]> #{endTimeTo}
                )
            </if>
            <if test="invitationStateArray != null and invitationStateArray.size() > 0">
                and
                (
                i.invitation_state in
                <foreach collection="invitationStateArray" item="invitationState" open="(" separator="," close=")">
                    #{invitationState}
                </foreach>
                OR
                s.session_state in
                <foreach collection="invitationStateArray" item="invitationState" open="(" separator="," close=")">
                    #{invitationState}
                </foreach>
                )
            </if>
            <if test="invitationTypeArray != null and invitationTypeArray.size() > 0">
                and i.invitation_type in
                <foreach collection="invitationTypeArray" item="invitationType" open="(" separator="," close=")">
                    #{invitationType}
                </foreach>
            </if>
            <if test="invitationNameArray != null and invitationNameArray.size() > 0">
                and (i.invitation_name in
                <foreach collection="invitationNameArray" item="invitationName" open="(" separator="," close=")">
                    #{invitationName}
                </foreach>
                or
                s.session_name in
                <foreach collection="invitationNameArray" item="invitationName" open="(" separator="," close=")">
                    #{invitationName}
                </foreach>
                )
            </if>
            <if test="invitationIdArray != null and invitationIdArray.size() > 0">
                and
                (
                i.invitation_id in
                <foreach collection="invitationIdArray" item="invitationId" open="(" separator="," close=")">
                    #{invitationId}
                </foreach>
              or
                s.session_id in
                <foreach collection="invitationIdArray" item="invitationId" open="(" separator="," close=")">
                    #{invitationId}
                </foreach>
                )
            </if>
            <if test="invitationName != null  and  invitationName !='' ">
                and i.invitation_name = #{invitationName}
            </if>
            <if test="invitationId != null and   invitationId !=''  ">
                and i.invitation_id = #{invitationId}
            </if>
        </where>

    </select>





    <select id="selectMeiPlatformList" resultType="com.bizark.op.api.entity.op.promotions.MarPlatformPromotionsInfo">
        select a.shop_id,a.invitation_id from erp.mar_platform_promotions_info a left join dashboard.accounts b
        on a.shop_id=b.id
        <where>
            b.sale_channel='2' and b.org_id in(1000049,1000707,1000704)
            <if test="invitationTypeList != null and invitationTypeList.size() > 0">
                and a.invitation_type in
                <foreach collection="invitationTypeList" item="invitationType" open="(" separator="," close=")">
                    #{invitationType}
                </foreach>
            </if>
            <if test="invitationStatusList != null and invitationStatusList.size() != 0">
                and a.invitation_state in
                <foreach collection="invitationStatusList" item="invitationState" open="(" separator="," close=")">
                    #{invitationState}
                </foreach>
            </if>
        </where>
        group by a.shop_id,a.invitation_id
    </select>


    <select id="queryShotPlatformPromotionsSession" resultType="com.bizark.op.api.entity.op.promotions.MarPlatformPromotionsInfo">
        SELECT
            a.shop_id,
            a.invitation_type,
            a.invitation_id
        FROM
            erp.mar_platform_promotions_info a
                LEFT JOIN dashboard.accounts b ON a.shop_id = b.id
        WHERE
            a.invitation_type IN
            <foreach collection="invitationTypeList" item="invitationType" open="(" separator="," close=")">
                #{invitationType}
            </foreach>
          AND b.sale_channel = '1'
          AND a.invitation_state IN (- 1, 1 )
        and b.org_id=1000049
        GROUP BY
            a.shop_id,
            a.invitation_type,
            a.invitation_id
    </select>

    <select id="queryLongPlatformPromotionsSession" resultType="com.bizark.op.api.entity.op.promotions.MarPlatformPromotionsInfo">
        SELECT
            a.shop_id,
            a.invitation_type
        FROM
            mar_platform_promotions_info a
                LEFT JOIN dashboard.accounts b ON a.shop_id = b.id
        WHERE
            a.invitation_type IN
            <foreach collection="invitationTypeList" item="invitationType" open="(" separator="," close=")">
                #{invitationType}
            </foreach>
          AND b.sale_channel = '1'

        and b.org_id=1000049
        GROUP BY
            a.shop_id,
            a.invitation_type
    </select>


    <select id="selectInvitationTypeAndValue" resultType="String">

        select distinct invitation_name from erp.mar_platform_promotions_info a left join dashboard.accounts b on a.shop_id=b.id
        where a.invitation_type = #{invitationType}
        and b.sale_channel=#{saleChannel}
        and a.organization_id=#{contextId}
        and a.invitation_state in(-1,1)
        <if test="invitationNameList != null and invitationNameList.size() != 0">
            <choose>
                <when test="invitationNameList.size() == 1">
                    and a.invitation_name like concat('%',#{invitationNameList[0]},'%')
                </when>
                <otherwise>
                    and a.invitation_name in
                    <foreach collection="invitationNameList" item="invitationName" open="(" separator="," close=")">
                        #{invitationName}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        and a.invitation_name is not null and a.invitation_name != ''
    </select>

</mapper>
