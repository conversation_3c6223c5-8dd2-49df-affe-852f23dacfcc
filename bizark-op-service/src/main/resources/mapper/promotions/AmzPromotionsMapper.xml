<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bizark.op.service.mapper.promotions.AmzPromotionsMapper">

    <!--促销详情结果-->
    <resultMap type="com.bizark.op.api.entity.op.promotions.AmzPromotions" id="AmzPromotionsResult">
        <result property="id"    column="id"    />
        <result property="promotionsName"    column="promotions_name"    />
        <result property="promotionsState"    column="promotions_state"    />
        <result property="country"    column="country"    />
        <result property="shopName"    column="shop_name"    />
        <result property="promotionsId"    column="promotions_id"    />
        <result property="promotionsType"    column="promotions_type"    />
        <result property="vendorCode"    column="vendor_code"    />
        <result property="beginTime"    column="begin_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="billingContact"    column="billing_contact"    />
        <result property="fundingAgreement"    column="funding_agreement"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdName"    column="created_name"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updatedName"    column="updated_name"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="disabledBy"    column="disabled_by"    />
        <result property="disabledName"    column="disabled_name"    />
        <result property="disabledAt"    column="disabled_at"    />
        <result property="revenue"    column="revenue"    />
        <result property="unitsSold"    column="units_sold"    />
        <result property="glanceViews"    column="glance_views"    />
        <result property="percentConversion"    column="percent_conversion"    />
        <result property="amountSpent"    column="amount_spent"    />
        <result property="budget"    column="budget"    />
        <result property="remark"    column="remark"    />
        <result property="channel"    column="channel"    />
        <result property="organizationId"    column="organization_id"    />
        <result property="discount"    column="discount"    />
        <result property="share"    column="share"    />
        <result property="redemptions"    column="redemptions"    />
        <result property="code"    column="code"    />
        <result property="discountFlag"    column="discount_flag"    />
        <result property="flag"    column="flag"    />
        <result property="merchandisingFee"    column="merchandising_fee"    />
        <result property="shopId"    column="shop_id"    />
        <result property="lastUpdateTime"    column="last_update_time"    />
        <result property="completeDate"    column="complete_date"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="subShopBack"    column="sub_shop_back"    />
    </resultMap>

<!--    促销列表结果-->
    <resultMap type="com.bizark.op.api.vo.promotions.AmzPromotionsListVO" id="AmzPromotionsListResult">
        <result property="id"    column="id"    />
        <result property="promotionsName"    column="promotions_name"    />
        <result property="promotionsState"    column="promotions_state"    />
        <result property="country"    column="country"    />
        <result property="shopId"    column="shop_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="promotionsId"    column="promotions_id"    />
        <result property="promotionsType"    column="promotions_type"    />
        <result property="vendorCode"    column="vendor_code"    />
        <result property="beginTime"    column="begin_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="billingContact"    column="billing_contact"    />
        <result property="fundingAgreement"    column="funding_agreement"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdName"    column="created_name"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updatedName"    column="updated_name"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="disabledBy"    column="disabled_by"    />
        <result property="disabledName"    column="disabled_name"    />
        <result property="disabledAt"    column="disabled_at"    />
        <result property="revenue"    column="revenue"    />
        <result property="unitsSold"    column="units_sold"    />
        <result property="glanceViews"    column="glance_views"    />
        <result property="percentConversion"    column="percent_conversion"    />
        <result property="amountSpent"    column="amount_spent"    />
        <result property="budget"    column="budget"    />
        <result property="remark"    column="remark"    />
        <result property="channel"    column="channel"    />
        <result property="organizationId"    column="organization_id"    />
        <result property="discount"    column="discount"    />
        <result property="share"    column="share"    />
        <result property="redemptions"    column="redemptions"    />
        <result property="code"    column="code"    />
        <result property="asin"    column="asin"    />
        <result property="sellerSku"    column="seller_sku"    />
        <result property="discountFlag"    column="discount_flag"    />
        <result property="flag"    column="flag"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="subShopBack"    column="sub_shop_back"    />
        <result property="completeDate"    column="complete_date"    />
        <result property="submissionDueDate"    column="submission_due_date"    />
        <result property="enrollment"    column="enrollment"    />
        <result property="agreementFlag"    column="agreement_flag"    />
        <result property="skcStock"    column="skc_stock"    />
        <result property="remainStock"    column="remain_stock"    />
        <result property="instanceId" column="instance_id"/>
        <result property="stageChangeBefore" column="stage_change_before"/>
        <result property="countryCode" column="country_code"/>

    </resultMap>

<!--    促销店铺-->
    <resultMap id="AmzPromotionsAccountList" type="com.bizark.op.api.entity.op.promotions.AccountWithCountry">
        <result property="id" column="id"></result>
        <result property="accountInit" column="account_init"></result>
        <result property="countryCode" column="country_code"></result>
        <result property="nameCn" column="name_cn"></result>
        <result property="nameEn" column="name_en"></result>
        <result property="channel" column="channel"></result>
    </resultMap>

    <!--    促销店铺国家-->
    <resultMap id="AmzPromotionsAccountCountryList" type="com.bizark.op.api.entity.op.promotions.AmzPromotionDateSet">
        <result property="channel" column="channel"/>
        <result property="channelKey" column="channel_key"/>
        <collection property="countryList" ofType="com.bizark.op.api.entity.op.promotions.AmzPromotionDateSetCountry">
            <result property="countryKey" column="country_key"/>
            <result property="countryValue" column="country_value"/>
        </collection>
    </resultMap>

    <resultMap id="getAccountWithPromotions" type="com.bizark.op.api.entity.op.promotions.AccountWithPromotions">
        <result property="shopId" column="shop_id"></result>
        <result property="webStoreName" column="store_name"></result>
        <result property="id" column="id"></result>
        <result property="promotionId" column="promotions_id"></result>
        <result property="endTime" column="end_time"></result>
        <result property="beginTime" column="begin_time"></result>

    </resultMap>
<!--促销列表结果列-->
    <sql id="selectAmzPromotionsListVo">
        select a.*,
               d.investment
               ,group_concat(p.asin) asin,group_concat(p.seller_sku) seller_sku,c.country_code
               from erp.amz_promotions a  left join  erp.amz_promotions_sku_detail p on a.id=p.promotions_id
               left join dashboard.accounts c on a.shop_id=c.id
               left join dashboard.product_channels d on c.flag=d.account_id and 	CAST(p.seller_sku AS CHAR CHARACTER SET utf8)=d.seller_sku and d.disabled_at=0
        <if test="approvalIds != null and approvalIds.size() != 0">
            left join (select re.id, group_concat(distinct ta.ASSIGNEE_) as approval_ids
            from activiti7.activiti_process_records re
            left join activiti7.act_ru_task ta on re.process_instance_id = ta.`PROC_INST_ID_`
            where ta.ASSIGNEE_ is not null
            and ta.ASSIGNEE_ != ''
            group by re.id) re on a.instance_id = re.id
        </if>
    </sql>

    <!--查询促销列表-->
    <select id="selectAmzPromotionsList" parameterType="com.bizark.op.api.request.AmzPromotionsQueryRequest" resultMap="AmzPromotionsListResult">

        <include refid="selectAmzPromotionsListVo"/>
        <include refid="list_query">
        </include>
        group by
        a.id
    </select>

    <select id="selectAmzPromotionsList_COUNT" resultType="Long">
        select count(id) from (select distinct a.id
                               from erp.amz_promotions a
        <if test="skuQueryFlag != null and skuQueryFlag == true">
            left join erp.amz_promotions_sku_detail p on a.id = p.promotions_id
        </if>
        <if test="(shopIdQueryFlag != null and shopIdQueryFlag == true) or (countryCodes != null and countryCodes.size() > 0 ) ">
            left join dashboard.accounts c on a.shop_id = c.id
        </if>
         <if test="skuMapQueryFlag != null and skuMapQueryFlag == true">
             left join dashboard.product_channels d on c.flag = d.account_id and CAST(p.seller_sku AS CHAR CHARACTER SET utf8) = d.seller_sku and d.disabled_at = 0
         </if>
        <if test="approvalIds != null and approvalIds.size() != 0">
            left join (select re.id, group_concat(distinct ta.ASSIGNEE_) as approval_ids
            from activiti7.activiti_process_records re
            left join activiti7.act_ru_task ta on re.process_instance_id = ta.`PROC_INST_ID_`
            where ta.ASSIGNEE_ is not null
            and ta.ASSIGNEE_ != ''
            group by re.id) re on a.instance_id = re.id
        </if>
        <include refid="list_query">
        </include>
        )t
    </select>


    <sql id="list_query">
        <where>
            a.channel in(1,2)
            <if test="channel !=null">
                and a.channel = #{channel}
            </if>
            <if test="shopIds!= null and shopIds.length>0">and a.shop_id in
                <foreach collection="shopIds" item="shopId" separator="," open="(" close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="dateType !=null and beginDate !=null and endDate!=null">
                <choose>
                    <when test="dateType == 1">
                        AND date(a.begin_time)<![CDATA[>=]]>date(#{beginDate})
                        and date(a.begin_time)<![CDATA[<=]]>date(#{endDate})
                    </when>
                    <when test="dateType == 2">
                        AND date(a.end_time)<![CDATA[>=]]>date(#{beginDate})
                        and date(a.end_time)<![CDATA[<=]]>date(#{endDate})
                    </when>
                    <when test="dateType == 3">
                        AND date(a.created_at)<![CDATA[>=]]>date(#{beginDate})
                        and date(a.created_at)<![CDATA[<=]]>date(#{endDate})
                    </when>
                    <when test="dateType == 4">
                        AND date(a.updated_at)<![CDATA[>=]]>date(#{beginDate})
                        and date(a.updated_at)<![CDATA[<=]]>date(#{endDate})
                    </when>
                    <when test="dateType == 5">
                        and ((date(a.begin_time) between date(#{beginDate}) and date(#{endDate}))
                        or (date(ifnull(a.complete_date,a.end_time)) between date(#{beginDate}) and date(#{endDate})))
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test="promotionsType != null ">and a.promotions_type = #{promotionsType}
            </if>
            <if test="promotionsTypeArray != null and promotionsTypeArray.length != 0">
                and a.promotions_type in
                <foreach collection="promotionsTypeArray" item="promotionsTypeItem" separator="," open="(" close=")">
                    #{promotionsTypeItem}
                </foreach>
            </if>
            <if test="promotionsState != null ">and a.promotions_state =#{promotionsState}
            </if>
            <if test="promotionType!=null and promotionType==1 and promotionValue!=null and promotionValue!=''">
                and
                <foreach collection="promotionValue.split(',')" item="promotionId" separator="or" open="(" close=")">
                    a.promotions_id like concat('%', #{promotionId},'%')
                </foreach>
            </if>
            <if test="promotionType!=null and promotionType==2 and promotionValue!=null and promotionValue!=''">
                and instr(a.promotions_name,#{promotionValue})>0
            </if>
            <if test="orgType!=null and orgValue!=null and orgValue.size()>0">
                <choose>
                    <when test="orgValue.size() == 1">
                        <choose>
                            <when test="orgType == 1">
                                and p.asin like concat('%', #{orgValue[0]} , '%')
                            </when>
                            <when test="orgType == 2">
                                and p.seller_sku like  concat('%', #{orgValue[0]} , '%')
                            </when>
                            <when test="orgType == 3">
                                and d.erpsku like  concat('%', #{orgValue[0]} , '%')
                            </when>
                            <when test="orgType == 4">
                                and d.asin1 like  concat('%', #{orgValue[0]} , '%')
                            </when>
                            <when test="orgType == 5">
                                and d.brand like  concat('%', #{orgValue[0]} , '%')
                            </when>
                            <otherwise>
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        <choose>
                            <when test="orgType == 1">
                                and p.asin in
                                <foreach item="a" collection="orgValue" open="(" close=")" separator=",">
                                    #{a}
                                </foreach>
                            </when>
                            <when test="orgType == 2">
                                and p.seller_sku in
                                <foreach item="s" collection="orgValue" open="(" close=")" separator=",">
                                    #{s}
                                </foreach>
                            </when>
                            <when test="orgType == 3">
                                and d.erpsku in
                                <foreach item="e" collection="orgValue" open="(" close=")" separator=",">
                                    #{e}
                                </foreach>
                            </when>
                            <when test="orgType == 4">
                                and d.asin1 in
                                <foreach item="as" collection="orgValue" open="(" close=")" separator=",">
                                    #{as}
                                </foreach>
                            </when>
                            <when test="orgType == 5">
                                and d.brand in
                                <foreach item="b" collection="orgValue" open="(" close=")" separator=",">
                                    #{b}
                                </foreach>
                            </when>
                            <otherwise>
                            </otherwise>
                        </choose>
                    </otherwise>
                </choose>
            </if>
            <if test="disabledBy == null">and a.disabled_by is null
            </if>
            <if test="contextId !=null"> and a.organization_id=#{contextId}</if>
            <if test="createdPeople!=null and createdPeople.length>0">
                and a.created_name in
                <foreach item="c" collection="createdPeople" open="(" close=")" separator=",">
                    #{c}
                </foreach>
            </if>
            <if test="approvalStatus != null and approvalStatus !=''">
                and find_in_set(a.approval_status,#{approvalStatus})
            </if>


            <if test="promotionsStateArray != null and promotionsStateArray.length != 0">
                and a.promotions_state in
                <foreach collection="promotionsStateArray" item="promotionsStateItem" separator="," open="(" close=")">
                    #{promotionsStateItem}
                </foreach>
            </if>
            <if test="submissionStatus != null and submissionStatus != ''">and find_in_set(p.submission_status,#{submissionStatus})</if>
            <if test="agreementFlag != null"> and a.agreement_flag = #{agreementFlag}</if>
            <if test="investment != null and investment != ''">
                and d.investment in
                <foreach collection="investment.split(',')" item="investmentItem" separator="," open="(" close=")">
                    #{investmentItem}
                </foreach>
            </if>
            <if test="operatorId != null and operatorId.length != 0">
                and d.operation_user_id in
                <foreach collection="operatorId" item="operatorIdItem" separator="," open="(" close=")">
                    #{operatorIdItem}
                </foreach>
            </if>
            <if test="skuIdArray != null and skuIdArray.size() != 0">
                and p.seller_sku in
                <foreach collection="skuIdArray" item="skuIdItem" separator="," open="(" close=")">
                    #{skuIdItem}
                </foreach>
            </if>
            <if test="approvalIds != null and approvalIds.size() != 0">
                and
                <foreach collection="approvalIds" item="approvalId" separator="or" open="(" close=")">
                    find_in_set(#{approvalId},re.approval_ids)
                </foreach>
            </if>
            <if test="discountPriceWarn != null">and p.discount_price_warn = #{discountPriceWarn}</if>
            <if test="countryCodes != null and countryCodes.size() != 0">
                and c.country_code in
                <foreach item="countryCode" collection="countryCodes" open="(" close=")" separator=",">
                    #{countryCode}
                </foreach>
            </if>
            <if test="repeatWithCoupon != null">
                <choose>
                    <when test="repeatWithCoupon == 1">
                        AND (a.sub_shop_back = 1
                            and date(a.begin_time) <![CDATA[<=]]> #{nowPstDate} and date(a.end_time) <![CDATA[>=]]> #{nowPstDate}
                            and EXISTS (SELECT 1
                                        FROM  erp.amz_promotions_coupons cp2 left join erp.amz_promotions_coupon cc2 ON cc2.coupon_id = cp2.id

                                        WHERE cp2.shop_id = a.shop_id
                                          AND cc2.asin = p.asin
                                and cp2.sub_shop_back = 1
                        and cp2.channel in(1,2)
                                    AND (
                                           (DATE(a.begin_time) BETWEEN DATE(cc2.begin_date) AND DATE(cc2.end_date))
                                           OR (DATE(a.end_time) BETWEEN DATE(cc2.begin_date) AND DATE(cc2.end_date))
                                         or(date(cc2.begin_date) between date(a.begin_time) and date(a.end_time))
                                        or(date(cc2.end_date) between date(a.begin_time) and date(a.end_time))
                                       )
                        )
                            )
                    </when>
                    <otherwise>
                        AND (a.sub_shop_back = 1
                        and date(a.begin_time) <![CDATA[<=]]> #{nowPstDate} and date(a.end_time) <![CDATA[>=]]> #{nowPstDate}
                        and EXISTS (SELECT 1
                        FROM  erp.amz_promotions_coupons cp2 left join erp.amz_promotions_coupon cc2 ON cc2.coupon_id = cp2.id

                        WHERE cp2.shop_id = a.shop_id
                        AND cc2.asin = p.asin
                        and cp2.sub_shop_back = 1
                        and cp2.channel in(1,2)
                        AND (
                        (DATE(a.begin_time) BETWEEN DATE(cc2.begin_date) AND DATE(cc2.end_date))
                        OR (DATE(a.end_time) BETWEEN DATE(cc2.begin_date) AND DATE(cc2.end_date))
                        or(date(cc2.begin_date) between date(a.begin_time) and date(a.end_time))
                        or(date(cc2.end_date) between date(a.begin_time) and date(a.end_time))
                        ))
                        )=false
                    </otherwise>
                </choose>
            </if>
        </where>
    </sql>
    <!--    促销详情结果列-->
    <sql id="selectAmzPromotionsVo">
        select a.id, a.promotions_name, a.promotions_state, a.country, a.shop_name, a.promotions_id, a.promotions_type, a.vendor_code, a.begin_time,a.end_time, a.billing_contact, a.funding_agreement, a.created_by, a.created_name, a.created_at, a.updated_by, a.updated_name, a.updated_at, a.disabled_by, a.disabled_name, a.disabled_at, a.revenue, a.units_sold, a.glance_views, a.percent_conversion, a.amount_spent, a.budget, a.remark, a.channel,a.organization_id ,a.discount,a.share,a.redemptions,a.code,a.discount_flag,a.flag,a.merchandising_fee,a.shop_id,a.last_update_time,a.complete_date,a.approval_status,a.sub_shop_back from amz_promotions a
    </sql>



<!--    促销店铺-->
   <sql id="selectAccountWithCountrySql">
       select a.id,a.account_init,a.country_code,b.name_cn,b.name_en,a.type as channel from dashboard.accounts a
           left join dashboard.countries b on a.country_code=b.code
   </sql>
<!--        查询促销店铺-->
    <select id="selectAccountWithCountry" resultMap="AmzPromotionsAccountList">
        <include refid="selectAccountWithCountrySql"></include>
        <where>
            <if test="shopId!=null"> and a.id=#{shopId}</if>
        </where>
    </select>

    <select id="selectAccountWithCountryList" resultMap="AmzPromotionsAccountCountryList">
        select
        case a.type
        when 'amazon' then 'SC'
        WHEN 'amazonvendords' then 'VC'
        WHEN 'walmart' then 'walmart'
        else null end  as channel_key,
        a.type as channel,
        a.country_code as country_value,
        b.name_cn as country_key
        from dashboard.accounts a
        left join dashboard.countries b on a.country_code=b.code
        <where>
            ((a.account_type ='Amazon_VC' and a.type='amazonvendords') or a.account_type='Amazon_SC' or a.account_type='walmart')
            <if test="contextId != null">and a.org_id = #{contextId}</if>
           <if test="dateSet.channel != null and dateSet.channel != ''">and a.type = #{dateSet.channel}</if>
            <if test="dateSet.country != null and dateSet.country != ''">and find_in_set(b.code,#{dateSet.country})</if>
        </where>

    </select>

    <sql id="selectAmzPromotionsCouponByStateSql">
        select a.id, a.promotions_name, a.promotions_state, a.country, a.shop_name, a.promotions_id, a.promotions_type, a.vendor_code, a.begin_time,a.end_time, a.billing_contact, a.funding_agreement, a.created_by, a.created_name, a.created_at, a.updated_by, a.updated_name, a.updated_at, a.disabled_by, a.disabled_name, a.disabled_at, a.revenue, a.units_sold, a.glance_views, a.percent_conversion, a.amount_spent, a.budget, a.remark, a.channel,a.organization_id ,a.discount,a.share,a.redemptions,a.code,a.discount_flag,a.flag,a.merchandising_fee,a.shop_id from amz_promotions a
        LEFT JOIN amz_promotions_sku_detail b on a.id=b.promotions_id
    </sql>
<!--    查询同一组织同一店铺同一asin指定状态的promotions-->
    <select id="selectAmzPromotionsListByState"  resultMap="AmzPromotionsResult">
        select a.id, a.promotions_name, a.promotions_state, a.country, a.shop_name, a.promotions_id, a.promotions_type, a.vendor_code, a.begin_time,a.end_time, a.billing_contact, a.funding_agreement, a.created_by, a.created_name, a.created_at, a.updated_by, a.updated_name, a.updated_at, a.disabled_by, a.disabled_name, a.disabled_at, a.revenue, a.units_sold, a.glance_views, a.percent_conversion, a.amount_spent, a.budget, a.remark, a.channel,a.organization_id ,a.discount,a.share,a.redemptions,a.code,a.discount_flag,a.flag,a.merchandising_fee,a.shop_id from amz_promotions a
        LEFT JOIN amz_promotions_sku_detail b on a.id=b.promotions_id
        left join dashboard.accounts c on a.shop_id=c.id
        <where>
            <if test="contextId != null ">and  a.organization_id = #{contextId}</if>
            <if test="shopId !=null"> and a.shop_id=#{shopId}</if>
            <if test="country != null and country != ''"> and c.country_code=#{country}</if>
            <if test="asin!=null"> and b.asin=#{asin}</if>
            <if test="promotionState!=null and promotionState.length>0">
                and (a.promotions_state in
                <foreach collection="promotionState" open="(" separator="," close=")" item="state">
                    #{state}
                </foreach>
                or (a.promotions_state is null and a.approval_status !='已退回'))
            </if>
        </where>
    </select>

    <sql id="getAccountWithPromotionsSql" >
        SELECT a.id shop_id,a.store_name,b.id,b.promotions_id,b.end_time,b.begin_time from erp.amz_promotions  b  left join  dashboard.accounts a  on b.shop_id=a.id
    </sql>
    <select id="getAccountWithPromotions" resultMap="getAccountWithPromotions">
        <include refid="getAccountWithPromotionsSql"></include>
        <where>
            <if test="1==1"> and b.approval_status !='待审批' and b.sub_shop_back=1 and b.complete_date is null and b.channel  in(1,2) and b.agreement_flag=0</if>
            <if test="date != null">and date_format(b.end_time, '%Y-%m-%d') &gt;=date_format(#{date}, '%Y-%m-%d')</if>
            <if test="state != null and state.length >0">
                and  (b.promotions_state not in
                <foreach collection="state" item="s" open="(" close=")" separator=",">
                    #{s}
                </foreach>
            </if>
            <if test="2==2">
                or b.promotions_state is null)
            </if>
        </where>
    </select>







    <select id="selectAmzPromotionsLists" parameterType="com.bizark.op.api.entity.op.promotions.AmzPromotions" resultMap="AmzPromotionsResult">
        <include refid="selectAmzPromotionsVo"/>
        <where>
            <if test="promotionsName != null  and promotionsName != ''"> and a.promotions_name like concat('%', #{promotionsName}, '%')</if>
            <if test="promotionsState != null "> and a.promotions_state = #{promotionsState}</if>
            <if test="country != null  and country != ''"> and a.country = #{country}</if>
            <if test="shopName != null  and shopName != ''"> and a.shop_name like concat('%', #{shopName}, '%')</if>
            <if test="promotionsId != null  and promotionsId != ''"> and a.promotions_id = #{promotionsId}</if>
            <if test="promotionsType != null "> and a.promotions_type = #{promotionsType}</if>
            <if test="vendorCode != null  and vendorCode != ''"> and a.vendor_code = #{vendorCode}</if>
            <if test="beginTime != null "> and a.begin_time = #{beginTime}</if>
            <if test="endTime != null "> and a.end_time = #{endTime}</if>
            <if test="billingContact != null  and billingContact != ''"> and a.billing_contact = #{billingContact}</if>
            <if test="fundingAgreement != null "> and a.funding_agreement = #{fundingAgreement}</if>
            <if test="revenue != null "> and a.revenue = #{revenue}</if>
            <if test="unitsSold != null "> and a.units_sold = #{unitsSold}</if>
            <if test="glanceViews != null "> and a.glance_views = #{glanceViews}</if>
            <if test="percentConversion != null "> and a.percent_conversion = #{percentConversion}</if>
            <if test="amountSpent != null "> and a.amount_spent = #{amountSpent}</if>
            <if test="budget != null "> and a.budget = #{budget}</if>
            <if test="channel != null "> and a.channel = #{channel}</if>
            <if test="organizationId != null "> and a.organization_id = #{organizationId}</if>
            <if test="discount != null "> and a.discount = #{discount}</if>
            <if test="share != null "> and a.share = #{share}</if>
            <if test="redemptions != null "> and a.redemptions = #{redemptions}</if>
            <if test="code != null  and code != ''"> and a.code = #{code}</if>
            <if test="discountFlag != null  and discountFlag != ''"> and a.discount_flag = #{discountFlag}</if>
            <if test="flag != null  and flag != ''"> and a.flag = #{flag}</if>
            <if test="merchandisingFee != null "> and a.merchandising_fee = #{merchandisingFee}</if>
            <if test="shopId != null "> and a.shop_id = #{shopId}</if>
        </where>
    </select>





    <update id="updatePromotionActiveStateAndApprovalState" parameterType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
        update amz_promotions
        <trim prefix="SET" suffixOverrides=",">
            <if test="promotionsState == null">promotions_state = #{promotionsState},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="updatedName != null">updated_name = #{updatedName},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="approvalStatus != null and approvalStatus!=''">approval_status = #{approvalStatus},</if>
        </trim>
        where id = #{id}
    </update>


    <select id="selectAmzPromotionListByConditions" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotionFeeDataVerify">
        select a.id,a.promotions_state,a.promotions_id,a.promotions_name,b.asin,b.per_funding from amz_promotions a left join amz_promotions_sku_detail b on a.id=b.promotions_id

        where a.promotions_id is not null and
           (b.per_funding is null or b.per_funding ='')
        and a.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>





    <select id="selectByPromotionIdAndAsinAndBeginDateAndEndDate" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
        select a.* from amz_promotions a left join amz_promotions_sku_detail b on a.id=b.promotions_id
        where a.organization_id=#{contextId} and a.promotions_id=#{promotionId}
        and (
        a.agreement_flag=0 or(a.agreement_flag=1 and b.asin=#{asin} and a.begin_time=#{beginTime} and a.end_time=#{endTime})
        )
    </select>


    <select id="selectAmzPromotionsListByTemu" parameterType="com.bizark.op.api.entity.op.promotions.AmzPromotions" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
        select  a.shop_id, GREATEST(begin_time, submission_due_date)  as begin_time,id,end_time ,a.id, ifnull(a.skc_stock,0) as skc_stock   ,a.remain_stock
        as remain_stock from amz_promotions a
        <where>
            and  channel=4
            and  end_time >=#{endTime}
            AND  GREATEST(a.begin_time, a.submission_due_date)  <![CDATA[<=]]> '2024-09-01'
            <if test="id !=null ">and a.id= #{id}</if>
            and a.platform_status!=14 and a.platform_status != 16
      </where>
        order by   GREATEST(begin_time, submission_due_date) Asc
    </select>


    <select id="selectAmzPromotionsListBySkcGroup" parameterType="com.bizark.op.api.entity.op.promotions.AmzPromotions" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotionsTemuVO">
        SELECT  b.parent_asin as skc , GROUP_CONCAT(DISTINCT a.id)  as promotions_ids , GROUP_CONCAT(DISTINCT b.seller_sku)  as seller_sku_list  FROM amz_promotions a
        LEFT JOIN amz_promotions_sku_detail b ON a.id=b.promotions_id
        <where>
            and   b.promotions_id is not null
            and  channel=4
            and  end_time >=#{endTime}

            and a.platform_status!=14 and a.platform_status != 16
            <if test="id !=null ">and a.id= #{id}</if>
            <if test="skc !=null ">and b.parent_asin= #{skc}</if>
            <if test="shopId !=null ">and a.shop_id= #{shopId}</if>
            <if test="ids != null and ids.size() >0">
                and a.id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY    b.parent_asin
    </select>


    <select id="selectTemuSkcList" resultType="string">
        SELECT DISTINCT  b.parent_asin
        from  amz_promotions a
        LEFT JOIN amz_promotions_sku_detail b ON a.id=b.promotions_id
        <where>
            and  a.channel=4
            and   b.parent_asin is not null
            <if test="invitationId !=null ">and a.invitation_id= #{invitationId}</if>
            <if test="shopId!=null ">and a.shop_id= #{shopId}</if>
            and a.platform_status!=14 and a.platform_status != 16
        </where>
    </select>

    <select id="selectAmzPromotionByInvitationIdAndParentAsin" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
        select a.* from erp.amz_promotions a left join erp.amz_promotions_sku_detail b on a.id=b.promotions_id
        where a.channel=4 and a.invitation_id=#{invitationId} and b.parent_asin=#{parentAsin} and a.shop_id=#{shopId}
        and a.platform_status!=14 and a.platform_status != 16
        limit 1
    </select>


    <resultMap id="selectAmzApprovalPromotionsDetailVo" type="com.bizark.op.api.vo.promotions.AmzPromotionApprovalDetailVo">

        <result property="id" column="promotion_id"/>
        <result property="promotionsId" column="promotions_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="title"/>
        <result property="activeName" column="promotion_name"/>
        <result property="beginAndEndDate" column="begin_and_end_date"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="operator" column="operator"/>
        <result property="createdName" column="created_name"/>
        <result property="instanceId" column="instance_id"/>
        <result property="stageChangeBefore" column="stage_change_before"/>
        <collection property="skuDetailList" ofType="com.bizark.op.api.vo.promotions.AmzPromotionApprovalDetailSkuItemVo">
            <result property="itemId" column="item_id"/>
            <result property="asin" column="asin"/>
            <result property="sellerSku" column="seller_sku"/>
            <result property="sku" column="sku"/>
            <result property="supplyPrice" column="supply_price"/>
            <result property="pageOriginalPrice" column="page_original_price"/>
            <result property="pageSalePrice" column="page_sale_price"/>
            <result property="category" column="category"/>
            <result property="totalAmount" column="budget"/>
            <result property="promotionDiscount" column="promotion_discount"/>
            <result property="expectedDiscountPrice" column="expected_discount_price"/>
            <result property="discountPriceWarn" column="discount_price_warn"/>
            <result property="num" column="num"/>
            <result property="discountPrice" column="discount_price"/>
        </collection>
    </resultMap>
    <select id="selectAmzApprovalDetailPromotionsById" resultMap="selectAmzApprovalPromotionsDetailVo">
        select a.id                                             as promotion_id,
               a.promotions_id,
               a.shop_id,
               a.created_name,
               c.title,
               a.promotions_name                                as promotion_name,
               concat_ws('~', date_format(a.begin_time, '%Y-%m-%d'),
                         date_format(a.end_time, '%Y-%m-%d'))   as begin_and_end_date
                ,
               a.budget                                         as total_amount,
               a.created_name,
               a.created_name                                   as operator,
               a.instance_id,
               b.id                                             as item_id,
               b.asin,
               b.seller_sku,
               null                                             as budget,
               d.seller_sku_price                               as supply_price,
               cast(m.list_price as DECIMAL(10, 2))             as page_original_price,
               if(m.cart_price is not null and m.cart_price != '', cast(m.cart_price as DECIMAL(10, 2)),
                  cast(if(m.discount_price is null or m.discount_price = '',
                          if(m.deal_price is null or m.deal_price = '',
                             if(m.list_price is null or m.list_price = '', null, m.list_price)
                              , m.deal_price)
                      , m.discount_price) as DECIMAL(10, 2)))   as page_sale_price,
               e.category_name                                  as category,

               case
                   when b.discount_flag like CONCAT('%', '/%', '%') ESCAPE '/' then CONCAT(b.discount, b.discount_flag)
                   else concat(b.discount_flag, b.discount) end as promotion_discount,
               d.erpsku                                         as sku,
               b.expected_discount_price,
               b.discount_price_warn,
               a.stage_change_before,
               b.num,b.discount_price


        from erp.amz_promotions a
                 left join erp.amz_promotions_sku_detail b on a.id = b.promotions_id
                 left join dashboard.accounts c on a.shop_id = c.id
                 left join dashboard.product_channels d
                           on c.flag = d.account_id and CAST(b.seller_sku AS CHAR CHARACTER SET utf8) = d.seller_sku
                 left join dashboard.product_channels_slave e on d.id = e.product_channel_id
                 left join erp.mar_listing_info m on b.asin = m.asin and c.country_code = m.country_code


        where a.id = #{id}
    </select>



    <select id="selectAmzPromotionsListByPromotionsIds"    resultType="com.bizark.op.api.entity.op.promotions.TemuPromotionsPriceVO" >
        select  a.id, GREATEST(begin_time, submission_due_date)  as begin_time,end_time,shop_id
        from amz_promotions a
        <where>
            and  a.channel=4
            and a.promotions_id in
            <foreach collection="promotionsIds" item="promotionsId" open="(" close=")" separator=",">
                #{promotionsId}
            </foreach>
            and a.platform_status!=14 and a.platform_status != 16
        </where>
    </select>

<select id="selectTemuNewActiveRepeatByInvitationTypeAndShopIdAndDate" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
    select a.*,max(c.api_end_date) as max_end_time from erp.amz_promotions a left join erp.amz_promotions_sku_detail b on a.id=b.promotions_id
    left join erp.mar_platform_promotions_session_info c on find_in_set(c.session_id,a.session_ids)>0
    where a.channel=4 and a.invitation_type=#{invitationType} and a.shop_id=#{shopId}
    and b.parent_asin=#{parentAsin} and a.platform_status!=14 and a.platform_status != 16
    group by a.id having max_end_time >= #{date}
</select>
<select id="selectTemuPromotionByShopIdAndInvitationTypeAndSkcId" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
    select a.* from erp.amz_promotions a left join erp.amz_promotions_sku_detail b on a.id=b.promotions_id
    where a.channel=4 and a.invitation_type=#{invitationType} and a.shop_id=#{shopId} and b.parent_asin=#{skcId}
    and a.session_ids is not null and a.session_ids!='' and a.platform_status!=14 and a.platform_status != 16
    <if test="id !=null ">and a.id!= #{id} </if>
    group by a.id
</select>


    <select id="selectTemuPromotionByShopIdAndInvitationTypeAndSkcIdAndSessionId"
            resultType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
        select a.*
        from erp.amz_promotions a
                 left join erp.amz_promotions_sku_detail b on a.id = b.promotions_id
        where a.channel = 4
          and a.invitation_type = #{invitationType}
          and a.shop_id = #{shopId}
          and b.parent_asin = #{skcId}
        and a.platform_status!= 14
        and a.platform_status != 16
          and
        <foreach collection="sessionId" item="item" open="(" close=")" separator="and">
            find_in_set(#{item}, a.session_ids)
        </foreach>
        order by a.created_at desc
        limit 1
    </select>



    <select id="selectSessionProtionList"    resultType="com.bizark.op.api.entity.op.promotions.TemuPromotionsPriceVO" >
        select  a.id, GREATEST(begin_time,ifnull(submission_due_date,0))  as begin_time,end_time,shop_id,(ifnull(skc_stock,0) -ifnull(remain_stock,0)) as unused_stock
        from amz_promotions a
        <where>
            and  a.channel=4
            and a.invitation_type in (5,1,27)
            and a.end_time >='2024-11-01'
            <if test="promotionsIds != null and promotionsIds.size() >0">
                and a.promotions_id in
                <foreach collection="promotionsIds" item="promotionsId" open="(" close=")" separator=",">
                    #{promotionsId}
                </foreach>
            </if>

            and a.platform_status!=14 and a.platform_status != 16
        </where>
    </select>

    <select id="selectPromotionAccount" resultType="com.bizark.op.api.entity.op.promotions.PromotionAccountVo">
        select id,
               org_id,
               account_init,
               flag,
               title,
               `type`,
               active,
               store_name,
               sale_channel,
               account_type,
               vendor_info,
               country_code
        from dashboard.accounts
        <where>
            <if test="shopId != null">
                and id in
                <foreach collection="shopId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="accountType != null and accountType.length > 0">
                and account_type in
                <foreach collection="accountType" item="types" open="(" close=")" separator=",">
                    #{types}
                </foreach>
            </if>
            <if test="type != null and type != ''">
                and `type` = #{type}
            </if>
            <if test="active != null and active != ''">
                and `active` = #{active}
            </if>
            <if test="organizationId != null">
                and org_id = #{organizationId}
            </if>
        </where>
    </select>


    <select id="selectCurrencyPromotionOtherPromotion" resultType="com.bizark.op.api.vo.promotions.AmzPromotionsListVO">
        select a.id,
               a.promotions_type,
               a.begin_time,
               a.end_time,
               a.promotions_name,
               b.asin,
               b.seller_sku,
               b.discount,
               b.discount_flag,
               case
                   when b.discount_flag like CONCAT('%', '/%', '%') ESCAPE '/' then CONCAT(b.discount, b.discount_flag)
                   else concat(b.discount_flag, b.discount) end as discount_string,
               b.per_funding,
        a.promotions_id,b.discount_price
        from erp.amz_promotions a
                 left join erp.amz_promotions_sku_detail b on a.id = b.promotions_id
        where a.channel in (1, 2)
          and a.disabled_by is null
          and a.organization_id = #{organizationId}
          and a.shop_id = #{shopId}
          and b.asin in
        <foreach collection="currencyCouponAsinList" item="asin" open="(" close=")" separator=",">
            #{asin}
        </foreach>
        and a.sub_shop_back = 1
        and a.end_time >= #{nowPstDate}
        <if test="promotionId != null">
            and a.id != #{promotionId}
        </if>
    </select>



    <select id="selectMeibenPromotionGroupByInvitationIdAndShopId" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
        select a.shop_id,a.invitation_id from erp.amz_promotions a left join dashboard.accounts b on a.shop_id=b.id
        where  a.channel=4 and a.sub_shop_back=1 and b.sale_channel='2' and a.invitation_id is not null and a.invitation_id!=''
        and a.end_time >=#{nowPstDate}
        <if test="platStatusList != null and platStatusList.size() != 0">
            and a.platform_status in
            <foreach collection="platStatusList" item="platStatus" open="(" close=")" separator=",">
                #{platStatus}
            </foreach>
        </if>
        and a.platform_status!=14 and a.platform_status != 16
        group by a.shop_id,a.invitation_id
    </select>

    <select id="selectMeibenPromotionByInvitationIdAndShopIdAndSkcIdList" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
        select a.*, c.parent_asin as skc
        from erp.amz_promotions a
                 left join dashboard.accounts b on a.shop_id = b.id
                 left join erp.amz_promotions_sku_detail c on a.id = c.promotions_id
        where a.channel = 4
          and a.sub_shop_back = 1
          and b.sale_channel = '2'
          and a.end_time >= #{nowPstDate}
        <if test="platStatusList != null and platStatusList.size() != 0">
            and a.platform_status in
            <foreach collection="platStatusList" item="platStatus" open="(" close=")" separator=",">
                #{platStatus}
            </foreach>
        </if>
          and a.shop_id IN
        <foreach collection="shopIdList" item="shopId" open="(" close=")" separator=",">
            #{shopId}
        </foreach>
        and a.invitation_id IN
        <foreach collection="invitationIdList" item="invitationId" open="(" close=")" separator=",">
            #{invitationId}
        </foreach>

        and a.platform_status != 14 and a.platform_status != 16
        group by a.id

    </select>


    <select id="selectExistSemiShortPromotion" resultType="com.bizark.op.api.entity.op.promotions.AmzPromotions">
        select a.shop_id, a.invitation_type
        from erp.amz_promotions a
                 left join dashboard.accounts b on a.shop_id = b.id
        where a.channel = 4
          and a.invitation_type in
        <foreach collection="invitationTypeList" item="invitationType" open="(" close=")" separator=",">
            #{invitationType}
        </foreach>
        and a.sub_shop_back = 1
        and a.platform_status in (4, 5)
        and b.sale_channel = '1'
        and b.org_id in (1000049,1000707,1000704)
        group by a.shop_id, a.invitation_type
    </select>

    <select id="selectSkuDetailByShopAndActiveType" resultType="String">
        select distinct c.parent_asin
        from erp.amz_promotions a
        left join dashboard.accounts b on a.shop_id = b.id
        left join erp.amz_promotions_sku_detail c on a.id=c.promotions_id
        where a.channel = 4
        and a.shop_id = #{shopId}
        and a.invitation_type = #{activeType}
        and a.sub_shop_back = 1
        and a.platform_status in (4, 5)
        and b.sale_channel = '1'
    </select>
</mapper>
