<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bizark.op.service.mapper.mar.WalmartShipmentAttachMapper">

    <select id="getGtin" resultType="com.bizark.op.api.entity.op.sale.ProductChannels">
        select account_id,seller_sku, gtin from dashboard.product_channels
        where  sell_status='Hit Shelve' and gtin is not null and gtin!=''
            and seller_sku is not null and seller_sku!=''
        and org_id=1000049 and account_id in('WAL-ZJHJ-US','DUMOS INC-US')
        order by updated_at desc
    </select>
</mapper>
